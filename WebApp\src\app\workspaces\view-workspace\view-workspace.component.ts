import { CommonModule } from '@angular/common';
import { Component, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import {
  Router,
  RouterLink,
  RouterLinkActive,
  RouterOutlet,
} from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { ThemeToggleComponent } from '../../components/theme-toogle.component';
import { AuthService } from '../../../shared/services/auth.service';

@Component({
  selector: 'app-view-workspace',
  standalone: true,
  imports: [
    CommonModule,
    NzBreadCrumbModule,
    RouterLink,
    RouterOutlet,
    RouterLinkActive,
  ],
  templateUrl: './view-workspace.component.html',
  styleUrl: './view-workspace.component.css',
})
export class ViewWorkspaceComponent implements AfterViewInit, OnDestroy {
  @ViewChild('mainContent') mainContent!: ElementRef;
  showScrollButton = false;
  isSidebarOpen = true;
  workspaceName = this.router.url.split('/')[2];
  tabName = this.router.url.split('/')[3];
  private scrollHandler: () => void;

  constructor(private router: Router, public authService: AuthService) {
    // Initialize the scroll handler
    this.scrollHandler = () => {
      const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');
      if (scrollableContent) {
        this.showScrollButton = scrollableContent.scrollTop > 300;
      }
    };
  }

  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    this.workspaceName.replace(/[^a-zA-Z0-9 ]/g, '');
    console.log(this.workspaceName);
  }

  get isAdmin() {
    return this.authService.isAdmin();
  }

  removeSpecialChars(str: string) {
    return decodeURIComponent(str).replace(/[^a-zA-Z0-9 ]/g, '')
  }

  ngDoCheck(): void {
    //Called every time that the input properties of a component or a directive are checked. Use it to extend change detection by performing a custom check.
    //Add 'implements DoCheck' to the class.
    this.tabName = this.router.url.split('/')[3];
  }

  ngAfterViewInit() {
    setTimeout(() => {
      const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');
      if (scrollableContent) {
        // Add scroll event listener
        scrollableContent.addEventListener('scroll', this.scrollHandler);

        // Initial check for scroll position
        this.scrollHandler();
      }
    });
  }

  ngOnDestroy() {
    const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');
    if (scrollableContent) {
      scrollableContent.removeEventListener('scroll', this.scrollHandler);
    }
  }


}

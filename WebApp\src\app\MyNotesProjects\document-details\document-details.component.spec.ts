import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DocumentDetailsComponent } from './document-details.component';
import { ActivatedRoute } from '@angular/router';
import { NotesService } from '../services/notes.service';
import { of } from 'rxjs';
import { MarkdownModule } from 'ngx-markdown';

describe('DocumentDetailsComponent', () => {
  let component: DocumentDetailsComponent;
  let fixture: ComponentFixture<DocumentDetailsComponent>;

  const mockNotesService = {
    getNoteById: jasmine.createSpy('getNoteById').and.returnValue(of({
      id: 1,
      title: 'Test Note',
      content: '# Test Content'
    }))
  };

  const mockActivatedRoute = {
    params: of({ id: '1' })
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DocumentDetailsComponent, MarkdownModule.forRoot()],
      providers: [
        { provide: NotesService, useValue: mockNotesService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DocumentDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load note details on init', () => {
    expect(mockNotesService.getNoteById).toHaveBeenCalledWith('1');
  });
});

<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">
  <!-- Main Container -->
  <div class="flex-1 px-6 py-4 overflow-hidden flex flex-col">
    <!-- Header -->
    <div class="sticky-header flex flex-col sm:flex-row justify-between mb-6 gap-4 bg-[var(--background-light-gray)] items-center">
      <div class="flex items-center gap-3">
        <button (click)="goBack()"
                class="w-10 h-10 flex items-center justify-center bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all duration-300 shadow-sm hover:shadow-md">
          <i class="ri-arrow-left-line"></i>
        </button>
        <div class="flex flex-col">
          <h1 class="text-2xl font-semibold text-[var(--text-dark)] flex items-center gap-2">
            <i class="ri-plug-2-line text-[var(--primary-purple)]"></i>
            <span class="animate-fadeIn">Plugin Details</span>
          </h1>
          <p class="text-sm text-[var(--text-medium-gray)] mt-1">Viewing details for {{ pluginName }}</p>
        </div>
      </div>

      @if (plugin?.type?.toLowerCase() === 'openapi') {
        <button (click)="resyncPlugin()"
                class="w-full sm:w-auto h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md"
                [disabled]="loading">
          <i class="ri-refresh-line" [class.animate-spin]="loading"></i>
          <span>Resync Plugin</span>
        </button>
      }
    </div>

    <!-- Content Area with Proper Y-Axis Scrolling -->
    <div class="flex-1 overflow-y-auto pr-1">
      <!-- Content States -->
      @if (loading) {
        <!-- Loading State -->
        <div class="flex items-center justify-center py-16 bg-[var(--background-white)] rounded-lg shadow-sm">
          <div class="flex flex-col items-center">
            <div class="w-12 h-12 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
              <i class="ri-loader-4-line text-[var(--primary-purple)] text-2xl animate-spin"></i>
            </div>
            <p class="text-[var(--text-medium-gray)]">Loading plugin details...</p>
          </div>
        </div>
      } @else {
        @if (plugin) {
          <!-- Plugin Details Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Left Column: Basic Information -->
          <div class="lg:col-span-1">
            <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-5 mb-6">
              <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4 flex items-center gap-2">
                <i class="ri-information-line text-[var(--primary-purple)]"></i>
                <span>Basic Information</span>
              </h3>

              <div class="space-y-4">
                <!-- Plugin Name -->
                <div class="info-item">
                  <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Plugin Name</label>
                  <div class="bg-[var(--hover-blue-gray)] bg-opacity-50 rounded-md px-3 py-2">
                    <p class="text-sm text-[var(--text-dark)] font-medium m-0">{{ plugin.pluginName }}</p>
                  </div>
                </div>

                <!-- Plugin Type -->
                <div class="info-item">
                  <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Type</label>
                  <div class="flex items-center">
                    <span class="inline-block px-2.5 py-1 rounded-full text-xs font-medium"
                      [ngClass]="plugin.type?.toLowerCase() === 'openapi' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'">
                      {{ plugin.type }}
                    </span>
                  </div>
                </div>

                <!-- API URL -->
                @if (plugin.url) {
                  <div class="info-item">
                    <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">API URL</label>
                    <div class="bg-[var(--hover-blue-gray)] bg-opacity-50 rounded-md px-3 py-2 break-all">
                      <a [href]="plugin.url" target="_blank"
                        class="text-sm text-[var(--primary-purple)] hover:underline">
                        {{ plugin.url }}
                      </a>
                    </div>
                  </div>
                }
              </div>
            </div>

            <!-- Dates Information -->
            <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-5">
              <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4 flex items-center gap-2">
                <i class="ri-calendar-line text-[var(--primary-purple)]"></i>
                <span>Timeline</span>
              </h3>

              <div class="space-y-4">
                <!-- Created Date -->
                <div class="info-item">
                  <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Created Date</label>
                  <div class="flex items-center gap-2">
                    <i class="ri-time-line text-[var(--text-medium-gray)]"></i>
                    <p class="text-sm text-[var(--text-dark)] m-0">{{ formatDate(plugin.createdDate) }}</p>
                  </div>
                </div>

                <!-- Last Modified Date -->
                @if (plugin.lastModifiedDate) {
                  <div class="info-item">
                    <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Last Modified</label>
                    <div class="flex items-center gap-2">
                      <i class="ri-edit-line text-[var(--text-medium-gray)]"></i>
                      <p class="text-sm text-[var(--text-dark)] m-0">{{ formatDate(plugin.lastModifiedDate) }}</p>
                    </div>
                  </div>
                }
              </div>
            </div>
          </div>

          <!-- Right Column: Functions -->
          <div class="lg:col-span-2">
            <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-5 h-full">
              <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4 flex items-center gap-2">
                <i class="ri-function-line text-[var(--primary-purple)]"></i>
                <span>Available Functions</span>
                <span class="inline-flex items-center justify-center px-2 py-0.5 ml-2 rounded-full text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
                  {{ getFunctionList(plugin.functions).length }}
                </span>
              </h3>

              <div class="functions-list space-y-2 max-h-[calc(100vh-300px)] overflow-y-auto pr-2 custom-scrollbar">
                @for (func of getFunctionList(plugin.functions); track func; let i = $index) {
                  <div class="function-item bg-[var(--hover-blue-gray)] bg-opacity-50 p-3 rounded-md hover:bg-opacity-70 transition-all duration-200 animate-fadeIn"
                       [ngStyle]="{'animation-delay': (i * 0.05) + 's'}">
                    <div class="flex items-start">
                      <div class="w-6 h-6 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center flex-shrink-0 mr-3 mt-0.5">
                        <span class="text-xs font-medium text-[var(--primary-purple)]">{{ i + 1 }}</span>
                      </div>
                      <div class="flex-1">
                        <p class="text-sm text-[var(--text-dark)] font-mono m-0">{{ func }}</p>
                      </div>
                    </div>
                  </div>
                }

                @if (getFunctionList(plugin.functions).length === 0) {
                  <div class="flex flex-col items-center justify-center py-8 px-4">
                    <div class="w-12 h-12 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-3">
                      <i class="ri-function-line text-2xl text-[var(--text-medium-gray)]"></i>
                    </div>
                    <p class="text-[var(--text-medium-gray)] text-center">No functions available for this plugin</p>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
        } @else {
          <!-- Error State -->
          <div class="flex flex-col items-center justify-center py-16 px-4 bg-[var(--background-white)] rounded-lg shadow-sm">
            <div class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
              <i class="ri-error-warning-line text-3xl text-[var(--text-medium-gray)]"></i>
            </div>
            <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">Plugin not found</h3>
            <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-6">
              The plugin you're looking for doesn't exist or may have been deleted.
            </p>
            <button
              (click)="goBack()"
              class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 flex items-center gap-2 shadow-sm hover:shadow-md"
            >
              <i class="ri-arrow-left-line"></i>
              <span>Back to Plugins</span>
            </button>
          </div>
        }
      }
    </div>
  </div>
</div>

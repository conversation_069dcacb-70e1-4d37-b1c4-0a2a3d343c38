<div class="h-screen w-[3rem] bg-[var(--background-white)] text-[var(--text-dark)] flex showSidebar transition-all">
  <div class="flex h-screen w-[3rem]">
    <!-- Left Icon Bar -->
    <div
      class="w-full bg-[var(--background-light-gray)] flex flex-col h-full py-[var(--padding-small)] border-r border-[var(--hover-blue-gray)]">
      <div class="*:cursor-pointer *:transition-all *:duration-300">
        <div
          class="w-9 h-9 rounded-[var(--border-radius-large)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex justify-center items-center mb-4 bg-transparent">
          <i class="ri-menu-2-fill text-[var(--text-medium-gray)] text-xl cursor-pointer"></i>
        </div>
        <button
          class="w-9 h-9 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex justify-center items-center mb-2 border-none outline-none bg-transparent"
          [class.!bg-[var(--secondary-purple)]]="activeTab === 'all'" (click)="activeTab = 'all'">
          <i class="ri-chat-ai-line text-[var(--text-medium-gray)] text-xl"
            [class.!text-black]="activeTab === 'all'"></i>
        </button>
        <button
          class="w-9 h-9 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex justify-center items-center mb-2 border-none outline-none bg-transparent"
          [class.!bg-[var(--secondary-purple)]]="activeTab === 'favorite'" (click)="activeTab = 'favorite'">
          <i class="ri-star-line text-[var(--text-medium-gray)] text-xl"
            [class.text-black]="activeTab === 'favorite'"></i>
        </button>
        <button
          class="w-9 h-9 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex justify-center items-center mb-2 border-none outline-none bg-transparent"
          [class.!bg-[var(--secondary-purple)]]="activeTab === 'archive'" (click)="activeTab = 'archive'">
          <i class="ri-archive-line text-[var(--text-medium-gray)] text-xl"
            [class.text-black]="activeTab === 'archive'"></i>
        </button>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 flex flex-col h-full bg-[var(--background-white)] relative left-[10%]">
      <!-- Header -->
      <div
        class="px-[var(--padding-small)] py-[var(--padding-small)] border-b border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2 w-[16rem]">
        <div class="flex items-center justify-between w-full">
          <span class="font-bold text-[var(--text-dark)]">Chat With Ai</span>
          <div class="flex items-center gap-2">
            <button
              class="w-6 h-6 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex sm:hidden justify-center items-center outline-none border-none bg-transparent text-lg font-bold cursor-pointer"
              (click)="addNewChats($event);">
              <i class="ri-add-line text-[var(--text-dark)]"></i>
            </button>
            <button
              class="w-6 h-6 rounded-[var(--border-radius-small)] hidden hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] sm:flex justify-center items-center outline-none border-none bg-transparent text-lg font-bold cursor-pointer"
              (click)="addNewChats($event);">
              <i class="ri-add-line text-[var(--text-dark)]"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Tab Switcher for All/Pinned -->
      <div *ngIf="activeTab === 'all' || activeTab === 'pinned-history'"
        class="px-[var(--padding-small)] py-[var(--padding-small)] border-b border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2">
        <div class="flex items-center gap-2 p-1 w-full bg-[var(--header-bg)] rounded-[var(--border-radius-small)]">
          <button
            class="px-3 py-1 rounded-[var(--border-radius-small)] text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] outline-none border-none w-1/2 cursor-pointer bg-transparent"
            [class.!bg-[var(--background-white)]]="activeTab === 'all'" (click)="activeTab = 'all'">
            All
          </button>
          <button
            class="px-3 py-1 rounded-[var(--border-radius-small)] bg-[var(--header-bg)] text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] outline-none border-none w-1/2 cursor-pointer"
            [class.!bg-[var(--background-white)]]="activeTab === 'pinned-history'"
            (click)="activeTab = 'pinned-history'">
            Pinned
          </button>
        </div>
      </div>

      <!-- Search Bar -->
      <div class="px-[var(--padding-small)]">
        <div class="flex items-center relative bg-[var(--header-bg)] px-2 rounded-md">
          <i class="ri-search-line text-[var(--text-medium-gray)]"></i>
          <input type="text" placeholder="Search"
            class="w-full px-2 py-1 text-[var(--text-dark)] rounded-[var(--border-radius-small)] outline-none border-none bg-transparent" />
        </div>
      </div>

      <!-- Chat List Template -->
      <ng-template #chatListTemplate let-chats="chats" let-isGrouped="isGrouped">
        <div class="py-[var(--padding-small)]">
          <div *ngIf="isGrouped">
            <div *ngFor="let group of chats | keyvalue : originalOrder">
              <div class="py-2 text-[var(--text-medium-gray)]" *ngIf="group.value.length > 1">{{ group.key }}</div>
              <div *ngFor="let chat of group.value; trackBy: trackByChatId">
                <ng-container *ngTemplateOutlet="chatItem; context: { $implicit: chat }"></ng-container>
              </div>
            </div>
          </div>
          <div *ngIf="!isGrouped">
            <div *ngFor="let chat of chats; trackBy: trackByChatId">
              <ng-container *ngTemplateOutlet="chatItem; context: { $implicit: chat }"></ng-container>
            </div>
          </div>
          <div *ngIf="!isGrouped && chats.length === 0"
            class="flex items-center justify-center px-2 py-4 flex-col gap-2 h-full">
            <span class="text-[var(--text-medium-gray)]">No {{ getCurrentTabTitle() }} for now.</span>
            <i class="ri-emotion-sad-line text-[var(--text-medium-gray)] text-4xl"></i>
          </div>

          <div *ngIf="hasMoreForCurrentTab()"
            class="px-[var(--padding-small)] py-[var(--padding-small)] border-t border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2">
            <button (click)="loadMoreChatList()"
              class="w-full py-2 px-3 rounded-[var(--border-radius-small)] bg-[var(--header-bg)] text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] mt-2 flex items-center justify-center outline-none border-none cursor-pointer">
              Show more <i class="ri-arrow-down-s-line ml-2"></i>
            </button>
          </div>
        </div>
      </ng-template>

      <!-- Chat Item Template -->
      <ng-template #chatItem let-chat>
        <div
          class="w-auto py-1 px-2 rounded-[8px] border border-[var(--hover-blue-gray)] flex sm:hidden items-center justify-between text-[var(--text-dark)] font-medium hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)] mb-1 cursor-pointer"
          [class.!bg-[var(--secondary-purple)]]="chat.id == chatListService.chatId"
          [class.!text-black]="chat.id == chatListService.chatId"
          (click)="toggleChat($event, chat); togglingService.toggleNavbar()">
          <div class="flex items-center gap-2">
            <span class="rounded-[4px] border border-[var(--hover-blue-gray)] flex items-center justify-center">
              <i class="ri-chat-1-line text-[var(--text-medium-gray)] text-sm"
                *ngIf="chat.id != chatListService.chatId"></i>
              <i class="ri-check-line text-[var(--primary-purple)] text-sm"
                [class.text-black]="chat.id == chatListService.chatId" *ngIf="chat.id == chatListService.chatId"></i>
            </span>
            <div class="flex flex-col text-left text-black" [class.!text-black]="chat.id == chatListService.chatId">
              <span class="text-[var(--text-dark)] text-sm truncate"
                [class.text-dark]="chat.id == chatListService.chatId" style="
                  max-width: 8rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                ">{{ chat.title }}</span>
              <span class="text-[var(--text-medium-gray)] text-xs truncate" style="
                  max-width: 8rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "></span>
            </div>
          </div>
        </div>
        <div
          class="w-auto hidden sm:flex py-1 px-2 rounded-[8px] border border-[var(--hover-blue-gray)] items-center justify-between text-[var(--text-dark)] font-medium hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)] mb-1 cursor-pointer group"
          [class.!bg-[var(--secondary-purple)]]="chat.id == chatListService.chatId" (click)="toggleChat($event, chat)">
          <div class="flex items-center gap-2">
            <span class="rounded-[4px] border border-[var(--hover-blue-gray)] flex items-center justify-center">
              <i class="ri-chat-1-line text-[var(--text-medium-gray)] text-sm"
                *ngIf="chat.id != chatListService.chatId"></i>
              <i class="ri-check-line text-[var(--primary-purple)] text-sm"
                [class.text-black]="chat.id == chatListService.chatId" *ngIf="chat.id == chatListService.chatId"></i>
            </span>
            <div class="flex flex-col text-left">
              <span class="text-[var(--text-dark)] text-sm truncate"
                [class.!text-black]="chat.id == chatListService.chatId" style="
                  max-width: 8rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                ">{{ chat.title }}</span>
              <span class="text-[var(--text-medium-gray)] text-xs truncate" style="
                  max-width: 8rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "></span>
            </div>
          </div>
          <button nz-popover [nzPopoverContent]="contentTemplate" nzPopoverTrigger="click"
            class="!bg-transparent !border-none !outline-none hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] rounded-[var(--border-radius-small)] text-[var(--text-dark)] flex items-center justify-center gap-2 p-0 text-3xl opacity-0 group-hover:opacity-100 cursor-pointer">
            <i class="ri-more-fill text-[var(--dark-text)] text-3xl"
              [class.!text-black]="chat.id == chatListService.chatId"></i>
          </button>
          <ng-template #contentTemplate>
            <div>
              <div
                class="py-1 group flex items-center justify-between gap-2 hover:bg-slate-300 px-2 cursor-pointer rounded-sm text-[var(--text-dark)] hover:text-black"
                (click)="addToPinnedChat(chat)">
                Pin Chat <i class="ri-pushpin-2-line group-hover:text-black" [class.ri-pushpin-fill]="chat.isPinned"
                  [class.ri-pushpin-line]="!chat.isPinned" [class.text-[var(--text-medium-gray)]]="!chat.isPinned"
                  [class.text-[var(--primary-purple)]]="chat.isPinned"></i>
              </div>
              <div
                class="py-1 group flex items-center justify-between gap-2 hover:bg-slate-300 px-2 cursor-pointer rounded-sm text-[var(--text-dark)] hover:text-black"
                (click)="addToFavChat(chat)">
                Add to Favorites <i class="ri-star-line group-hover:text-black" [class.ri-star-fill]="chat.isFavorite"
                  [class.ri-star-line]="!chat.isFavorite" [class.text-[var(--text-medium-gray)]]="!chat.isFavorite"
                  [class.text-[var(--primary-purple)]]="chat.isFavorite"></i>
              </div>
              <div
                class="py-1 group flex items-center justify-between gap-2 hover:bg-slate-300 px-2 cursor-pointer rounded-sm text-[var(--text-dark)] hover:text-black"
                (click)="addToArchiveChat(chat)">
                Add to Archive <i class="ri-archive-line group-hover:text-black"
                  [class.ri-archive-fill]="chat.isArchived" [class.ri-archive-line]="!chat.isArchived"
                  [class.text-[var(--text-medium-gray)]]="!chat.isArchived"
                  [class.text-[var(--primary-purple)]]="chat.isArchived"></i>
              </div>
            </div>
          </ng-template>
        </div>
      </ng-template>

      <!-- Dynamic Tab Content -->
      <div class="flex-1 flex flex-col">
        <div class="flex items-center px-[var(--padding-small)] mb-1"
          *ngIf="activeTab !== 'all' && activeTab !== 'pinned-history'">
          <span class="text-[var(--text-medium-gray)]">{{ activeTab === 'favorite' ? 'Favorite Chats' : activeTab ===
            'archive' ? 'Archive Chats' : '' }}</span>
        </div>
        <div
          class="flex-1 px-[var(--padding-small)] overflow-y-auto h-auto transition-[var(--transition-default)] duration-300 ease-in-out [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:bg-[var(--hover-blue-gray)] [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-transparent hover:[&::-webkit-scrollbar-thumb]:bg-[var(--secondary-purple)] hover:[&::-webkit-scrollbar-thumb]:cursor-pointer max-h-[82vh]">
          <ng-container
            *ngTemplateOutlet="chatListTemplate; context: { chats: getChatsForTab(), isGrouped: isGroupedForTab() }"></ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

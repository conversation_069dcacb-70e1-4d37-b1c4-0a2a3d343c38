<header
  class="fixed top-0 left-0 right-0 flex flex-col header-bg z-50 shadow-md w-full h-[65px]"
  [ngClass]="{
    'dark-header': themeService.isDarkMode(),
    'light-header': !themeService.isDarkMode()
  }"
>
  <!-- Top Row with Hamburger, Title, Search and Profile -->
  <div
    class="flex items-center justify-between pl-[1.5rem] px-2 h-[34px] w-full"
  >
    <!-- Left Section with Toggle Button and Title -->
    <div class="flex items-center h-full">
      <!-- Toggle Button Centered Vertically -->
      <div class="flex items-center justify-center h-full">
        <button
          class="w-8 h-8 rounded-md border-none transition-all flex justify-center items-center menu-button mr-5 ml-[-8px] mt-7 "
          (click)="togglingservice.toggleNavbar()"
        >
          <i class="ri-menu-2-fill menu-icon text-xl"></i>
        </button>
      </div>

      <!-- AI Work Hub Title -->
      <h1 class="text-xl font-bold header-title flex items-center">
        {{ isAtHomePage || currentUrl === "/" ? "AI Hub" : "AI Work Hub" }}
      </h1>
    </div>

    <!-- Global Search Bar - Centered -->
    <div class="flex-1 max-w-md mx-auto mt-7 relative">
      <div class="relative">
        <input
          type="text"
          class="w-full h-9 pl-9 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-purple focus:border-transparent text-sm search-input"
          placeholder="Search agents by name..."
          [(ngModel)]="searchQuery"
          (input)="onSearchInputChange()"
          (focus)="showSearchResults = true"
          (blur)="onSearchBlur()"
        />
        <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
        <!-- <button *ngIf="searchQuery"
          class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 focus:outline-none" (click)="clearSearch()">
          <i class="ri-close-line"></i>
        </button> -->
      </div>

      <!-- Enhanced Agent Search Results Dropdown -->
      <div
        *ngIf="showSearchResults && filteredResults.length > 0"
        class="absolute left-0 right-0 mt-1 max-h-96 overflow-y-auto rounded-lg shadow-lg z-50 search-results"
        [ngClass]="{
          'bg-[#2b2b33] border border-[#4a4a55]': themeService.isDarkMode(),
          'bg-white border border-gray-200': !themeService.isDarkMode()
        }"
      >
        <div class="p-3">
          <h3 class="text-xs font-bold uppercase mb-3 px-2"
              [ngClass]="{
                'text-gray-400': themeService.isDarkMode(),
                'text-gray-500': !themeService.isDarkMode()
              }">
            AGENTS
          </h3>

          <!-- Simplified Agent Search Result Items -->
          <div
            *ngFor="let result of filteredResults"
            class="p-3 rounded-lg mb-2 last:mb-0 transition-all duration-200 cursor-pointer agent-search-card"
            [ngClass]="{
              'bg-[#3a3a45] border border-[#4a4a55] hover:border-[#00c39a]': themeService.isDarkMode(),
              'bg-[#f8f9fa] border border-[#e9ecef] hover:border-[var(--primary-purple)]': !themeService.isDarkMode()
            }"
            (click)="navigateToResult(result)"
          >
            <!-- Simplified Agent Layout: Icon + Name + View Chat Button -->
            <div class="flex items-center justify-between">
              <!-- Left side: Agent Icon and Name -->
              <div class="flex items-center flex-1 min-w-0">
                <div class="flex-shrink-0 mr-3">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center"
                       [ngClass]="{
                         'bg-[#00c39a]': themeService.isDarkMode(),
                         'bg-[var(--primary-purple)]': !themeService.isDarkMode()
                       }">
                    <i class="ri-robot-line text-white text-sm"></i>
                  </div>
                </div>

                <div class="flex-1 min-w-0">
                  <h4 class="text-base font-semibold truncate"
                      [ngClass]="{
                        'text-white': themeService.isDarkMode(),
                        'text-[var(--text-dark)]': !themeService.isDarkMode()
                      }">
                    {{ result.title }}
                  </h4>
                </div>
              </div>

              <!-- Right side: View Chat Button -->
              <div class="flex-shrink-0 ml-3">
                <button class="view-chat-btn text-xs px-3 py-1.5 rounded-md transition-all duration-200 flex items-center gap-1 border-none"
                        [ngClass]="{
                          'bg-[#00c39a] hover:bg-[#00b389] text-white': themeService.isDarkMode(),
                          'bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] text-white': !themeService.isDarkMode()
                        }"
                        (click)="viewAgentChat(result.agentData, $event)">
                  <i class="ri-chat-3-line text-xs"></i>
                  <span>View Chat</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading Message -->
      <div
        *ngIf="showSearchResults && searchQuery && isLoadingAgents"
        class="absolute left-0 right-0 mt-1 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-50 text-center"
      >
        <div class="flex items-center justify-center gap-2">
          <div class="w-4 h-4 border-2 border-primary-purple border-t-transparent rounded-full animate-spin"></div>
          <p class="text-gray-600">Loading agents...</p>
        </div>
      </div>

      <!-- No Results Message -->
      <div
        *ngIf="showSearchResults && searchQuery && filteredResults.length === 0 && !isLoadingAgents"
        class="absolute left-0 right-0 mt-1 p-4 rounded-lg shadow-lg z-50 text-center"
        [ngClass]="{
          'bg-[#2b2b33] border border-[#4a4a55]': themeService.isDarkMode(),
          'bg-white border border-gray-200': !themeService.isDarkMode()
        }"
      >
        <i class="ri-robot-line text-2xl mb-2"
           [ngClass]="{
             'text-gray-400': themeService.isDarkMode(),
             'text-gray-500': !themeService.isDarkMode()
           }"></i>
        <p [ngClass]="{
             'text-gray-300': themeService.isDarkMode(),
             'text-gray-600': !themeService.isDarkMode()
           }">No agents found for "{{ searchQuery }}"</p>
        <p class="text-xs mt-1"
           [ngClass]="{
             'text-gray-400': themeService.isDarkMode(),
             'text-gray-500': !themeService.isDarkMode()
           }">Try searching for exact agent names</p>
      </div>
    </div>

    <!-- Right Section with Profile Menu -->
    <div class="flex items-center h-full mt-7 mr-4 relative">
      <div class="relative" #profileContainer>
        <!-- Profile Button - With status indicator -->
        <button
          class="flex items-center justify-center h-8 px-2 rounded-full transition-all profile-button"
          (click)="toggleProfileMenu($event)"
          #profileButton
        >
          <div class="relative">
            <div
              class="w-9 h-9 rounded-full flex items-center justify-center user-avatar"
            >
              <span class="user-initial">{{
                user?.name || authService.getUserName().slice(0, 2)
              }}</span>
            </div>
            <!-- Status indicator -->
            <div
              class="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-500 border-2 status-indicator"
            ></div>
          </div>
        </button>

        <!-- Profile Menu Dropdown -->
        <div
          #userMenu
          id="userMenu"
          class="hidden opacity-0 absolute top-full right-0 mt-2 rounded-lg p-3 w-64 shadow-lg user-menu"
          style="z-index: 1050"
        >
          <div class="space-y-2">
            <div
              *ngIf="authService.isUserLoggedIn"
              class="p-2 text-center rounded-[var(--border-radius-small)]"
              [ngClass]="{
                'bg-[#f5f5f7]': !themeService.isDarkMode(),
                'bg-[#2b2b33]': themeService.isDarkMode()
              }"
            >
              <p
                class="text-[var(--font-size-body)] font-[var(--font-weight-regular)] m-0"
                [ngClass]="{
                  'text-black': !themeService.isDarkMode(),
                  'text-white': themeService.isDarkMode()
                }"
              >
                Welcome, {{ user?.name || authService.getUserName() }}
              </p>
              <p
                class="m-0"
                [ngClass]="{
                  'text-[#666]': !themeService.isDarkMode(),
                  'text-[var(--text-medium-gray)]': themeService.isDarkMode()
                }"
              >
                {{ user?.email }}
              </p>
            </div>
            <button
              *ngIf="!authService.isUserLoggedIn"
              class="flex items-center gap-2 w-full p-2 rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer border-none outline-none font-[var(--font-family)]"
              [ngClass]="{
                'bg-white text-black hover:bg-[var(--primary-purple)] hover:text-white':
                  !themeService.isDarkMode(),
                'bg-[var(--background-white)] text-[var(--text-dark)] hover:bg-[var(--secondary-purple)] hover:text-black':
                  themeService.isDarkMode()
              }"
              routerLink="/login"
            >
              <i
                class="ri-login-box-line"
                [ngClass]="{
                  'text-[var(--primary-purple)]': themeService.isDarkMode(),
                  'text-[var(--secondary-purple)]': !themeService.isDarkMode()
                }"
              ></i>
              <span>Login</span>
            </button>
            <div
              class="flex items-center justify-between p-2 rounded-[var(--border-radius-small)]"
              [ngClass]="{
                'bg-white': !themeService.isDarkMode(),
                'bg-[var(--background-white)]': themeService.isDarkMode()
              }"
            >
              <span
                [ngClass]="{
                  'text-black': !themeService.isDarkMode(),
                  'text-[var(--text-dark)]': themeService.isDarkMode()
                }"
                >Theme</span
              >
              <app-theme-toggle></app-theme-toggle>
            </div>
            <button
              *ngIf="authService.isUserLoggedIn"
              class="flex items-center gap-2 w-full p-2 rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer border-none outline-none font-[var(--font-family)] justify-center"
              [ngClass]="{
                'bg-white text-black hover:bg-[var(--primary-purple)] hover:text-white':
                  !themeService.isDarkMode(),
                'bg-[var(--background-white)] text-[var(--text-dark)] hover:bg-[var(--secondary-purple)] hover:text-black':
                  themeService.isDarkMode()
              }"
              (click)="closeProfileMenu(); logout()"
            >
              <i
                class="ri-logout-box-line"
                [ngClass]="{
                  'text-[var(--primary-purple)]': themeService.isDarkMode(),
                  'text-[var(--secondary-purple)]': !themeService.isDarkMode()
                }"
              ></i>
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Row with Breadcrumbs -->
  <div
    class="flex items-center h-[20px] w-full pl-[65px]"
    *ngIf="authService.isUserLoggedIn"
  >
    <!-- Always show AI Hub as first breadcrumb -->
    <a
      [routerLink]="['/']"
      class="text-sm hover:text-[var(--primary-purple)] transition-colors whitespace-nowrap"
      [ngClass]="{
        'breadcrumb-active': isAtHomePage || currentUrl === '/',
        'breadcrumb-inactive': !isAtHomePage && currentUrl !== '/'
      }"
    >
      AI Hub
    </a>
    <!-- Show additional breadcrumbs only when not at home page -->
    <ng-container *ngIf="!isAtHomePage && currentUrl !== '/'">
      <ng-container *ngFor="let link of breadCrumsList; let i = index">
        <ng-container *ngIf="i > 0">
          <span class="breadcrumb-separator mx-1"> / </span>
          <a
            [routerLink]="[link.link]"
            class="text-sm hover:text-[var(--primary-purple)] transition-colors whitespace-nowrap"
            [ngClass]="{
              'breadcrumb-active': i === breadCrumsList.length - 1,
              'breadcrumb-inactive': i !== breadCrumsList.length - 1
            }"
          >
            {{ link.title || "Chat History" }}
          </a>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
</header>

{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-overlay.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i1 from '@angular/cdk/overlay';\nimport { ConnectionPositionPair, CdkOverlayOrigin } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { ElementRef, Directive, Input, NgModule } from '@angular/core';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst POSITION_MAP = {\n  top: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'top'\n  }, {\n    overlayX: 'center',\n    overlayY: 'bottom'\n  }),\n  topCenter: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'top'\n  }, {\n    overlayX: 'center',\n    overlayY: 'bottom'\n  }),\n  topLeft: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'top'\n  }, {\n    overlayX: 'start',\n    overlayY: 'bottom'\n  }),\n  topRight: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'top'\n  }, {\n    overlayX: 'end',\n    overlayY: 'bottom'\n  }),\n  right: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'center'\n  }, {\n    overlayX: 'start',\n    overlayY: 'center'\n  }),\n  rightTop: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'top'\n  }, {\n    overlayX: 'start',\n    overlayY: 'top'\n  }),\n  rightBottom: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'bottom'\n  }, {\n    overlayX: 'start',\n    overlayY: 'bottom'\n  }),\n  bottom: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'bottom'\n  }, {\n    overlayX: 'center',\n    overlayY: 'top'\n  }),\n  bottomCenter: new ConnectionPositionPair({\n    originX: 'center',\n    originY: 'bottom'\n  }, {\n    overlayX: 'center',\n    overlayY: 'top'\n  }),\n  bottomLeft: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'bottom'\n  }, {\n    overlayX: 'start',\n    overlayY: 'top'\n  }),\n  bottomRight: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'bottom'\n  }, {\n    overlayX: 'end',\n    overlayY: 'top'\n  }),\n  left: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'center'\n  }, {\n    overlayX: 'end',\n    overlayY: 'center'\n  }),\n  leftTop: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'top'\n  }, {\n    overlayX: 'end',\n    overlayY: 'top'\n  }),\n  leftBottom: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'bottom'\n  }, {\n    overlayX: 'end',\n    overlayY: 'bottom'\n  })\n};\nconst DEFAULT_TOOLTIP_POSITIONS = [POSITION_MAP.top, POSITION_MAP.right, POSITION_MAP.bottom, POSITION_MAP.left];\nconst DEFAULT_CASCADER_POSITIONS = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topLeft, POSITION_MAP.topRight, POSITION_MAP.topCenter, POSITION_MAP.bottomCenter];\nconst DEFAULT_MENTION_TOP_POSITIONS = [new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'bottom'\n}, {\n  overlayX: 'start',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'bottom'\n}, {\n  overlayX: 'end',\n  overlayY: 'bottom'\n})];\nconst DEFAULT_MENTION_BOTTOM_POSITIONS = [POSITION_MAP.bottomLeft, new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'bottom'\n}, {\n  overlayX: 'end',\n  overlayY: 'top'\n})];\nfunction getPlacementName(position) {\n  for (const placement in POSITION_MAP) {\n    if (position.connectionPair.originX === POSITION_MAP[placement].originX && position.connectionPair.originY === POSITION_MAP[placement].originY && position.connectionPair.overlayX === POSITION_MAP[placement].overlayX && position.connectionPair.overlayY === POSITION_MAP[placement].overlayY) {\n      return placement;\n    }\n  }\n  return undefined;\n}\nconst DATE_PICKER_POSITION_MAP = {\n  bottomLeft: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'bottom'\n  }, {\n    overlayX: 'start',\n    overlayY: 'top'\n  }, undefined, 2),\n  topLeft: new ConnectionPositionPair({\n    originX: 'start',\n    originY: 'top'\n  }, {\n    overlayX: 'start',\n    overlayY: 'bottom'\n  }, undefined, -2),\n  bottomRight: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'bottom'\n  }, {\n    overlayX: 'end',\n    overlayY: 'top'\n  }, undefined, 2),\n  topRight: new ConnectionPositionPair({\n    originX: 'end',\n    originY: 'top'\n  }, {\n    overlayX: 'end',\n    overlayY: 'bottom'\n  }, undefined, -2)\n};\nconst DEFAULT_DATE_PICKER_POSITIONS = [DATE_PICKER_POSITION_MAP.bottomLeft, DATE_PICKER_POSITION_MAP.topLeft, DATE_PICKER_POSITION_MAP.bottomRight, DATE_PICKER_POSITION_MAP.topRight];\nclass NzConnectedOverlayDirective {\n  constructor(cdkConnectedOverlay, nzDestroyService) {\n    this.cdkConnectedOverlay = cdkConnectedOverlay;\n    this.nzDestroyService = nzDestroyService;\n    this.nzArrowPointAtCenter = false;\n    this.cdkConnectedOverlay.backdropClass = 'nz-overlay-transparent-backdrop';\n    this.cdkConnectedOverlay.positionChange.pipe(takeUntil(this.nzDestroyService)).subscribe(position => {\n      if (this.nzArrowPointAtCenter) {\n        this.updateArrowPosition(position);\n      }\n    });\n  }\n  updateArrowPosition(position) {\n    const originRect = this.getOriginRect();\n    const placement = getPlacementName(position);\n    let offsetX = 0;\n    let offsetY = 0;\n    if (placement === 'topLeft' || placement === 'bottomLeft') {\n      offsetX = originRect.width / 2 - 14;\n    } else if (placement === 'topRight' || placement === 'bottomRight') {\n      offsetX = -(originRect.width / 2 - 14);\n    } else if (placement === 'leftTop' || placement === 'rightTop') {\n      offsetY = originRect.height / 2 - 10;\n    } else if (placement === 'leftBottom' || placement === 'rightBottom') {\n      offsetY = -(originRect.height / 2 - 10);\n    }\n    if (this.cdkConnectedOverlay.offsetX !== offsetX || this.cdkConnectedOverlay.offsetY !== offsetY) {\n      this.cdkConnectedOverlay.offsetY = offsetY;\n      this.cdkConnectedOverlay.offsetX = offsetX;\n      this.cdkConnectedOverlay.overlayRef.updatePosition();\n    }\n  }\n  getFlexibleConnectedPositionStrategyOrigin() {\n    if (this.cdkConnectedOverlay.origin instanceof CdkOverlayOrigin) {\n      return this.cdkConnectedOverlay.origin.elementRef;\n    } else {\n      return this.cdkConnectedOverlay.origin;\n    }\n  }\n  getOriginRect() {\n    const origin = this.getFlexibleConnectedPositionStrategyOrigin();\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n  static {\n    this.ɵfac = function NzConnectedOverlayDirective_Factory(t) {\n      return new (t || NzConnectedOverlayDirective)(i0.ɵɵdirectiveInject(i1.CdkConnectedOverlay), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzConnectedOverlayDirective,\n      selectors: [[\"\", \"cdkConnectedOverlay\", \"\", \"nzConnectedOverlay\", \"\"]],\n      inputs: {\n        nzArrowPointAtCenter: \"nzArrowPointAtCenter\"\n      },\n      exportAs: [\"nzConnectedOverlay\"],\n      features: [i0.ɵɵProvidersFeature([NzDestroyService])]\n    });\n  }\n}\n__decorate([InputBoolean()], NzConnectedOverlayDirective.prototype, \"nzArrowPointAtCenter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzConnectedOverlayDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkConnectedOverlay][nzConnectedOverlay]',\n      exportAs: 'nzConnectedOverlay',\n      providers: [NzDestroyService]\n    }]\n  }], () => [{\n    type: i1.CdkConnectedOverlay\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzArrowPointAtCenter: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzOverlayModule {\n  static {\n    this.ɵfac = function NzOverlayModule_Factory(t) {\n      return new (t || NzOverlayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzOverlayModule,\n      declarations: [NzConnectedOverlayDirective],\n      exports: [NzConnectedOverlayDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzOverlayModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NzConnectedOverlayDirective],\n      exports: [NzConnectedOverlayDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction overlayZIndexSetter(overlayRef, zIndex) {\n  if (!zIndex) return;\n  overlayRef['_host'].style.zIndex = `${zIndex}`;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DATE_PICKER_POSITION_MAP, DEFAULT_CASCADER_POSITIONS, DEFAULT_DATE_PICKER_POSITIONS, DEFAULT_MENTION_BOTTOM_POSITIONS, DEFAULT_MENTION_TOP_POSITIONS, DEFAULT_TOOLTIP_POSITIONS, NzConnectedOverlayDirective, NzOverlayModule, POSITION_MAP, getPlacementName, overlayZIndexSetter };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,eAAe;AAAA,EACnB,KAAK,IAAI,uBAAuB;AAAA,IAC9B,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,WAAW,IAAI,uBAAuB;AAAA,IACpC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,IAAI,uBAAuB;AAAA,IAClC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,IAAI,uBAAuB;AAAA,IACnC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,IAAI,uBAAuB;AAAA,IAChC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,UAAU,IAAI,uBAAuB;AAAA,IACnC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,IAAI,uBAAuB;AAAA,IACtC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,QAAQ,IAAI,uBAAuB;AAAA,IACjC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc,IAAI,uBAAuB;AAAA,IACvC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,IAAI,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,aAAa,IAAI,uBAAuB;AAAA,IACtC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,MAAM,IAAI,uBAAuB;AAAA,IAC/B,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,SAAS,IAAI,uBAAuB;AAAA,IAClC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,IAAI,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACH;AACA,IAAM,4BAA4B,CAAC,aAAa,KAAK,aAAa,OAAO,aAAa,QAAQ,aAAa,IAAI;AAC/G,IAAM,6BAA6B,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,SAAS,aAAa,UAAU,aAAa,WAAW,aAAa,YAAY;AACrL,IAAM,gCAAgC,CAAC,IAAI,uBAAuB;AAAA,EAChE,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,IAAM,mCAAmC,CAAC,aAAa,YAAY,IAAI,uBAAuB;AAAA,EAC5F,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,SAAS,iBAAiB,UAAU;AAClC,aAAW,aAAa,cAAc;AACpC,QAAI,SAAS,eAAe,YAAY,aAAa,SAAS,EAAE,WAAW,SAAS,eAAe,YAAY,aAAa,SAAS,EAAE,WAAW,SAAS,eAAe,aAAa,aAAa,SAAS,EAAE,YAAY,SAAS,eAAe,aAAa,aAAa,SAAS,EAAE,UAAU;AAChS,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,2BAA2B;AAAA,EAC/B,YAAY,IAAI,uBAAuB;AAAA,IACrC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG,QAAW,CAAC;AAAA,EACf,SAAS,IAAI,uBAAuB;AAAA,IAClC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG,QAAW,EAAE;AAAA,EAChB,aAAa,IAAI,uBAAuB;AAAA,IACtC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG,QAAW,CAAC;AAAA,EACf,UAAU,IAAI,uBAAuB;AAAA,IACnC,SAAS;AAAA,IACT,SAAS;AAAA,EACX,GAAG;AAAA,IACD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG,QAAW,EAAE;AAClB;AACA,IAAM,gCAAgC,CAAC,yBAAyB,YAAY,yBAAyB,SAAS,yBAAyB,aAAa,yBAAyB,QAAQ;AACrL,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,qBAAqB,kBAAkB;AACjD,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAC5B,SAAK,oBAAoB,gBAAgB;AACzC,SAAK,oBAAoB,eAAe,KAAK,UAAU,KAAK,gBAAgB,CAAC,EAAE,UAAU,cAAY;AACnG,UAAI,KAAK,sBAAsB;AAC7B,aAAK,oBAAoB,QAAQ;AAAA,MACnC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,UAAU;AAC5B,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,cAAc,aAAa,cAAc,cAAc;AACzD,gBAAU,WAAW,QAAQ,IAAI;AAAA,IACnC,WAAW,cAAc,cAAc,cAAc,eAAe;AAClE,gBAAU,EAAE,WAAW,QAAQ,IAAI;AAAA,IACrC,WAAW,cAAc,aAAa,cAAc,YAAY;AAC9D,gBAAU,WAAW,SAAS,IAAI;AAAA,IACpC,WAAW,cAAc,gBAAgB,cAAc,eAAe;AACpE,gBAAU,EAAE,WAAW,SAAS,IAAI;AAAA,IACtC;AACA,QAAI,KAAK,oBAAoB,YAAY,WAAW,KAAK,oBAAoB,YAAY,SAAS;AAChG,WAAK,oBAAoB,UAAU;AACnC,WAAK,oBAAoB,UAAU;AACnC,WAAK,oBAAoB,WAAW,eAAe;AAAA,IACrD;AAAA,EACF;AAAA,EACA,6CAA6C;AAC3C,QAAI,KAAK,oBAAoB,kBAAkB,kBAAkB;AAC/D,aAAO,KAAK,oBAAoB,OAAO;AAAA,IACzC,OAAO;AACL,aAAO,KAAK,oBAAoB;AAAA,IAClC;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS,KAAK,2CAA2C;AAC/D,QAAI,kBAAkB,YAAY;AAChC,aAAO,OAAO,cAAc,sBAAsB;AAAA,IACpD;AAEA,QAAI,kBAAkB,SAAS;AAC7B,aAAO,OAAO,sBAAsB;AAAA,IACtC;AACA,UAAM,QAAQ,OAAO,SAAS;AAC9B,UAAM,SAAS,OAAO,UAAU;AAEhC,WAAO;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,QAAQ,OAAO,IAAI;AAAA,MACnB,MAAM,OAAO;AAAA,MACb,OAAO,OAAO,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,aAAO,KAAK,KAAK,8BAAgC,kBAAqB,mBAAmB,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IACvI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,IAAI,sBAAsB,EAAE,CAAC;AAAA,MACrE,QAAQ;AAAA,QACN,sBAAsB;AAAA,MACxB;AAAA,MACA,UAAU,CAAC,oBAAoB;AAAA,MAC/B,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,4BAA4B,WAAW,wBAAwB,MAAM;AAAA,CACjG,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,cAAc,CAAC,2BAA2B;AAAA,MAC1C,SAAS,CAAC,2BAA2B;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,2BAA2B;AAAA,MAC1C,SAAS,CAAC,2BAA2B;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,SAAS,oBAAoB,YAAY,QAAQ;AAC/C,MAAI,CAAC,OAAQ;AACb,aAAW,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM;AAC9C;", "names": []}
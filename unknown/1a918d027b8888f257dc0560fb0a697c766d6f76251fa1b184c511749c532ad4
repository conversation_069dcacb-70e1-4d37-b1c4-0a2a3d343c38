<div class="min-h-[90vh] bg-[var(--background-light-gray)] text-[var(--text-dark)] flex items-center justify-center"
style="height: calc(100vh - 52px);"
>
  <div class="w-full max-w-md p-[var(--padding-large)] bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] mx-4 sm:mx-6 md:mx-auto">
    <!-- Logo/Brand -->
    <div class="flex items-center justify-center gap-2 mb-[var(--padding-large)]">
      <span class="text-[var(--font-size-header)] font-[var(--font-weight-bold)]  sm:text-[24px]  h-10 w-10 rounded-full flex items-center justify-center bg-slate-200 text-lg">
       AI
      </span>
    </div>

    <!-- Register Form -->
    <div class="space-y-[var(--padding-medium)]">
      <h1 class="text-[var(--font-size-header)] font-[var(--font-weight-medium)] text-center   sm:text-[26px] md:text-[28px]">
        Sign up to AI World
      </h1>

      <div class="space-y-[var(--padding-small)]">
        <!-- Name Input -->
        <div class="space-y-2">
          <label class="block      text-[var(--text-medium-gray)] font-[var(--font-family)] sm:text-[15px]">
            Name
          </label>
          <input
            type="text"
            [(ngModel)]="user.name"
            placeholder="Enter Your Name"
            class="w-full bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-[var(--padding-medium)] py-[var(--padding-small)] text-[var(--text-dark)] placeholder:text-[var(--text-medium-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] font-[var(--font-family)] border-none line-height-[var(--line-height)] sm:px-[var(--padding-large)] sm:py-[var(--padding-medium)] sm:text-[16px] md:text-[17px]"
            autocomplete="name"
          >
        </div>

        <!-- Email Input -->
        <div class="space-y-2">
          <label class="block      text-[var(--text-medium-gray)] font-[var(--font-family)] sm:text-[15px]">
            Email
          </label>
          <input
            type="email"
            [(ngModel)]="user.email"
            placeholder="Enter Your Email"
            class="w-full bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-[var(--padding-medium)] py-[var(--padding-small)] text-[var(--text-dark)] placeholder:text-[var(--text-medium-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] font-[var(--font-family)] border-none line-height-[var(--line-height)] sm:px-[var(--padding-large)] sm:py-[var(--padding-medium)] sm:text-[16px] md:text-[17px]"
          >
        </div>

        <!-- Password Input -->
        <div class="space-y-2">
          <label class="block      text-[var(--text-medium-gray)] font-[var(--font-family)] sm:text-[15px]">
            Password
          </label>
          <input
            type="password"
            [(ngModel)]="user.password"
            placeholder="Enter Your Password"
            class="w-full bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-[var(--padding-medium)] py-[var(--padding-small)] text-[var(--text-dark)] placeholder:text-[var(--text-medium-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] font-[var(--font-family)] border-none line-height-[var(--line-height)] sm:px-[var(--padding-large)] sm:py-[var(--padding-medium)] sm:text-[16px] md:text-[17px]"
          >
        </div>
      </div>

      <!-- Sign Up Button -->
      <div class="mt-[var(--padding-medium)] flex justify-center">
        <button
          (click)="register()"
          class="outline-none border-none px-[var(--padding-large)] py-2 cursor-pointer bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] font-[var(--font-family)] hover:text-black sm:px-[var(--padding-large)] sm:py-[var(--padding-small)] sm:text-[15px] md:text-[16px] min-h-[40px]"
        >
          Sign up
        </button>
      </div>

      <!-- Login Link -->
      <div class="flex items-center justify-center mt-[var(--padding-medium)] text-[var(--text-medium-gray)] font-[var(--font-family)]      sm:text-[15px] md:text-[16px]">
        <span>
          Have an account?
          <a routerLink="/login" class="cursor-pointer text-[var(--primary-purple)] hover:text-[var(--secondary-purple)] transition-[var(--transition-default)] underline sm:text-[15px] md:text-[16px]">
            Login here
          </a>
        </span>
      </div>
    </div>
  </div>
</div>

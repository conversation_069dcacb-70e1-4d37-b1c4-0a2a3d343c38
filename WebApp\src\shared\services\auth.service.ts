import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { DateTime } from 'luxon';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private userSubject = new BehaviorSubject<any | null>(null);
  user$ = this.userSubject.asObservable();
  private readonly TOKEN_KEY = 'jwt_token';
  isUserLoggedIn = false;
  modelName: any = null;

  constructor(private router: Router) {
    this.loadUser();
    this.getModel();
  }

  // Save JWT token to cookie with security settings
  setToken(token: string, expiryDate: DateTime): void {
    const cookieValue = `${this.TOKEN_KEY}=${token}; expires=${expiryDate.toJSDate().toUTCString()}; path=/; SameSite=Strict; Secure`;
    document.cookie = cookieValue;
    this.loadUser(); // Reload user data from the token
  }

  // Get JWT token from cookies
  getToken(): string | null {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === this.TOKEN_KEY) {
        return value;
      }
    }
    return null;
  }

  // Decode JWT token
  decodeToken(token: string): any {
    try {
      // Split the token and get the payload part (second segment)
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      // console.log(jsonPayload); // Log the decoded payload for debugging purposes

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  loadUser(): void {
    const token = this.getToken();
    if (token) {
      const decodedToken = this.decodeToken(token);
      if (decodedToken) {
        this.userSubject.next(decodedToken);
        this.isUserLoggedIn = true;
      } else {
        this.isUserLoggedIn = false;
      }
    } else {
      this.isUserLoggedIn = false;
    }
  }

  // saveUser(user: any, token: string, expiryDate: DateTime): void {
  //   this.setToken(token, expiryDate);
  //   this.userSubject.next(user);
  //   this.isUserLoggedIn = true;
  // }

  getUser(): any | null {
    const user = this.userSubject.value;
    // Log the user object for debugging
    // console.log('User from auth service:', user);
    return user;
  }

  /**
   * Gets the user's name from the JWT token
   * @returns The user's name or empty string if not found
   */
  getUserName(): string {
    const user = this.getUser();
    if (!user) return '';

    // Try different possible claim names for the user name
    return user.name ||
      user['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] ||
      user['unique_name'] ||
      '';
  }

  logout() {
    // Remove token cookie by setting it to expire immediately
    document.cookie = `${this.TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict; Secure`;
    this.userSubject.next(null);
    this.isUserLoggedIn = false;
    this.router.navigate(['/login']);
  }

  hasRole(role: string): boolean {
    const roles = this.getUserRoles();
    return roles.map((r) => r.toLowerCase()).includes(role.toLowerCase());
  }

  getUserRoles(): string[] {
    const user = this.getUser();

    return user?.role;
  }

  // is admin rols
  isAdmin(): boolean {
    const user = this.getUser();
    if (!user) {
      return false;
    }
    // Check if the user has the 'admin' role
    const roles = this.getUserRoles();

    return roles.includes('Admin');
  }

  setModel(model: any): void {
    localStorage.setItem('model', JSON.stringify(model));
    this.modelName = model;
  }

  getModel(): any | null {
    const model = localStorage.getItem('model');
    this.modelName = model ? JSON.parse(model) : 'Select Model';
    return this.modelName;
  }
}

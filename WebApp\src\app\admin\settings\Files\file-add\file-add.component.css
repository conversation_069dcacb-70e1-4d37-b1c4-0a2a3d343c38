/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Sticky header styling */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-light-gray);
  backdrop-filter: blur(5px);
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* File upload area styling */
.file-upload-area {
  transition: all 0.3s ease;
  border: 2px dashed var(--hover-blue-gray);
  background-color: var(--background-light-gray);
  border-radius: 0.5rem;
}

.file-upload-area:hover {
  border-color: var(--primary-purple);
  background-color: rgba(var(--primary-purple-rgb), 0.05);
}

/* File preview styling */
.file-preview {
  background-color: var(--background-light-gray);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.file-preview:hover {
  background-color: rgba(var(--primary-purple-rgb), 0.05);
}

/* Button styling */
.primary-button {
  background-color: var(--primary-purple);
  color: white;
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.primary-button:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.secondary-button {
  background-color: var(--hover-blue-gray);
  color: var(--text-dark);
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.secondary-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Dark mode support */
:host-context(.dark-theme) .file-upload-area {
  border-color: var(--hover-blue-gray);
  background-color: rgba(255, 255, 255, 0.05);
}

:host-context(.dark-theme) .file-upload-area:hover {
  border-color: var(--primary-purple);
  background-color: rgba(var(--primary-purple-rgb), 0.1);
}

:host-context(.dark-theme) .file-preview {
  background-color: rgba(255, 255, 255, 0.05);
}

:host-context(.dark-theme) .file-preview:hover {
  background-color: rgba(var(--primary-purple-rgb), 0.1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .file-upload-container {
    padding: 1rem;
  }

  .file-upload-area {
    height: 120px;
  }
}
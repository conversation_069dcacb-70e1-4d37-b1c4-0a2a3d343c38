<div class="h-full relative flex flex-col"
     [ngClass]="{'bg-[#2b2b33]': themeService.isDarkMode(), 'bg-white': !themeService.isDarkMode()}">
  
  <!-- Width indicator (visible during drag) -->
  <div class="width-indicator" *ngIf="isDragging">{{ Math.round(splitSizes.sidebarArea) }}%</div>

  <!-- Hidden splitter controls - only shown on hover -->
  <div class="splitter-hover-area" (dblclick)="onGutterDoubleClick($event)"></div>

  <div>
    <div class="px-4 pt-4">
      <button
        class="w-full flex items-center justify-between gap-2 p-2 px-3 rounded-md hover:opacity-90 transition-opacity outline-none border-none cursor-pointer shadow-sm text-white"
        [ngClass]="{
          'bg-[#00c39a] focus:ring-2 focus:ring-[#00c39a]': themeService.isDarkMode(),
          'bg-[var(--primary-purple)] focus:ring-2 focus:ring-[var(--primary-purple)]': !themeService.isDarkMode()
        }"
        (click)="showDocumentsList()">
        <span class="font-medium truncate">
          {{ isPublicRoute ? 'View All Notes' : 'View All Documents' }}
        </span>
        <button type="button" (click)="addDocument($event)"
          class="ml-2 flex items-center justify-center rounded-full bg-white w-6 h-6 transition-opacity outline-none border-none"
          [ngClass]="{
            'text-[#00c39a] hover:bg-[rgba(255,255,255,0.9)]': themeService.isDarkMode(),
            'text-[var(--primary-purple)] hover:bg-[rgba(255,255,255,0.9)]': !themeService.isDarkMode()
          }"
          tabindex="-1">
          <i class="ri-add-line"></i>
        </button>
      </button>
    </div>
  </div>

  <!-- Favorites Section -->
  <div class="px-4 pt-4 mt-2">
    <div class="uppercase text-xs font-semibold tracking-wider mb-2"
         [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}">
      Favorites</div>
    <div class="space-y-1">
      <div *ngFor="let doc of favorites"
        class="flex items-center justify-between py-1.5 px-2 rounded cursor-pointer transition-all"
        [ngClass]="{
          'hover:bg-[rgba(255,255,255,0.05)] text-white': themeService.isDarkMode(),
          'hover:bg-[rgba(107,70,193,0.05)] text-[var(--text-dark)]': !themeService.isDarkMode()
        }"
        (click)="selectDocument(doc)">
        <div class="truncate text-sm flex items-center">
          <i class="ri-file-text-line mr-2"
             [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
          {{ doc.title }}
        </div>
        <i class="ri-star-fill text-yellow-400"></i>
      </div>
    </div>
  </div>

  <!-- Recent Section -->
  <div class="px-4 pt-4 flex-grow overflow-y-auto">
    <div class="uppercase text-xs font-semibold tracking-wider mb-2"
         [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}">
      Recent</div>
    <div class="space-y-1">
      <div *ngFor="let doc of recentDocuments"
        class="flex items-center justify-between py-1.5 px-2 rounded cursor-pointer transition-all"
        [ngClass]="{
          'hover:bg-[rgba(255,255,255,0.05)] text-white': themeService.isDarkMode(),
          'hover:bg-[rgba(107,70,193,0.05)] text-[var(--text-dark)]': !themeService.isDarkMode()
        }"
        (click)="selectDocument(doc)">
        <div class="truncate text-sm flex items-center">
          <i class="ri-file-text-line mr-2"
             [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
          {{ doc.title }}
        </div>
        <i class="ri-time-line"
           [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}"></i>
      </div>
    </div>
  </div>
</div>

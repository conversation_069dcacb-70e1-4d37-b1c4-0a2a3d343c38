import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

export interface DocumentUpdateEvent {
  type: 'created' | 'updated' | 'deleted' | 'favorited';
  document?: any;
  workspaceName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentSyncService {
  private documentUpdatedSource = new Subject<DocumentUpdateEvent>();

  // Observable that components can subscribe to
  documentUpdated$ = this.documentUpdatedSource.asObservable();

  constructor() { }

  // Method to notify that documents have been updated
  notifyDocumentUpdated(event?: DocumentUpdateEvent) {
    this.documentUpdatedSource.next(event || { type: 'updated' });
  }

  // Specific methods for different types of updates
  notifyDocumentCreated(document: any, workspaceName?: string) {
    this.documentUpdatedSource.next({
      type: 'created',
      document,
      workspaceName
    });
  }

  notifyDocumentDeleted(document: any, workspaceName?: string) {
    this.documentUpdatedSource.next({
      type: 'deleted',
      document,
      workspaceName
    });
  }

  notifyDocumentFavorited(document: any, workspaceName?: string) {
    this.documentUpdatedSource.next({
      type: 'favorited',
      document,
      workspaceName
    });
  }
}

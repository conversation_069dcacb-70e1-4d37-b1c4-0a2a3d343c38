import { <PERSON>mpo<PERSON>, OnInit, After<PERSON><PERSON>w<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MarkdownModule } from 'ngx-markdown';

import EditorJS, { ToolConstructable } from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Warning from '@editorjs/warning';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import Link from '@editorjs/link';
import Table from '@editorjs/table';
import ImageTool from '@editorjs/image';
import { MemoryServiceProxy } from '../../../../shared/service-proxies/service-proxies';

// Import the actual MemoryDto from service-proxies
import { MemoryDto   } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { NzModalModule } from 'ng-zorro-antd/modal';


@Component({
  selector: 'app-add-or-edit-memory',
  standalone: true,
  imports: [CommonModule, FormsModule, MarkdownModule,ServiceProxyModule],
  templateUrl: './add-or-edit-memory.component.html',
  styleUrls: ['./add-or-edit-memory.component.css'],
  providers: [MemoryServiceProxy,ServiceProxyModule,NzModalModule],
})
export class AddOrEditMemoryComponent implements OnInit, AfterViewInit, OnDestroy {

  memories : MemoryDto = new MemoryDto();


  public editor: EditorJS | null = null;
  private editorInitialized = false;
  private pendingContent: any = null;

  currentMemoryId?: string;
  isSaving = false;
  hasContent = false;
  isEditMode = false;

  showTagInput = false;

  constructor(
    public router: Router,
    private route: ActivatedRoute,
    private _memoryService: MemoryServiceProxy,
  ) { }

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.currentMemoryId = params['id'];
      if (this.currentMemoryId == "add") {
        // This is create mode (new memory)
        this.isEditMode = false;
        // Initialize with empty memory for create mode
        this.memories = new MemoryDto();
        this.memories.id = "00000000-0000-0000-0000-000000000000";
      } else if (this.currentMemoryId) {
        // This is edit mode (existing memory)
        this.isEditMode = true;
        this.loadMemoryData();
      } else {
        // Fallback: Initialize with empty memory for create mode
        this.isEditMode = false;
        this.memories = new MemoryDto();
        this.memories.id = "00000000-0000-0000-0000-000000000000";
      }
    });
  }

  loadMemoryData() {
    if (!this.currentMemoryId) return;

    this._memoryService.getById(this.currentMemoryId).subscribe({
      next: (res: any) => {
        console.log('Loaded memory data:', res);
        // Create a new MemoryDto and copy properties from the response
        const customMemory = new MemoryDto();
        customMemory.id = res.id; // Assign the actual memory ID
        customMemory.content = res.content;

        this.memories = customMemory;

        // Try to parse the content if it's in JSON format
        if (res.content && typeof res.content === 'string') {
          try {
            // Check if content is in EditorJS format
            if (res.content.startsWith('{')) {
              const parsedContent = JSON.parse(res.content);

              // Remove metadata if present
              if (parsedContent.metadata) {
                delete parsedContent.metadata;
              }

              // Clean the loaded content to remove any HTML entities
              const cleanedContent = this.cleanEditorContent(parsedContent);

              // If editor is already initialized, update it directly
              if (this.editorInitialized && this.editor) {
                this.updateEditorContent(cleanedContent);
              } else {
                // Otherwise, store it to be loaded when editor is ready
                this.pendingContent = cleanedContent;
              }
            } else {
              // If content is plain text, clean it and prepare it for editor
              const cleanedText = this.cleanTextContent(res.content);

              if (this.editorInitialized && this.editor) {
                this.editor.blocks.clear();
                this.editor.blocks.insert('paragraph', {
                  text: cleanedText
                });
              } else {
                // Create a simple blocks structure for plain text
                this.pendingContent = {
                  blocks: [
                    {
                      type: 'paragraph',
                      data: {
                        text: cleanedText
                      }
                    }
                  ]
                };
              }
            }
          } catch (e) {
            console.error('Error parsing memory content:', e);
            // Handle as plain text if parsing fails
            const cleanedText = this.cleanTextContent(res.content);

            if (this.editorInitialized && this.editor) {
              this.editor.blocks.clear();
              this.editor.blocks.insert('paragraph', {
                text: cleanedText
              });
            } else {
              this.pendingContent = {
                blocks: [
                  {
                    type: 'paragraph',
                    data: {
                      text: cleanedText
                    }
                  }
                ]
              };
            }
          }
        }
      },
      error: (err) => {
        console.error('Error loading memory:', err);
      }
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.initializeEditor();
      // Clear any default content that might be loaded
      if (this.editor && this.editor.blocks) {
        this.editor.blocks.clear();
      }
    }, 0);
  }

  ngOnDestroy() {
    if (this.editor) {
      this.editor.destroy();
    }
  }



  private initializeEditor() {
    if (this.editor) return; // Prevent re-initialization

    const editorElement = document.getElementById('editor');
    if (!editorElement) {
      console.error('Element with ID "editor" is missing.');
      return;
    }

    this.editor = new EditorJS({
      holder: 'editor',
      minHeight: 400,
      placeholder: 'Begin typing your memory content here...',
      autofocus: true,
      // Enable the built-in toolbar since we removed the custom one
      inlineToolbar: true,
      onChange: () => {
        this.editor?.save().then(data => {
          this.hasContent = data.blocks.length > 0;
        });
      },
      onReady: () => {
        this.editorInitialized = true;
        if (this.pendingContent) {
          this.updateEditorContent(this.pendingContent);
          this.pendingContent = null;
        } else {
          // Ensure no default content is shown
          this.editor?.blocks.clear();
        }
      },
      tools: {
        header: {
          class: Header as unknown as ToolConstructable,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4],
            defaultLevel: 1
          }
        },
        list: {
          class: List as unknown as ToolConstructable,
          inlineToolbar: true
        },
        checklist: {
          class: Checklist as unknown as ToolConstructable,
          inlineToolbar: true
        },
        quote: {
          class: Quote as unknown as ToolConstructable,
          inlineToolbar: true
        },
        warning: {
          class: Warning as unknown as ToolConstructable,
          inlineToolbar: true
        },
        marker: {
          class: Marker as unknown as ToolConstructable,
          inlineToolbar: true
        },
        code: {
          class: CodeTool as unknown as ToolConstructable,
          inlineToolbar: true
        },
        delimiter: {
          class: Delimiter as unknown as ToolConstructable
        },
        inlineCode: {
          class: InlineCode as unknown as ToolConstructable
        },
        link: {
          class: Link as unknown as ToolConstructable,
          inlineToolbar: true
        },
        table: {
          class: Table as unknown as ToolConstructable,
          inlineToolbar: true
        }
      }
    });
  }

  async saveContent() {
    this.isSaving = true;
    try {
      // Get editor content
      const editorData = await this.editor?.save();
      if (!editorData) {
        throw new Error('Failed to get editor data');
      }

      // Clean the editor data before saving
      const cleanedEditorData = this.cleanEditorContent(editorData);

      // Set content as JSON string
      this.memories.content = JSON.stringify(cleanedEditorData);

      // Create a standard MemoryDto for the API
      const memoryDto = new MemoryDto();
      memoryDto.content = this.memories.content.trim();

      // Set ID based on mode:
      // - For create mode (new memory): use empty GUID
      // - For edit mode (existing memory): use actual memory ID
      if (this.isEditMode) {
        // Edit mode: use the actual memory ID
        memoryDto.id = this.memories.id;
        console.log('Updating existing memory with ID:', memoryDto.id);
      } else {
        // Create mode: use empty GUID
        memoryDto.id = "00000000-0000-0000-0000-000000000000";
        console.log('Creating new memory with empty GUID');
      }

      console.log('Cleaned memory data:', memoryDto);

      // Save the memory
      this._memoryService.createOrEdit(memoryDto).subscribe({
        next: () => {
          console.log('Memory saved successfully');
          this.router.navigate(['/settings/memory']);
        },
        error: (err) => {
          console.error('Error saving memory:', err);
          this.isSaving = false;
        }
      });
    } catch (error) {
      console.error('Error preparing memory data:', error);
      this.isSaving = false;
    }
  }

  clearContent() {
    if (this.editor && this.editor.blocks) {
      this.editor.blocks.clear();
    }
  }

  /**
   * Clean EditorJS content by removing HTML entities and excessive whitespace
   * while preserving the block structure
   */
  private cleanEditorContent(editorData: any): any {
    if (!editorData || !editorData.blocks) {
      return editorData;
    }

    // Create a deep copy to avoid modifying the original data
    const cleanedData = JSON.parse(JSON.stringify(editorData));

    // Process each block
    cleanedData.blocks = cleanedData.blocks.map((block: any) => {
      return this.cleanBlock(block);
    });

    return cleanedData;
  }

  /**
   * Clean individual block content based on block type
   */
  private cleanBlock(block: any): any {
    if (!block || !block.data) {
      return block;
    }

    const cleanedBlock = { ...block };

    switch (block.type) {
      case 'paragraph':
      case 'header':
        if (cleanedBlock.data.text) {
          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);
        }
        break;

      case 'list':
        if (cleanedBlock.data.items && Array.isArray(cleanedBlock.data.items)) {
          cleanedBlock.data.items = cleanedBlock.data.items.map((item: any) => {
            if (typeof item === 'string') {
              return this.cleanTextContent(item);
            } else if (item && typeof item === 'object' && item.content) {
              return {
                ...item,
                content: this.cleanTextContent(item.content)
              };
            }
            return item;
          });
        }
        break;

      case 'checklist':
        if (cleanedBlock.data.items && Array.isArray(cleanedBlock.data.items)) {
          cleanedBlock.data.items = cleanedBlock.data.items.map((item: any) => {
            if (item && item.text) {
              return {
                ...item,
                text: this.cleanTextContent(item.text)
              };
            }
            return item;
          });
        }
        break;

      case 'quote':
        if (cleanedBlock.data.text) {
          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);
        }
        if (cleanedBlock.data.caption) {
          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);
        }
        break;

      case 'warning':
        if (cleanedBlock.data.title) {
          cleanedBlock.data.title = this.cleanTextContent(cleanedBlock.data.title);
        }
        if (cleanedBlock.data.message) {
          cleanedBlock.data.message = this.cleanTextContent(cleanedBlock.data.message);
        }
        break;

      case 'code':
        if (cleanedBlock.data.code) {
          // For code blocks, we want to preserve formatting but still clean HTML entities
          cleanedBlock.data.code = this.cleanHtmlEntities(cleanedBlock.data.code);
        }
        break;

      case 'table':
        if (cleanedBlock.data.content && Array.isArray(cleanedBlock.data.content)) {
          cleanedBlock.data.content = cleanedBlock.data.content.map((row: any[]) => {
            return row.map((cell: string) => this.cleanTextContent(cell));
          });
        }
        break;

      case 'image':
        if (cleanedBlock.data.caption) {
          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);
        }
        break;

      default:
        // For any other block types, try to clean common text properties
        if (cleanedBlock.data.text) {
          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);
        }
        if (cleanedBlock.data.caption) {
          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);
        }
        break;
    }

    return cleanedBlock;
  }

  /**
   * Clean text content by removing HTML entities and normalizing whitespace
   */
  private cleanTextContent(text: string): string {
    if (!text || typeof text !== 'string') {
      return text;
    }

    // First, decode HTML entities
    let cleanedText = this.cleanHtmlEntities(text);

    // Normalize whitespace: replace multiple spaces with single space
    cleanedText = cleanedText.replace(/\s+/g, ' ');

    // Trim leading and trailing whitespace
    cleanedText = cleanedText.trim();

    return cleanedText;
  }

  /**
   * Convert HTML entities back to regular characters
   */
  private cleanHtmlEntities(text: string): string {
    if (!text || typeof text !== 'string') {
      return text;
    }

    // Create a temporary DOM element to decode HTML entities
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = text;
    let decodedText = tempDiv.textContent || tempDiv.innerText || '';

    // Additional manual replacements for common entities that might not be caught
    decodedText = decodedText
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&apos;/g, "'");

    return decodedText;
  }

  async updateEditorContent(newContent: any) {
    try {
      if (!this.editor) {
        console.warn('Editor not initialized yet');
        return;
      }

      await this.editor.isReady;
      await this.editor.blocks.clear();
      await this.editor.render(newContent);

      this.hasContent = true;
    } catch (error) {
      console.error('Error updating editor content:', error);
    }
  }


  showTagInputField() {
    this.showTagInput = true;
    setTimeout(() => {
      document.getElementById('tagInput')?.focus();
    }, 0);
  }

  // Helper methods for toolbar buttons
  addHeader() {
    if (this.editor) {
      this.editor.blocks.insert('header');
    }
  }

  addList(style: 'ordered' | 'unordered') {
    if (this.editor) {
      this.editor.blocks.insert('list', { style });
    }
  }

  addChecklist() {
    if (this.editor) {
      this.editor.blocks.insert('checklist');
    }
  }

  addQuote() {
    if (this.editor) {
      this.editor.blocks.insert('quote');
    }
  }

  addCode() {
    if (this.editor) {
      this.editor.blocks.insert('code');
    }
  }

  addImage() {
    if (this.editor) {
      this.editor.blocks.insert('image');
    }
  }

  addTable() {
    if (this.editor) {
      this.editor.blocks.insert('table');
    }
  }

  addWarning() {
    if (this.editor) {
      this.editor.blocks.insert('warning');
    }
  }

  addDelimiter() {
    if (this.editor) {
      this.editor.blocks.insert('delimiter');
    }
  }

  addLink() {
    if (this.editor) {
      this.editor.blocks.insert('link');
    }
  }

  /**
   * Test method to verify content cleaning functionality
   * This can be called from browser console for debugging: component.testContentCleaning()
   */
  testContentCleaning() {
    const testData = {
      blocks: [
        {
          type: 'paragraph',
          data: {
            text: 'This is a test&nbsp;&nbsp;&nbsp;with multiple&nbsp;spaces and&amp;entities'
          }
        },
        {
          type: 'header',
          data: {
            text: 'Header&nbsp;with&nbsp;&nbsp;spaces',
            level: 2
          }
        },
        {
          type: 'list',
          data: {
            items: [
              'Item&nbsp;one&nbsp;&nbsp;with spaces',
              'Item&nbsp;two&amp;with&lt;entities&gt;'
            ]
          }
        }
      ]
    };

    console.log('Original data:', testData);
    const cleaned = this.cleanEditorContent(testData);
    console.log('Cleaned data:', cleaned);

    return {
      original: testData,
      cleaned: cleaned
    };
  }
}

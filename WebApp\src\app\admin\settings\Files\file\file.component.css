/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Animation for scale in effect */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

/* Text truncation for card content */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effects for cards */
.group {
  transition: all 0.3s ease;
  border: 1px solid var(--hover-blue-gray);
}

.group:hover {
  transform: translateX(4px);
  border-color: var(--primary-purple);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Action buttons animation */
.action-button {
  opacity: 0.9;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-button:hover {
  opacity: 1;
  transform: translateY(-2px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Sticky header styling */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-light-gray);
  backdrop-filter: blur(5px);
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Dark mode support */
:host-context(.dark-theme) .bg-white {
  background-color: var(--background-dark);
}

:host-context(.dark-theme) .text-dark {
  color: var(--text-light);
}

:host-context(.dark-theme) .border-gray {
  border-color: var(--hover-dark-gray);
}

/* File card specific styling */
.file-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease;
  border: 1px solid var(--hover-blue-gray);
  border-radius: 0.375rem;
  overflow: hidden;
}

.file-card:hover {
  transform: translateY(-4px);
  border-color: var(--primary-purple);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.02em;
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.status-badge i {
  color: white;
}

.status-badge.generated {
  background-color: rgb(16, 185, 129);
  border: 1px solid rgba(16, 185, 129, 0.8);
}

.status-badge.pending {
  background-color: rgb(245, 158, 11);
  border: 1px solid rgba(245, 158, 11, 0.8);
}

/* Dark mode support for status badges */
:host-context(.dark-theme) .status-badge.generated {
  background-color: rgb(5, 150, 105);
  border: 1px solid rgba(16, 185, 129, 0.8);
}

:host-context(.dark-theme) .status-badge.pending {
  background-color: rgb(217, 119, 6);
  border: 1px solid rgba(245, 158, 11, 0.8);
}

/* Modal backdrop */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

/* Ensure proper theme support for file details modal */
:host-context(.dark-theme) .file-details-modal {
  background-color: var(--background-white);
  color: var(--text-dark);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .file-card .opacity-0 {
    opacity: 1 !important;
    transform: translateY(0);
  }
}
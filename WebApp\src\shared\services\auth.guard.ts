import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { AuthService } from './auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  let adminPages = ['user-management', 'connections', 'models', 'agents', 'embedding', 'prompts-library', 'project-memory', 'documents'];
  // Check if the user is authenticated
  const authService = inject(AuthService);
  if (!authService.isUserLoggedIn) {
    // Redirect to the login page if not authenticated
    window.location.href = '/login';
    return false;
  } else {
    // Check if the user is trying to access admin pages and if they are an admin
    if (adminPages.some(page => state.url.includes(page)) && !authService.isAdmin()) {
      // Redirect to home or some other page if not admin
      window.location.href = '/not-found';
      return false;
    }
  }
  return true;
};

<div class="h-[calc(100vh - 74px)] w-full bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">

  <!-- Main content with splitter -->
  <as-split direction="horizontal" [gutterSize]="4" [useTransition]="true" (dragEnd)="onSplitDragEnd($event)">
    <!-- Main Chat Container -->
    <as-split-area [size]="mainContentSplitSizes.chatArea">
      <div class="flex flex-col h-full">
        <!-- Chat Messages -->
        <div #chatContainer (scroll)="onChatScroll()" class="flex-1 overflow-y-auto px-2 py-4 sm:px-4 sm:py-6"
          *ngIf="chatHistory">
          <!-- All the existing chat message content here -->
        </div>

        <!-- Scroll Down Arrow Button -->
        <button *ngIf="showScrollButton" (click)="scrollToBottom()"
          class="fixed cursor-pointer bottom-60 right-[50%] p-2 bg-[var(--primary-purple)] rounded-full text-[var(--background-white)] shadow-md"
          style="z-index: 9999;">
          <i class="ri-arrow-down-s-line text-xl"></i>
        </button>

        <!-- Welcome Screen -->
        <div *ngIf="chatHistory==null" class="p-2 border-t relative h-full flex items-center justify-center">
          <!-- All the existing welcome screen content here -->
        </div>

        <!-- Input Section -->
        <div *ngIf="chatHistory!=null" class="p-2 sm:p-4 border-t border-[var(--hover-blue-gray)] relative">
          <!-- All the existing input section content here -->
        </div>
      </div>
    </as-split-area>

    <!-- Search Results Sidebar - Integrated as a split area -->
    <as-split-area *ngIf="showSearchResultsSidebar" [size]="mainContentSplitSizes.searchResults" [minSize]="20">
      <div class="h-full bg-[var(--background-white)] border-l border-[var(--hover-blue-gray)] overflow-y-auto">

        <!-- Search Results Header - Sticky -->
        <div
          class="flex items-center justify-between p-4 border-b border-[var(--hover-blue-gray)] sticky top-0 bg-[var(--background-white)] z-10 shadow-sm backdrop-blur-sm transition-all duration-200">
          <h3 class="text-[var(--text-dark)] font-semibold flex items-center gap-2">
            <i class="ri-search-line"></i>
            <span>Source References</span>
          </h3>
          <button (click)="showSearchResultsSidebar = false"
            class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] transition-colors p-1 rounded-full hover:bg-[var(--hover-blue-gray)]">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <!-- Search Results Content -->
        <div class="p-4">
          <!-- Source References Section -->
          <div *ngIf="webSearchResults > 0" class="mb-6">
            <div
              class="sticky top-[57px] bg-[var(--background-white)] pt-2 pb-3 z-[5] shadow-sm backdrop-blur-sm transition-all duration-200">
              <div class="flex items-center gap-2 mb-2">
                <div class="flex items-center gap-1 bg-[#1E1E1E] text-white px-2 py-1 rounded-md">
                  <i class="ri-file-list-line"></i>
                  <i class="ri-global-line"></i>
                </div>
                <h3 class="font-semibold text-[var(--text-dark)]">
                  <span *ngIf="currentSourceName">{{ currentSourceName }} • </span>
                  {{ webSearchResults }} Source References
                </h3>
              </div>

              <div class="text-xs text-[var(--text-medium-gray)]">
                References used to generate the response
              </div>
            </div>

            <div class="space-y-4">
              <div *ngFor="let result of searchResults"
                class="bg-[var(--background-white)] p-4 rounded-lg shadow-sm border border-[var(--hover-blue-gray)] transition-all hover:border-[var(--primary-purple)] cursor-pointer">
                <!-- Search result content here -->
              </div>
            </div>
          </div>

          <!-- Search Factors Section -->
          <div *ngIf="searchFactorCount > 0">
            <!-- Search factors content here -->
          </div>

          <!-- Empty State -->
          <div *ngIf="webSearchResults === 0 && searchFactorCount === 0"
            class="text-center py-8 text-[var(--text-medium-gray)]">
            <i class="ri-search-line text-4xl mb-2 block"></i>
            <p>No search results available</p>
            <p class="text-xs mt-2">Try clicking the "Test Search" button to simulate results</p>
          </div>
        </div>
      </div>
    </as-split-area>
  </as-split>

  <!-- Agent Tools Sidebar - Using NgZorro Drawer for this case -->
  <nz-drawer nzWidth="350px" [nzClosable]="true" [nzVisible]="isAgentSidebarOpen" nzPlacement="right"
    (nzOnClose)="toggleAgentSidebar()" [nzTitle]="agentSidebarTitle">
    <ng-container *nzDrawerContent>
      <div class="p-4">
        <div *ngIf="workspaceAgents.length === 0" class="text-center py-8 text-[var(--text-medium-gray)]">
          <i class="ri-tools-line text-4xl mb-2 block"></i>
          <p>No agents available for this workspace</p>
        </div>

        <div *ngIf="workspaceAgents.length > 0" class="space-y-4">
          <div *ngFor="let agent of workspaceAgents" (click)="selectAgent(agent)"
            class="bg-[var(--background-white)] p-4 rounded-lg shadow-sm border border-[var(--hover-blue-gray)] transition-all hover:border-[var(--primary-purple)] cursor-pointer">
            <!-- Agent content here -->
          </div>
        </div>
      </div>
    </ng-container>
  </nz-drawer>
</div>

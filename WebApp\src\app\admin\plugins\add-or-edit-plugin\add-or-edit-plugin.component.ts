import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';

@Component({
  selector: 'app-add-or-edit-plugin',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule
  ],
  templateUrl: './add-or-edit-plugin.component.html',
  styleUrl: './add-or-edit-plugin.component.css'
})
export class AddOrEditPluginComponent {
  plugin = {
    pluginName: '',
    openApiUrl: ''
  };

  constructor(private modalRef: NzModalRef) {}

  submitForm(form: any): void {
    if (form.valid) {
      this.modalRef.close(this.plugin);
    }
  }

  handleCancel(): void {
    this.modalRef.destroy();
  }
}

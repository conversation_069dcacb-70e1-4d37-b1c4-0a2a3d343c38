<!-- Daily Insight Content -->
<div class="p-4 overflow-y-auto max-h-[calc(100vh-74px)]">
  <!-- Header Section -->
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center gap-1.5">
      <span class="w-1.5 h-5 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--secondary-purple)] rounded-full"></span>
      <div class="flex flex-col">
        <span class="font-bold text-[var(--text-dark)] text-lg">Daily Insights</span>
        <span class="text-xs text-[var(--text-medium-gray)]">Welcome to your AI Hub</span>
      </div>
    </div>

    <!-- Refresh Button -->
    <!-- <div class="tooltip-container">
      <button
        class="w-7 h-7 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-all duration-300 flex justify-center items-center outline-none border-none bg-transparent text-lg cursor-pointer"
        (click)="refreshDailyInsights()">
        <i
          class="ri-refresh-line text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)] transition-colors duration-300"></i>
      </button>
      <span class="custom-tooltip refresh-tooltip">Refresh</span>
    </div> -->
  </div>
  <div class="space-y-6">
    <!-- Quick Actions Section -->
    <div class="space-y-4">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <i class="ri-flashlight-line text-[var(--primary-purple)] text-lg"></i>
          <h3 class="text-base font-semibold text-[var(--text-dark)]">Quick Actions</h3>
        </div>
        <div class="h-px flex-grow bg-[var(--hover-blue-gray)] ml-2 opacity-50"></div>
      </div>

      <!-- Action Cards with enhanced styling -->
      <div class="grid grid-cols-1 gap-4">
        <!-- New Chat Card -->
        <button (click)="goToChat()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-chat-new-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">New Chat</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Start a conversation</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button>

        <!-- Workspaces Card -->
        <button (click)="goToWorkspace()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-folder-add-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">Workspaces</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Organize projects</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button>

        <!-- Prompt Library Card -->
        <button (click)="goToPromptLibrary()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-code-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">Prompt Library</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Browse prompts</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button>
      </div>
    </div>

    <!-- Tips & Resources Section -->
    <div class="space-y-4">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <i class="ri-lightbulb-line text-[var(--primary-purple)] text-lg"></i>
          <h3 class="text-base font-semibold text-[var(--text-dark)]">Tips & Resources</h3>
        </div>
        <div class="h-px flex-grow bg-[var(--hover-blue-gray)] ml-2 opacity-50"></div>
      </div>

      <div
        class="bg-[var(--background-light-gray)] p-3 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] shadow-sm">
        <div class="flex items-start gap-3">
          <div
            class="w-8 h-8 rounded-full bg-[var(--secondary-purple)] bg-opacity-30 flex items-center justify-center flex-shrink-0 mt-0.5">
            <i class="ri-information-line ftext-[var(--primary-purple)]"></i>
          </div>
          <div>
            <p class="text-sm text-[var(--text-dark)] mb-1">Get started with AI Hub by creating a new chat or
              exploring your workspaces.</p>
            <p class="text-xs text-[var(--text-medium-gray)]">Check back here for daily insights from your AI
              agents.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

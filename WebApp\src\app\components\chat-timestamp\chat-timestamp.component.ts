import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RelativeTimePipe } from '../../services/relative-time.pipe';

@Component({
  selector: 'app-chat-timestamp',
  standalone: true,
  imports: [CommonModule, RelativeTimePipe],
  template: `
    <div class="flex items-center gap-2">
      <div class="flex-shrink-0" *ngIf="showIcon">
        <div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
          <i class="ri-time-line text-[var(--text-medium-gray)] text-xs"></i>
        </div>
      </div>
      <span class="text-[var(--text-medium-gray)] text-xs sm:text-sm">
        {{ timestamp | relativeTime | async }}
      </span>
    </div>
  `,
  styles: [`
    :host {
      display: inline-flex;
    }
  `]
})
export class ChatTimestampComponent {
  @Input() timestamp: number = 0;
  @Input() showIcon: boolean = true;
}

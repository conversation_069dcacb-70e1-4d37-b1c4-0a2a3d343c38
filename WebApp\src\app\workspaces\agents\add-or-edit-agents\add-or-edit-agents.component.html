<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">
  <!-- Main Container -->
  <div class="flex flex-row h-full">
    <!-- Main Form Container - will shrink when chat is visible -->
    <div class="px-6 py-4 transition-all duration-300 bg-[var(--background-light-gray)] flex-grow overflow-y-auto"
      [ngClass]="{'!w-2/3': showTestResults}">
      <!-- Header -->
      <div
        class="sticky-header flex flex-col sm:flex-row justify-between mb-6 gap-4 bg-[var(--background-light-gray)] items-center">
        <div class="flex items-center gap-3">
          <button [routerLink]="['..']"
            class="w-10 h-10 flex items-center justify-center bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all duration-300 shadow-sm hover:shadow-md">
            <i class="ri-arrow-left-line"></i>
          </button>
          <div class="flex flex-col">
            <h1 class="text-2xl font-semibold text-[var(--text-dark)] flex items-center gap-2">
              <i class="ri-robot-line text-[var(--primary-purple)]"></i>
              <span class="animate-fadeIn">{{ isEditing ? 'Edit AI Agent' : 'Add AI Agent' }}</span>
            </h1>
            <p class="text-sm text-[var(--text-medium-gray)] mt-1">{{ isEditing ? 'Update your AI agent settings' :
              'Configure a new AI agent' }}</p>
          </div>
        </div>

        <div class="flex items-center gap-3">
          <button [routerLink]="['..']"
            class="h-10 px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md">
            <i class="ri-close-line"></i>
            <span>Cancel</span>
          </button>
          <button *ngIf="!isEditing" (click)="saveAgent()"
            class="h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md">
            <i class="ri-save-line"></i>
            <span>Save Agent</span>
          </button>
          <button *ngIf="isEditing" (click)="saveAgent()"
            class="h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md">
            <i class="ri-save-line"></i>
            <span>Update Agent</span>
          </button>
        </div>
      </div>

      <!-- Form Content -->
      <div class="space-y-6">
        <!-- Agent Configuration Card -->
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-5">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4 flex items-center gap-2">
            <i class="ri-settings-3-line text-[var(--primary-purple)]"></i>
            <span>Agent Configuration</span>
          </h3>

          <!-- Agent Name -->
          <div class="mb-5">
            <label for="agentName" class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Agent
              Name</label>
            <input type="text" id="agentName" [(ngModel)]="currentAgent.agentName" placeholder="e.g. AnswerAgent"
              class="w-full h-10 px-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200">
          </div>

          <!-- Model Selection and Plugin Selection -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
            <!-- Model Selection -->
            <div>
              <label for="model" class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Model</label>
              <div class="relative">
                <input placeholder="Search model name..." [(ngModel)]="modelSearchQuery" (input)="onChange($event)"
                  [nzAutocomplete]="auto"
                  class="w-full h-10 px-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
                  [value]="modelSearchQuery">
              </div>
              <nz-autocomplete #auto
                class="mt-2 w-[100px] border border-[var(--hover-blue-gray)] rounded-md shadow-sm [&_.ant-select-item]:text-[var(--text-dark)] [&_.ant-select-item-option-active]:bg-[var(--background-light-gray)] [&_.ant-select-item-option-selected]:bg-[var(--background-light-gray)] [&_.ant-select-dropdown]:bg-[var(--background-white)]">
                @for (option of filteredModels; track option.modelName) {
                <nz-auto-option [nzValue]="option.modelName"
                  class="p-3 hover:bg-[var(--background-light-gray)] text-[var(--text-dark)] cursor-pointer transition-all duration-200">
                  <div (click)="updateModel(option.modelName)" class="text-[var(--text-dark)] flex items-center gap-2">
                    {{ option.modelName | removeProviderPrefix }}
                    <span
                      class="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-[var(--primary-purple)] text-white">
                      {{ option.modelName ? (option.modelName.split('_')[0]) : 'No provider' }}
                    </span>
                  </div>
                </nz-auto-option>
                }
              </nz-autocomplete>
              <!-- Show selected model (for user display) -->
              <div *ngIf="currentAgent.modelName" class="mt-2 text-xs text-[var(--text-medium-gray)]">
                Selected Model: <span class="font-semibold text-[var(--text-dark)]">{{ currentAgent.modelName |
                  removeProviderPrefix }}</span>
                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-[var(--primary-purple)] text-white">
                  Provider: {{ currentAgent.modelName ? currentAgent.modelName.split('_')[0] : 'No provider' }}
                </span>
              </div>
            </div>

            <!-- Plugin Selection -->
            <div>
              <label for="plugins" class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">Select
                Plugins</label>
              <nz-select [(ngModel)]="selectedPlugins" nzSize="large" nzMode="tags" [nzMaxTagCount]="2"
                nzMode="multiple" nzPlaceHolder="Please Select Plugins" class="w-full rounded-md border border-[var(--hover-blue-gray)] text-[var(--text-dark)] bg-[var(--background-light-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200
                [&_.ant-select-selection-item]:text-[var(--text-dark)]
                [&_.ant-select-selection-item]:bg-[var(--background-white)]
                [&_.ant-select-selection-item]:border-[var(--hover-blue-gray)]
                [&_.ant-select-dropdown]:bg-[var(--background-white)]
                [&_.ant-select-item]:text-[var(--text-dark)]
                [&_.ant-select-item-option-active]:bg-[var(--background-light-gray)]
                [&_.ant-select-item-option-selected]:bg-[var(--background-light-gray)]">
                @for (option of plugins; track option) {
                <nz-option
                  class="rounded-md border border-[var(--hover-blue-gray)] text-[var(--text-dark)] bg-[var(--background-light-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent"
                  [nzLabel]="option" [nzValue]="option"></nz-option>
                }
              </nz-select>
            </div>
          </div>
        </div>

        <!-- Instructions Card -->
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-5">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4 flex items-center gap-2">
            <i class="ri-file-text-line text-[var(--primary-purple)]"></i>
            <span>Agent Instructions</span>
          </h3>

          <!-- Agent Description for AI Generation -->
          <div class="mb-5">
            <label for="agentDescription" class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">
              Agent Description
            </label>
            <div class="flex flex-col space-y-3">
              <input type="text" id="agentDescription" [(ngModel)]="currentAgent.userInstructions"
                placeholder="Briefly describe how this agent should work..." [disabled]="!canGenerateInstructions()"
                class="w-full h-10 px-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
              <div class="flex justify-end">
                <button (click)="generateInstructions()"
                  [disabled]="!canGenerateInstructions() || !currentAgent.userInstructions || isGeneratingInstructions"
                  class="h-10 px-4 py-2 bg-[var(--secondary-purple)] text-[var(--text-dark)] text-sm font-medium rounded-md hover:bg-[var(--primary-purple)] hover:text-white transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed">
                  <i class="ri-magic-line" *ngIf="!isGeneratingInstructions"></i>
                  <span *ngIf="!isGeneratingInstructions">Generate Instructions with AI</span>
                  <span *ngIf="isGeneratingInstructions" class="flex items-center gap-2">
                    <i class="ri-loader-4-line animate-spin"></i>
                    Generating...
                  </span>
                </button>
              </div>
            </div>
          </div>

          <!-- Instructions -->
          <div>
            <label for="instructions" class="text-xs font-medium text-[var(--text-medium-gray)] block mb-1">
              Instructions
            </label>
            <textarea id="instructions" [(ngModel)]="currentAgent.instructions"
              placeholder="Enter detailed instructions for your AI agent..." [disabled]="isInstructionsDisabled"
              class="w-full px-4 py-3 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 resize-y min-h-[200px] max-h-[400px] disabled:opacity-50 disabled:cursor-not-allowed"></textarea>
          </div>
        </div>

        <!-- Test Agent Card -->
        <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-5">
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-4 flex items-center gap-2">
            <i class="ri-chat-3-line text-[var(--primary-purple)]"></i>
            <span>Test Your Agent</span>
          </h3>

          <div class="flex items-center p-3 bg-blue-50 rounded-md mb-4 border border-blue-100">
            <i class="ri-information-line text-blue-500 text-lg mr-3"></i>
            <p class="text-sm text-blue-700">
              <span class="font-medium">Optional:</span> You can test your agent before saving to make sure it works as
              expected.
            </p>
          </div>

          <!-- Test Button -->
          <div class="flex justify-center">
            <button [disabled]="!hasRequiredFields()" (click)="testAgent()"
              class="h-10 px-6 py-2 bg-[var(--secondary-purple)] text-[var(--text-dark)] text-sm font-medium rounded-md hover:bg-[var(--primary-purple)] hover:text-white transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed">
              <i class="ri-message-3-line"></i>
              <span>Open Chat Interface</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Sidebar for Chat Testing -->
    <div
      class="border-l border-[var(--hover-blue-gray)] bg-[var(--background-white)] shadow-md transition-all duration-300 flex-col overflow-hidden"
      style="height: calc(100vh - 74px);"
      [ngClass]="{'w-1/3 flex translate-x-0 opacity-100': showTestResults, 'w-0 hidden translate-x-full opacity-0': !showTestResults}">

      <!-- Chat Header -->
      <div
        class="p-4 border-b border-[var(--hover-blue-gray)] flex justify-between items-center sticky top-0 bg-[var(--primary-purple)] z-10">
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3">
            <i class="ri-robot-line text-[var(--primary-purple)]"></i>
          </div>
          <h3 class="text-lg font-medium text-white">Chat with {{ currentAgent.agentName || 'Agent' }}</h3>
        </div>
        <button (click)="showTestResults = false"
          class="text-white hover:text-white p-2 rounded-full hover:bg-[rgba(255,255,255,0.2)] transition-all duration-200">
          <i class="ri-close-line"></i>
        </button>
      </div>

      <!-- Chat Messages Container -->
      <div class="flex-grow overflow-y-auto p-4" style="height: calc(100vh - 180px);" #sidebarContainer>
        <!-- Chat Messages -->
        <div class="space-y-4">
          <div *ngFor="let message of chatMessages" class="mb-4">
            <!-- User Message -->
            <div *ngIf="message.sender === 'user'" class="flex items-start mb-6">
              <div
                class="w-10 h-10 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mr-3 shadow-sm">
                <i class="ri-user-line text-[var(--primary-purple)]"></i>
              </div>
              <div class="relative">
                <div
                  class="absolute top-0 -left-2 w-0 h-0 border-8 border-transparent border-r-[var(--background-light-gray)]">
                </div>
                <div class="bg-[var(--background-light-gray)] p-4 rounded-2xl rounded-tl-none max-w-[280px] shadow-sm">
                  <p class="text-sm text-[var(--text-dark)] break-words">{{ message.content }}</p>
                </div>
                <div class="text-xs text-[var(--text-medium-gray)] mt-1 ml-2">
                  {{ message.timestamp | date:'shortTime' }}
                </div>
              </div>
            </div>

            <!-- Agent Message -->
            <div *ngIf="message.sender === 'agent'" class="flex items-start mb-6 justify-end">
              <div class="relative">
                <div
                  class="absolute top-0 -right-2 w-0 h-0 border-8 border-transparent border-l-[var(--primary-purple)]">
                </div>
                <div
                  class="bg-[var(--primary-purple)] p-4 rounded-2xl rounded-tr-none max-w-[280px] text-white shadow-sm">
                  <p class="text-sm break-words whitespace-pre-wrap">{{ message.content }}</p>
                </div>
                <div class="text-xs text-[var(--text-medium-gray)] mt-1 mr-2 text-right">
                  {{ message.timestamp | date:'shortTime' }}
                </div>
              </div>
              <div
                class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center ml-3 shadow-sm">
                <i class="ri-robot-line text-white"></i>
              </div>
            </div>

            <!-- Loading Message -->
            <div *ngIf="message.sender === 'loading'" class="flex items-start mb-6 justify-end">
              <div class="relative">
                <div
                  class="absolute top-0 -right-2 w-0 h-0 border-8 border-transparent border-l-[var(--primary-purple)]">
                </div>
                <div
                  class="bg-[var(--primary-purple)] p-4 rounded-2xl rounded-tr-none max-w-[280px] text-white flex items-center shadow-sm">
                  <div class="flex space-x-2">
                    <div class="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
                  </div>
                </div>
              </div>
              <div
                class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center ml-3 shadow-sm">
                <i class="ri-robot-line text-white"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t border-[var(--hover-blue-gray)] bg-[var(--background-white)] shadow-lg sticky bottom-0">
        <div
          class="flex items-center bg-[var(--background-light-gray)] rounded-full overflow-hidden border border-[var(--hover-blue-gray)] shadow-inner">
          <input type="text" [(ngModel)]="chatInput" (keyup.enter)="sendChatMessage()"
            placeholder="Type your question here..."
            class="flex-1 p-3 border-none focus:outline-none text-sm text-[var(--text-dark)] bg-transparent"
            [disabled]="isTestingAgent">
          <button (click)="sendChatMessage()"
            class="p-2 mx-1 my-1 bg-[var(--primary-purple)] text-white rounded-full hover:bg-opacity-90 transition-all duration-200 focus:outline-none flex items-center justify-center w-9 h-9"
            [disabled]="isTestingAgent || !chatInput.trim()">
            <i class="ri-send-plane-fill" *ngIf="!isTestingAgent"></i>
            <i class="ri-loader-4-line animate-spin" *ngIf="isTestingAgent"></i>
          </button>
        </div>
        <div class="text-xs text-center mt-2 text-[var(--text-medium-gray)]">
          Press Enter to send your message
        </div>
      </div>
    </div>
  </div>
</div>

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatModelServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-chat-model',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule],
  templateUrl: './chat-model.component.html',
})
export class ChatModelComponent implements OnInit {
  models: any[] = [];
  currentModel: any = {
    id: '',
    modelId: '',
    apIkey: '',
    endpoint: '',
    provider: ''
  };
  isEditing = false;
  showForm = false;

  constructor(private chatModelService: ChatModelServiceProxy) {}

  ngOnInit(): void {
    this.loadChatModels();
  }

  loadChatModels() {
    this.chatModelService.getAll().subscribe((result) => {
      this.models = result;
    });
  }

  addNewModel(): void {
    this.isEditing = false;
    this.currentModel = {
      modelId: '',
      apIkey: '',
      endpoint: '',
      provider: ''
    };
    this.showForm = true;
  }

  editModel(model: any): void {
    this.isEditing = true;
    this.currentModel = { ...model };
    this.showForm = true;
  }

  saveModel(): void {
    this.chatModelService.createOrUpdate(this.currentModel).subscribe((res: any) => {
      console.log(res);

      if (res) {
        if (this.isEditing) {
          this.models = this.models.map(m => m.id === this.currentModel.id ? this.currentModel : m);
        } else {
          this.models.push(res);
        }
        this.showForm = false;
        this.resetForm();
      }
    });
  }

  deleteModel(modelId: string): void {
    if (confirm('Are you sure you want to delete this model?')) {
      this.chatModelService.delete(modelId).subscribe((res: any) => {
        if (res) {
          this.models = this.models.filter(model => model.modelId !== modelId);
        }
      });
    }
  }

  resetForm(): void {
    this.currentModel = {
      id: '',
      modelId: '',
      apIkey: '',
      endpoint: '',
      provider: ''
    };
    this.showForm = false;
  }

  hideApiKey(apiKey: string): string {
    if (!apiKey) return '';
    return apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 4);
  }

  generateUniqueId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}

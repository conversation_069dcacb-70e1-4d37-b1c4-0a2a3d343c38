import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnInit, OnChanges, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { ThemeService } from '../../../../shared/services/theam.service';
import { DocsServiceProxy, UpdateFavoriteDto, TrackDocumentOpenDto } from '../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-document-sidebar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './document-sidebar.component.html',
  styleUrl: './document-sidebar.component.css'
})
export class DocumentSidebarComponent implements OnInit, OnChanges {
  @Input() workspaceName: string = '';
  @Input() isPublicRoute: boolean = false;
  @Input() isDragging: boolean = false;
  @Input() splitSizes: any = { sidebarArea: 20, contentArea: 80 };
  @Input() sidebarState: 'collapsed' | 'narrow' | 'expanded' = 'narrow';

  @Output() documentSelected = new EventEmitter<any>();
  @Output() addDocumentClicked = new EventEmitter<Event>();
  @Output() showDocumentsListClicked = new EventEmitter<void>();
  @Output() gutterDoubleClick = new EventEmitter<any>();

  favorites: any[] = [];
  recentDocuments: any[] = [];

  // Reference to Math for use in the template
  Math = Math;

  // Inject ThemeService
  themeService = inject(ThemeService);

  constructor(
    private docsService: DocsServiceProxy
  ) { }

  ngOnInit() {
    this.loadSidebarData();
  }

  ngOnChanges() {
    // Reload data when workspace changes
    if (this.workspaceName) {
      this.loadSidebarData();
    }
  }

  loadSidebarData() {
    this.updateFavoritesAndRecent();
  }

  updateFavoritesAndRecent() {
    // Get favorites from API
    this.docsService.getFavorites(this.workspaceName).subscribe(
      (data: any) => {
        this.favorites = data;
      },
      (error: any) => {
        console.error('Error loading favorites:', error);
        this.favorites = [];
      }
    );

    // Get recently opened documents from API
    this.docsService.getRecentlyOpened(this.workspaceName, 5).subscribe(
      (data: any) => {
        this.recentDocuments = data;
      },
      (error: any) => {
        console.error('Error loading recent documents:', error);
        this.recentDocuments = [];
      }
    );
  }

  selectDocument(document: any) {
    this.documentSelected.emit(document);
    this.addToRecentDocuments(document);
  }

  addDocument(event: Event) {
    this.addDocumentClicked.emit(event);
  }

  showDocumentsList() {
    this.showDocumentsListClicked.emit();
  }

  onGutterDoubleClick(event: any) {
    this.gutterDoubleClick.emit(event);
  }

  isDocumentFavorite(document: any): boolean {
    return this.favorites.some((fav) => fav.id === document.id);
  }

  toggleFavorite(document: any) {
    const isFavorite = this.isDocumentFavorite(document);

    // Use the API to update favorite status
    this.docsService.updateFavoriteStatus({
      id: document.id,
      isFavorite: !isFavorite
    } as UpdateFavoriteDto).subscribe(
      (response: any) => {
        // Update local state after successful API update
        if (isFavorite) {
          this.favorites = this.favorites.filter((fav) => fav.id !== document.id);
        } else {
          this.favorites.push({ ...document, isFavorite: true });
        }
      },
      (error: any) => {
        console.error('Error updating favorite status:', error);
      }
    );
  }

  addToRecentDocuments(document: any) {
    // Use the API to track document opens
    this.docsService.trackDocumentOpen({
      id: document.id
    } as TrackDocumentOpenDto).subscribe(
      (response: any) => {
        // Update local state after successful API update
        this.recentDocuments = this.recentDocuments.filter(
          (doc) => doc.id !== document.id
        );
        this.recentDocuments.unshift(document);
        if (this.recentDocuments.length > 5) {
          this.recentDocuments = this.recentDocuments.slice(0, 5);
        }
      },
      (error: any) => {
        console.error('Error tracking document open:', error);
      }
    );
  }
}

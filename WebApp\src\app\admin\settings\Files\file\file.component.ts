import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import {
  FileDto,
  FileServiceProxy,
} from '../../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-file',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule],
  templateUrl: './file.component.html',
})
export class FileComponent implements OnInit {
  files: FileDto[] = [];
  loading: boolean = true;
  searchTerm: string = '';
  selectedFile: FileDto | null = null;
  fileToDelete: FileDto | null = null;
  sampleFiles: FileDto[] = [];
  aiPrompt: string = '';
  syncingAI: boolean = false;
  showRegenerateForm: boolean = false;

  constructor(private fileService: FileServiceProxy) {}

  ngOnInit(): void {
    // Load files when component initializes
    this.loadFiles();
  }

  loadFiles(): void {
    this.fileService.getAllFiles('File').subscribe({
      next: (res) => {
        this.sampleFiles = res;
        this.files = this.sampleFiles;

        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading files:', err);
        this.loading = false;
      },
    });
    // Simulate loading data from a service
  }

  searchFiles(): void {
    if (!this.searchTerm.trim()) {
      this.files = this.sampleFiles;
      return;
    }
    const term = this.searchTerm.toLowerCase();
    this.files = this.sampleFiles.filter(
      (file) =>
        (file.fileName?.toLowerCase() || '').includes(term) ||
        (file.description && file.description.toLowerCase().includes(term))
    );
  }

  getShortDescription(description: string): string {
    if (!description) return '';

    // Extract first 150 characters for preview
    return description.length > 150
      ? description.substring(0, 150) + '...'
      : description;
  }

  selectFile(file: FileDto): void {
    this.selectedFile = file;
    this.aiPrompt = ''; // Reset prompt when selecting a file
    this.showRegenerateForm = false; // Hide regenerate form initially
  }

  closeFileDetail(): void {
    this.selectedFile = null;
    this.aiPrompt = ''; // Reset prompt when closing detail
    this.showRegenerateForm = false;
  }

  toggleRegenerateForm(): void {
    this.showRegenerateForm = !this.showRegenerateForm;
    if (!this.showRegenerateForm) {
      this.aiPrompt = ''; // Clear prompt when hiding the form
    }
  }

  downloadFile(file: FileDto): void {
    if (!file || !file.fileName) {
      console.error('Invalid file or file name');
      return;
    }

    // Create a link element to trigger the download
    // Use the API endpoint directly
    const baseUrl = location.origin; // Get the base URL from the current location
    const downloadUrl = `${baseUrl}/api/File/Getfile/${encodeURIComponent(
      file.fileName
    )}`;

    // Create a temporary anchor element to trigger the download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = file.fileName || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  confirmDelete(file: FileDto): void {
    this.fileToDelete = file;
  }

  deleteFile(file: FileDto): void {
    if (!file || !file.fileName) {
      console.error('Invalid file or file name');
      return;
    }

    console.log('Deleting file:', file.fileName);

    // Call the service to delete the file - pass an array with the filename
    this.fileService.deleteFile([file.fileName]).subscribe({
      next: (res) => {
        console.log('File deleted:', res);
        this.files = this.files.filter((f) => f !== file);
        this.fileToDelete = null;
        this.loadFiles();
      },
      error: (err) => {
        console.error('Error deleting file:', err);
      },
    });
  }

  cancelDelete(): void {
    this.fileToDelete = null;
  }

  syncWithAI(file: FileDto): void {
    if (!file || !file.fileName || !this.aiPrompt.trim()) {
      console.error('Invalid file, file name, or empty prompt');
      return;
    }

    this.syncingAI = true;

    this.fileService.syncWithAI(file.fileName, this.aiPrompt).subscribe({
      next: (response) => {
        console.log('AI sync successful:', response);
        // Reload the file to get the updated AI analysis
        if (file.fileName) {
          this.loadFileDetails(file.fileName);
        }
        this.aiPrompt = ''; // Clear the prompt after successful sync
        this.syncingAI = false;
      },
      error: (err) => {
        console.error('Error syncing with AI:', err);
        this.syncingAI = false;
      },
    });
  }

  loadFileDetails(fileName: string): void {
    this.fileService.getFileDto(fileName).subscribe({
      next: (fileDetails) => {
        // Update the selected file with the latest details
        this.selectedFile = fileDetails;

        // Also update the file in the files array
        const index = this.files.findIndex((f) => f.fileName === fileName);
        if (index !== -1) {
          this.files[index] = fileDetails;
        }
      },
      error: (err) => {
        console.error('Error loading file details:', err);
      },
    });
  }
}

import { CommonModule } from '@angular/common';
import {
  Component,
  inject,
  OnInit,
  OnChanges,
  ViewChild,
  ElementRef,
  HostListener
} from '@angular/core';
import { TogglingService } from '../../toggling.service';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterLink,
} from '@angular/router';
import { AuthService } from '../../../shared/services/auth.service';
import { AgentDefinitionServiceProxy, ModelDetailsServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { ThemeService } from '../../../shared/services/theam.service';
import { ThemeToggleComponent } from '../../components/theme-toogle.component';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    ServiceProxyModule,
    FormsModule,
    NzBreadCrumbModule,
    RouterLink,
    ThemeToggleComponent,
  ],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css',
})
export class HeaderComponent {
  @ViewChild('userMenu') userMenu!: ElementRef;
  @ViewChild('profileButton') profileButton!: ElementRef;
  @ViewChild('profileContainer') profileContainer!: ElementRef;

  currentUrl: string = '';
  tab: any = '';
  router = inject(Router);

  togglingservice = inject(TogglingService);
  themeService = inject(ThemeService);
  models: any = [];
  filteredModels: any = [];
  searchModelQuery = '';
  selectedModel = '';
  breadCrumsList: any = [];
  isAtHomePage: boolean = false;
  workspaceName: string = '';
  displayHeaderTitle: string = '';

  // Profile-related properties
  isProfileMenuOpen = false;
  user: any;

  // Global Search Properties
  searchQuery: string = '';
  showSearchResults: boolean = false;
  searchResults: any[] = [];
  filteredResults: any[] = [];
  searchTimeout: any = null;

  // Agent Search Properties
  agents: any[] = [];
  filteredAgents: any[] = [];
  isLoadingAgents: boolean = false;

  constructor(
    public auth: AuthService,
    private route: ActivatedRoute,
    private modelDetailsService: ModelDetailsServiceProxy,
    public authService: AuthService,
    private message: NzMessageService,
    private _agentDefinition: AgentDefinitionServiceProxy
  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (this.router.url.includes('workspaces')) {
          this.workspaceName = this.router.url.split('/')[2] || '';
          console.log(this.workspaceName);
          this.workspaceName = decodeURIComponent(this.workspaceName);
        } else {
          this.workspaceName = "";
        }

        // Check if URL is empty or just the root URL
        if (this.router.url === '' || this.router.url === '/') {
          this.isAtHomePage = true;
        } else {
          this.isAtHomePage = false;
        }

        // Update display header title based on URL
        this.updateDisplayHeaderTitle();
      }
    });

    this.getBreadCrumsList(); // Initialize the breadcrumb list on component creation
  }

  updateDisplayHeaderTitle() {
    // Check if URL is empty or just the root URL
    if (this.currentUrl === '' || this.currentUrl === '/' || this.isAtHomePage) {
      this.displayHeaderTitle = 'AI Hub';
      return;
    }

    if (this.currentUrl.includes('/settings/agents/new')) {
      this.displayHeaderTitle = 'Agent new';
    } else if (this.currentUrl.includes('/workspaces/') && this.currentUrl.endsWith('/chat')) {
      this.displayHeaderTitle = `${this.workspaceName.toLowerCase()} agent new`;
    } else if (this.currentUrl.includes('/workspaces/') && this.currentUrl.endsWith('/agents/new')) {
      this.displayHeaderTitle = `${this.workspaceName.toLowerCase()} agent new`;
    } else if (this.breadCrumsList.length > 0) {
      this.displayHeaderTitle = this.breadCrumsList[this.breadCrumsList.length - 1].title;
    } else {
      this.displayHeaderTitle = 'AI Hub';
    }
  }

  getBreadCrumsList() {
    let previousUrl = '';

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.currentUrl = this.router.url;

        // Only reset and rebuild breadcrumbs if the URL has actually changed
        // This prevents breadcrumbs from disappearing when clicking the same route
        if (this.currentUrl !== previousUrl) {
          previousUrl = this.currentUrl;
          this.breadCrumsList = []; // Reset the breadcrumb list only when URL changes

          // Create breadcrumb objects with title & link
          const segments = this.currentUrl
            .split('/')
            .filter((segment) => segment.length > 0);
          let path = '';

          // Set the home breadcrumb
          this.breadCrumsList = [{ title: 'AI Hub', link: '/' }];

          // If we're at the root URL, don't add any more breadcrumbs
          if (this.currentUrl === '/' || this.currentUrl === '') {
            return;
          }

          segments.forEach((segment) => {
            path += `/${decodeURIComponent(segment)}`;
            this.breadCrumsList.push({ title: decodeURIComponent(segment), link: path });
          });
        }

        // Handle basic breadcrumb transformations
        if (this.breadCrumsList[this.breadCrumsList.length - 2]?.title === 'home') {
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Chat History';
          this.breadCrumsList.splice(this.breadCrumsList.length - 2, 1);
        }

        if (this.breadCrumsList[this.breadCrumsList.length - 2]?.title === 'chat') {
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Chat History';
          this.breadCrumsList.splice(this.breadCrumsList.length - 2, 1);
        }

        if (this.breadCrumsList[this.breadCrumsList.length - 1]?.title === '0') {
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Chat History';
          this.breadCrumsList.splice(this.breadCrumsList.length - 2, 1);
        }

        // Special cases for agent breadcrumbs
        if (this.currentUrl.includes('/settings/agents/new')) {
          // For /settings/agents/new URL pattern
          this.breadCrumsList[this.breadCrumsList.length - 1].title = 'Agent new';
        } else if (this.workspaceName &&
          (this.currentUrl.endsWith('/agents/new') || this.currentUrl.endsWith('/chat'))) {
          // For both /workspaces/NAME/agents/new and /workspaces/NAME/chat URL patterns
          this.breadCrumsList[this.breadCrumsList.length - 1].title = `${this.workspaceName.toLowerCase()} agent new`;
        }

        console.log('Breadcrumbs Updated:', this.breadCrumsList);
      }
    });
  }

  async ngOnInit() {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    this.tab = this.route.snapshot.paramMap.get('adnminTab');
    this.getBreadCrums();

    // Load user info and models
    this.user = await this.auth.getUser();
    this.loadActiveModels();
    this.loadAllAgents();
  }

  loadAllAgents() {
    this.isLoadingAgents = true;
    this._agentDefinition.getAll().subscribe({
      next: (result: any) => {
        this.agents = result;
        this.filteredAgents = [...this.agents];
        this.isLoadingAgents = false;
      },
      error: (error: any) => {
        console.error('Error loading agents:', error);
        this.isLoadingAgents = false;
        // Fallback to empty array
        this.agents = [];
        this.filteredAgents = [];
      }
    });
  }

  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: MouseEvent) {
    // Check if userMenu and profileButton are defined
    if (!this.userMenu || !this.profileButton || !this.profileContainer) return;

    // Get the DOM elements
    const userMenuElement = this.userMenu.nativeElement;
    const profileButtonElement = this.profileButton.nativeElement;
    const profileContainerElement = this.profileContainer.nativeElement;

    // Check if the click was outside both the menu and the button
    if (this.isProfileMenuOpen &&
        !userMenuElement.contains(event.target) &&
        !profileContainerElement.contains(event.target)) {
      // Close the menu
      this.closeProfileMenu();
    }
  }

  // Method to toggle profile menu
  toggleProfileMenu(event: Event) {
    event.stopPropagation();
    this.isProfileMenuOpen = !this.isProfileMenuOpen;

    // Toggle classes on the menu element
    if (this.userMenu) {
      this.userMenu.nativeElement.classList.toggle('opacity-0');
      this.userMenu.nativeElement.classList.toggle('hidden');
    }
  }

  // Method to close profile menu
  closeProfileMenu() {
    this.isProfileMenuOpen = false;

    // Add classes to hide the menu
    if (this.userMenu) {
      this.userMenu.nativeElement.classList.add('opacity-0');
      this.userMenu.nativeElement.classList.add('hidden');
    }
  }

  getBreadCrums() {
    // console.log(this.breadCrumsList);
  }

  ngOnChanges(): void {
    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.
    //Add '${implements OnChanges}' to the class.

    // Update URL segments for debugging
    const url = this.router.url;
    console.log('URL segments:', url);
  }

  loadActiveModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((result: any) => {
      this.models = result;
      // console.log(this.models);
      this.filteredModels = result;
    });
  }

  searchModels() {
    this.filteredModels = this.models.filter((model: any) =>
      model.modelName
        .toLowerCase()
        .includes(this.searchModelQuery.toLowerCase())
    );
  }

  navigateToSettingsPanel() {
    this.router.navigate(['settings', 'users']);
  }

  logout() {
    this.closeProfileMenu();
    this.auth.logout();
  }

  getModels() {
    this.selectedModel = this.auth.getModel();
    this.message.success('Model Changed Successfully!');
  }

  getUserInitial(): string {
    // Get user information from auth service
    if (this.user?.name) {
      return this.user.name.charAt(0).toUpperCase();
    } else if (this.auth.getUserName()) {
      return this.auth.getUserName().charAt(0).toUpperCase();
    }
    // Default fallback
    return 'U';
  }

  // Global Search Methods
  onSearchInputChange(): void {
    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce the search to avoid excessive API calls
    this.searchTimeout = setTimeout(() => {
      if (this.searchQuery.trim()) {
        this.performSearch();
      } else {
        this.filteredResults = [];
      }
    }, 300);
  }

  performSearch(): void {
    const query = this.searchQuery.toLowerCase();

    // Only search agents by agentName
    const agentResults = this.agents
      .filter(agent =>
        agent.agentName?.toLowerCase().includes(query)
      )
      .map(agent => ({
        id: agent.guid,
        type: 'agent',
        title: agent.agentName,
        snippet: '', // Remove description/instructions
        date: new Date(),
        agentData: agent
      }));

    // Only show agent results
    this.filteredResults = agentResults;
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.filteredResults = [];
    this.showSearchResults = false;
  }

  onSearchBlur(): void {
    // Delay hiding results to allow clicks on results
    setTimeout(() => {
      this.showSearchResults = false;
    }, 200);
  }

  navigateToResult(result: any): void {
    // Navigate based on result type
    switch(result.type) {
      case 'chat':
        if (result.workspaceId) {
          this.router.navigate(['/workspaces', result.workspaceId, 'chat', result.id]);
        } else {
          this.router.navigate(['/chat', result.id]);
        }
        break;
      case 'document':
        this.router.navigate(['/notes', result.id]);
        break;
      case 'workspace':
        this.router.navigate(['/workspaces', result.id]);
        break;
      case 'agent':
        // Navigate to agent chat if it's a real agent, otherwise to settings
        if (result.agentData) {
          this.router.navigate(['/agent-chat', result.agentData.agentName]);
        } else {
          this.router.navigate(['/settings', 'agents', result.id]);
        }
        break;
    }

    this.clearSearch();
  }



  /**
   * Search agents specifically by agent name
   */
  searchAgentsByName(query: string): any[] {
    if (!query || query.trim().length === 0) {
      return [];
    }

    const searchTerm = query.toLowerCase().trim();
    return this.agents.filter(agent =>
      agent.agentName?.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get agent search results for display
   */
  getAgentSearchResults(): any[] {
    return this.filteredResults.filter(result => result.type === 'agent');
  }

  /**
   * Navigate to agent chat page
   * @param agent The agent to view chat for
   * @param event The click event
   */
  viewAgentChat(agent: any, event: Event): void {
    // Prevent the card click event from firing
    event.stopPropagation();

    if (!agent || !agent.agentName) return;

    // Navigate to agent chat page
    this.router.navigate(['/agent-chat', agent.agentName]);

    // Clear search
    this.clearSearch();
  }


}

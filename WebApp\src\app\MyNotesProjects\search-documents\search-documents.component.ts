import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NotesService, Note } from '../services/notes.service';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MarkdownModule } from 'ngx-markdown';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

interface EditorBlock {
  id: string;
  type: string;
  data: {
    text?: string;
    style?: string;
    items?: Array<{ content: string; items: any[] }>;
    link?: string;
    meta?: { image?: { url?: string }; title?: string; description?: string };
  };
}

interface EditorData {
  time: number;
  blocks: EditorBlock[];
  version: string;
}

@Component({
  selector: 'app-search-documents',
  templateUrl: './search-documents.component.html',
  styleUrls: ['./search-documents.component.css'],
  imports: [CommonModule, FormsModule, MarkdownModule],
  standalone: true,
})
export class SearchDocumentsComponent implements OnInit {
  searchQuery: string = '';
  searchResults: Note[] = [];
  isLoading: boolean = false;
  @Output() favoriteToggled = new EventEmitter<Note>();

  constructor(
    private notesService: NotesService,
    private sanitizer: DomSanitizer,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      if (params['q']) {
        this.searchQuery = params['q'];
        this.onSearch();
      }
    });
  }

  onSearch() {
    this.isLoading = true;
    this.notesService.searchNotes(this.searchQuery).subscribe({
      next: results => {
        console.log('Search results:', results);
        this.searchResults = results.map(note => ({
          ...note,
          content: this.formatContent(note.content)
        }));
        this.isLoading = false;
      },
      error: error => {
        console.error('Error searching notes:', error);
        this.isLoading = false;
      }
    });
  }

  private formatContent(content: string): SafeHtml {
    try {
      const editorData: EditorData = JSON.parse(content);
      let html = '';

      editorData.blocks.forEach(block => {
        switch (block.type) {
          case 'paragraph':
            html += `<p class="mb-4">${block.data.text || ''}</p>`;
            break;
          case 'list':
            html += '<ul class="list-disc pl-6 mb-4">';
            block.data.items?.forEach(item => {
              html += `<li class="mb-2">${item?.content || ''}</li>`;
            });
            html += '</ul>';
            break;
          case 'link':
            const { link, meta } = block.data;
            html += `
              <div class="mb-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700">
                <a href="${link}" target="_blank" rel="noopener noreferrer"
                   class="flex items-start no-underline">
                  ${meta?.image?.url ? `<div class="flex-shrink-0 mr-4"><img src="${meta.image.url}" alt="" class="w-16 h-16 object-cover rounded"></div>` : ''}
                  <div class="flex-grow">
                    <h3 class="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-1">
                      ${meta?.title || link}
                    </h3>
                    ${meta?.description ? `<p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-2">${meta.description}</p>` : ''}
                    <span class="text-gray-500 dark:text-gray-400 text-xs">${link ? new URL(link).hostname : ''}</span>
                  </div>
                </a>
              </div>
            `;
            break;
        }
      });

      return this.sanitizer.bypassSecurityTrustHtml(html);
    } catch (e) {
      console.error('Error parsing content:', e);
      return this.sanitizer.bypassSecurityTrustHtml('<p>Error displaying content</p>');
    }
  }

  toggleFavorite(note: Note) {
    if (!note?.id) return;

    this.notesService.toggleFavorite(note.id).subscribe({
      next: updatedNote => {
        console.log(updatedNote);
        note.isFavourite = updatedNote.isFavourite;
        this.favoriteToggled.emit(note);
      },
      error: error => {
        console.error('Error toggling favorite:', error);
      }
    });
  }

  goToNote(noteId: number) {
    this.router.navigate(['student/search', noteId]);
  }

  getSlicedContent(note: Note): string {
    return note?.content?.length > 50 ? note.content.slice(0, 50) + '...' : note.content;
  }
}

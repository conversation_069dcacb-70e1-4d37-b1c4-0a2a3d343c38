import { Component, Inject } from '@angular/core';
import { NzModalRef, NzModalService, NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import {
  UserAccountServiceProxy,
  AssignWorkspaceServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { AddUserComponent } from '../add-user/add-user.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { AuthService } from '../../../shared/services/auth.service';

@Component({
  selector: 'app-workspace-users-dialog',
  standalone: true,
  imports: [ServiceProxyModule, CommonModule, NzTableModule, NzButtonModule],
  templateUrl: './workspace-users-dialog.component.html',
  styleUrl: './workspace-users-dialog.component.css',
})
export class WorkspaceUsersDialogComponent {
  constructor(
    private modalRef: NzModalRef,
    private userAccountService: UserAccountServiceProxy,
    private modalService: NzModalService,
    private assignWorkspaceService: AssignWorkspaceServiceProxy,
    public authService: AuthService,
    @Inject(NZ_MODAL_DATA) public data: { id: number }
  ) { }
  users: any = []; // Array to hold user data
  workspaceId: number = this.data.id; // Get the workspace ID from modal data

  ngOnInit(): void {
    console.log(this.data.id);
    if (this.data.id) {
      this.loadUsers();
    }
  }
  get isAdmin() {
    return this.authService.isAdmin();
  }
  loadUsers() {
    console.log(this.data.id);

    this.assignWorkspaceService
      .getUsersByWorkspaceId(this.data.id)
      .subscribe((users: any) => {
        this.users = users;
        console.log(users);
      });
    // this.userAccountService.getAll().subscribe((users: any) => {
    //   this.users = users;
    //   console.log(this.users);
    // });
  }
  addUser() {
    const modelRef = this.modalService.create({
      nzTitle: 'User List',
      nzContent: AddUserComponent,
      nzData: {
        id: this.data.id, // Pass any necessary parameters to the dialog component
        users: this.users, // Pass the current user list to the dialog component
      },
      nzWidth: '800px',
      nzFooter: null, // We handle the footer in the dialog component

      nzMaskClosable: true,  // Allows closing on click outside the modal
      nzClosable: true,
    });
    modelRef.afterClose.subscribe((result) => {
      if (result && result.addedUsers) {
        // Update the user list with added users
        this.users = [...this.users, ...result.addedUsers];
      }
    });
  }
  removeUser(user: any) {
    this.assignWorkspaceService
      .removeUser(this.data.id, user.email)
      .subscribe((response: any) => {
        console.log('User removed:', response);
        // Remove the user from the list
        this.users = this.users.filter((u: any) => u.email !== user.email);
      });
  }
  closeDialog(): void {
    this.modalRef.close({
      // Return any necessary data to the parent component
    });
  }
}

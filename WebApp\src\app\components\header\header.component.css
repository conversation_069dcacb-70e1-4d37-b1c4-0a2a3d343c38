/* Hide scrollbar for WebKit browsers */
.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* Header styling - Base styles */
header {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  height: 65px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light theme header */
.light-header {
  background-color: #FFFFFF;
  color: var(--text-dark);
  border-bottom: 1px solid #E2E8F0;
}

/* Dark theme header */
.dark-header {
  background-color: #1A1E23;
  color: #FFFFFF;
}

/* Header title styling */
.header-title {
  transition: color 0.3s ease;
}

.light-header .header-title {
  color: var(--text-dark);
}

.dark-header .header-title {
  color: #FFFFFF;
}

/* Menu button styling */
.menu-button {
  transition: background-color 0.3s ease;
}

.light-header .menu-button {
  background-color: #F7FAFC;
}

.dark-header .menu-button {
  background-color: #2D3748;
}

/* Define primary-purple if not already defined elsewhere */
:root {
  --primary-purple: #7C3AED;
}

.light-header .menu-button:hover {
  background-color: #EDF2F7;
}

.dark-header .menu-button:hover {
  background-color: #4A5568;
}

/* Menu icon styling */
.menu-icon {
  transition: color 0.2s ease;
}

.light-header .menu-icon {
  color: #718096;
}

.dark-header .menu-icon {
  color: #A0AEC0;
}

header button:hover .menu-icon {
  color: var(--primary-purple);
}

/* Search bar styling */
.search-input {
  background-color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.dark-header .search-input {
  background-color: rgba(45, 55, 72, 0.9);
  border-color: #4A5568;
  color: #EDF2F7;
}

.search-input:focus {
  box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3);
}

.light-header .search-input::placeholder {
  color: #A0AEC0;
}

.dark-header .search-input::placeholder {
  color: #718096;
}

/* Search results styling */
.search-results {
  max-height: 500px;
  overflow-y: auto;
}

.dark-header .search-results {
  background-color: #2D3748;
  border-color: #4A5568;
  color: #EDF2F7;
}

.dark-header .search-results h3 {
  color: #CBD5E0;
}

.dark-header .search-results p {
  color: #E2E8F0;
}

.dark-header .search-results p.text-gray-500 {
  color: #A0AEC0;
}

.dark-header .search-results div.hover\:bg-gray-100:hover {
  background-color: #4A5568;
}

.dark-header .search-results .border-gray-200 {
  border-color: #4A5568;
}

/* Breadcrumb styling */
.breadcrumb-separator {
  transition: color 0.3s ease;
}

.light-header .breadcrumb-separator {
  color: #718096;
}

.dark-header .breadcrumb-separator {
  color: #A0AEC0;
}

.breadcrumb-active {
  transition: color 0.3s ease;
  font-weight: 500;
}

.light-header .breadcrumb-active {
  color: var(--primary-purple);
}

.dark-header .breadcrumb-active {
  color: #60a5fa;
}

.breadcrumb-inactive {
  transition: color 0.3s ease;
  opacity: 0.8;
}

.light-header .breadcrumb-inactive {
  color: #718096;
}

.dark-header .breadcrumb-inactive {
  color: #A0AEC0;
}

/* AI Hub breadcrumb specific styling */
a[routerLink="/"] {
  font-weight: 500;
  transition: all 0.2s ease;
}

a[routerLink="/"]:hover {
  color: var(--primary-purple);
  transform: translateY(-1px);
}

/* Link styling */
header a {
  transition: color 0.2s ease;
}

.light-header a:hover {
  color: var(--primary-purple);
}

.dark-header a:hover {
  color: var(--secondary-purple);
}

/* User controls container */
.user-controls-container {
  min-width: 0;
  max-width: 300px;
  overflow: hidden;
}

::ng-deep h1 {
  margin-bottom: 0px;
}

/* Profile button styling */
.profile-button {
  background-color: transparent;
  transition: all 0.3s ease;
  border: none;
}

.light-header .profile-button {
  color: var(--text-dark);
}

.dark-header .profile-button {
  color: #FFFFFF;
}

/* User avatar styling */
.user-avatar {
  background-color: var(--primary-purple); /* Softer cream color to match image */
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  color: white; 
}

.dark-header .user-avatar {
  background-color: var(--primary-purple);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.light-header .user-avatar {
  /* background-color: var(--background-purple); */
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Status indicator styling */
.status-indicator {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.dark-header .status-indicator {
  border-color: #1e1e24;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
}

.light-header .status-indicator {
  border-color: white;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8);
}



/* Profile menu styling */
#userMenu {
  transition: opacity 0.2s ease, visibility 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

/* User menu theme styling */
.user-menu {
  border: 1px solid var(--sidebar-border);
  z-index: 1050;
}

/* Dark theme user menu */
.dark-header .user-menu {
  background-color: #2D3748;
  color: #FFFFFF;
  border-color: #4A5568;
}

/* Light theme user menu */
.light-header .user-menu {
  background-color: white;
  color: black;
  border: 1px solid #e0e0e0;
}

#userMenu:not(.hidden) {
  animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Simplified Agent Search Card Styling */
.agent-search-card {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.agent-search-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dark theme agent search card */
:host-context(.dark-theme) .agent-search-card:hover {
  box-shadow: 0 2px 8px rgba(0, 195, 154, 0.15);
}

/* Light theme agent search card */
:host-context(:not(.dark-theme)) .agent-search-card:hover {
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
}

/* View Chat Button styling */
.view-chat-btn {
  font-weight: 500;
  cursor: pointer;
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.view-chat-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-chat-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Dark theme view chat button */
:host-context(.dark-theme) .view-chat-btn {
  background-color: #00c39a;
  color: white;
}

:host-context(.dark-theme) .view-chat-btn:hover {
  background-color: #00b389;
  box-shadow: 0 2px 4px rgba(0, 195, 154, 0.2);
}

/* Light theme view chat button */
:host-context(:not(.dark-theme)) .view-chat-btn {
  background-color: var(--primary-purple);
  color: white;
}

:host-context(:not(.dark-theme)) .view-chat-btn:hover {
  background-color: var(--secondary-purple);
  box-shadow: 0 2px 4px rgba(124, 58, 237, 0.2);
}

/* Agent card shine effect */
.agent-search-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.agent-search-card:hover::before {
  left: 100%;
}

/* Line clamp utility for description */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  header .flex-1 {
    display: none;
  }

  header .user-controls-container {
    max-width: 150px;
  }

  .view-chat-btn {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .agent-search-card {
    padding: 0.5rem;
  }

  /* Adjust spacing for mobile */
  .agent-search-card .flex-shrink-0 {
    margin-left: 0.5rem;
  }
}

import {
  __commonJS,
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-EIB7IA3J.js";

// node_modules/quill/dist/quill.js
var require_quill = __commonJS({
  "node_modules/quill/dist/quill.js"(exports, module) {
    !function(t, e) {
      "object" == typeof exports && "object" == typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define([], e) : "object" == typeof exports ? exports.Quill = e() : t.Quill = e();
    }(self, function() {
      return function() {
        var t = { 9698: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Ay: function() {
            return c;
          }, Ji: function() {
            return d;
          }, mG: function() {
            return h;
          }, zo: function() {
            return u;
          } });
          var r2 = n2(6003), i = n2(5232), s = n2.n(i), o = n2(3036), l = n2(4850), a = n2(5508);
          class c extends r2.BlockBlot {
            cache = {};
            delta() {
              return null == this.cache.delta && (this.cache.delta = h(this)), this.cache.delta;
            }
            deleteAt(t3, e3) {
              super.deleteAt(t3, e3), this.cache = {};
            }
            formatAt(t3, e3, n3, i2) {
              e3 <= 0 || (this.scroll.query(n3, r2.Scope.BLOCK) ? t3 + e3 === this.length() && this.format(n3, i2) : super.formatAt(t3, Math.min(e3, this.length() - t3 - 1), n3, i2), this.cache = {});
            }
            insertAt(t3, e3, n3) {
              if (null != n3) return super.insertAt(t3, e3, n3), void (this.cache = {});
              if (0 === e3.length) return;
              const r3 = e3.split("\n"), i2 = r3.shift();
              i2.length > 0 && (t3 < this.length() - 1 || null == this.children.tail ? super.insertAt(Math.min(t3, this.length() - 1), i2) : this.children.tail.insertAt(this.children.tail.length(), i2), this.cache = {});
              let s2 = this;
              r3.reduce((t4, e4) => (s2 = s2.split(t4, true), s2.insertAt(0, e4), e4.length), t3 + i2.length);
            }
            insertBefore(t3, e3) {
              const { head: n3 } = this.children;
              super.insertBefore(t3, e3), n3 instanceof o.A && n3.remove(), this.cache = {};
            }
            length() {
              return null == this.cache.length && (this.cache.length = super.length() + 1), this.cache.length;
            }
            moveChildren(t3, e3) {
              super.moveChildren(t3, e3), this.cache = {};
            }
            optimize(t3) {
              super.optimize(t3), this.cache = {};
            }
            path(t3) {
              return super.path(t3, true);
            }
            removeChild(t3) {
              super.removeChild(t3), this.cache = {};
            }
            split(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              if (e3 && (0 === t3 || t3 >= this.length() - 1)) {
                const e4 = this.clone();
                return 0 === t3 ? (this.parent.insertBefore(e4, this), this) : (this.parent.insertBefore(e4, this.next), e4);
              }
              const n3 = super.split(t3, e3);
              return this.cache = {}, n3;
            }
          }
          c.blotName = "block", c.tagName = "P", c.defaultChild = o.A, c.allowedChildren = [o.A, l.A, r2.EmbedBlot, a.A];
          class u extends r2.EmbedBlot {
            attach() {
              super.attach(), this.attributes = new r2.AttributorStore(this.domNode);
            }
            delta() {
              return new (s())().insert(this.value(), __spreadValues(__spreadValues({}, this.formats()), this.attributes.values()));
            }
            format(t3, e3) {
              const n3 = this.scroll.query(t3, r2.Scope.BLOCK_ATTRIBUTE);
              null != n3 && this.attributes.attribute(n3, e3);
            }
            formatAt(t3, e3, n3, r3) {
              this.format(n3, r3);
            }
            insertAt(t3, e3, n3) {
              if (null != n3) return void super.insertAt(t3, e3, n3);
              const r3 = e3.split("\n"), i2 = r3.pop(), s2 = r3.map((t4) => {
                const e4 = this.scroll.create(c.blotName);
                return e4.insertAt(0, t4), e4;
              }), o2 = this.split(t3);
              s2.forEach((t4) => {
                this.parent.insertBefore(t4, o2);
              }), i2 && this.parent.insertBefore(this.scroll.create("text", i2), o2);
            }
          }
          function h(t3) {
            let e3 = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
            return t3.descendants(r2.LeafBlot).reduce((t4, n3) => 0 === n3.length() ? t4 : t4.insert(n3.value(), d(n3, {}, e3)), new (s())()).insert("\n", d(t3));
          }
          function d(t3) {
            let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, n3 = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2];
            return null == t3 ? e3 : ("formats" in t3 && "function" == typeof t3.formats && (e3 = __spreadValues(__spreadValues({}, e3), t3.formats()), n3 && delete e3["code-token"]), null == t3.parent || "scroll" === t3.parent.statics.blotName || t3.parent.statics.scope !== t3.statics.scope ? e3 : d(t3.parent, e3, n3));
          }
          u.scope = r2.Scope.BLOCK_BLOT;
        }, 3036: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6003);
          class i extends r2.EmbedBlot {
            static value() {
            }
            optimize() {
              (this.prev || this.next) && this.remove();
            }
            length() {
              return 0;
            }
            value() {
              return "";
            }
          }
          i.blotName = "break", i.tagName = "BR", e2.A = i;
        }, 580: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6003);
          class i extends r2.ContainerBlot {
          }
          e2.A = i;
        }, 4541: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6003), i = n2(5508);
          class s extends r2.EmbedBlot {
            static blotName = "cursor";
            static className = "ql-cursor";
            static tagName = "span";
            static CONTENTS = "\uFEFF";
            static value() {
            }
            constructor(t3, e3, n3) {
              super(t3, e3), this.selection = n3, this.textNode = document.createTextNode(s.CONTENTS), this.domNode.appendChild(this.textNode), this.savedLength = 0;
            }
            detach() {
              null != this.parent && this.parent.removeChild(this);
            }
            format(t3, e3) {
              if (0 !== this.savedLength) return void super.format(t3, e3);
              let n3 = this, i2 = 0;
              for (; null != n3 && n3.statics.scope !== r2.Scope.BLOCK_BLOT; ) i2 += n3.offset(n3.parent), n3 = n3.parent;
              null != n3 && (this.savedLength = s.CONTENTS.length, n3.optimize(), n3.formatAt(i2, s.CONTENTS.length, t3, e3), this.savedLength = 0);
            }
            index(t3, e3) {
              return t3 === this.textNode ? 0 : super.index(t3, e3);
            }
            length() {
              return this.savedLength;
            }
            position() {
              return [this.textNode, this.textNode.data.length];
            }
            remove() {
              super.remove(), this.parent = null;
            }
            restore() {
              if (this.selection.composing || null == this.parent) return null;
              const t3 = this.selection.getNativeRange();
              for (; null != this.domNode.lastChild && this.domNode.lastChild !== this.textNode; ) this.domNode.parentNode.insertBefore(this.domNode.lastChild, this.domNode);
              const e3 = this.prev instanceof i.A ? this.prev : null, n3 = e3 ? e3.length() : 0, r3 = this.next instanceof i.A ? this.next : null, o = r3 ? r3.text : "", { textNode: l } = this, a = l.data.split(s.CONTENTS).join("");
              let c;
              if (l.data = s.CONTENTS, e3) c = e3, (a || r3) && (e3.insertAt(e3.length(), a + o), r3 && r3.remove());
              else if (r3) c = r3, r3.insertAt(0, a);
              else {
                const t4 = document.createTextNode(a);
                c = this.scroll.create(t4), this.parent.insertBefore(c, this);
              }
              if (this.remove(), t3) {
                const i2 = (t4, i3) => e3 && t4 === e3.domNode ? i3 : t4 === l ? n3 + i3 - 1 : r3 && t4 === r3.domNode ? n3 + a.length + i3 : null, s2 = i2(t3.start.node, t3.start.offset), o2 = i2(t3.end.node, t3.end.offset);
                if (null !== s2 && null !== o2) return { startNode: c.domNode, startOffset: s2, endNode: c.domNode, endOffset: o2 };
              }
              return null;
            }
            update(t3, e3) {
              if (t3.some((t4) => "characterData" === t4.type && t4.target === this.textNode)) {
                const t4 = this.restore();
                t4 && (e3.range = t4);
              }
            }
            optimize(t3) {
              super.optimize(t3);
              let { parent: e3 } = this;
              for (; e3; ) {
                if ("A" === e3.domNode.tagName) {
                  this.savedLength = s.CONTENTS.length, e3.isolate(this.offset(e3), this.length()).unwrap(), this.savedLength = 0;
                  break;
                }
                e3 = e3.parent;
              }
            }
            value() {
              return "";
            }
          }
          e2.A = s;
        }, 746: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6003), i = n2(5508);
          const s = "\uFEFF";
          class o extends r2.EmbedBlot {
            constructor(t3, e3) {
              super(t3, e3), this.contentNode = document.createElement("span"), this.contentNode.setAttribute("contenteditable", "false"), Array.from(this.domNode.childNodes).forEach((t4) => {
                this.contentNode.appendChild(t4);
              }), this.leftGuard = document.createTextNode(s), this.rightGuard = document.createTextNode(s), this.domNode.appendChild(this.leftGuard), this.domNode.appendChild(this.contentNode), this.domNode.appendChild(this.rightGuard);
            }
            index(t3, e3) {
              return t3 === this.leftGuard ? 0 : t3 === this.rightGuard ? 1 : super.index(t3, e3);
            }
            restore(t3) {
              let e3, n3 = null;
              const r3 = t3.data.split(s).join("");
              if (t3 === this.leftGuard) if (this.prev instanceof i.A) {
                const t4 = this.prev.length();
                this.prev.insertAt(t4, r3), n3 = { startNode: this.prev.domNode, startOffset: t4 + r3.length };
              } else e3 = document.createTextNode(r3), this.parent.insertBefore(this.scroll.create(e3), this), n3 = { startNode: e3, startOffset: r3.length };
              else t3 === this.rightGuard && (this.next instanceof i.A ? (this.next.insertAt(0, r3), n3 = { startNode: this.next.domNode, startOffset: r3.length }) : (e3 = document.createTextNode(r3), this.parent.insertBefore(this.scroll.create(e3), this.next), n3 = { startNode: e3, startOffset: r3.length }));
              return t3.data = s, n3;
            }
            update(t3, e3) {
              t3.forEach((t4) => {
                if ("characterData" === t4.type && (t4.target === this.leftGuard || t4.target === this.rightGuard)) {
                  const n3 = this.restore(t4.target);
                  n3 && (e3.range = n3);
                }
              });
            }
          }
          e2.A = o;
        }, 4850: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6003), i = n2(3036), s = n2(5508);
          class o extends r2.InlineBlot {
            static allowedChildren = [o, i.A, r2.EmbedBlot, s.A];
            static order = ["cursor", "inline", "link", "underline", "strike", "italic", "bold", "script", "code"];
            static compare(t3, e3) {
              const n3 = o.order.indexOf(t3), r3 = o.order.indexOf(e3);
              return n3 >= 0 || r3 >= 0 ? n3 - r3 : t3 === e3 ? 0 : t3 < e3 ? -1 : 1;
            }
            formatAt(t3, e3, n3, i2) {
              if (o.compare(this.statics.blotName, n3) < 0 && this.scroll.query(n3, r2.Scope.BLOT)) {
                const r3 = this.isolate(t3, e3);
                i2 && r3.wrap(n3, i2);
              } else super.formatAt(t3, e3, n3, i2);
            }
            optimize(t3) {
              if (super.optimize(t3), this.parent instanceof o && o.compare(this.statics.blotName, this.parent.statics.blotName) > 0) {
                const t4 = this.parent.isolate(this.offset(), this.length());
                this.moveChildren(t4), t4.wrap(this);
              }
            }
          }
          e2.A = o;
        }, 5508: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return i;
          }, X: function() {
            return o;
          } });
          var r2 = n2(6003);
          class i extends r2.TextBlot {
          }
          const s = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#39;" };
          function o(t3) {
            return t3.replace(/[&<>"']/g, (t4) => s[t4]);
          }
        }, 3729: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { default: function() {
            return R;
          } });
          var r2 = n2(6142), i = n2(9698), s = n2(3036), o = n2(580), l = n2(4541), a = n2(746), c = n2(4850), u = n2(6003), h = n2(5232), d = n2.n(h), f = n2(5374);
          function p(t3) {
            return t3 instanceof i.Ay || t3 instanceof i.zo;
          }
          function g(t3) {
            return "function" == typeof t3.updateContent;
          }
          class m extends u.ScrollBlot {
            static blotName = "scroll";
            static className = "ql-editor";
            static tagName = "DIV";
            static defaultChild = i.Ay;
            static allowedChildren = [i.Ay, i.zo, o.A];
            constructor(t3, e3, n3) {
              let { emitter: r3 } = n3;
              super(t3, e3), this.emitter = r3, this.batch = false, this.optimize(), this.enable(), this.domNode.addEventListener("dragstart", (t4) => this.handleDragStart(t4));
            }
            batchStart() {
              Array.isArray(this.batch) || (this.batch = []);
            }
            batchEnd() {
              if (!this.batch) return;
              const t3 = this.batch;
              this.batch = false, this.update(t3);
            }
            emitMount(t3) {
              this.emitter.emit(f.A.events.SCROLL_BLOT_MOUNT, t3);
            }
            emitUnmount(t3) {
              this.emitter.emit(f.A.events.SCROLL_BLOT_UNMOUNT, t3);
            }
            emitEmbedUpdate(t3, e3) {
              this.emitter.emit(f.A.events.SCROLL_EMBED_UPDATE, t3, e3);
            }
            deleteAt(t3, e3) {
              const [n3, r3] = this.line(t3), [o2] = this.line(t3 + e3);
              if (super.deleteAt(t3, e3), null != o2 && n3 !== o2 && r3 > 0) {
                if (n3 instanceof i.zo || o2 instanceof i.zo) return void this.optimize();
                const t4 = o2.children.head instanceof s.A ? null : o2.children.head;
                n3.moveChildren(o2, t4), n3.remove();
              }
              this.optimize();
            }
            enable() {
              let t3 = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
              this.domNode.setAttribute("contenteditable", t3 ? "true" : "false");
            }
            formatAt(t3, e3, n3, r3) {
              super.formatAt(t3, e3, n3, r3), this.optimize();
            }
            insertAt(t3, e3, n3) {
              if (t3 >= this.length()) if (null == n3 || null == this.scroll.query(e3, u.Scope.BLOCK)) {
                const t4 = this.scroll.create(this.statics.defaultChild.blotName);
                this.appendChild(t4), null == n3 && e3.endsWith("\n") ? t4.insertAt(0, e3.slice(0, -1), n3) : t4.insertAt(0, e3, n3);
              } else {
                const t4 = this.scroll.create(e3, n3);
                this.appendChild(t4);
              }
              else super.insertAt(t3, e3, n3);
              this.optimize();
            }
            insertBefore(t3, e3) {
              if (t3.statics.scope === u.Scope.INLINE_BLOT) {
                const n3 = this.scroll.create(this.statics.defaultChild.blotName);
                n3.appendChild(t3), super.insertBefore(n3, e3);
              } else super.insertBefore(t3, e3);
            }
            insertContents(t3, e3) {
              const n3 = this.deltaToRenderBlocks(e3.concat(new (d())().insert("\n"))), r3 = n3.pop();
              if (null == r3) return;
              this.batchStart();
              const s2 = n3.shift();
              if (s2) {
                const e4 = "block" === s2.type && (0 === s2.delta.length() || !this.descendant(i.zo, t3)[0] && t3 < this.length()), n4 = "block" === s2.type ? s2.delta : new (d())().insert({ [s2.key]: s2.value });
                b(this, t3, n4);
                const r4 = "block" === s2.type ? 1 : 0, o3 = t3 + n4.length() + r4;
                e4 && this.insertAt(o3 - 1, "\n");
                const l3 = (0, i.Ji)(this.line(t3)[0]), a2 = h.AttributeMap.diff(l3, s2.attributes) || {};
                Object.keys(a2).forEach((t4) => {
                  this.formatAt(o3 - 1, 1, t4, a2[t4]);
                }), t3 = o3;
              }
              let [o2, l2] = this.children.find(t3);
              n3.length && (o2 && (o2 = o2.split(l2), l2 = 0), n3.forEach((t4) => {
                if ("block" === t4.type) b(this.createBlock(t4.attributes, o2 || void 0), 0, t4.delta);
                else {
                  const e4 = this.create(t4.key, t4.value);
                  this.insertBefore(e4, o2 || void 0), Object.keys(t4.attributes).forEach((n4) => {
                    e4.format(n4, t4.attributes[n4]);
                  });
                }
              })), "block" === r3.type && r3.delta.length() && b(this, o2 ? o2.offset(o2.scroll) + l2 : this.length(), r3.delta), this.batchEnd(), this.optimize();
            }
            isEnabled() {
              return "true" === this.domNode.getAttribute("contenteditable");
            }
            leaf(t3) {
              const e3 = this.path(t3).pop();
              if (!e3) return [null, -1];
              const [n3, r3] = e3;
              return n3 instanceof u.LeafBlot ? [n3, r3] : [null, -1];
            }
            line(t3) {
              return t3 === this.length() ? this.line(t3 - 1) : this.descendant(p, t3);
            }
            lines() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : Number.MAX_VALUE;
              const n3 = (t4, e4, r3) => {
                let i2 = [], s2 = r3;
                return t4.children.forEachAt(e4, r3, (t5, e5, r4) => {
                  p(t5) ? i2.push(t5) : t5 instanceof u.ContainerBlot && (i2 = i2.concat(n3(t5, e5, s2))), s2 -= r4;
                }), i2;
              };
              return n3(this, t3, e3);
            }
            optimize() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [], e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
              this.batch || (super.optimize(t3, e3), t3.length > 0 && this.emitter.emit(f.A.events.SCROLL_OPTIMIZE, t3, e3));
            }
            path(t3) {
              return super.path(t3).slice(1);
            }
            remove() {
            }
            update(t3) {
              if (this.batch) return void (Array.isArray(t3) && (this.batch = this.batch.concat(t3)));
              let e3 = f.A.sources.USER;
              "string" == typeof t3 && (e3 = t3), Array.isArray(t3) || (t3 = this.observer.takeRecords()), (t3 = t3.filter((t4) => {
                let { target: e4 } = t4;
                const n3 = this.find(e4, true);
                return n3 && !g(n3);
              })).length > 0 && this.emitter.emit(f.A.events.SCROLL_BEFORE_UPDATE, e3, t3), super.update(t3.concat([])), t3.length > 0 && this.emitter.emit(f.A.events.SCROLL_UPDATE, e3, t3);
            }
            updateEmbedAt(t3, e3, n3) {
              const [r3] = this.descendant((t4) => t4 instanceof i.zo, t3);
              r3 && r3.statics.blotName === e3 && g(r3) && r3.updateContent(n3);
            }
            handleDragStart(t3) {
              t3.preventDefault();
            }
            deltaToRenderBlocks(t3) {
              const e3 = [];
              let n3 = new (d())();
              return t3.forEach((t4) => {
                const r3 = t4?.insert;
                if (r3) if ("string" == typeof r3) {
                  const i2 = r3.split("\n");
                  i2.slice(0, -1).forEach((r4) => {
                    n3.insert(r4, t4.attributes), e3.push({ type: "block", delta: n3, attributes: t4.attributes ?? {} }), n3 = new (d())();
                  });
                  const s2 = i2[i2.length - 1];
                  s2 && n3.insert(s2, t4.attributes);
                } else {
                  const i2 = Object.keys(r3)[0];
                  if (!i2) return;
                  this.query(i2, u.Scope.INLINE) ? n3.push(t4) : (n3.length() && e3.push({ type: "block", delta: n3, attributes: {} }), n3 = new (d())(), e3.push({ type: "blockEmbed", key: i2, value: r3[i2], attributes: t4.attributes ?? {} }));
                }
              }), n3.length() && e3.push({ type: "block", delta: n3, attributes: {} }), e3;
            }
            createBlock(t3, e3) {
              let n3;
              const r3 = {};
              Object.entries(t3).forEach((t4) => {
                let [e4, i3] = t4;
                null != this.query(e4, u.Scope.BLOCK & u.Scope.BLOT) ? n3 = e4 : r3[e4] = i3;
              });
              const i2 = this.create(n3 || this.statics.defaultChild.blotName, n3 ? t3[n3] : void 0);
              this.insertBefore(i2, e3 || void 0);
              const s2 = i2.length();
              return Object.entries(r3).forEach((t4) => {
                let [e4, n4] = t4;
                i2.formatAt(0, s2, e4, n4);
              }), i2;
            }
          }
          function b(t3, e3, n3) {
            n3.reduce((e4, n4) => {
              const r3 = h.Op.length(n4);
              let s2 = n4.attributes || {};
              if (null != n4.insert) {
                if ("string" == typeof n4.insert) {
                  const r4 = n4.insert;
                  t3.insertAt(e4, r4);
                  const [o2] = t3.descendant(u.LeafBlot, e4), l2 = (0, i.Ji)(o2);
                  s2 = h.AttributeMap.diff(l2, s2) || {};
                } else if ("object" == typeof n4.insert) {
                  const r4 = Object.keys(n4.insert)[0];
                  if (null == r4) return e4;
                  if (t3.insertAt(e4, r4, n4.insert[r4]), null != t3.scroll.query(r4, u.Scope.INLINE)) {
                    const [n5] = t3.descendant(u.LeafBlot, e4), r5 = (0, i.Ji)(n5);
                    s2 = h.AttributeMap.diff(r5, s2) || {};
                  }
                }
              }
              return Object.keys(s2).forEach((n5) => {
                t3.formatAt(e4, r3, n5, s2[n5]);
              }), e4 + r3;
            }, e3);
          }
          var y = m, v = n2(5508), A = n2(584), x = n2(4266);
          class N extends x.A {
            static DEFAULTS = { delay: 1e3, maxStack: 100, userOnly: false };
            lastRecorded = 0;
            ignoreChange = false;
            stack = { undo: [], redo: [] };
            currentRange = null;
            constructor(t3, e3) {
              super(t3, e3), this.quill.on(r2.Ay.events.EDITOR_CHANGE, (t4, e4, n3, i2) => {
                t4 === r2.Ay.events.SELECTION_CHANGE ? e4 && i2 !== r2.Ay.sources.SILENT && (this.currentRange = e4) : t4 === r2.Ay.events.TEXT_CHANGE && (this.ignoreChange || (this.options.userOnly && i2 !== r2.Ay.sources.USER ? this.transform(e4) : this.record(e4, n3)), this.currentRange = w(this.currentRange, e4));
              }), this.quill.keyboard.addBinding({ key: "z", shortKey: true }, this.undo.bind(this)), this.quill.keyboard.addBinding({ key: ["z", "Z"], shortKey: true, shiftKey: true }, this.redo.bind(this)), /Win/i.test(navigator.platform) && this.quill.keyboard.addBinding({ key: "y", shortKey: true }, this.redo.bind(this)), this.quill.root.addEventListener("beforeinput", (t4) => {
                "historyUndo" === t4.inputType ? (this.undo(), t4.preventDefault()) : "historyRedo" === t4.inputType && (this.redo(), t4.preventDefault());
              });
            }
            change(t3, e3) {
              if (0 === this.stack[t3].length) return;
              const n3 = this.stack[t3].pop();
              if (!n3) return;
              const i2 = this.quill.getContents(), s2 = n3.delta.invert(i2);
              this.stack[e3].push({ delta: s2, range: w(n3.range, s2) }), this.lastRecorded = 0, this.ignoreChange = true, this.quill.updateContents(n3.delta, r2.Ay.sources.USER), this.ignoreChange = false, this.restoreSelection(n3);
            }
            clear() {
              this.stack = { undo: [], redo: [] };
            }
            cutoff() {
              this.lastRecorded = 0;
            }
            record(t3, e3) {
              if (0 === t3.ops.length) return;
              this.stack.redo = [];
              let n3 = t3.invert(e3), r3 = this.currentRange;
              const i2 = Date.now();
              if (this.lastRecorded + this.options.delay > i2 && this.stack.undo.length > 0) {
                const t4 = this.stack.undo.pop();
                t4 && (n3 = n3.compose(t4.delta), r3 = t4.range);
              } else this.lastRecorded = i2;
              0 !== n3.length() && (this.stack.undo.push({ delta: n3, range: r3 }), this.stack.undo.length > this.options.maxStack && this.stack.undo.shift());
            }
            redo() {
              this.change("redo", "undo");
            }
            transform(t3) {
              E(this.stack.undo, t3), E(this.stack.redo, t3);
            }
            undo() {
              this.change("undo", "redo");
            }
            restoreSelection(t3) {
              if (t3.range) this.quill.setSelection(t3.range, r2.Ay.sources.USER);
              else {
                const e3 = function(t4, e4) {
                  const n3 = e4.reduce((t5, e5) => t5 + (e5.delete || 0), 0);
                  let r3 = e4.length() - n3;
                  return function(t5, e5) {
                    const n4 = e5.ops[e5.ops.length - 1];
                    return null != n4 && (null != n4.insert ? "string" == typeof n4.insert && n4.insert.endsWith("\n") : null != n4.attributes && Object.keys(n4.attributes).some((e6) => null != t5.query(e6, u.Scope.BLOCK)));
                  }(t4, e4) && (r3 -= 1), r3;
                }(this.quill.scroll, t3.delta);
                this.quill.setSelection(e3, r2.Ay.sources.USER);
              }
            }
          }
          function E(t3, e3) {
            let n3 = e3;
            for (let e4 = t3.length - 1; e4 >= 0; e4 -= 1) {
              const r3 = t3[e4];
              t3[e4] = { delta: n3.transform(r3.delta, true), range: r3.range && w(r3.range, n3) }, n3 = r3.delta.transform(n3), 0 === t3[e4].delta.length() && t3.splice(e4, 1);
            }
          }
          function w(t3, e3) {
            if (!t3) return t3;
            const n3 = e3.transformPosition(t3.index);
            return { index: n3, length: e3.transformPosition(t3.index + t3.length) - n3 };
          }
          var q = n2(8123);
          class k extends x.A {
            constructor(t3, e3) {
              super(t3, e3), t3.root.addEventListener("drop", (e4) => {
                e4.preventDefault();
                let n3 = null;
                if (document.caretRangeFromPoint) n3 = document.caretRangeFromPoint(e4.clientX, e4.clientY);
                else if (document.caretPositionFromPoint) {
                  const t4 = document.caretPositionFromPoint(e4.clientX, e4.clientY);
                  n3 = document.createRange(), n3.setStart(t4.offsetNode, t4.offset), n3.setEnd(t4.offsetNode, t4.offset);
                }
                const r3 = n3 && t3.selection.normalizeNative(n3);
                if (r3) {
                  const n4 = t3.selection.normalizedToRange(r3);
                  e4.dataTransfer?.files && this.upload(n4, e4.dataTransfer.files);
                }
              });
            }
            upload(t3, e3) {
              const n3 = [];
              Array.from(e3).forEach((t4) => {
                t4 && this.options.mimetypes?.includes(t4.type) && n3.push(t4);
              }), n3.length > 0 && this.options.handler.call(this, t3, n3);
            }
          }
          k.DEFAULTS = { mimetypes: ["image/png", "image/jpeg"], handler(t3, e3) {
            if (!this.quill.scroll.query("image")) return;
            const n3 = e3.map((t4) => new Promise((e4) => {
              const n4 = new FileReader();
              n4.onload = () => {
                e4(n4.result);
              }, n4.readAsDataURL(t4);
            }));
            Promise.all(n3).then((e4) => {
              const n4 = e4.reduce((t4, e5) => t4.insert({ image: e5 }), new (d())().retain(t3.index).delete(t3.length));
              this.quill.updateContents(n4, f.A.sources.USER), this.quill.setSelection(t3.index + e4.length, f.A.sources.SILENT);
            });
          } };
          var _ = k;
          const L = ["insertText", "insertReplacementText"];
          class S extends x.A {
            constructor(t3, e3) {
              super(t3, e3), t3.root.addEventListener("beforeinput", (t4) => {
                this.handleBeforeInput(t4);
              }), /Android/i.test(navigator.userAgent) || t3.on(r2.Ay.events.COMPOSITION_BEFORE_START, () => {
                this.handleCompositionStart();
              });
            }
            deleteRange(t3) {
              (0, q.Xo)({ range: t3, quill: this.quill });
            }
            replaceText(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "";
              if (0 === t3.length) return false;
              if (e3) {
                const n3 = this.quill.getFormat(t3.index, 1);
                this.deleteRange(t3), this.quill.updateContents(new (d())().retain(t3.index).insert(e3, n3), r2.Ay.sources.USER);
              } else this.deleteRange(t3);
              return this.quill.setSelection(t3.index + e3.length, 0, r2.Ay.sources.SILENT), true;
            }
            handleBeforeInput(t3) {
              if (this.quill.composition.isComposing || t3.defaultPrevented || !L.includes(t3.inputType)) return;
              const e3 = t3.getTargetRanges ? t3.getTargetRanges()[0] : null;
              if (!e3 || true === e3.collapsed) return;
              const n3 = function(t4) {
                return "string" == typeof t4.data ? t4.data : t4.dataTransfer?.types.includes("text/plain") ? t4.dataTransfer.getData("text/plain") : null;
              }(t3);
              if (null == n3) return;
              const r3 = this.quill.selection.normalizeNative(e3), i2 = r3 ? this.quill.selection.normalizedToRange(r3) : null;
              i2 && this.replaceText(i2, n3) && t3.preventDefault();
            }
            handleCompositionStart() {
              const t3 = this.quill.getSelection();
              t3 && this.replaceText(t3);
            }
          }
          var O = S;
          const T = /Mac/i.test(navigator.platform);
          class j extends x.A {
            isListening = false;
            selectionChangeDeadline = 0;
            constructor(t3, e3) {
              super(t3, e3), this.handleArrowKeys(), this.handleNavigationShortcuts();
            }
            handleArrowKeys() {
              this.quill.keyboard.addBinding({ key: ["ArrowLeft", "ArrowRight"], offset: 0, shiftKey: null, handler(t3, e3) {
                let { line: n3, event: i2 } = e3;
                if (!(n3 instanceof u.ParentBlot && n3.uiNode)) return true;
                const s2 = "rtl" === getComputedStyle(n3.domNode).direction;
                return !!(s2 && "ArrowRight" !== i2.key || !s2 && "ArrowLeft" !== i2.key) || (this.quill.setSelection(t3.index - 1, t3.length + (i2.shiftKey ? 1 : 0), r2.Ay.sources.USER), false);
              } });
            }
            handleNavigationShortcuts() {
              this.quill.root.addEventListener("keydown", (t3) => {
                !t3.defaultPrevented && ((t4) => "ArrowLeft" === t4.key || "ArrowRight" === t4.key || "ArrowUp" === t4.key || "ArrowDown" === t4.key || "Home" === t4.key || !(!T || "a" !== t4.key || true !== t4.ctrlKey))(t3) && this.ensureListeningToSelectionChange();
              });
            }
            ensureListeningToSelectionChange() {
              this.selectionChangeDeadline = Date.now() + 100, this.isListening || (this.isListening = true, document.addEventListener("selectionchange", () => {
                this.isListening = false, Date.now() <= this.selectionChangeDeadline && this.handleSelectionChange();
              }, { once: true }));
            }
            handleSelectionChange() {
              const t3 = document.getSelection();
              if (!t3) return;
              const e3 = t3.getRangeAt(0);
              if (true !== e3.collapsed || 0 !== e3.startOffset) return;
              const n3 = this.quill.scroll.find(e3.startContainer);
              if (!(n3 instanceof u.ParentBlot && n3.uiNode)) return;
              const r3 = document.createRange();
              r3.setStartAfter(n3.uiNode), r3.setEndAfter(n3.uiNode), t3.removeAllRanges(), t3.addRange(r3);
            }
          }
          var C = j;
          r2.Ay.register({ "blots/block": i.Ay, "blots/block/embed": i.zo, "blots/break": s.A, "blots/container": o.A, "blots/cursor": l.A, "blots/embed": a.A, "blots/inline": c.A, "blots/scroll": y, "blots/text": v.A, "modules/clipboard": A.Ay, "modules/history": N, "modules/keyboard": q.Ay, "modules/uploader": _, "modules/input": O, "modules/uiNode": C });
          var R = r2.Ay;
        }, 5374: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return o;
          } });
          var r2 = n2(8920), i = n2(7356);
          const s = (0, n2(6078).A)("quill:events");
          ["selectionchange", "mousedown", "mouseup", "click"].forEach((t3) => {
            document.addEventListener(t3, function() {
              for (var t4 = arguments.length, e3 = new Array(t4), n3 = 0; n3 < t4; n3++) e3[n3] = arguments[n3];
              Array.from(document.querySelectorAll(".ql-container")).forEach((t5) => {
                const n4 = i.A.get(t5);
                n4 && n4.emitter && n4.emitter.handleDOM(...e3);
              });
            });
          });
          var o = class extends r2 {
            static events = { EDITOR_CHANGE: "editor-change", SCROLL_BEFORE_UPDATE: "scroll-before-update", SCROLL_BLOT_MOUNT: "scroll-blot-mount", SCROLL_BLOT_UNMOUNT: "scroll-blot-unmount", SCROLL_OPTIMIZE: "scroll-optimize", SCROLL_UPDATE: "scroll-update", SCROLL_EMBED_UPDATE: "scroll-embed-update", SELECTION_CHANGE: "selection-change", TEXT_CHANGE: "text-change", COMPOSITION_BEFORE_START: "composition-before-start", COMPOSITION_START: "composition-start", COMPOSITION_BEFORE_END: "composition-before-end", COMPOSITION_END: "composition-end" };
            static sources = { API: "api", SILENT: "silent", USER: "user" };
            constructor() {
              super(), this.domListeners = {}, this.on("error", s.error);
            }
            emit() {
              for (var t3 = arguments.length, e3 = new Array(t3), n3 = 0; n3 < t3; n3++) e3[n3] = arguments[n3];
              return s.log.call(s, ...e3), super.emit(...e3);
            }
            handleDOM(t3) {
              for (var e3 = arguments.length, n3 = new Array(e3 > 1 ? e3 - 1 : 0), r3 = 1; r3 < e3; r3++) n3[r3 - 1] = arguments[r3];
              (this.domListeners[t3.type] || []).forEach((e4) => {
                let { node: r4, handler: i2 } = e4;
                (t3.target === r4 || r4.contains(t3.target)) && i2(t3, ...n3);
              });
            }
            listenDOM(t3, e3, n3) {
              this.domListeners[t3] || (this.domListeners[t3] = []), this.domListeners[t3].push({ node: e3, handler: n3 });
            }
          };
        }, 7356: function(t2, e2) {
          "use strict";
          e2.A = /* @__PURE__ */ new WeakMap();
        }, 6078: function(t2, e2) {
          "use strict";
          const n2 = ["error", "warn", "log", "info"];
          let r2 = "warn";
          function i(t3) {
            if (r2 && n2.indexOf(t3) <= n2.indexOf(r2)) {
              for (var e3 = arguments.length, i2 = new Array(e3 > 1 ? e3 - 1 : 0), s2 = 1; s2 < e3; s2++) i2[s2 - 1] = arguments[s2];
              console[t3](...i2);
            }
          }
          function s(t3) {
            return n2.reduce((e3, n3) => (e3[n3] = i.bind(console, n3, t3), e3), {});
          }
          s.level = (t3) => {
            r2 = t3;
          }, i.level = s.level, e2.A = s;
        }, 4266: function(t2, e2) {
          "use strict";
          e2.A = class {
            static DEFAULTS = {};
            constructor(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
              this.quill = t3, this.options = e3;
            }
          };
        }, 6142: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Ay: function() {
            return I;
          } });
          var r2 = n2(8347), i = n2(6003), s = n2(5232), o = n2.n(s), l = n2(3707), a = n2(5123), c = n2(9698), u = n2(3036), h = n2(4541), d = n2(5508), f = n2(8298);
          const p = /^[ -~]*$/;
          function g(t3, e3, n3) {
            if (0 === t3.length) {
              const [t4] = y(n3.pop());
              return e3 <= 0 ? `</li></${t4}>` : `</li></${t4}>${g([], e3 - 1, n3)}`;
            }
            const [{ child: r3, offset: i2, length: s2, indent: o2, type: l2 }, ...a2] = t3, [c2, u2] = y(l2);
            if (o2 > e3) return n3.push(l2), o2 === e3 + 1 ? `<${c2}><li${u2}>${m(r3, i2, s2)}${g(a2, o2, n3)}` : `<${c2}><li>${g(t3, e3 + 1, n3)}`;
            const h2 = n3[n3.length - 1];
            if (o2 === e3 && l2 === h2) return `</li><li${u2}>${m(r3, i2, s2)}${g(a2, o2, n3)}`;
            const [d2] = y(n3.pop());
            return `</li></${d2}>${g(t3, e3 - 1, n3)}`;
          }
          function m(t3, e3, n3) {
            let r3 = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
            if ("html" in t3 && "function" == typeof t3.html) return t3.html(e3, n3);
            if (t3 instanceof d.A) return (0, d.X)(t3.value().slice(e3, e3 + n3)).replaceAll(" ", "&nbsp;");
            if (t3 instanceof i.ParentBlot) {
              if ("list-container" === t3.statics.blotName) {
                const r4 = [];
                return t3.children.forEachAt(e3, n3, (t4, e4, n4) => {
                  const i3 = "formats" in t4 && "function" == typeof t4.formats ? t4.formats() : {};
                  r4.push({ child: t4, offset: e4, length: n4, indent: i3.indent || 0, type: i3.list });
                }), g(r4, -1, []);
              }
              const i2 = [];
              if (t3.children.forEachAt(e3, n3, (t4, e4, n4) => {
                i2.push(m(t4, e4, n4));
              }), r3 || "list" === t3.statics.blotName) return i2.join("");
              const { outerHTML: s2, innerHTML: o2 } = t3.domNode, [l2, a2] = s2.split(`>${o2}<`);
              return "<table" === l2 ? `<table style="border: 1px solid #000;">${i2.join("")}<${a2}` : `${l2}>${i2.join("")}<${a2}`;
            }
            return t3.domNode instanceof Element ? t3.domNode.outerHTML : "";
          }
          function b(t3, e3) {
            return Object.keys(e3).reduce((n3, r3) => {
              if (null == t3[r3]) return n3;
              const i2 = e3[r3];
              return i2 === t3[r3] ? n3[r3] = i2 : Array.isArray(i2) ? i2.indexOf(t3[r3]) < 0 ? n3[r3] = i2.concat([t3[r3]]) : n3[r3] = i2 : n3[r3] = [i2, t3[r3]], n3;
            }, {});
          }
          function y(t3) {
            const e3 = "ordered" === t3 ? "ol" : "ul";
            switch (t3) {
              case "checked":
                return [e3, ' data-list="checked"'];
              case "unchecked":
                return [e3, ' data-list="unchecked"'];
              default:
                return [e3, ""];
            }
          }
          function v(t3) {
            return t3.reduce((t4, e3) => {
              if ("string" == typeof e3.insert) {
                const n3 = e3.insert.replace(/\r\n/g, "\n").replace(/\r/g, "\n");
                return t4.insert(n3, e3.attributes);
              }
              return t4.push(e3);
            }, new (o())());
          }
          function A(t3, e3) {
            let { index: n3, length: r3 } = t3;
            return new f.Q(n3 + e3, r3);
          }
          var x = class {
            constructor(t3) {
              this.scroll = t3, this.delta = this.getDelta();
            }
            applyDelta(t3) {
              this.scroll.update();
              let e3 = this.scroll.length();
              this.scroll.batchStart();
              const n3 = v(t3), l2 = new (o())();
              return function(t4) {
                const e4 = [];
                return t4.forEach((t5) => {
                  "string" == typeof t5.insert ? t5.insert.split("\n").forEach((n4, r3) => {
                    r3 && e4.push({ insert: "\n", attributes: t5.attributes }), n4 && e4.push({ insert: n4, attributes: t5.attributes });
                  }) : e4.push(t5);
                }), e4;
              }(n3.ops.slice()).reduce((t4, n4) => {
                const o2 = s.Op.length(n4);
                let a2 = n4.attributes || {}, u2 = false, h2 = false;
                if (null != n4.insert) {
                  if (l2.retain(o2), "string" == typeof n4.insert) {
                    const o3 = n4.insert;
                    h2 = !o3.endsWith("\n") && (e3 <= t4 || !!this.scroll.descendant(c.zo, t4)[0]), this.scroll.insertAt(t4, o3);
                    const [l3, u3] = this.scroll.line(t4);
                    let d2 = (0, r2.A)({}, (0, c.Ji)(l3));
                    if (l3 instanceof c.Ay) {
                      const [t5] = l3.descendant(i.LeafBlot, u3);
                      t5 && (d2 = (0, r2.A)(d2, (0, c.Ji)(t5)));
                    }
                    a2 = s.AttributeMap.diff(d2, a2) || {};
                  } else if ("object" == typeof n4.insert) {
                    const o3 = Object.keys(n4.insert)[0];
                    if (null == o3) return t4;
                    const l3 = null != this.scroll.query(o3, i.Scope.INLINE);
                    if (l3) (e3 <= t4 || this.scroll.descendant(c.zo, t4)[0]) && (h2 = true);
                    else if (t4 > 0) {
                      const [e4, n5] = this.scroll.descendant(i.LeafBlot, t4 - 1);
                      e4 instanceof d.A ? "\n" !== e4.value()[n5] && (u2 = true) : e4 instanceof i.EmbedBlot && e4.statics.scope === i.Scope.INLINE_BLOT && (u2 = true);
                    }
                    if (this.scroll.insertAt(t4, o3, n4.insert[o3]), l3) {
                      const [e4] = this.scroll.descendant(i.LeafBlot, t4);
                      if (e4) {
                        const t5 = (0, r2.A)({}, (0, c.Ji)(e4));
                        a2 = s.AttributeMap.diff(t5, a2) || {};
                      }
                    }
                  }
                  e3 += o2;
                } else if (l2.push(n4), null !== n4.retain && "object" == typeof n4.retain) {
                  const e4 = Object.keys(n4.retain)[0];
                  if (null == e4) return t4;
                  this.scroll.updateEmbedAt(t4, e4, n4.retain[e4]);
                }
                Object.keys(a2).forEach((e4) => {
                  this.scroll.formatAt(t4, o2, e4, a2[e4]);
                });
                const f2 = u2 ? 1 : 0, p2 = h2 ? 1 : 0;
                return e3 += f2 + p2, l2.retain(f2), l2.delete(p2), t4 + o2 + f2 + p2;
              }, 0), l2.reduce((t4, e4) => "number" == typeof e4.delete ? (this.scroll.deleteAt(t4, e4.delete), t4) : t4 + s.Op.length(e4), 0), this.scroll.batchEnd(), this.scroll.optimize(), this.update(n3);
            }
            deleteText(t3, e3) {
              return this.scroll.deleteAt(t3, e3), this.update(new (o())().retain(t3).delete(e3));
            }
            formatLine(t3, e3) {
              let n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
              this.scroll.update(), Object.keys(n3).forEach((r4) => {
                this.scroll.lines(t3, Math.max(e3, 1)).forEach((t4) => {
                  t4.format(r4, n3[r4]);
                });
              }), this.scroll.optimize();
              const r3 = new (o())().retain(t3).retain(e3, (0, l.A)(n3));
              return this.update(r3);
            }
            formatText(t3, e3) {
              let n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
              Object.keys(n3).forEach((r4) => {
                this.scroll.formatAt(t3, e3, r4, n3[r4]);
              });
              const r3 = new (o())().retain(t3).retain(e3, (0, l.A)(n3));
              return this.update(r3);
            }
            getContents(t3, e3) {
              return this.delta.slice(t3, t3 + e3);
            }
            getDelta() {
              return this.scroll.lines().reduce((t3, e3) => t3.concat(e3.delta()), new (o())());
            }
            getFormat(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0, n3 = [], r3 = [];
              0 === e3 ? this.scroll.path(t3).forEach((t4) => {
                const [e4] = t4;
                e4 instanceof c.Ay ? n3.push(e4) : e4 instanceof i.LeafBlot && r3.push(e4);
              }) : (n3 = this.scroll.lines(t3, e3), r3 = this.scroll.descendants(i.LeafBlot, t3, e3));
              const [s2, o2] = [n3, r3].map((t4) => {
                const e4 = t4.shift();
                if (null == e4) return {};
                let n4 = (0, c.Ji)(e4);
                for (; Object.keys(n4).length > 0; ) {
                  const e5 = t4.shift();
                  if (null == e5) return n4;
                  n4 = b((0, c.Ji)(e5), n4);
                }
                return n4;
              });
              return __spreadValues(__spreadValues({}, s2), o2);
            }
            getHTML(t3, e3) {
              const [n3, r3] = this.scroll.line(t3);
              if (n3) {
                const i2 = n3.length();
                return n3.length() >= r3 + e3 && (0 !== r3 || e3 !== i2) ? m(n3, r3, e3, true) : m(this.scroll, t3, e3, true);
              }
              return "";
            }
            getText(t3, e3) {
              return this.getContents(t3, e3).filter((t4) => "string" == typeof t4.insert).map((t4) => t4.insert).join("");
            }
            insertContents(t3, e3) {
              const n3 = v(e3), r3 = new (o())().retain(t3).concat(n3);
              return this.scroll.insertContents(t3, n3), this.update(r3);
            }
            insertEmbed(t3, e3, n3) {
              return this.scroll.insertAt(t3, e3, n3), this.update(new (o())().retain(t3).insert({ [e3]: n3 }));
            }
            insertText(t3, e3) {
              let n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
              return e3 = e3.replace(/\r\n/g, "\n").replace(/\r/g, "\n"), this.scroll.insertAt(t3, e3), Object.keys(n3).forEach((r3) => {
                this.scroll.formatAt(t3, e3.length, r3, n3[r3]);
              }), this.update(new (o())().retain(t3).insert(e3, (0, l.A)(n3)));
            }
            isBlank() {
              if (0 === this.scroll.children.length) return true;
              if (this.scroll.children.length > 1) return false;
              const t3 = this.scroll.children.head;
              if (t3?.statics.blotName !== c.Ay.blotName) return false;
              const e3 = t3;
              return !(e3.children.length > 1) && e3.children.head instanceof u.A;
            }
            removeFormat(t3, e3) {
              const n3 = this.getText(t3, e3), [r3, i2] = this.scroll.line(t3 + e3);
              let s2 = 0, l2 = new (o())();
              null != r3 && (s2 = r3.length() - i2, l2 = r3.delta().slice(i2, i2 + s2 - 1).insert("\n"));
              const a2 = this.getContents(t3, e3 + s2).diff(new (o())().insert(n3).concat(l2)), c2 = new (o())().retain(t3).concat(a2);
              return this.applyDelta(c2);
            }
            update(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [], n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0;
              const r3 = this.delta;
              if (1 === e3.length && "characterData" === e3[0].type && e3[0].target.data.match(p) && this.scroll.find(e3[0].target)) {
                const i2 = this.scroll.find(e3[0].target), s2 = (0, c.Ji)(i2), l2 = i2.offset(this.scroll), a2 = e3[0].oldValue.replace(h.A.CONTENTS, ""), u2 = new (o())().insert(a2), d2 = new (o())().insert(i2.value()), f2 = n3 && { oldRange: A(n3.oldRange, -l2), newRange: A(n3.newRange, -l2) };
                t3 = new (o())().retain(l2).concat(u2.diff(d2, f2)).reduce((t4, e4) => e4.insert ? t4.insert(e4.insert, s2) : t4.push(e4), new (o())()), this.delta = r3.compose(t3);
              } else this.delta = this.getDelta(), t3 && (0, a.A)(r3.compose(t3), this.delta) || (t3 = r3.diff(this.delta, n3));
              return t3;
            }
          }, N = n2(5374), E = n2(7356), w = n2(6078), q = n2(4266), k = n2(746), _ = class {
            isComposing = false;
            constructor(t3, e3) {
              this.scroll = t3, this.emitter = e3, this.setupListeners();
            }
            setupListeners() {
              this.scroll.domNode.addEventListener("compositionstart", (t3) => {
                this.isComposing || this.handleCompositionStart(t3);
              }), this.scroll.domNode.addEventListener("compositionend", (t3) => {
                this.isComposing && queueMicrotask(() => {
                  this.handleCompositionEnd(t3);
                });
              });
            }
            handleCompositionStart(t3) {
              const e3 = t3.target instanceof Node ? this.scroll.find(t3.target, true) : null;
              !e3 || e3 instanceof k.A || (this.emitter.emit(N.A.events.COMPOSITION_BEFORE_START, t3), this.scroll.batchStart(), this.emitter.emit(N.A.events.COMPOSITION_START, t3), this.isComposing = true);
            }
            handleCompositionEnd(t3) {
              this.emitter.emit(N.A.events.COMPOSITION_BEFORE_END, t3), this.scroll.batchEnd(), this.emitter.emit(N.A.events.COMPOSITION_END, t3), this.isComposing = false;
            }
          }, L = n2(9609);
          const S = (t3) => {
            const e3 = t3.getBoundingClientRect(), n3 = "offsetWidth" in t3 && Math.abs(e3.width) / t3.offsetWidth || 1, r3 = "offsetHeight" in t3 && Math.abs(e3.height) / t3.offsetHeight || 1;
            return { top: e3.top, right: e3.left + t3.clientWidth * n3, bottom: e3.top + t3.clientHeight * r3, left: e3.left };
          }, O = (t3) => {
            const e3 = parseInt(t3, 10);
            return Number.isNaN(e3) ? 0 : e3;
          }, T = (t3, e3, n3, r3, i2, s2) => t3 < n3 && e3 > r3 ? 0 : t3 < n3 ? -(n3 - t3 + i2) : e3 > r3 ? e3 - t3 > r3 - n3 ? t3 + i2 - n3 : e3 - r3 + s2 : 0;
          const j = ["block", "break", "cursor", "inline", "scroll", "text"];
          const C = (0, w.A)("quill"), R = new i.Registry();
          i.ParentBlot.uiClass = "ql-ui";
          class I {
            static DEFAULTS = { bounds: null, modules: { clipboard: true, keyboard: true, history: true, uploader: true }, placeholder: "", readOnly: false, registry: R, theme: "default" };
            static events = N.A.events;
            static sources = N.A.sources;
            static version = "2.0.3";
            static imports = { delta: o(), parchment: i, "core/module": q.A, "core/theme": L.A };
            static debug(t3) {
              true === t3 && (t3 = "log"), w.A.level(t3);
            }
            static find(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              return E.A.get(t3) || R.find(t3, e3);
            }
            static import(t3) {
              return null == this.imports[t3] && C.error(`Cannot import ${t3}. Are you sure it was registered?`), this.imports[t3];
            }
            static register() {
              if ("string" != typeof (arguments.length <= 0 ? void 0 : arguments[0])) {
                const t3 = arguments.length <= 0 ? void 0 : arguments[0], e3 = !!(arguments.length <= 1 ? void 0 : arguments[1]), n3 = "attrName" in t3 ? t3.attrName : t3.blotName;
                "string" == typeof n3 ? this.register(`formats/${n3}`, t3, e3) : Object.keys(t3).forEach((n4) => {
                  this.register(n4, t3[n4], e3);
                });
              } else {
                const t3 = arguments.length <= 0 ? void 0 : arguments[0], e3 = arguments.length <= 1 ? void 0 : arguments[1], n3 = !!(arguments.length <= 2 ? void 0 : arguments[2]);
                null == this.imports[t3] || n3 || C.warn(`Overwriting ${t3} with`, e3), this.imports[t3] = e3, (t3.startsWith("blots/") || t3.startsWith("formats/")) && e3 && "boolean" != typeof e3 && "abstract" !== e3.blotName && R.register(e3), "function" == typeof e3.register && e3.register(R);
              }
            }
            constructor(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
              if (this.options = function(t4, e4) {
                const n4 = B(t4);
                if (!n4) throw new Error("Invalid Quill container");
                const s3 = !e4.theme || e4.theme === I.DEFAULTS.theme ? L.A : I.import(`themes/${e4.theme}`);
                if (!s3) throw new Error(`Invalid theme ${e4.theme}. Did you register it?`);
                const _a = I.DEFAULTS, { modules: o2 } = _a, l3 = __objRest(_a, ["modules"]), _b = s3.DEFAULTS, { modules: a2 } = _b, c2 = __objRest(_b, ["modules"]);
                let u2 = M(e4.modules);
                null != u2 && u2.toolbar && u2.toolbar.constructor !== Object && (u2 = __spreadProps(__spreadValues({}, u2), { toolbar: { container: u2.toolbar } }));
                const h2 = (0, r2.A)({}, M(o2), M(a2), u2), d2 = __spreadValues(__spreadValues(__spreadValues({}, l3), U(c2)), U(e4));
                let f2 = e4.registry;
                return f2 ? e4.formats && C.warn('Ignoring "formats" option because "registry" is specified') : f2 = e4.formats ? ((t5, e5, n5) => {
                  const r3 = new i.Registry();
                  return j.forEach((t6) => {
                    const n6 = e5.query(t6);
                    n6 && r3.register(n6);
                  }), t5.forEach((t6) => {
                    let i2 = e5.query(t6);
                    i2 || n5.error(`Cannot register "${t6}" specified in "formats" config. Are you sure it was registered?`);
                    let s4 = 0;
                    for (; i2; ) if (r3.register(i2), i2 = "blotName" in i2 ? i2.requiredContainer ?? null : null, s4 += 1, s4 > 100) {
                      n5.error(`Cycle detected in registering blot requiredContainer: "${t6}"`);
                      break;
                    }
                  }), r3;
                })(e4.formats, d2.registry, C) : d2.registry, __spreadProps(__spreadValues({}, d2), { registry: f2, container: n4, theme: s3, modules: Object.entries(h2).reduce((t5, e5) => {
                  let [n5, i2] = e5;
                  if (!i2) return t5;
                  const s4 = I.import(`modules/${n5}`);
                  return null == s4 ? (C.error(`Cannot load ${n5} module. Are you sure you registered it?`), t5) : __spreadProps(__spreadValues({}, t5), { [n5]: (0, r2.A)({}, s4.DEFAULTS || {}, i2) });
                }, {}), bounds: B(d2.bounds) });
              }(t3, e3), this.container = this.options.container, null == this.container) return void C.error("Invalid Quill container", t3);
              this.options.debug && I.debug(this.options.debug);
              const n3 = this.container.innerHTML.trim();
              this.container.classList.add("ql-container"), this.container.innerHTML = "", E.A.set(this.container, this), this.root = this.addContainer("ql-editor"), this.root.classList.add("ql-blank"), this.emitter = new N.A();
              const s2 = i.ScrollBlot.blotName, l2 = this.options.registry.query(s2);
              if (!l2 || !("blotName" in l2)) throw new Error(`Cannot initialize Quill without "${s2}" blot`);
              if (this.scroll = new l2(this.options.registry, this.root, { emitter: this.emitter }), this.editor = new x(this.scroll), this.selection = new f.A(this.scroll, this.emitter), this.composition = new _(this.scroll, this.emitter), this.theme = new this.options.theme(this, this.options), this.keyboard = this.theme.addModule("keyboard"), this.clipboard = this.theme.addModule("clipboard"), this.history = this.theme.addModule("history"), this.uploader = this.theme.addModule("uploader"), this.theme.addModule("input"), this.theme.addModule("uiNode"), this.theme.init(), this.emitter.on(N.A.events.EDITOR_CHANGE, (t4) => {
                t4 === N.A.events.TEXT_CHANGE && this.root.classList.toggle("ql-blank", this.editor.isBlank());
              }), this.emitter.on(N.A.events.SCROLL_UPDATE, (t4, e4) => {
                const n4 = this.selection.lastRange, [r3] = this.selection.getRange(), i2 = n4 && r3 ? { oldRange: n4, newRange: r3 } : void 0;
                D.call(this, () => this.editor.update(null, e4, i2), t4);
              }), this.emitter.on(N.A.events.SCROLL_EMBED_UPDATE, (t4, e4) => {
                const n4 = this.selection.lastRange, [r3] = this.selection.getRange(), i2 = n4 && r3 ? { oldRange: n4, newRange: r3 } : void 0;
                D.call(this, () => {
                  const n5 = new (o())().retain(t4.offset(this)).retain({ [t4.statics.blotName]: e4 });
                  return this.editor.update(n5, [], i2);
                }, I.sources.USER);
              }), n3) {
                const t4 = this.clipboard.convert({ html: `${n3}<p><br></p>`, text: "\n" });
                this.setContents(t4);
              }
              this.history.clear(), this.options.placeholder && this.root.setAttribute("data-placeholder", this.options.placeholder), this.options.readOnly && this.disable(), this.allowReadOnlyEdits = false;
            }
            addContainer(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null;
              if ("string" == typeof t3) {
                const e4 = t3;
                (t3 = document.createElement("div")).classList.add(e4);
              }
              return this.container.insertBefore(t3, e3), t3;
            }
            blur() {
              this.selection.setRange(null);
            }
            deleteText(t3, e3, n3) {
              return [t3, e3, , n3] = P(t3, e3, n3), D.call(this, () => this.editor.deleteText(t3, e3), n3, t3, -1 * e3);
            }
            disable() {
              this.enable(false);
            }
            editReadOnly(t3) {
              this.allowReadOnlyEdits = true;
              const e3 = t3();
              return this.allowReadOnlyEdits = false, e3;
            }
            enable() {
              let t3 = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
              this.scroll.enable(t3), this.container.classList.toggle("ql-disabled", !t3);
            }
            focus() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
              this.selection.focus(), t3.preventScroll || this.scrollSelectionIntoView();
            }
            format(t3, e3) {
              let n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : N.A.sources.API;
              return D.call(this, () => {
                const n4 = this.getSelection(true);
                let r3 = new (o())();
                if (null == n4) return r3;
                if (this.scroll.query(t3, i.Scope.BLOCK)) r3 = this.editor.formatLine(n4.index, n4.length, { [t3]: e3 });
                else {
                  if (0 === n4.length) return this.selection.format(t3, e3), r3;
                  r3 = this.editor.formatText(n4.index, n4.length, { [t3]: e3 });
                }
                return this.setSelection(n4, N.A.sources.SILENT), r3;
              }, n3);
            }
            formatLine(t3, e3, n3, r3, i2) {
              let s2;
              return [t3, e3, s2, i2] = P(t3, e3, n3, r3, i2), D.call(this, () => this.editor.formatLine(t3, e3, s2), i2, t3, 0);
            }
            formatText(t3, e3, n3, r3, i2) {
              let s2;
              return [t3, e3, s2, i2] = P(t3, e3, n3, r3, i2), D.call(this, () => this.editor.formatText(t3, e3, s2), i2, t3, 0);
            }
            getBounds(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0, n3 = null;
              if (n3 = "number" == typeof t3 ? this.selection.getBounds(t3, e3) : this.selection.getBounds(t3.index, t3.length), !n3) return null;
              const r3 = this.container.getBoundingClientRect();
              return { bottom: n3.bottom - r3.top, height: n3.height, left: n3.left - r3.left, right: n3.right - r3.left, top: n3.top - r3.top, width: n3.width };
            }
            getContents() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this.getLength() - t3;
              return [t3, e3] = P(t3, e3), this.editor.getContents(t3, e3);
            }
            getFormat() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.getSelection(true), e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;
              return "number" == typeof t3 ? this.editor.getFormat(t3, e3) : this.editor.getFormat(t3.index, t3.length);
            }
            getIndex(t3) {
              return t3.offset(this.scroll);
            }
            getLength() {
              return this.scroll.length();
            }
            getLeaf(t3) {
              return this.scroll.leaf(t3);
            }
            getLine(t3) {
              return this.scroll.line(t3);
            }
            getLines() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : Number.MAX_VALUE;
              return "number" != typeof t3 ? this.scroll.lines(t3.index, t3.length) : this.scroll.lines(t3, e3);
            }
            getModule(t3) {
              return this.theme.modules[t3];
            }
            getSelection() {
              return arguments.length > 0 && void 0 !== arguments[0] && arguments[0] && this.focus(), this.update(), this.selection.getRange()[0];
            }
            getSemanticHTML() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, e3 = arguments.length > 1 ? arguments[1] : void 0;
              return "number" == typeof t3 && (e3 = e3 ?? this.getLength() - t3), [t3, e3] = P(t3, e3), this.editor.getHTML(t3, e3);
            }
            getText() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0, e3 = arguments.length > 1 ? arguments[1] : void 0;
              return "number" == typeof t3 && (e3 = e3 ?? this.getLength() - t3), [t3, e3] = P(t3, e3), this.editor.getText(t3, e3);
            }
            hasFocus() {
              return this.selection.hasFocus();
            }
            insertEmbed(t3, e3, n3) {
              let r3 = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : I.sources.API;
              return D.call(this, () => this.editor.insertEmbed(t3, e3, n3), r3, t3);
            }
            insertText(t3, e3, n3, r3, i2) {
              let s2;
              return [t3, , s2, i2] = P(t3, 0, n3, r3, i2), D.call(this, () => this.editor.insertText(t3, e3, s2), i2, t3, e3.length);
            }
            isEnabled() {
              return this.scroll.isEnabled();
            }
            off() {
              return this.emitter.off(...arguments);
            }
            on() {
              return this.emitter.on(...arguments);
            }
            once() {
              return this.emitter.once(...arguments);
            }
            removeFormat(t3, e3, n3) {
              return [t3, e3, , n3] = P(t3, e3, n3), D.call(this, () => this.editor.removeFormat(t3, e3), n3, t3);
            }
            scrollRectIntoView(t3) {
              ((t4, e3) => {
                const n3 = t4.ownerDocument;
                let r3 = e3, i2 = t4;
                for (; i2; ) {
                  const t5 = i2 === n3.body, e4 = t5 ? { top: 0, right: window.visualViewport?.width ?? n3.documentElement.clientWidth, bottom: window.visualViewport?.height ?? n3.documentElement.clientHeight, left: 0 } : S(i2), o2 = getComputedStyle(i2), l2 = T(r3.left, r3.right, e4.left, e4.right, O(o2.scrollPaddingLeft), O(o2.scrollPaddingRight)), a2 = T(r3.top, r3.bottom, e4.top, e4.bottom, O(o2.scrollPaddingTop), O(o2.scrollPaddingBottom));
                  if (l2 || a2) if (t5) n3.defaultView?.scrollBy(l2, a2);
                  else {
                    const { scrollLeft: t6, scrollTop: e5 } = i2;
                    a2 && (i2.scrollTop += a2), l2 && (i2.scrollLeft += l2);
                    const n4 = i2.scrollLeft - t6, s3 = i2.scrollTop - e5;
                    r3 = { left: r3.left - n4, top: r3.top - s3, right: r3.right - n4, bottom: r3.bottom - s3 };
                  }
                  i2 = t5 || "fixed" === o2.position ? null : (s2 = i2).parentElement || s2.getRootNode().host || null;
                }
                var s2;
              })(this.root, t3);
            }
            scrollIntoView() {
              console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."), this.scrollSelectionIntoView();
            }
            scrollSelectionIntoView() {
              const t3 = this.selection.lastRange, e3 = t3 && this.selection.getBounds(t3.index, t3.length);
              e3 && this.scrollRectIntoView(e3);
            }
            setContents(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : N.A.sources.API;
              return D.call(this, () => {
                t3 = new (o())(t3);
                const e4 = this.getLength(), n3 = this.editor.deleteText(0, e4), r3 = this.editor.insertContents(0, t3), i2 = this.editor.deleteText(this.getLength() - 1, 1);
                return n3.compose(r3).compose(i2);
              }, e3);
            }
            setSelection(t3, e3, n3) {
              null == t3 ? this.selection.setRange(null, e3 || I.sources.API) : ([t3, e3, , n3] = P(t3, e3, n3), this.selection.setRange(new f.Q(Math.max(0, t3), e3), n3), n3 !== N.A.sources.SILENT && this.scrollSelectionIntoView());
            }
            setText(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : N.A.sources.API;
              const n3 = new (o())().insert(t3);
              return this.setContents(n3, e3);
            }
            update() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : N.A.sources.USER;
              const e3 = this.scroll.update(t3);
              return this.selection.update(t3), e3;
            }
            updateContents(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : N.A.sources.API;
              return D.call(this, () => (t3 = new (o())(t3), this.editor.applyDelta(t3)), e3, true);
            }
          }
          function B(t3) {
            return "string" == typeof t3 ? document.querySelector(t3) : t3;
          }
          function M(t3) {
            return Object.entries(t3 ?? {}).reduce((t4, e3) => {
              let [n3, r3] = e3;
              return __spreadProps(__spreadValues({}, t4), { [n3]: true === r3 ? {} : r3 });
            }, {});
          }
          function U(t3) {
            return Object.fromEntries(Object.entries(t3).filter((t4) => void 0 !== t4[1]));
          }
          function D(t3, e3, n3, r3) {
            if (!this.isEnabled() && e3 === N.A.sources.USER && !this.allowReadOnlyEdits) return new (o())();
            let i2 = null == n3 ? null : this.getSelection();
            const s2 = this.editor.delta, l2 = t3();
            if (null != i2 && (true === n3 && (n3 = i2.index), null == r3 ? i2 = z(i2, l2, e3) : 0 !== r3 && (i2 = z(i2, n3, r3, e3)), this.setSelection(i2, N.A.sources.SILENT)), l2.length() > 0) {
              const t4 = [N.A.events.TEXT_CHANGE, l2, s2, e3];
              this.emitter.emit(N.A.events.EDITOR_CHANGE, ...t4), e3 !== N.A.sources.SILENT && this.emitter.emit(...t4);
            }
            return l2;
          }
          function P(t3, e3, n3, r3, i2) {
            let s2 = {};
            return "number" == typeof t3.index && "number" == typeof t3.length ? "number" != typeof e3 ? (i2 = r3, r3 = n3, n3 = e3, e3 = t3.length, t3 = t3.index) : (e3 = t3.length, t3 = t3.index) : "number" != typeof e3 && (i2 = r3, r3 = n3, n3 = e3, e3 = 0), "object" == typeof n3 ? (s2 = n3, i2 = r3) : "string" == typeof n3 && (null != r3 ? s2[n3] = r3 : i2 = n3), [t3, e3, s2, i2 = i2 || N.A.sources.API];
          }
          function z(t3, e3, n3, r3) {
            const i2 = "number" == typeof n3 ? n3 : 0;
            if (null == t3) return null;
            let s2, o2;
            return e3 && "function" == typeof e3.transformPosition ? [s2, o2] = [t3.index, t3.index + t3.length].map((t4) => e3.transformPosition(t4, r3 !== N.A.sources.USER)) : [s2, o2] = [t3.index, t3.index + t3.length].map((t4) => t4 < e3 || t4 === e3 && r3 === N.A.sources.USER ? t4 : i2 >= 0 ? t4 + i2 : Math.max(e3, t4 + i2)), new f.Q(s2, o2 - s2);
          }
        }, 8298: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Q: function() {
            return a;
          } });
          var r2 = n2(6003), i = n2(5123), s = n2(3707), o = n2(5374);
          const l = (0, n2(6078).A)("quill:selection");
          class a {
            constructor(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;
              this.index = t3, this.length = e3;
            }
          }
          function c(t3, e3) {
            try {
              e3.parentNode;
            } catch (t4) {
              return false;
            }
            return t3.contains(e3);
          }
          e2.A = class {
            constructor(t3, e3) {
              this.emitter = e3, this.scroll = t3, this.composing = false, this.mouseDown = false, this.root = this.scroll.domNode, this.cursor = this.scroll.create("cursor", this), this.savedRange = new a(0, 0), this.lastRange = this.savedRange, this.lastNative = null, this.handleComposition(), this.handleDragging(), this.emitter.listenDOM("selectionchange", document, () => {
                this.mouseDown || this.composing || setTimeout(this.update.bind(this, o.A.sources.USER), 1);
              }), this.emitter.on(o.A.events.SCROLL_BEFORE_UPDATE, () => {
                if (!this.hasFocus()) return;
                const t4 = this.getNativeRange();
                null != t4 && t4.start.node !== this.cursor.textNode && this.emitter.once(o.A.events.SCROLL_UPDATE, (e4, n3) => {
                  try {
                    this.root.contains(t4.start.node) && this.root.contains(t4.end.node) && this.setNativeRange(t4.start.node, t4.start.offset, t4.end.node, t4.end.offset);
                    const r3 = n3.some((t5) => "characterData" === t5.type || "childList" === t5.type || "attributes" === t5.type && t5.target === this.root);
                    this.update(r3 ? o.A.sources.SILENT : e4);
                  } catch (t5) {
                  }
                });
              }), this.emitter.on(o.A.events.SCROLL_OPTIMIZE, (t4, e4) => {
                if (e4.range) {
                  const { startNode: t5, startOffset: n3, endNode: r3, endOffset: i2 } = e4.range;
                  this.setNativeRange(t5, n3, r3, i2), this.update(o.A.sources.SILENT);
                }
              }), this.update(o.A.sources.SILENT);
            }
            handleComposition() {
              this.emitter.on(o.A.events.COMPOSITION_BEFORE_START, () => {
                this.composing = true;
              }), this.emitter.on(o.A.events.COMPOSITION_END, () => {
                if (this.composing = false, this.cursor.parent) {
                  const t3 = this.cursor.restore();
                  if (!t3) return;
                  setTimeout(() => {
                    this.setNativeRange(t3.startNode, t3.startOffset, t3.endNode, t3.endOffset);
                  }, 1);
                }
              });
            }
            handleDragging() {
              this.emitter.listenDOM("mousedown", document.body, () => {
                this.mouseDown = true;
              }), this.emitter.listenDOM("mouseup", document.body, () => {
                this.mouseDown = false, this.update(o.A.sources.USER);
              });
            }
            focus() {
              this.hasFocus() || (this.root.focus({ preventScroll: true }), this.setRange(this.savedRange));
            }
            format(t3, e3) {
              this.scroll.update();
              const n3 = this.getNativeRange();
              if (null != n3 && n3.native.collapsed && !this.scroll.query(t3, r2.Scope.BLOCK)) {
                if (n3.start.node !== this.cursor.textNode) {
                  const t4 = this.scroll.find(n3.start.node, false);
                  if (null == t4) return;
                  if (t4 instanceof r2.LeafBlot) {
                    const e4 = t4.split(n3.start.offset);
                    t4.parent.insertBefore(this.cursor, e4);
                  } else t4.insertBefore(this.cursor, n3.start.node);
                  this.cursor.attach();
                }
                this.cursor.format(t3, e3), this.scroll.optimize(), this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length), this.update();
              }
            }
            getBounds(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;
              const n3 = this.scroll.length();
              let r3;
              t3 = Math.min(t3, n3 - 1), e3 = Math.min(t3 + e3, n3 - 1) - t3;
              let [i2, s2] = this.scroll.leaf(t3);
              if (null == i2) return null;
              if (e3 > 0 && s2 === i2.length()) {
                const [e4] = this.scroll.leaf(t3 + 1);
                if (e4) {
                  const [n4] = this.scroll.line(t3), [r4] = this.scroll.line(t3 + 1);
                  n4 === r4 && (i2 = e4, s2 = 0);
                }
              }
              [r3, s2] = i2.position(s2, true);
              const o2 = document.createRange();
              if (e3 > 0) return o2.setStart(r3, s2), [i2, s2] = this.scroll.leaf(t3 + e3), null == i2 ? null : ([r3, s2] = i2.position(s2, true), o2.setEnd(r3, s2), o2.getBoundingClientRect());
              let l2, a2 = "left";
              if (r3 instanceof Text) {
                if (!r3.data.length) return null;
                s2 < r3.data.length ? (o2.setStart(r3, s2), o2.setEnd(r3, s2 + 1)) : (o2.setStart(r3, s2 - 1), o2.setEnd(r3, s2), a2 = "right"), l2 = o2.getBoundingClientRect();
              } else {
                if (!(i2.domNode instanceof Element)) return null;
                l2 = i2.domNode.getBoundingClientRect(), s2 > 0 && (a2 = "right");
              }
              return { bottom: l2.top + l2.height, height: l2.height, left: l2[a2], right: l2[a2], top: l2.top, width: 0 };
            }
            getNativeRange() {
              const t3 = document.getSelection();
              if (null == t3 || t3.rangeCount <= 0) return null;
              const e3 = t3.getRangeAt(0);
              if (null == e3) return null;
              const n3 = this.normalizeNative(e3);
              return l.info("getNativeRange", n3), n3;
            }
            getRange() {
              const t3 = this.scroll.domNode;
              if ("isConnected" in t3 && !t3.isConnected) return [null, null];
              const e3 = this.getNativeRange();
              return null == e3 ? [null, null] : [this.normalizedToRange(e3), e3];
            }
            hasFocus() {
              return document.activeElement === this.root || null != document.activeElement && c(this.root, document.activeElement);
            }
            normalizedToRange(t3) {
              const e3 = [[t3.start.node, t3.start.offset]];
              t3.native.collapsed || e3.push([t3.end.node, t3.end.offset]);
              const n3 = e3.map((t4) => {
                const [e4, n4] = t4, i3 = this.scroll.find(e4, true), s3 = i3.offset(this.scroll);
                return 0 === n4 ? s3 : i3 instanceof r2.LeafBlot ? s3 + i3.index(e4, n4) : s3 + i3.length();
              }), i2 = Math.min(Math.max(...n3), this.scroll.length() - 1), s2 = Math.min(i2, ...n3);
              return new a(s2, i2 - s2);
            }
            normalizeNative(t3) {
              if (!c(this.root, t3.startContainer) || !t3.collapsed && !c(this.root, t3.endContainer)) return null;
              const e3 = { start: { node: t3.startContainer, offset: t3.startOffset }, end: { node: t3.endContainer, offset: t3.endOffset }, native: t3 };
              return [e3.start, e3.end].forEach((t4) => {
                let { node: e4, offset: n3 } = t4;
                for (; !(e4 instanceof Text) && e4.childNodes.length > 0; ) if (e4.childNodes.length > n3) e4 = e4.childNodes[n3], n3 = 0;
                else {
                  if (e4.childNodes.length !== n3) break;
                  e4 = e4.lastChild, n3 = e4 instanceof Text ? e4.data.length : e4.childNodes.length > 0 ? e4.childNodes.length : e4.childNodes.length + 1;
                }
                t4.node = e4, t4.offset = n3;
              }), e3;
            }
            rangeToNative(t3) {
              const e3 = this.scroll.length(), n3 = (t4, n4) => {
                t4 = Math.min(e3 - 1, t4);
                const [r3, i2] = this.scroll.leaf(t4);
                return r3 ? r3.position(i2, n4) : [null, -1];
              };
              return [...n3(t3.index, false), ...n3(t3.index + t3.length, true)];
            }
            setNativeRange(t3, e3) {
              let n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : t3, r3 = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : e3, i2 = arguments.length > 4 && void 0 !== arguments[4] && arguments[4];
              if (l.info("setNativeRange", t3, e3, n3, r3), null != t3 && (null == this.root.parentNode || null == t3.parentNode || null == n3.parentNode)) return;
              const s2 = document.getSelection();
              if (null != s2) if (null != t3) {
                this.hasFocus() || this.root.focus({ preventScroll: true });
                const { native: o2 } = this.getNativeRange() || {};
                if (null == o2 || i2 || t3 !== o2.startContainer || e3 !== o2.startOffset || n3 !== o2.endContainer || r3 !== o2.endOffset) {
                  t3 instanceof Element && "BR" === t3.tagName && (e3 = Array.from(t3.parentNode.childNodes).indexOf(t3), t3 = t3.parentNode), n3 instanceof Element && "BR" === n3.tagName && (r3 = Array.from(n3.parentNode.childNodes).indexOf(n3), n3 = n3.parentNode);
                  const i3 = document.createRange();
                  i3.setStart(t3, e3), i3.setEnd(n3, r3), s2.removeAllRanges(), s2.addRange(i3);
                }
              } else s2.removeAllRanges(), this.root.blur();
            }
            setRange(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1], n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : o.A.sources.API;
              if ("string" == typeof e3 && (n3 = e3, e3 = false), l.info("setRange", t3), null != t3) {
                const n4 = this.rangeToNative(t3);
                this.setNativeRange(...n4, e3);
              } else this.setNativeRange(null);
              this.update(n3);
            }
            update() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : o.A.sources.USER;
              const e3 = this.lastRange, [n3, r3] = this.getRange();
              if (this.lastRange = n3, this.lastNative = r3, null != this.lastRange && (this.savedRange = this.lastRange), !(0, i.A)(e3, this.lastRange)) {
                if (!this.composing && null != r3 && r3.native.collapsed && r3.start.node !== this.cursor.textNode) {
                  const t4 = this.cursor.restore();
                  t4 && this.setNativeRange(t4.startNode, t4.startOffset, t4.endNode, t4.endOffset);
                }
                const n4 = [o.A.events.SELECTION_CHANGE, (0, s.A)(this.lastRange), (0, s.A)(e3), t3];
                this.emitter.emit(o.A.events.EDITOR_CHANGE, ...n4), t3 !== o.A.sources.SILENT && this.emitter.emit(...n4);
              }
            }
          };
        }, 9609: function(t2, e2) {
          "use strict";
          class n2 {
            static DEFAULTS = { modules: {} };
            static themes = { default: n2 };
            modules = {};
            constructor(t3, e3) {
              this.quill = t3, this.options = e3;
            }
            init() {
              Object.keys(this.options.modules).forEach((t3) => {
                null == this.modules[t3] && this.addModule(t3);
              });
            }
            addModule(t3) {
              const e3 = this.quill.constructor.import(`modules/${t3}`);
              return this.modules[t3] = new e3(this.quill, this.options.modules[t3] || {}), this.modules[t3];
            }
          }
          e2.A = n2;
        }, 8276: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Hu: function() {
            return l;
          }, gS: function() {
            return s;
          }, qh: function() {
            return o;
          } });
          var r2 = n2(6003);
          const i = { scope: r2.Scope.BLOCK, whitelist: ["right", "center", "justify"] }, s = new r2.Attributor("align", "align", i), o = new r2.ClassAttributor("align", "ql-align", i), l = new r2.StyleAttributor("align", "text-align", i);
        }, 9541: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { l: function() {
            return s;
          }, s: function() {
            return o;
          } });
          var r2 = n2(6003), i = n2(8638);
          const s = new r2.ClassAttributor("background", "ql-bg", { scope: r2.Scope.INLINE }), o = new i.a2("background", "background-color", { scope: r2.Scope.INLINE });
        }, 9404: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Ay: function() {
            return h;
          }, Cy: function() {
            return d;
          }, EJ: function() {
            return u;
          } });
          var r2 = n2(9698), i = n2(3036), s = n2(4541), o = n2(4850), l = n2(5508), a = n2(580), c = n2(6142);
          class u extends a.A {
            static create(t3) {
              const e3 = super.create(t3);
              return e3.setAttribute("spellcheck", "false"), e3;
            }
            code(t3, e3) {
              return this.children.map((t4) => t4.length() <= 1 ? "" : t4.domNode.innerText).join("\n").slice(t3, t3 + e3);
            }
            html(t3, e3) {
              return `<pre>
${(0, l.X)(this.code(t3, e3))}
</pre>`;
            }
          }
          class h extends r2.Ay {
            static TAB = "  ";
            static register() {
              c.Ay.register(u);
            }
          }
          class d extends o.A {
          }
          d.blotName = "code", d.tagName = "CODE", h.blotName = "code-block", h.className = "ql-code-block", h.tagName = "DIV", u.blotName = "code-block-container", u.className = "ql-code-block-container", u.tagName = "DIV", u.allowedChildren = [h], h.allowedChildren = [l.A, i.A, s.A], h.requiredContainer = u;
        }, 8638: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { JM: function() {
            return o;
          }, a2: function() {
            return i;
          }, g3: function() {
            return s;
          } });
          var r2 = n2(6003);
          class i extends r2.StyleAttributor {
            value(t3) {
              let e3 = super.value(t3);
              return e3.startsWith("rgb(") ? (e3 = e3.replace(/^[^\d]+/, "").replace(/[^\d]+$/, ""), `#${e3.split(",").map((t4) => `00${parseInt(t4, 10).toString(16)}`.slice(-2)).join("")}`) : e3;
            }
          }
          const s = new r2.ClassAttributor("color", "ql-color", { scope: r2.Scope.INLINE }), o = new i("color", "color", { scope: r2.Scope.INLINE });
        }, 7912: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Mc: function() {
            return s;
          }, VL: function() {
            return l;
          }, sY: function() {
            return o;
          } });
          var r2 = n2(6003);
          const i = { scope: r2.Scope.BLOCK, whitelist: ["rtl"] }, s = new r2.Attributor("direction", "dir", i), o = new r2.ClassAttributor("direction", "ql-direction", i), l = new r2.StyleAttributor("direction", "direction", i);
        }, 6772: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { q: function() {
            return s;
          }, z: function() {
            return l;
          } });
          var r2 = n2(6003);
          const i = { scope: r2.Scope.INLINE, whitelist: ["serif", "monospace"] }, s = new r2.ClassAttributor("font", "ql-font", i);
          class o extends r2.StyleAttributor {
            value(t3) {
              return super.value(t3).replace(/["']/g, "");
            }
          }
          const l = new o("font", "font-family", i);
        }, 664: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { U: function() {
            return i;
          }, r: function() {
            return s;
          } });
          var r2 = n2(6003);
          const i = new r2.ClassAttributor("size", "ql-size", { scope: r2.Scope.INLINE, whitelist: ["small", "large", "huge"] }), s = new r2.StyleAttributor("size", "font-size", { scope: r2.Scope.INLINE, whitelist: ["10px", "18px", "32px"] });
        }, 584: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Ay: function() {
            return S;
          }, hV: function() {
            return I;
          } });
          var r2 = n2(6003), i = n2(5232), s = n2.n(i), o = n2(9698), l = n2(6078), a = n2(4266), c = n2(6142), u = n2(8276), h = n2(9541), d = n2(9404), f = n2(8638), p = n2(7912), g = n2(6772), m = n2(664), b = n2(8123);
          const y = /font-weight:\s*normal/, v = ["P", "OL", "UL"], A = (t3) => t3 && v.includes(t3.tagName), x = /\bmso-list:[^;]*ignore/i, N = /\bmso-list:[^;]*\bl(\d+)/i, E = /\bmso-list:[^;]*\blevel(\d+)/i, w = [function(t3) {
            "urn:schemas-microsoft-com:office:word" === t3.documentElement.getAttribute("xmlns:w") && ((t4) => {
              const e3 = Array.from(t4.querySelectorAll("[style*=mso-list]")), n3 = [], r3 = [];
              e3.forEach((t5) => {
                (t5.getAttribute("style") || "").match(x) ? n3.push(t5) : r3.push(t5);
              }), n3.forEach((t5) => t5.parentNode?.removeChild(t5));
              const i2 = t4.documentElement.innerHTML, s2 = r3.map((t5) => ((t6, e4) => {
                const n4 = t6.getAttribute("style"), r4 = n4?.match(N);
                if (!r4) return null;
                const i3 = Number(r4[1]), s3 = n4?.match(E), o2 = s3 ? Number(s3[1]) : 1, l2 = new RegExp(`@list l${i3}:level${o2}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`, "i"), a2 = e4.match(l2);
                return { id: i3, indent: o2, type: a2 && "bullet" === a2[1] ? "bullet" : "ordered", element: t6 };
              })(t5, i2)).filter((t5) => t5);
              for (; s2.length; ) {
                const t5 = [];
                let e4 = s2.shift();
                for (; e4; ) t5.push(e4), e4 = s2.length && s2[0]?.element === e4.element.nextElementSibling && s2[0].id === e4.id ? s2.shift() : null;
                const n4 = document.createElement("ul");
                t5.forEach((t6) => {
                  const e5 = document.createElement("li");
                  e5.setAttribute("data-list", t6.type), t6.indent > 1 && e5.setAttribute("class", "ql-indent-" + (t6.indent - 1)), e5.innerHTML = t6.element.innerHTML, n4.appendChild(e5);
                });
                const r4 = t5[0]?.element, { parentNode: i3 } = r4 ?? {};
                r4 && i3?.replaceChild(n4, r4), t5.slice(1).forEach((t6) => {
                  let { element: e5 } = t6;
                  i3?.removeChild(e5);
                });
              }
            })(t3);
          }, function(t3) {
            t3.querySelector('[id^="docs-internal-guid-"]') && (((t4) => {
              Array.from(t4.querySelectorAll('b[style*="font-weight"]')).filter((t5) => t5.getAttribute("style")?.match(y)).forEach((e3) => {
                const n3 = t4.createDocumentFragment();
                n3.append(...e3.childNodes), e3.parentNode?.replaceChild(n3, e3);
              });
            })(t3), ((t4) => {
              Array.from(t4.querySelectorAll("br")).filter((t5) => A(t5.previousElementSibling) && A(t5.nextElementSibling)).forEach((t5) => {
                t5.parentNode?.removeChild(t5);
              });
            })(t3));
          }];
          const q = (0, l.A)("quill:clipboard"), k = [[Node.TEXT_NODE, function(t3, e3, n3) {
            let r3 = t3.data;
            if ("O:P" === t3.parentElement?.tagName) return e3.insert(r3.trim());
            if (!R(t3)) {
              if (0 === r3.trim().length && r3.includes("\n") && !function(t4, e4) {
                return t4.previousElementSibling && t4.nextElementSibling && !j(t4.previousElementSibling, e4) && !j(t4.nextElementSibling, e4);
              }(t3, n3)) return e3;
              r3 = r3.replace(/[^\S\u00a0]/g, " "), r3 = r3.replace(/ {2,}/g, " "), (null == t3.previousSibling && null != t3.parentElement && j(t3.parentElement, n3) || t3.previousSibling instanceof Element && j(t3.previousSibling, n3)) && (r3 = r3.replace(/^ /, "")), (null == t3.nextSibling && null != t3.parentElement && j(t3.parentElement, n3) || t3.nextSibling instanceof Element && j(t3.nextSibling, n3)) && (r3 = r3.replace(/ $/, "")), r3 = r3.replaceAll(" ", " ");
            }
            return e3.insert(r3);
          }], [Node.TEXT_NODE, M], ["br", function(t3, e3) {
            return T(e3, "\n") || e3.insert("\n"), e3;
          }], [Node.ELEMENT_NODE, M], [Node.ELEMENT_NODE, function(t3, e3, n3) {
            const i2 = n3.query(t3);
            if (null == i2) return e3;
            if (i2.prototype instanceof r2.EmbedBlot) {
              const e4 = {}, r3 = i2.value(t3);
              if (null != r3) return e4[i2.blotName] = r3, new (s())().insert(e4, i2.formats(t3, n3));
            } else if (i2.prototype instanceof r2.BlockBlot && !T(e3, "\n") && e3.insert("\n"), "blotName" in i2 && "formats" in i2 && "function" == typeof i2.formats) return O(e3, i2.blotName, i2.formats(t3, n3), n3);
            return e3;
          }], [Node.ELEMENT_NODE, function(t3, e3, n3) {
            const i2 = r2.Attributor.keys(t3), s2 = r2.ClassAttributor.keys(t3), o2 = r2.StyleAttributor.keys(t3), l2 = {};
            return i2.concat(s2).concat(o2).forEach((e4) => {
              let i3 = n3.query(e4, r2.Scope.ATTRIBUTE);
              null != i3 && (l2[i3.attrName] = i3.value(t3), l2[i3.attrName]) || (i3 = _[e4], null == i3 || i3.attrName !== e4 && i3.keyName !== e4 || (l2[i3.attrName] = i3.value(t3) || void 0), i3 = L[e4], null == i3 || i3.attrName !== e4 && i3.keyName !== e4 || (i3 = L[e4], l2[i3.attrName] = i3.value(t3) || void 0));
            }), Object.entries(l2).reduce((t4, e4) => {
              let [r3, i3] = e4;
              return O(t4, r3, i3, n3);
            }, e3);
          }], [Node.ELEMENT_NODE, function(t3, e3, n3) {
            const r3 = {}, i2 = t3.style || {};
            return "italic" === i2.fontStyle && (r3.italic = true), "underline" === i2.textDecoration && (r3.underline = true), "line-through" === i2.textDecoration && (r3.strike = true), (i2.fontWeight?.startsWith("bold") || parseInt(i2.fontWeight, 10) >= 700) && (r3.bold = true), e3 = Object.entries(r3).reduce((t4, e4) => {
              let [r4, i3] = e4;
              return O(t4, r4, i3, n3);
            }, e3), parseFloat(i2.textIndent || 0) > 0 ? new (s())().insert("	").concat(e3) : e3;
          }], ["li", function(t3, e3, n3) {
            const r3 = n3.query(t3);
            if (null == r3 || "list" !== r3.blotName || !T(e3, "\n")) return e3;
            let i2 = -1, o2 = t3.parentNode;
            for (; null != o2; ) ["OL", "UL"].includes(o2.tagName) && (i2 += 1), o2 = o2.parentNode;
            return i2 <= 0 ? e3 : e3.reduce((t4, e4) => e4.insert ? e4.attributes && "number" == typeof e4.attributes.indent ? t4.push(e4) : t4.insert(e4.insert, __spreadValues({ indent: i2 }, e4.attributes || {})) : t4, new (s())());
          }], ["ol, ul", function(t3, e3, n3) {
            const r3 = t3;
            let i2 = "OL" === r3.tagName ? "ordered" : "bullet";
            const s2 = r3.getAttribute("data-checked");
            return s2 && (i2 = "true" === s2 ? "checked" : "unchecked"), O(e3, "list", i2, n3);
          }], ["pre", function(t3, e3, n3) {
            const r3 = n3.query("code-block");
            return O(e3, "code-block", !r3 || !("formats" in r3) || "function" != typeof r3.formats || r3.formats(t3, n3), n3);
          }], ["tr", function(t3, e3, n3) {
            const r3 = "TABLE" === t3.parentElement?.tagName ? t3.parentElement : t3.parentElement?.parentElement;
            return null != r3 ? O(e3, "table", Array.from(r3.querySelectorAll("tr")).indexOf(t3) + 1, n3) : e3;
          }], ["b", B("bold")], ["i", B("italic")], ["strike", B("strike")], ["style", function() {
            return new (s())();
          }]], _ = [u.gS, p.Mc].reduce((t3, e3) => (t3[e3.keyName] = e3, t3), {}), L = [u.Hu, h.s, f.JM, p.VL, g.z, m.r].reduce((t3, e3) => (t3[e3.keyName] = e3, t3), {});
          class S extends a.A {
            static DEFAULTS = { matchers: [] };
            constructor(t3, e3) {
              super(t3, e3), this.quill.root.addEventListener("copy", (t4) => this.onCaptureCopy(t4, false)), this.quill.root.addEventListener("cut", (t4) => this.onCaptureCopy(t4, true)), this.quill.root.addEventListener("paste", this.onCapturePaste.bind(this)), this.matchers = [], k.concat(this.options.matchers ?? []).forEach((t4) => {
                let [e4, n3] = t4;
                this.addMatcher(e4, n3);
              });
            }
            addMatcher(t3, e3) {
              this.matchers.push([t3, e3]);
            }
            convert(t3) {
              let { html: e3, text: n3 } = t3, r3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
              if (r3[d.Ay.blotName]) return new (s())().insert(n3 || "", { [d.Ay.blotName]: r3[d.Ay.blotName] });
              if (!e3) return new (s())().insert(n3 || "", r3);
              const i2 = this.convertHTML(e3);
              return T(i2, "\n") && (null == i2.ops[i2.ops.length - 1].attributes || r3.table) ? i2.compose(new (s())().retain(i2.length() - 1).delete(1)) : i2;
            }
            normalizeHTML(t3) {
              ((t4) => {
                t4.documentElement && w.forEach((e3) => {
                  e3(t4);
                });
              })(t3);
            }
            convertHTML(t3) {
              const e3 = new DOMParser().parseFromString(t3, "text/html");
              this.normalizeHTML(e3);
              const n3 = e3.body, r3 = /* @__PURE__ */ new WeakMap(), [i2, s2] = this.prepareMatching(n3, r3);
              return I(this.quill.scroll, n3, i2, s2, r3);
            }
            dangerouslyPasteHTML(t3, e3) {
              let n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : c.Ay.sources.API;
              if ("string" == typeof t3) {
                const n4 = this.convert({ html: t3, text: "" });
                this.quill.setContents(n4, e3), this.quill.setSelection(0, c.Ay.sources.SILENT);
              } else {
                const r3 = this.convert({ html: e3, text: "" });
                this.quill.updateContents(new (s())().retain(t3).concat(r3), n3), this.quill.setSelection(t3 + r3.length(), c.Ay.sources.SILENT);
              }
            }
            onCaptureCopy(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              if (t3.defaultPrevented) return;
              t3.preventDefault();
              const [n3] = this.quill.selection.getRange();
              if (null == n3) return;
              const { html: r3, text: i2 } = this.onCopy(n3, e3);
              t3.clipboardData?.setData("text/plain", i2), t3.clipboardData?.setData("text/html", r3), e3 && (0, b.Xo)({ range: n3, quill: this.quill });
            }
            normalizeURIList(t3) {
              return t3.split(/\r?\n/).filter((t4) => "#" !== t4[0]).join("\n");
            }
            onCapturePaste(t3) {
              if (t3.defaultPrevented || !this.quill.isEnabled()) return;
              t3.preventDefault();
              const e3 = this.quill.getSelection(true);
              if (null == e3) return;
              const n3 = t3.clipboardData?.getData("text/html");
              let r3 = t3.clipboardData?.getData("text/plain");
              if (!n3 && !r3) {
                const e4 = t3.clipboardData?.getData("text/uri-list");
                e4 && (r3 = this.normalizeURIList(e4));
              }
              const i2 = Array.from(t3.clipboardData?.files || []);
              if (!n3 && i2.length > 0) this.quill.uploader.upload(e3, i2);
              else {
                if (n3 && i2.length > 0) {
                  const t4 = new DOMParser().parseFromString(n3, "text/html");
                  if (1 === t4.body.childElementCount && "IMG" === t4.body.firstElementChild?.tagName) return void this.quill.uploader.upload(e3, i2);
                }
                this.onPaste(e3, { html: n3, text: r3 });
              }
            }
            onCopy(t3) {
              const e3 = this.quill.getText(t3);
              return { html: this.quill.getSemanticHTML(t3), text: e3 };
            }
            onPaste(t3, e3) {
              let { text: n3, html: r3 } = e3;
              const i2 = this.quill.getFormat(t3.index), o2 = this.convert({ text: n3, html: r3 }, i2);
              q.log("onPaste", o2, { text: n3, html: r3 });
              const l2 = new (s())().retain(t3.index).delete(t3.length).concat(o2);
              this.quill.updateContents(l2, c.Ay.sources.USER), this.quill.setSelection(l2.length() - t3.length, c.Ay.sources.SILENT), this.quill.scrollSelectionIntoView();
            }
            prepareMatching(t3, e3) {
              const n3 = [], r3 = [];
              return this.matchers.forEach((i2) => {
                const [s2, o2] = i2;
                switch (s2) {
                  case Node.TEXT_NODE:
                    r3.push(o2);
                    break;
                  case Node.ELEMENT_NODE:
                    n3.push(o2);
                    break;
                  default:
                    Array.from(t3.querySelectorAll(s2)).forEach((t4) => {
                      if (e3.has(t4)) {
                        const n4 = e3.get(t4);
                        n4?.push(o2);
                      } else e3.set(t4, [o2]);
                    });
                }
              }), [n3, r3];
            }
          }
          function O(t3, e3, n3, r3) {
            return r3.query(e3) ? t3.reduce((t4, r4) => {
              if (!r4.insert) return t4;
              if (r4.attributes && r4.attributes[e3]) return t4.push(r4);
              const i2 = n3 ? { [e3]: n3 } : {};
              return t4.insert(r4.insert, __spreadValues(__spreadValues({}, i2), r4.attributes));
            }, new (s())()) : t3;
          }
          function T(t3, e3) {
            let n3 = "";
            for (let r3 = t3.ops.length - 1; r3 >= 0 && n3.length < e3.length; --r3) {
              const e4 = t3.ops[r3];
              if ("string" != typeof e4.insert) break;
              n3 = e4.insert + n3;
            }
            return n3.slice(-1 * e3.length) === e3;
          }
          function j(t3, e3) {
            if (!(t3 instanceof Element)) return false;
            const n3 = e3.query(t3);
            return !(n3 && n3.prototype instanceof r2.EmbedBlot) && ["address", "article", "blockquote", "canvas", "dd", "div", "dl", "dt", "fieldset", "figcaption", "figure", "footer", "form", "h1", "h2", "h3", "h4", "h5", "h6", "header", "iframe", "li", "main", "nav", "ol", "output", "p", "pre", "section", "table", "td", "tr", "ul", "video"].includes(t3.tagName.toLowerCase());
          }
          const C = /* @__PURE__ */ new WeakMap();
          function R(t3) {
            return null != t3 && (C.has(t3) || ("PRE" === t3.tagName ? C.set(t3, true) : C.set(t3, R(t3.parentNode))), C.get(t3));
          }
          function I(t3, e3, n3, r3, i2) {
            return e3.nodeType === e3.TEXT_NODE ? r3.reduce((n4, r4) => r4(e3, n4, t3), new (s())()) : e3.nodeType === e3.ELEMENT_NODE ? Array.from(e3.childNodes || []).reduce((s2, o2) => {
              let l2 = I(t3, o2, n3, r3, i2);
              return o2.nodeType === e3.ELEMENT_NODE && (l2 = n3.reduce((e4, n4) => n4(o2, e4, t3), l2), l2 = (i2.get(o2) || []).reduce((e4, n4) => n4(o2, e4, t3), l2)), s2.concat(l2);
            }, new (s())()) : new (s())();
          }
          function B(t3) {
            return (e3, n3, r3) => O(n3, t3, true, r3);
          }
          function M(t3, e3, n3) {
            if (!T(e3, "\n")) {
              if (j(t3, n3) && (t3.childNodes.length > 0 || t3 instanceof HTMLParagraphElement)) return e3.insert("\n");
              if (e3.length() > 0 && t3.nextSibling) {
                let r3 = t3.nextSibling;
                for (; null != r3; ) {
                  if (j(r3, n3)) return e3.insert("\n");
                  const t4 = n3.query(r3);
                  if (t4 && t4.prototype instanceof o.zo) return e3.insert("\n");
                  r3 = r3.firstChild;
                }
              }
            }
            return e3;
          }
        }, 8123: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { Ay: function() {
            return f;
          }, Xo: function() {
            return v;
          } });
          var r2 = n2(5123), i = n2(3707), s = n2(5232), o = n2.n(s), l = n2(6003), a = n2(6142), c = n2(6078), u = n2(4266);
          const h = (0, c.A)("quill:keyboard"), d = /Mac/i.test(navigator.platform) ? "metaKey" : "ctrlKey";
          class f extends u.A {
            static match(t3, e3) {
              return !["altKey", "ctrlKey", "metaKey", "shiftKey"].some((n3) => !!e3[n3] !== t3[n3] && null !== e3[n3]) && (e3.key === t3.key || e3.key === t3.which);
            }
            constructor(t3, e3) {
              super(t3, e3), this.bindings = {}, Object.keys(this.options.bindings).forEach((t4) => {
                this.options.bindings[t4] && this.addBinding(this.options.bindings[t4]);
              }), this.addBinding({ key: "Enter", shiftKey: null }, this.handleEnter), this.addBinding({ key: "Enter", metaKey: null, ctrlKey: null, altKey: null }, () => {
              }), /Firefox/i.test(navigator.userAgent) ? (this.addBinding({ key: "Backspace" }, { collapsed: true }, this.handleBackspace), this.addBinding({ key: "Delete" }, { collapsed: true }, this.handleDelete)) : (this.addBinding({ key: "Backspace" }, { collapsed: true, prefix: /^.?$/ }, this.handleBackspace), this.addBinding({ key: "Delete" }, { collapsed: true, suffix: /^.?$/ }, this.handleDelete)), this.addBinding({ key: "Backspace" }, { collapsed: false }, this.handleDeleteRange), this.addBinding({ key: "Delete" }, { collapsed: false }, this.handleDeleteRange), this.addBinding({ key: "Backspace", altKey: null, ctrlKey: null, metaKey: null, shiftKey: null }, { collapsed: true, offset: 0 }, this.handleBackspace), this.listen();
            }
            addBinding(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, n3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
              const r3 = function(t4) {
                if ("string" == typeof t4 || "number" == typeof t4) t4 = { key: t4 };
                else {
                  if ("object" != typeof t4) return null;
                  t4 = (0, i.A)(t4);
                }
                return t4.shortKey && (t4[d] = t4.shortKey, delete t4.shortKey), t4;
              }(t3);
              null != r3 ? ("function" == typeof e3 && (e3 = { handler: e3 }), "function" == typeof n3 && (n3 = { handler: n3 }), (Array.isArray(r3.key) ? r3.key : [r3.key]).forEach((t4) => {
                const i2 = __spreadValues(__spreadValues(__spreadProps(__spreadValues({}, r3), { key: t4 }), e3), n3);
                this.bindings[i2.key] = this.bindings[i2.key] || [], this.bindings[i2.key].push(i2);
              })) : h.warn("Attempted to add invalid keyboard binding", r3);
            }
            listen() {
              this.quill.root.addEventListener("keydown", (t3) => {
                if (t3.defaultPrevented || t3.isComposing) return;
                if (229 === t3.keyCode && ("Enter" === t3.key || "Backspace" === t3.key)) return;
                const e3 = (this.bindings[t3.key] || []).concat(this.bindings[t3.which] || []).filter((e4) => f.match(t3, e4));
                if (0 === e3.length) return;
                const n3 = a.Ay.find(t3.target, true);
                if (n3 && n3.scroll !== this.quill.scroll) return;
                const i2 = this.quill.getSelection();
                if (null == i2 || !this.quill.hasFocus()) return;
                const [s2, o2] = this.quill.getLine(i2.index), [c2, u2] = this.quill.getLeaf(i2.index), [h2, d2] = 0 === i2.length ? [c2, u2] : this.quill.getLeaf(i2.index + i2.length), p2 = c2 instanceof l.TextBlot ? c2.value().slice(0, u2) : "", g2 = h2 instanceof l.TextBlot ? h2.value().slice(d2) : "", m2 = { collapsed: 0 === i2.length, empty: 0 === i2.length && s2.length() <= 1, format: this.quill.getFormat(i2), line: s2, offset: o2, prefix: p2, suffix: g2, event: t3 };
                e3.some((t4) => {
                  if (null != t4.collapsed && t4.collapsed !== m2.collapsed) return false;
                  if (null != t4.empty && t4.empty !== m2.empty) return false;
                  if (null != t4.offset && t4.offset !== m2.offset) return false;
                  if (Array.isArray(t4.format)) {
                    if (t4.format.every((t5) => null == m2.format[t5])) return false;
                  } else if ("object" == typeof t4.format && !Object.keys(t4.format).every((e4) => true === t4.format[e4] ? null != m2.format[e4] : false === t4.format[e4] ? null == m2.format[e4] : (0, r2.A)(t4.format[e4], m2.format[e4]))) return false;
                  return !(null != t4.prefix && !t4.prefix.test(m2.prefix) || null != t4.suffix && !t4.suffix.test(m2.suffix) || true === t4.handler.call(this, i2, m2, t4));
                }) && t3.preventDefault();
              });
            }
            handleBackspace(t3, e3) {
              const n3 = /[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e3.prefix) ? 2 : 1;
              if (0 === t3.index || this.quill.getLength() <= 1) return;
              let r3 = {};
              const [i2] = this.quill.getLine(t3.index);
              let l2 = new (o())().retain(t3.index - n3).delete(n3);
              if (0 === e3.offset) {
                const [e4] = this.quill.getLine(t3.index - 1);
                if (e4 && !("block" === e4.statics.blotName && e4.length() <= 1)) {
                  const e5 = i2.formats(), n4 = this.quill.getFormat(t3.index - 1, 1);
                  if (r3 = s.AttributeMap.diff(e5, n4) || {}, Object.keys(r3).length > 0) {
                    const e6 = new (o())().retain(t3.index + i2.length() - 2).retain(1, r3);
                    l2 = l2.compose(e6);
                  }
                }
              }
              this.quill.updateContents(l2, a.Ay.sources.USER), this.quill.focus();
            }
            handleDelete(t3, e3) {
              const n3 = /^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e3.suffix) ? 2 : 1;
              if (t3.index >= this.quill.getLength() - n3) return;
              let r3 = {};
              const [i2] = this.quill.getLine(t3.index);
              let l2 = new (o())().retain(t3.index).delete(n3);
              if (e3.offset >= i2.length() - 1) {
                const [e4] = this.quill.getLine(t3.index + 1);
                if (e4) {
                  const n4 = i2.formats(), o2 = this.quill.getFormat(t3.index, 1);
                  r3 = s.AttributeMap.diff(n4, o2) || {}, Object.keys(r3).length > 0 && (l2 = l2.retain(e4.length() - 1).retain(1, r3));
                }
              }
              this.quill.updateContents(l2, a.Ay.sources.USER), this.quill.focus();
            }
            handleDeleteRange(t3) {
              v({ range: t3, quill: this.quill }), this.quill.focus();
            }
            handleEnter(t3, e3) {
              const n3 = Object.keys(e3.format).reduce((t4, n4) => (this.quill.scroll.query(n4, l.Scope.BLOCK) && !Array.isArray(e3.format[n4]) && (t4[n4] = e3.format[n4]), t4), {}), r3 = new (o())().retain(t3.index).delete(t3.length).insert("\n", n3);
              this.quill.updateContents(r3, a.Ay.sources.USER), this.quill.setSelection(t3.index + 1, a.Ay.sources.SILENT), this.quill.focus();
            }
          }
          const p = { bindings: { bold: b("bold"), italic: b("italic"), underline: b("underline"), indent: { key: "Tab", format: ["blockquote", "indent", "list"], handler(t3, e3) {
            return !(!e3.collapsed || 0 === e3.offset) || (this.quill.format("indent", "+1", a.Ay.sources.USER), false);
          } }, outdent: { key: "Tab", shiftKey: true, format: ["blockquote", "indent", "list"], handler(t3, e3) {
            return !(!e3.collapsed || 0 === e3.offset) || (this.quill.format("indent", "-1", a.Ay.sources.USER), false);
          } }, "outdent backspace": { key: "Backspace", collapsed: true, shiftKey: null, metaKey: null, ctrlKey: null, altKey: null, format: ["indent", "list"], offset: 0, handler(t3, e3) {
            null != e3.format.indent ? this.quill.format("indent", "-1", a.Ay.sources.USER) : null != e3.format.list && this.quill.format("list", false, a.Ay.sources.USER);
          } }, "indent code-block": g(true), "outdent code-block": g(false), "remove tab": { key: "Tab", shiftKey: true, collapsed: true, prefix: /\t$/, handler(t3) {
            this.quill.deleteText(t3.index - 1, 1, a.Ay.sources.USER);
          } }, tab: { key: "Tab", handler(t3, e3) {
            if (e3.format.table) return true;
            this.quill.history.cutoff();
            const n3 = new (o())().retain(t3.index).delete(t3.length).insert("	");
            return this.quill.updateContents(n3, a.Ay.sources.USER), this.quill.history.cutoff(), this.quill.setSelection(t3.index + 1, a.Ay.sources.SILENT), false;
          } }, "blockquote empty enter": { key: "Enter", collapsed: true, format: ["blockquote"], empty: true, handler() {
            this.quill.format("blockquote", false, a.Ay.sources.USER);
          } }, "list empty enter": { key: "Enter", collapsed: true, format: ["list"], empty: true, handler(t3, e3) {
            const n3 = { list: false };
            e3.format.indent && (n3.indent = false), this.quill.formatLine(t3.index, t3.length, n3, a.Ay.sources.USER);
          } }, "checklist enter": { key: "Enter", collapsed: true, format: { list: "checked" }, handler(t3) {
            const [e3, n3] = this.quill.getLine(t3.index), r3 = __spreadProps(__spreadValues({}, e3.formats()), { list: "checked" }), i2 = new (o())().retain(t3.index).insert("\n", r3).retain(e3.length() - n3 - 1).retain(1, { list: "unchecked" });
            this.quill.updateContents(i2, a.Ay.sources.USER), this.quill.setSelection(t3.index + 1, a.Ay.sources.SILENT), this.quill.scrollSelectionIntoView();
          } }, "header enter": { key: "Enter", collapsed: true, format: ["header"], suffix: /^$/, handler(t3, e3) {
            const [n3, r3] = this.quill.getLine(t3.index), i2 = new (o())().retain(t3.index).insert("\n", e3.format).retain(n3.length() - r3 - 1).retain(1, { header: null });
            this.quill.updateContents(i2, a.Ay.sources.USER), this.quill.setSelection(t3.index + 1, a.Ay.sources.SILENT), this.quill.scrollSelectionIntoView();
          } }, "table backspace": { key: "Backspace", format: ["table"], collapsed: true, offset: 0, handler() {
          } }, "table delete": { key: "Delete", format: ["table"], collapsed: true, suffix: /^$/, handler() {
          } }, "table enter": { key: "Enter", shiftKey: null, format: ["table"], handler(t3) {
            const e3 = this.quill.getModule("table");
            if (e3) {
              const [n3, r3, i2, s2] = e3.getTable(t3), l2 = function(t4, e4, n4, r4) {
                return null == e4.prev && null == e4.next ? null == n4.prev && null == n4.next ? 0 === r4 ? -1 : 1 : null == n4.prev ? -1 : 1 : null == e4.prev ? -1 : null == e4.next ? 1 : null;
              }(0, r3, i2, s2);
              if (null == l2) return;
              let c2 = n3.offset();
              if (l2 < 0) {
                const e4 = new (o())().retain(c2).insert("\n");
                this.quill.updateContents(e4, a.Ay.sources.USER), this.quill.setSelection(t3.index + 1, t3.length, a.Ay.sources.SILENT);
              } else if (l2 > 0) {
                c2 += n3.length();
                const t4 = new (o())().retain(c2).insert("\n");
                this.quill.updateContents(t4, a.Ay.sources.USER), this.quill.setSelection(c2, a.Ay.sources.USER);
              }
            }
          } }, "table tab": { key: "Tab", shiftKey: null, format: ["table"], handler(t3, e3) {
            const { event: n3, line: r3 } = e3, i2 = r3.offset(this.quill.scroll);
            n3.shiftKey ? this.quill.setSelection(i2 - 1, a.Ay.sources.USER) : this.quill.setSelection(i2 + r3.length(), a.Ay.sources.USER);
          } }, "list autofill": { key: " ", shiftKey: null, collapsed: true, format: { "code-block": false, blockquote: false, table: false }, prefix: /^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/, handler(t3, e3) {
            if (null == this.quill.scroll.query("list")) return true;
            const { length: n3 } = e3.prefix, [r3, i2] = this.quill.getLine(t3.index);
            if (i2 > n3) return true;
            let s2;
            switch (e3.prefix.trim()) {
              case "[]":
              case "[ ]":
                s2 = "unchecked";
                break;
              case "[x]":
                s2 = "checked";
                break;
              case "-":
              case "*":
                s2 = "bullet";
                break;
              default:
                s2 = "ordered";
            }
            this.quill.insertText(t3.index, " ", a.Ay.sources.USER), this.quill.history.cutoff();
            const l2 = new (o())().retain(t3.index - i2).delete(n3 + 1).retain(r3.length() - 2 - i2).retain(1, { list: s2 });
            return this.quill.updateContents(l2, a.Ay.sources.USER), this.quill.history.cutoff(), this.quill.setSelection(t3.index - n3, a.Ay.sources.SILENT), false;
          } }, "code exit": { key: "Enter", collapsed: true, format: ["code-block"], prefix: /^$/, suffix: /^\s*$/, handler(t3) {
            const [e3, n3] = this.quill.getLine(t3.index);
            let r3 = 2, i2 = e3;
            for (; null != i2 && i2.length() <= 1 && i2.formats()["code-block"]; ) if (i2 = i2.prev, r3 -= 1, r3 <= 0) {
              const r4 = new (o())().retain(t3.index + e3.length() - n3 - 2).retain(1, { "code-block": null }).delete(1);
              return this.quill.updateContents(r4, a.Ay.sources.USER), this.quill.setSelection(t3.index - 1, a.Ay.sources.SILENT), false;
            }
            return true;
          } }, "embed left": m("ArrowLeft", false), "embed left shift": m("ArrowLeft", true), "embed right": m("ArrowRight", false), "embed right shift": m("ArrowRight", true), "table down": y(false), "table up": y(true) } };
          function g(t3) {
            return { key: "Tab", shiftKey: !t3, format: { "code-block": true }, handler(e3, n3) {
              let { event: r3 } = n3;
              const i2 = this.quill.scroll.query("code-block"), { TAB: s2 } = i2;
              if (0 === e3.length && !r3.shiftKey) return this.quill.insertText(e3.index, s2, a.Ay.sources.USER), void this.quill.setSelection(e3.index + s2.length, a.Ay.sources.SILENT);
              const o2 = 0 === e3.length ? this.quill.getLines(e3.index, 1) : this.quill.getLines(e3);
              let { index: l2, length: c2 } = e3;
              o2.forEach((e4, n4) => {
                t3 ? (e4.insertAt(0, s2), 0 === n4 ? l2 += s2.length : c2 += s2.length) : e4.domNode.textContent.startsWith(s2) && (e4.deleteAt(0, s2.length), 0 === n4 ? l2 -= s2.length : c2 -= s2.length);
              }), this.quill.update(a.Ay.sources.USER), this.quill.setSelection(l2, c2, a.Ay.sources.SILENT);
            } };
          }
          function m(t3, e3) {
            return { key: t3, shiftKey: e3, altKey: null, ["ArrowLeft" === t3 ? "prefix" : "suffix"]: /^$/, handler(n3) {
              let { index: r3 } = n3;
              "ArrowRight" === t3 && (r3 += n3.length + 1);
              const [i2] = this.quill.getLeaf(r3);
              return !(i2 instanceof l.EmbedBlot && ("ArrowLeft" === t3 ? e3 ? this.quill.setSelection(n3.index - 1, n3.length + 1, a.Ay.sources.USER) : this.quill.setSelection(n3.index - 1, a.Ay.sources.USER) : e3 ? this.quill.setSelection(n3.index, n3.length + 1, a.Ay.sources.USER) : this.quill.setSelection(n3.index + n3.length + 1, a.Ay.sources.USER), 1));
            } };
          }
          function b(t3) {
            return { key: t3[0], shortKey: true, handler(e3, n3) {
              this.quill.format(t3, !n3.format[t3], a.Ay.sources.USER);
            } };
          }
          function y(t3) {
            return { key: t3 ? "ArrowUp" : "ArrowDown", collapsed: true, format: ["table"], handler(e3, n3) {
              const r3 = t3 ? "prev" : "next", i2 = n3.line, s2 = i2.parent[r3];
              if (null != s2) {
                if ("table-row" === s2.statics.blotName) {
                  let t4 = s2.children.head, e4 = i2;
                  for (; null != e4.prev; ) e4 = e4.prev, t4 = t4.next;
                  const r4 = t4.offset(this.quill.scroll) + Math.min(n3.offset, t4.length() - 1);
                  this.quill.setSelection(r4, 0, a.Ay.sources.USER);
                }
              } else {
                const e4 = i2.table()[r3];
                null != e4 && (t3 ? this.quill.setSelection(e4.offset(this.quill.scroll) + e4.length() - 1, 0, a.Ay.sources.USER) : this.quill.setSelection(e4.offset(this.quill.scroll), 0, a.Ay.sources.USER));
              }
              return false;
            } };
          }
          function v(t3) {
            let { quill: e3, range: n3 } = t3;
            const r3 = e3.getLines(n3);
            let i2 = {};
            if (r3.length > 1) {
              const t4 = r3[0].formats(), e4 = r3[r3.length - 1].formats();
              i2 = s.AttributeMap.diff(e4, t4) || {};
            }
            e3.deleteText(n3, a.Ay.sources.USER), Object.keys(i2).length > 0 && e3.formatLine(n3.index, 1, i2, a.Ay.sources.USER), e3.setSelection(n3.index, a.Ay.sources.SILENT);
          }
          f.DEFAULTS = p;
        }, 8920: function(t2) {
          "use strict";
          var e2 = Object.prototype.hasOwnProperty, n2 = "~";
          function r2() {
          }
          function i(t3, e3, n3) {
            this.fn = t3, this.context = e3, this.once = n3 || false;
          }
          function s(t3, e3, r3, s2, o2) {
            if ("function" != typeof r3) throw new TypeError("The listener must be a function");
            var l2 = new i(r3, s2 || t3, o2), a = n2 ? n2 + e3 : e3;
            return t3._events[a] ? t3._events[a].fn ? t3._events[a] = [t3._events[a], l2] : t3._events[a].push(l2) : (t3._events[a] = l2, t3._eventsCount++), t3;
          }
          function o(t3, e3) {
            0 == --t3._eventsCount ? t3._events = new r2() : delete t3._events[e3];
          }
          function l() {
            this._events = new r2(), this._eventsCount = 0;
          }
          Object.create && (r2.prototype = /* @__PURE__ */ Object.create(null), new r2().__proto__ || (n2 = false)), l.prototype.eventNames = function() {
            var t3, r3, i2 = [];
            if (0 === this._eventsCount) return i2;
            for (r3 in t3 = this._events) e2.call(t3, r3) && i2.push(n2 ? r3.slice(1) : r3);
            return Object.getOwnPropertySymbols ? i2.concat(Object.getOwnPropertySymbols(t3)) : i2;
          }, l.prototype.listeners = function(t3) {
            var e3 = n2 ? n2 + t3 : t3, r3 = this._events[e3];
            if (!r3) return [];
            if (r3.fn) return [r3.fn];
            for (var i2 = 0, s2 = r3.length, o2 = new Array(s2); i2 < s2; i2++) o2[i2] = r3[i2].fn;
            return o2;
          }, l.prototype.listenerCount = function(t3) {
            var e3 = n2 ? n2 + t3 : t3, r3 = this._events[e3];
            return r3 ? r3.fn ? 1 : r3.length : 0;
          }, l.prototype.emit = function(t3, e3, r3, i2, s2, o2) {
            var l2 = n2 ? n2 + t3 : t3;
            if (!this._events[l2]) return false;
            var a, c, u = this._events[l2], h = arguments.length;
            if (u.fn) {
              switch (u.once && this.removeListener(t3, u.fn, void 0, true), h) {
                case 1:
                  return u.fn.call(u.context), true;
                case 2:
                  return u.fn.call(u.context, e3), true;
                case 3:
                  return u.fn.call(u.context, e3, r3), true;
                case 4:
                  return u.fn.call(u.context, e3, r3, i2), true;
                case 5:
                  return u.fn.call(u.context, e3, r3, i2, s2), true;
                case 6:
                  return u.fn.call(u.context, e3, r3, i2, s2, o2), true;
              }
              for (c = 1, a = new Array(h - 1); c < h; c++) a[c - 1] = arguments[c];
              u.fn.apply(u.context, a);
            } else {
              var d, f = u.length;
              for (c = 0; c < f; c++) switch (u[c].once && this.removeListener(t3, u[c].fn, void 0, true), h) {
                case 1:
                  u[c].fn.call(u[c].context);
                  break;
                case 2:
                  u[c].fn.call(u[c].context, e3);
                  break;
                case 3:
                  u[c].fn.call(u[c].context, e3, r3);
                  break;
                case 4:
                  u[c].fn.call(u[c].context, e3, r3, i2);
                  break;
                default:
                  if (!a) for (d = 1, a = new Array(h - 1); d < h; d++) a[d - 1] = arguments[d];
                  u[c].fn.apply(u[c].context, a);
              }
            }
            return true;
          }, l.prototype.on = function(t3, e3, n3) {
            return s(this, t3, e3, n3, false);
          }, l.prototype.once = function(t3, e3, n3) {
            return s(this, t3, e3, n3, true);
          }, l.prototype.removeListener = function(t3, e3, r3, i2) {
            var s2 = n2 ? n2 + t3 : t3;
            if (!this._events[s2]) return this;
            if (!e3) return o(this, s2), this;
            var l2 = this._events[s2];
            if (l2.fn) l2.fn !== e3 || i2 && !l2.once || r3 && l2.context !== r3 || o(this, s2);
            else {
              for (var a = 0, c = [], u = l2.length; a < u; a++) (l2[a].fn !== e3 || i2 && !l2[a].once || r3 && l2[a].context !== r3) && c.push(l2[a]);
              c.length ? this._events[s2] = 1 === c.length ? c[0] : c : o(this, s2);
            }
            return this;
          }, l.prototype.removeAllListeners = function(t3) {
            var e3;
            return t3 ? (e3 = n2 ? n2 + t3 : t3, this._events[e3] && o(this, e3)) : (this._events = new r2(), this._eventsCount = 0), this;
          }, l.prototype.off = l.prototype.removeListener, l.prototype.addListener = l.prototype.on, l.prefixed = n2, l.EventEmitter = l, t2.exports = l;
        }, 5090: function(t2) {
          var e2 = -1, n2 = 1, r2 = 0;
          function i(t3, g2, m2, b2, y2) {
            if (t3 === g2) return t3 ? [[r2, t3]] : [];
            if (null != m2) {
              var A2 = function(t4, e3, n3) {
                var r3 = "number" == typeof n3 ? { index: n3, length: 0 } : n3.oldRange, i2 = "number" == typeof n3 ? null : n3.newRange, s2 = t4.length, o2 = e3.length;
                if (0 === r3.length && (null === i2 || 0 === i2.length)) {
                  var l2 = r3.index, a2 = t4.slice(0, l2), c2 = t4.slice(l2), u2 = i2 ? i2.index : null, h2 = l2 + o2 - s2;
                  if ((null === u2 || u2 === h2) && !(h2 < 0 || h2 > o2)) {
                    var d2 = e3.slice(0, h2);
                    if ((g3 = e3.slice(h2)) === c2) {
                      var f2 = Math.min(l2, h2);
                      if ((b3 = a2.slice(0, f2)) === (A3 = d2.slice(0, f2))) return v(b3, a2.slice(f2), d2.slice(f2), c2);
                    }
                  }
                  if (null === u2 || u2 === l2) {
                    var p2 = l2, g3 = (d2 = e3.slice(0, p2), e3.slice(p2));
                    if (d2 === a2) {
                      var m3 = Math.min(s2 - p2, o2 - p2);
                      if ((y3 = c2.slice(c2.length - m3)) === (x2 = g3.slice(g3.length - m3))) return v(a2, c2.slice(0, c2.length - m3), g3.slice(0, g3.length - m3), y3);
                    }
                  }
                }
                if (r3.length > 0 && i2 && 0 === i2.length) {
                  var b3 = t4.slice(0, r3.index), y3 = t4.slice(r3.index + r3.length);
                  if (!(o2 < (f2 = b3.length) + (m3 = y3.length))) {
                    var A3 = e3.slice(0, f2), x2 = e3.slice(o2 - m3);
                    if (b3 === A3 && y3 === x2) return v(b3, t4.slice(f2, s2 - m3), e3.slice(f2, o2 - m3), y3);
                  }
                }
                return null;
              }(t3, g2, m2);
              if (A2) return A2;
            }
            var x = o(t3, g2), N = t3.substring(0, x);
            x = a(t3 = t3.substring(x), g2 = g2.substring(x));
            var E = t3.substring(t3.length - x), w = function(t4, l2) {
              var c2;
              if (!t4) return [[n2, l2]];
              if (!l2) return [[e2, t4]];
              var u2 = t4.length > l2.length ? t4 : l2, h2 = t4.length > l2.length ? l2 : t4, d2 = u2.indexOf(h2);
              if (-1 !== d2) return c2 = [[n2, u2.substring(0, d2)], [r2, h2], [n2, u2.substring(d2 + h2.length)]], t4.length > l2.length && (c2[0][0] = c2[2][0] = e2), c2;
              if (1 === h2.length) return [[e2, t4], [n2, l2]];
              var f2 = function(t5, e3) {
                var n3 = t5.length > e3.length ? t5 : e3, r3 = t5.length > e3.length ? e3 : t5;
                if (n3.length < 4 || 2 * r3.length < n3.length) return null;
                function i2(t6, e4, n4) {
                  for (var r4, i3, s3, l4, c4 = t6.substring(n4, n4 + Math.floor(t6.length / 4)), u4 = -1, h4 = ""; -1 !== (u4 = e4.indexOf(c4, u4 + 1)); ) {
                    var d4 = o(t6.substring(n4), e4.substring(u4)), f4 = a(t6.substring(0, n4), e4.substring(0, u4));
                    h4.length < f4 + d4 && (h4 = e4.substring(u4 - f4, u4) + e4.substring(u4, u4 + d4), r4 = t6.substring(0, n4 - f4), i3 = t6.substring(n4 + d4), s3 = e4.substring(0, u4 - f4), l4 = e4.substring(u4 + d4));
                  }
                  return 2 * h4.length >= t6.length ? [r4, i3, s3, l4, h4] : null;
                }
                var s2, l3, c3, u3, h3, d3 = i2(n3, r3, Math.ceil(n3.length / 4)), f3 = i2(n3, r3, Math.ceil(n3.length / 2));
                return d3 || f3 ? (s2 = f3 ? d3 && d3[4].length > f3[4].length ? d3 : f3 : d3, t5.length > e3.length ? (l3 = s2[0], c3 = s2[1], u3 = s2[2], h3 = s2[3]) : (u3 = s2[0], h3 = s2[1], l3 = s2[2], c3 = s2[3]), [l3, c3, u3, h3, s2[4]]) : null;
              }(t4, l2);
              if (f2) {
                var p2 = f2[0], g3 = f2[1], m3 = f2[2], b3 = f2[3], y3 = f2[4], v2 = i(p2, m3), A3 = i(g3, b3);
                return v2.concat([[r2, y3]], A3);
              }
              return function(t5, r3) {
                for (var i2 = t5.length, o2 = r3.length, l3 = Math.ceil((i2 + o2) / 2), a2 = l3, c3 = 2 * l3, u3 = new Array(c3), h3 = new Array(c3), d3 = 0; d3 < c3; d3++) u3[d3] = -1, h3[d3] = -1;
                u3[a2 + 1] = 0, h3[a2 + 1] = 0;
                for (var f3 = i2 - o2, p3 = f3 % 2 != 0, g4 = 0, m4 = 0, b4 = 0, y4 = 0, v3 = 0; v3 < l3; v3++) {
                  for (var A4 = -v3 + g4; A4 <= v3 - m4; A4 += 2) {
                    for (var x2 = a2 + A4, N2 = (_ = A4 === -v3 || A4 !== v3 && u3[x2 - 1] < u3[x2 + 1] ? u3[x2 + 1] : u3[x2 - 1] + 1) - A4; _ < i2 && N2 < o2 && t5.charAt(_) === r3.charAt(N2); ) _++, N2++;
                    if (u3[x2] = _, _ > i2) m4 += 2;
                    else if (N2 > o2) g4 += 2;
                    else if (p3 && (q = a2 + f3 - A4) >= 0 && q < c3 && -1 !== h3[q] && _ >= (w2 = i2 - h3[q])) return s(t5, r3, _, N2);
                  }
                  for (var E2 = -v3 + b4; E2 <= v3 - y4; E2 += 2) {
                    for (var w2, q = a2 + E2, k = (w2 = E2 === -v3 || E2 !== v3 && h3[q - 1] < h3[q + 1] ? h3[q + 1] : h3[q - 1] + 1) - E2; w2 < i2 && k < o2 && t5.charAt(i2 - w2 - 1) === r3.charAt(o2 - k - 1); ) w2++, k++;
                    if (h3[q] = w2, w2 > i2) y4 += 2;
                    else if (k > o2) b4 += 2;
                    else if (!p3) {
                      var _;
                      if ((x2 = a2 + f3 - E2) >= 0 && x2 < c3 && -1 !== u3[x2]) {
                        if (N2 = a2 + (_ = u3[x2]) - x2, _ >= (w2 = i2 - w2)) return s(t5, r3, _, N2);
                      }
                    }
                  }
                }
                return [[e2, t5], [n2, r3]];
              }(t4, l2);
            }(t3 = t3.substring(0, t3.length - x), g2 = g2.substring(0, g2.length - x));
            return N && w.unshift([r2, N]), E && w.push([r2, E]), p(w, y2), b2 && function(t4) {
              for (var i2 = false, s2 = [], o2 = 0, g3 = null, m3 = 0, b3 = 0, y3 = 0, v2 = 0, A3 = 0; m3 < t4.length; ) t4[m3][0] == r2 ? (s2[o2++] = m3, b3 = v2, y3 = A3, v2 = 0, A3 = 0, g3 = t4[m3][1]) : (t4[m3][0] == n2 ? v2 += t4[m3][1].length : A3 += t4[m3][1].length, g3 && g3.length <= Math.max(b3, y3) && g3.length <= Math.max(v2, A3) && (t4.splice(s2[o2 - 1], 0, [e2, g3]), t4[s2[o2 - 1] + 1][0] = n2, o2--, m3 = --o2 > 0 ? s2[o2 - 1] : -1, b3 = 0, y3 = 0, v2 = 0, A3 = 0, g3 = null, i2 = true)), m3++;
              for (i2 && p(t4), function(t5) {
                function e3(t6, e4) {
                  if (!t6 || !e4) return 6;
                  var n4 = t6.charAt(t6.length - 1), r3 = e4.charAt(0), i4 = n4.match(c), s4 = r3.match(c), o4 = i4 && n4.match(u), l3 = s4 && r3.match(u), a2 = o4 && n4.match(h), p3 = l3 && r3.match(h), g5 = a2 && t6.match(d), m5 = p3 && e4.match(f);
                  return g5 || m5 ? 5 : a2 || p3 ? 4 : i4 && !o4 && l3 ? 3 : o4 || l3 ? 2 : i4 || s4 ? 1 : 0;
                }
                for (var n3 = 1; n3 < t5.length - 1; ) {
                  if (t5[n3 - 1][0] == r2 && t5[n3 + 1][0] == r2) {
                    var i3 = t5[n3 - 1][1], s3 = t5[n3][1], o3 = t5[n3 + 1][1], l2 = a(i3, s3);
                    if (l2) {
                      var p2 = s3.substring(s3.length - l2);
                      i3 = i3.substring(0, i3.length - l2), s3 = p2 + s3.substring(0, s3.length - l2), o3 = p2 + o3;
                    }
                    for (var g4 = i3, m4 = s3, b4 = o3, y4 = e3(i3, s3) + e3(s3, o3); s3.charAt(0) === o3.charAt(0); ) {
                      i3 += s3.charAt(0), s3 = s3.substring(1) + o3.charAt(0), o3 = o3.substring(1);
                      var v3 = e3(i3, s3) + e3(s3, o3);
                      v3 >= y4 && (y4 = v3, g4 = i3, m4 = s3, b4 = o3);
                    }
                    t5[n3 - 1][1] != g4 && (g4 ? t5[n3 - 1][1] = g4 : (t5.splice(n3 - 1, 1), n3--), t5[n3][1] = m4, b4 ? t5[n3 + 1][1] = b4 : (t5.splice(n3 + 1, 1), n3--));
                  }
                  n3++;
                }
              }(t4), m3 = 1; m3 < t4.length; ) {
                if (t4[m3 - 1][0] == e2 && t4[m3][0] == n2) {
                  var x2 = t4[m3 - 1][1], N2 = t4[m3][1], E2 = l(x2, N2), w2 = l(N2, x2);
                  E2 >= w2 ? (E2 >= x2.length / 2 || E2 >= N2.length / 2) && (t4.splice(m3, 0, [r2, N2.substring(0, E2)]), t4[m3 - 1][1] = x2.substring(0, x2.length - E2), t4[m3 + 1][1] = N2.substring(E2), m3++) : (w2 >= x2.length / 2 || w2 >= N2.length / 2) && (t4.splice(m3, 0, [r2, x2.substring(0, w2)]), t4[m3 - 1][0] = n2, t4[m3 - 1][1] = N2.substring(0, N2.length - w2), t4[m3 + 1][0] = e2, t4[m3 + 1][1] = x2.substring(w2), m3++), m3++;
                }
                m3++;
              }
            }(w), w;
          }
          function s(t3, e3, n3, r3) {
            var s2 = t3.substring(0, n3), o2 = e3.substring(0, r3), l2 = t3.substring(n3), a2 = e3.substring(r3), c2 = i(s2, o2), u2 = i(l2, a2);
            return c2.concat(u2);
          }
          function o(t3, e3) {
            if (!t3 || !e3 || t3.charAt(0) !== e3.charAt(0)) return 0;
            for (var n3 = 0, r3 = Math.min(t3.length, e3.length), i2 = r3, s2 = 0; n3 < i2; ) t3.substring(s2, i2) == e3.substring(s2, i2) ? s2 = n3 = i2 : r3 = i2, i2 = Math.floor((r3 - n3) / 2 + n3);
            return g(t3.charCodeAt(i2 - 1)) && i2--, i2;
          }
          function l(t3, e3) {
            var n3 = t3.length, r3 = e3.length;
            if (0 == n3 || 0 == r3) return 0;
            n3 > r3 ? t3 = t3.substring(n3 - r3) : n3 < r3 && (e3 = e3.substring(0, n3));
            var i2 = Math.min(n3, r3);
            if (t3 == e3) return i2;
            for (var s2 = 0, o2 = 1; ; ) {
              var l2 = t3.substring(i2 - o2), a2 = e3.indexOf(l2);
              if (-1 == a2) return s2;
              o2 += a2, 0 != a2 && t3.substring(i2 - o2) != e3.substring(0, o2) || (s2 = o2, o2++);
            }
          }
          function a(t3, e3) {
            if (!t3 || !e3 || t3.slice(-1) !== e3.slice(-1)) return 0;
            for (var n3 = 0, r3 = Math.min(t3.length, e3.length), i2 = r3, s2 = 0; n3 < i2; ) t3.substring(t3.length - i2, t3.length - s2) == e3.substring(e3.length - i2, e3.length - s2) ? s2 = n3 = i2 : r3 = i2, i2 = Math.floor((r3 - n3) / 2 + n3);
            return m(t3.charCodeAt(t3.length - i2)) && i2--, i2;
          }
          var c = /[^a-zA-Z0-9]/, u = /\s/, h = /[\r\n]/, d = /\n\r?\n$/, f = /^\r?\n\r?\n/;
          function p(t3, i2) {
            t3.push([r2, ""]);
            for (var s2, l2 = 0, c2 = 0, u2 = 0, h2 = "", d2 = ""; l2 < t3.length; ) if (l2 < t3.length - 1 && !t3[l2][1]) t3.splice(l2, 1);
            else switch (t3[l2][0]) {
              case n2:
                u2++, d2 += t3[l2][1], l2++;
                break;
              case e2:
                c2++, h2 += t3[l2][1], l2++;
                break;
              case r2:
                var f2 = l2 - u2 - c2 - 1;
                if (i2) {
                  if (f2 >= 0 && y(t3[f2][1])) {
                    var g2 = t3[f2][1].slice(-1);
                    if (t3[f2][1] = t3[f2][1].slice(0, -1), h2 = g2 + h2, d2 = g2 + d2, !t3[f2][1]) {
                      t3.splice(f2, 1), l2--;
                      var m2 = f2 - 1;
                      t3[m2] && t3[m2][0] === n2 && (u2++, d2 = t3[m2][1] + d2, m2--), t3[m2] && t3[m2][0] === e2 && (c2++, h2 = t3[m2][1] + h2, m2--), f2 = m2;
                    }
                  }
                  b(t3[l2][1]) && (g2 = t3[l2][1].charAt(0), t3[l2][1] = t3[l2][1].slice(1), h2 += g2, d2 += g2);
                }
                if (l2 < t3.length - 1 && !t3[l2][1]) {
                  t3.splice(l2, 1);
                  break;
                }
                if (h2.length > 0 || d2.length > 0) {
                  h2.length > 0 && d2.length > 0 && (0 !== (s2 = o(d2, h2)) && (f2 >= 0 ? t3[f2][1] += d2.substring(0, s2) : (t3.splice(0, 0, [r2, d2.substring(0, s2)]), l2++), d2 = d2.substring(s2), h2 = h2.substring(s2)), 0 !== (s2 = a(d2, h2)) && (t3[l2][1] = d2.substring(d2.length - s2) + t3[l2][1], d2 = d2.substring(0, d2.length - s2), h2 = h2.substring(0, h2.length - s2)));
                  var v2 = u2 + c2;
                  0 === h2.length && 0 === d2.length ? (t3.splice(l2 - v2, v2), l2 -= v2) : 0 === h2.length ? (t3.splice(l2 - v2, v2, [n2, d2]), l2 = l2 - v2 + 1) : 0 === d2.length ? (t3.splice(l2 - v2, v2, [e2, h2]), l2 = l2 - v2 + 1) : (t3.splice(l2 - v2, v2, [e2, h2], [n2, d2]), l2 = l2 - v2 + 2);
                }
                0 !== l2 && t3[l2 - 1][0] === r2 ? (t3[l2 - 1][1] += t3[l2][1], t3.splice(l2, 1)) : l2++, u2 = 0, c2 = 0, h2 = "", d2 = "";
            }
            "" === t3[t3.length - 1][1] && t3.pop();
            var A2 = false;
            for (l2 = 1; l2 < t3.length - 1; ) t3[l2 - 1][0] === r2 && t3[l2 + 1][0] === r2 && (t3[l2][1].substring(t3[l2][1].length - t3[l2 - 1][1].length) === t3[l2 - 1][1] ? (t3[l2][1] = t3[l2 - 1][1] + t3[l2][1].substring(0, t3[l2][1].length - t3[l2 - 1][1].length), t3[l2 + 1][1] = t3[l2 - 1][1] + t3[l2 + 1][1], t3.splice(l2 - 1, 1), A2 = true) : t3[l2][1].substring(0, t3[l2 + 1][1].length) == t3[l2 + 1][1] && (t3[l2 - 1][1] += t3[l2 + 1][1], t3[l2][1] = t3[l2][1].substring(t3[l2 + 1][1].length) + t3[l2 + 1][1], t3.splice(l2 + 1, 1), A2 = true)), l2++;
            A2 && p(t3, i2);
          }
          function g(t3) {
            return t3 >= 55296 && t3 <= 56319;
          }
          function m(t3) {
            return t3 >= 56320 && t3 <= 57343;
          }
          function b(t3) {
            return m(t3.charCodeAt(0));
          }
          function y(t3) {
            return g(t3.charCodeAt(t3.length - 1));
          }
          function v(t3, i2, s2, o2) {
            return y(t3) || b(o2) ? null : function(t4) {
              for (var e3 = [], n3 = 0; n3 < t4.length; n3++) t4[n3][1].length > 0 && e3.push(t4[n3]);
              return e3;
            }([[r2, t3], [e2, i2], [n2, s2], [r2, o2]]);
          }
          function A(t3, e3, n3, r3) {
            return i(t3, e3, n3, r3, true);
          }
          A.INSERT = n2, A.DELETE = e2, A.EQUAL = r2, t2.exports = A;
        }, 9629: function(t2, e2, n2) {
          t2 = n2.nmd(t2);
          var r2 = "__lodash_hash_undefined__", i = 9007199254740991, s = "[object Arguments]", o = "[object Boolean]", l = "[object Date]", a = "[object Function]", c = "[object GeneratorFunction]", u = "[object Map]", h = "[object Number]", d = "[object Object]", f = "[object Promise]", p = "[object RegExp]", g = "[object Set]", m = "[object String]", b = "[object Symbol]", y = "[object WeakMap]", v = "[object ArrayBuffer]", A = "[object DataView]", x = "[object Float32Array]", N = "[object Float64Array]", E = "[object Int8Array]", w = "[object Int16Array]", q = "[object Int32Array]", k = "[object Uint8Array]", _ = "[object Uint8ClampedArray]", L = "[object Uint16Array]", S = "[object Uint32Array]", O = /\w*$/, T = /^\[object .+?Constructor\]$/, j = /^(?:0|[1-9]\d*)$/, C = {};
          C[s] = C["[object Array]"] = C[v] = C[A] = C[o] = C[l] = C[x] = C[N] = C[E] = C[w] = C[q] = C[u] = C[h] = C[d] = C[p] = C[g] = C[m] = C[b] = C[k] = C[_] = C[L] = C[S] = true, C["[object Error]"] = C[a] = C[y] = false;
          var R = "object" == typeof n2.g && n2.g && n2.g.Object === Object && n2.g, I = "object" == typeof self && self && self.Object === Object && self, B = R || I || Function("return this")(), M = e2 && !e2.nodeType && e2, U = M && t2 && !t2.nodeType && t2, D = U && U.exports === M;
          function P(t3, e3) {
            return t3.set(e3[0], e3[1]), t3;
          }
          function z(t3, e3) {
            return t3.add(e3), t3;
          }
          function F(t3, e3, n3, r3) {
            var i2 = -1, s2 = t3 ? t3.length : 0;
            for (r3 && s2 && (n3 = t3[++i2]); ++i2 < s2; ) n3 = e3(n3, t3[i2], i2, t3);
            return n3;
          }
          function H(t3) {
            var e3 = false;
            if (null != t3 && "function" != typeof t3.toString) try {
              e3 = !!(t3 + "");
            } catch (t4) {
            }
            return e3;
          }
          function $(t3) {
            var e3 = -1, n3 = Array(t3.size);
            return t3.forEach(function(t4, r3) {
              n3[++e3] = [r3, t4];
            }), n3;
          }
          function V(t3, e3) {
            return function(n3) {
              return t3(e3(n3));
            };
          }
          function K(t3) {
            var e3 = -1, n3 = Array(t3.size);
            return t3.forEach(function(t4) {
              n3[++e3] = t4;
            }), n3;
          }
          var W, Z = Array.prototype, G = Function.prototype, X = Object.prototype, Q = B["__core-js_shared__"], J = (W = /[^.]+$/.exec(Q && Q.keys && Q.keys.IE_PROTO || "")) ? "Symbol(src)_1." + W : "", Y = G.toString, tt = X.hasOwnProperty, et = X.toString, nt = RegExp("^" + Y.call(tt).replace(/[\\^$.*+?()[\]{}|]/g, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"), rt = D ? B.Buffer : void 0, it = B.Symbol, st = B.Uint8Array, ot = V(Object.getPrototypeOf, Object), lt = Object.create, at = X.propertyIsEnumerable, ct = Z.splice, ut = Object.getOwnPropertySymbols, ht = rt ? rt.isBuffer : void 0, dt = V(Object.keys, Object), ft = Bt(B, "DataView"), pt = Bt(B, "Map"), gt = Bt(B, "Promise"), mt = Bt(B, "Set"), bt = Bt(B, "WeakMap"), yt = Bt(Object, "create"), vt = zt(ft), At = zt(pt), xt = zt(gt), Nt = zt(mt), Et = zt(bt), wt = it ? it.prototype : void 0, qt = wt ? wt.valueOf : void 0;
          function kt(t3) {
            var e3 = -1, n3 = t3 ? t3.length : 0;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          function _t(t3) {
            var e3 = -1, n3 = t3 ? t3.length : 0;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          function Lt(t3) {
            var e3 = -1, n3 = t3 ? t3.length : 0;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          function St(t3) {
            this.__data__ = new _t(t3);
          }
          function Ot(t3, e3, n3) {
            var r3 = t3[e3];
            tt.call(t3, e3) && Ft(r3, n3) && (void 0 !== n3 || e3 in t3) || (t3[e3] = n3);
          }
          function Tt(t3, e3) {
            for (var n3 = t3.length; n3--; ) if (Ft(t3[n3][0], e3)) return n3;
            return -1;
          }
          function jt(t3, e3, n3, r3, i2, f2, y2) {
            var T2;
            if (r3 && (T2 = f2 ? r3(t3, i2, f2, y2) : r3(t3)), void 0 !== T2) return T2;
            if (!Wt(t3)) return t3;
            var j2 = Ht(t3);
            if (j2) {
              if (T2 = function(t4) {
                var e4 = t4.length, n4 = t4.constructor(e4);
                return e4 && "string" == typeof t4[0] && tt.call(t4, "index") && (n4.index = t4.index, n4.input = t4.input), n4;
              }(t3), !e3) return function(t4, e4) {
                var n4 = -1, r4 = t4.length;
                for (e4 || (e4 = Array(r4)); ++n4 < r4; ) e4[n4] = t4[n4];
                return e4;
              }(t3, T2);
            } else {
              var R2 = Ut(t3), I2 = R2 == a || R2 == c;
              if (Vt(t3)) return function(t4, e4) {
                if (e4) return t4.slice();
                var n4 = new t4.constructor(t4.length);
                return t4.copy(n4), n4;
              }(t3, e3);
              if (R2 == d || R2 == s || I2 && !f2) {
                if (H(t3)) return f2 ? t3 : {};
                if (T2 = function(t4) {
                  return "function" != typeof t4.constructor || Pt(t4) ? {} : Wt(e4 = ot(t4)) ? lt(e4) : {};
                  var e4;
                }(I2 ? {} : t3), !e3) return function(t4, e4) {
                  return Rt(t4, Mt(t4), e4);
                }(t3, function(t4, e4) {
                  return t4 && Rt(e4, Zt(e4), t4);
                }(T2, t3));
              } else {
                if (!C[R2]) return f2 ? t3 : {};
                T2 = function(t4, e4, n4, r4) {
                  var i3, s2 = t4.constructor;
                  switch (e4) {
                    case v:
                      return Ct(t4);
                    case o:
                    case l:
                      return new s2(+t4);
                    case A:
                      return function(t5, e5) {
                        var n5 = e5 ? Ct(t5.buffer) : t5.buffer;
                        return new t5.constructor(n5, t5.byteOffset, t5.byteLength);
                      }(t4, r4);
                    case x:
                    case N:
                    case E:
                    case w:
                    case q:
                    case k:
                    case _:
                    case L:
                    case S:
                      return function(t5, e5) {
                        var n5 = e5 ? Ct(t5.buffer) : t5.buffer;
                        return new t5.constructor(n5, t5.byteOffset, t5.length);
                      }(t4, r4);
                    case u:
                      return function(t5, e5, n5) {
                        return F(e5 ? n5($(t5), true) : $(t5), P, new t5.constructor());
                      }(t4, r4, n4);
                    case h:
                    case m:
                      return new s2(t4);
                    case p:
                      return function(t5) {
                        var e5 = new t5.constructor(t5.source, O.exec(t5));
                        return e5.lastIndex = t5.lastIndex, e5;
                      }(t4);
                    case g:
                      return function(t5, e5, n5) {
                        return F(e5 ? n5(K(t5), true) : K(t5), z, new t5.constructor());
                      }(t4, r4, n4);
                    case b:
                      return i3 = t4, qt ? Object(qt.call(i3)) : {};
                  }
                }(t3, R2, jt, e3);
              }
            }
            y2 || (y2 = new St());
            var B2 = y2.get(t3);
            if (B2) return B2;
            if (y2.set(t3, T2), !j2) var M2 = n3 ? function(t4) {
              return function(t5, e4, n4) {
                var r4 = e4(t5);
                return Ht(t5) ? r4 : function(t6, e5) {
                  for (var n5 = -1, r5 = e5.length, i3 = t6.length; ++n5 < r5; ) t6[i3 + n5] = e5[n5];
                  return t6;
                }(r4, n4(t5));
              }(t4, Zt, Mt);
            }(t3) : Zt(t3);
            return function(t4, e4) {
              for (var n4 = -1, r4 = t4 ? t4.length : 0; ++n4 < r4 && false !== e4(t4[n4], n4); ) ;
            }(M2 || t3, function(i3, s2) {
              M2 && (i3 = t3[s2 = i3]), Ot(T2, s2, jt(i3, e3, n3, r3, s2, t3, y2));
            }), T2;
          }
          function Ct(t3) {
            var e3 = new t3.constructor(t3.byteLength);
            return new st(e3).set(new st(t3)), e3;
          }
          function Rt(t3, e3, n3, r3) {
            n3 || (n3 = {});
            for (var i2 = -1, s2 = e3.length; ++i2 < s2; ) {
              var o2 = e3[i2], l2 = r3 ? r3(n3[o2], t3[o2], o2, n3, t3) : void 0;
              Ot(n3, o2, void 0 === l2 ? t3[o2] : l2);
            }
            return n3;
          }
          function It(t3, e3) {
            var n3, r3, i2 = t3.__data__;
            return ("string" == (r3 = typeof (n3 = e3)) || "number" == r3 || "symbol" == r3 || "boolean" == r3 ? "__proto__" !== n3 : null === n3) ? i2["string" == typeof e3 ? "string" : "hash"] : i2.map;
          }
          function Bt(t3, e3) {
            var n3 = function(t4, e4) {
              return null == t4 ? void 0 : t4[e4];
            }(t3, e3);
            return function(t4) {
              return !(!Wt(t4) || (e4 = t4, J && J in e4)) && (Kt(t4) || H(t4) ? nt : T).test(zt(t4));
              var e4;
            }(n3) ? n3 : void 0;
          }
          kt.prototype.clear = function() {
            this.__data__ = yt ? yt(null) : {};
          }, kt.prototype.delete = function(t3) {
            return this.has(t3) && delete this.__data__[t3];
          }, kt.prototype.get = function(t3) {
            var e3 = this.__data__;
            if (yt) {
              var n3 = e3[t3];
              return n3 === r2 ? void 0 : n3;
            }
            return tt.call(e3, t3) ? e3[t3] : void 0;
          }, kt.prototype.has = function(t3) {
            var e3 = this.__data__;
            return yt ? void 0 !== e3[t3] : tt.call(e3, t3);
          }, kt.prototype.set = function(t3, e3) {
            return this.__data__[t3] = yt && void 0 === e3 ? r2 : e3, this;
          }, _t.prototype.clear = function() {
            this.__data__ = [];
          }, _t.prototype.delete = function(t3) {
            var e3 = this.__data__, n3 = Tt(e3, t3);
            return !(n3 < 0 || (n3 == e3.length - 1 ? e3.pop() : ct.call(e3, n3, 1), 0));
          }, _t.prototype.get = function(t3) {
            var e3 = this.__data__, n3 = Tt(e3, t3);
            return n3 < 0 ? void 0 : e3[n3][1];
          }, _t.prototype.has = function(t3) {
            return Tt(this.__data__, t3) > -1;
          }, _t.prototype.set = function(t3, e3) {
            var n3 = this.__data__, r3 = Tt(n3, t3);
            return r3 < 0 ? n3.push([t3, e3]) : n3[r3][1] = e3, this;
          }, Lt.prototype.clear = function() {
            this.__data__ = { hash: new kt(), map: new (pt || _t)(), string: new kt() };
          }, Lt.prototype.delete = function(t3) {
            return It(this, t3).delete(t3);
          }, Lt.prototype.get = function(t3) {
            return It(this, t3).get(t3);
          }, Lt.prototype.has = function(t3) {
            return It(this, t3).has(t3);
          }, Lt.prototype.set = function(t3, e3) {
            return It(this, t3).set(t3, e3), this;
          }, St.prototype.clear = function() {
            this.__data__ = new _t();
          }, St.prototype.delete = function(t3) {
            return this.__data__.delete(t3);
          }, St.prototype.get = function(t3) {
            return this.__data__.get(t3);
          }, St.prototype.has = function(t3) {
            return this.__data__.has(t3);
          }, St.prototype.set = function(t3, e3) {
            var n3 = this.__data__;
            if (n3 instanceof _t) {
              var r3 = n3.__data__;
              if (!pt || r3.length < 199) return r3.push([t3, e3]), this;
              n3 = this.__data__ = new Lt(r3);
            }
            return n3.set(t3, e3), this;
          };
          var Mt = ut ? V(ut, Object) : function() {
            return [];
          }, Ut = function(t3) {
            return et.call(t3);
          };
          function Dt(t3, e3) {
            return !!(e3 = null == e3 ? i : e3) && ("number" == typeof t3 || j.test(t3)) && t3 > -1 && t3 % 1 == 0 && t3 < e3;
          }
          function Pt(t3) {
            var e3 = t3 && t3.constructor;
            return t3 === ("function" == typeof e3 && e3.prototype || X);
          }
          function zt(t3) {
            if (null != t3) {
              try {
                return Y.call(t3);
              } catch (t4) {
              }
              try {
                return t3 + "";
              } catch (t4) {
              }
            }
            return "";
          }
          function Ft(t3, e3) {
            return t3 === e3 || t3 != t3 && e3 != e3;
          }
          (ft && Ut(new ft(new ArrayBuffer(1))) != A || pt && Ut(new pt()) != u || gt && Ut(gt.resolve()) != f || mt && Ut(new mt()) != g || bt && Ut(new bt()) != y) && (Ut = function(t3) {
            var e3 = et.call(t3), n3 = e3 == d ? t3.constructor : void 0, r3 = n3 ? zt(n3) : void 0;
            if (r3) switch (r3) {
              case vt:
                return A;
              case At:
                return u;
              case xt:
                return f;
              case Nt:
                return g;
              case Et:
                return y;
            }
            return e3;
          });
          var Ht = Array.isArray;
          function $t(t3) {
            return null != t3 && function(t4) {
              return "number" == typeof t4 && t4 > -1 && t4 % 1 == 0 && t4 <= i;
            }(t3.length) && !Kt(t3);
          }
          var Vt = ht || function() {
            return false;
          };
          function Kt(t3) {
            var e3 = Wt(t3) ? et.call(t3) : "";
            return e3 == a || e3 == c;
          }
          function Wt(t3) {
            var e3 = typeof t3;
            return !!t3 && ("object" == e3 || "function" == e3);
          }
          function Zt(t3) {
            return $t(t3) ? function(t4, e3) {
              var n3 = Ht(t4) || function(t5) {
                return function(t6) {
                  return /* @__PURE__ */ function(t7) {
                    return !!t7 && "object" == typeof t7;
                  }(t6) && $t(t6);
                }(t5) && tt.call(t5, "callee") && (!at.call(t5, "callee") || et.call(t5) == s);
              }(t4) ? function(t5, e4) {
                for (var n4 = -1, r4 = Array(t5); ++n4 < t5; ) r4[n4] = e4(n4);
                return r4;
              }(t4.length, String) : [], r3 = n3.length, i2 = !!r3;
              for (var o2 in t4) !e3 && !tt.call(t4, o2) || i2 && ("length" == o2 || Dt(o2, r3)) || n3.push(o2);
              return n3;
            }(t3) : function(t4) {
              if (!Pt(t4)) return dt(t4);
              var e3 = [];
              for (var n3 in Object(t4)) tt.call(t4, n3) && "constructor" != n3 && e3.push(n3);
              return e3;
            }(t3);
          }
          t2.exports = function(t3) {
            return jt(t3, true, true);
          };
        }, 4162: function(t2, e2, n2) {
          t2 = n2.nmd(t2);
          var r2 = "__lodash_hash_undefined__", i = 1, s = 2, o = 9007199254740991, l = "[object Arguments]", a = "[object Array]", c = "[object AsyncFunction]", u = "[object Boolean]", h = "[object Date]", d = "[object Error]", f = "[object Function]", p = "[object GeneratorFunction]", g = "[object Map]", m = "[object Number]", b = "[object Null]", y = "[object Object]", v = "[object Promise]", A = "[object Proxy]", x = "[object RegExp]", N = "[object Set]", E = "[object String]", w = "[object Undefined]", q = "[object WeakMap]", k = "[object ArrayBuffer]", _ = "[object DataView]", L = /^\[object .+?Constructor\]$/, S = /^(?:0|[1-9]\d*)$/, O = {};
          O["[object Float32Array]"] = O["[object Float64Array]"] = O["[object Int8Array]"] = O["[object Int16Array]"] = O["[object Int32Array]"] = O["[object Uint8Array]"] = O["[object Uint8ClampedArray]"] = O["[object Uint16Array]"] = O["[object Uint32Array]"] = true, O[l] = O[a] = O[k] = O[u] = O[_] = O[h] = O[d] = O[f] = O[g] = O[m] = O[y] = O[x] = O[N] = O[E] = O[q] = false;
          var T = "object" == typeof n2.g && n2.g && n2.g.Object === Object && n2.g, j = "object" == typeof self && self && self.Object === Object && self, C = T || j || Function("return this")(), R = e2 && !e2.nodeType && e2, I = R && t2 && !t2.nodeType && t2, B = I && I.exports === R, M = B && T.process, U = function() {
            try {
              return M && M.binding && M.binding("util");
            } catch (t3) {
            }
          }(), D = U && U.isTypedArray;
          function P(t3, e3) {
            for (var n3 = -1, r3 = null == t3 ? 0 : t3.length; ++n3 < r3; ) if (e3(t3[n3], n3, t3)) return true;
            return false;
          }
          function z(t3) {
            var e3 = -1, n3 = Array(t3.size);
            return t3.forEach(function(t4, r3) {
              n3[++e3] = [r3, t4];
            }), n3;
          }
          function F(t3) {
            var e3 = -1, n3 = Array(t3.size);
            return t3.forEach(function(t4) {
              n3[++e3] = t4;
            }), n3;
          }
          var H, $, V, K = Array.prototype, W = Function.prototype, Z = Object.prototype, G = C["__core-js_shared__"], X = W.toString, Q = Z.hasOwnProperty, J = (H = /[^.]+$/.exec(G && G.keys && G.keys.IE_PROTO || "")) ? "Symbol(src)_1." + H : "", Y = Z.toString, tt = RegExp("^" + X.call(Q).replace(/[\\^$.*+?()[\]{}|]/g, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"), et = B ? C.Buffer : void 0, nt = C.Symbol, rt = C.Uint8Array, it = Z.propertyIsEnumerable, st = K.splice, ot = nt ? nt.toStringTag : void 0, lt = Object.getOwnPropertySymbols, at = et ? et.isBuffer : void 0, ct = ($ = Object.keys, V = Object, function(t3) {
            return $(V(t3));
          }), ut = It(C, "DataView"), ht = It(C, "Map"), dt = It(C, "Promise"), ft = It(C, "Set"), pt = It(C, "WeakMap"), gt = It(Object, "create"), mt = Dt(ut), bt = Dt(ht), yt = Dt(dt), vt = Dt(ft), At = Dt(pt), xt = nt ? nt.prototype : void 0, Nt = xt ? xt.valueOf : void 0;
          function Et(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          function wt(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          function qt(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          function kt(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.__data__ = new qt(); ++e3 < n3; ) this.add(t3[e3]);
          }
          function _t(t3) {
            var e3 = this.__data__ = new wt(t3);
            this.size = e3.size;
          }
          function Lt(t3, e3) {
            for (var n3 = t3.length; n3--; ) if (Pt(t3[n3][0], e3)) return n3;
            return -1;
          }
          function St(t3) {
            return null == t3 ? void 0 === t3 ? w : b : ot && ot in Object(t3) ? function(t4) {
              var e3 = Q.call(t4, ot), n3 = t4[ot];
              try {
                t4[ot] = void 0;
                var r3 = true;
              } catch (t5) {
              }
              var i2 = Y.call(t4);
              return r3 && (e3 ? t4[ot] = n3 : delete t4[ot]), i2;
            }(t3) : function(t4) {
              return Y.call(t4);
            }(t3);
          }
          function Ot(t3) {
            return Wt(t3) && St(t3) == l;
          }
          function Tt(t3, e3, n3, r3, o2) {
            return t3 === e3 || (null == t3 || null == e3 || !Wt(t3) && !Wt(e3) ? t3 != t3 && e3 != e3 : function(t4, e4, n4, r4, o3, c2) {
              var f2 = Ft(t4), p2 = Ft(e4), b2 = f2 ? a : Mt(t4), v2 = p2 ? a : Mt(e4), A2 = (b2 = b2 == l ? y : b2) == y, w2 = (v2 = v2 == l ? y : v2) == y, q2 = b2 == v2;
              if (q2 && Ht(t4)) {
                if (!Ht(e4)) return false;
                f2 = true, A2 = false;
              }
              if (q2 && !A2) return c2 || (c2 = new _t()), f2 || Zt(t4) ? jt(t4, e4, n4, r4, o3, c2) : function(t5, e5, n5, r5, o4, l2, a2) {
                switch (n5) {
                  case _:
                    if (t5.byteLength != e5.byteLength || t5.byteOffset != e5.byteOffset) return false;
                    t5 = t5.buffer, e5 = e5.buffer;
                  case k:
                    return !(t5.byteLength != e5.byteLength || !l2(new rt(t5), new rt(e5)));
                  case u:
                  case h:
                  case m:
                    return Pt(+t5, +e5);
                  case d:
                    return t5.name == e5.name && t5.message == e5.message;
                  case x:
                  case E:
                    return t5 == e5 + "";
                  case g:
                    var c3 = z;
                  case N:
                    var f3 = r5 & i;
                    if (c3 || (c3 = F), t5.size != e5.size && !f3) return false;
                    var p3 = a2.get(t5);
                    if (p3) return p3 == e5;
                    r5 |= s, a2.set(t5, e5);
                    var b3 = jt(c3(t5), c3(e5), r5, o4, l2, a2);
                    return a2.delete(t5), b3;
                  case "[object Symbol]":
                    if (Nt) return Nt.call(t5) == Nt.call(e5);
                }
                return false;
              }(t4, e4, b2, n4, r4, o3, c2);
              if (!(n4 & i)) {
                var L2 = A2 && Q.call(t4, "__wrapped__"), S2 = w2 && Q.call(e4, "__wrapped__");
                if (L2 || S2) {
                  var O2 = L2 ? t4.value() : t4, T2 = S2 ? e4.value() : e4;
                  return c2 || (c2 = new _t()), o3(O2, T2, n4, r4, c2);
                }
              }
              return !!q2 && (c2 || (c2 = new _t()), function(t5, e5, n5, r5, s2, o4) {
                var l2 = n5 & i, a2 = Ct(t5), c3 = a2.length;
                if (c3 != Ct(e5).length && !l2) return false;
                for (var u2 = c3; u2--; ) {
                  var h2 = a2[u2];
                  if (!(l2 ? h2 in e5 : Q.call(e5, h2))) return false;
                }
                var d2 = o4.get(t5);
                if (d2 && o4.get(e5)) return d2 == e5;
                var f3 = true;
                o4.set(t5, e5), o4.set(e5, t5);
                for (var p3 = l2; ++u2 < c3; ) {
                  var g2 = t5[h2 = a2[u2]], m2 = e5[h2];
                  if (r5) var b3 = l2 ? r5(m2, g2, h2, e5, t5, o4) : r5(g2, m2, h2, t5, e5, o4);
                  if (!(void 0 === b3 ? g2 === m2 || s2(g2, m2, n5, r5, o4) : b3)) {
                    f3 = false;
                    break;
                  }
                  p3 || (p3 = "constructor" == h2);
                }
                if (f3 && !p3) {
                  var y2 = t5.constructor, v3 = e5.constructor;
                  y2 == v3 || !("constructor" in t5) || !("constructor" in e5) || "function" == typeof y2 && y2 instanceof y2 && "function" == typeof v3 && v3 instanceof v3 || (f3 = false);
                }
                return o4.delete(t5), o4.delete(e5), f3;
              }(t4, e4, n4, r4, o3, c2));
            }(t3, e3, n3, r3, Tt, o2));
          }
          function jt(t3, e3, n3, r3, o2, l2) {
            var a2 = n3 & i, c2 = t3.length, u2 = e3.length;
            if (c2 != u2 && !(a2 && u2 > c2)) return false;
            var h2 = l2.get(t3);
            if (h2 && l2.get(e3)) return h2 == e3;
            var d2 = -1, f2 = true, p2 = n3 & s ? new kt() : void 0;
            for (l2.set(t3, e3), l2.set(e3, t3); ++d2 < c2; ) {
              var g2 = t3[d2], m2 = e3[d2];
              if (r3) var b2 = a2 ? r3(m2, g2, d2, e3, t3, l2) : r3(g2, m2, d2, t3, e3, l2);
              if (void 0 !== b2) {
                if (b2) continue;
                f2 = false;
                break;
              }
              if (p2) {
                if (!P(e3, function(t4, e4) {
                  if (i2 = e4, !p2.has(i2) && (g2 === t4 || o2(g2, t4, n3, r3, l2))) return p2.push(e4);
                  var i2;
                })) {
                  f2 = false;
                  break;
                }
              } else if (g2 !== m2 && !o2(g2, m2, n3, r3, l2)) {
                f2 = false;
                break;
              }
            }
            return l2.delete(t3), l2.delete(e3), f2;
          }
          function Ct(t3) {
            return function(t4, e3, n3) {
              var r3 = e3(t4);
              return Ft(t4) ? r3 : function(t5, e4) {
                for (var n4 = -1, r4 = e4.length, i2 = t5.length; ++n4 < r4; ) t5[i2 + n4] = e4[n4];
                return t5;
              }(r3, n3(t4));
            }(t3, Gt, Bt);
          }
          function Rt(t3, e3) {
            var n3, r3, i2 = t3.__data__;
            return ("string" == (r3 = typeof (n3 = e3)) || "number" == r3 || "symbol" == r3 || "boolean" == r3 ? "__proto__" !== n3 : null === n3) ? i2["string" == typeof e3 ? "string" : "hash"] : i2.map;
          }
          function It(t3, e3) {
            var n3 = function(t4, e4) {
              return null == t4 ? void 0 : t4[e4];
            }(t3, e3);
            return function(t4) {
              return !(!Kt(t4) || function(t5) {
                return !!J && J in t5;
              }(t4)) && ($t(t4) ? tt : L).test(Dt(t4));
            }(n3) ? n3 : void 0;
          }
          Et.prototype.clear = function() {
            this.__data__ = gt ? gt(null) : {}, this.size = 0;
          }, Et.prototype.delete = function(t3) {
            var e3 = this.has(t3) && delete this.__data__[t3];
            return this.size -= e3 ? 1 : 0, e3;
          }, Et.prototype.get = function(t3) {
            var e3 = this.__data__;
            if (gt) {
              var n3 = e3[t3];
              return n3 === r2 ? void 0 : n3;
            }
            return Q.call(e3, t3) ? e3[t3] : void 0;
          }, Et.prototype.has = function(t3) {
            var e3 = this.__data__;
            return gt ? void 0 !== e3[t3] : Q.call(e3, t3);
          }, Et.prototype.set = function(t3, e3) {
            var n3 = this.__data__;
            return this.size += this.has(t3) ? 0 : 1, n3[t3] = gt && void 0 === e3 ? r2 : e3, this;
          }, wt.prototype.clear = function() {
            this.__data__ = [], this.size = 0;
          }, wt.prototype.delete = function(t3) {
            var e3 = this.__data__, n3 = Lt(e3, t3);
            return !(n3 < 0 || (n3 == e3.length - 1 ? e3.pop() : st.call(e3, n3, 1), --this.size, 0));
          }, wt.prototype.get = function(t3) {
            var e3 = this.__data__, n3 = Lt(e3, t3);
            return n3 < 0 ? void 0 : e3[n3][1];
          }, wt.prototype.has = function(t3) {
            return Lt(this.__data__, t3) > -1;
          }, wt.prototype.set = function(t3, e3) {
            var n3 = this.__data__, r3 = Lt(n3, t3);
            return r3 < 0 ? (++this.size, n3.push([t3, e3])) : n3[r3][1] = e3, this;
          }, qt.prototype.clear = function() {
            this.size = 0, this.__data__ = { hash: new Et(), map: new (ht || wt)(), string: new Et() };
          }, qt.prototype.delete = function(t3) {
            var e3 = Rt(this, t3).delete(t3);
            return this.size -= e3 ? 1 : 0, e3;
          }, qt.prototype.get = function(t3) {
            return Rt(this, t3).get(t3);
          }, qt.prototype.has = function(t3) {
            return Rt(this, t3).has(t3);
          }, qt.prototype.set = function(t3, e3) {
            var n3 = Rt(this, t3), r3 = n3.size;
            return n3.set(t3, e3), this.size += n3.size == r3 ? 0 : 1, this;
          }, kt.prototype.add = kt.prototype.push = function(t3) {
            return this.__data__.set(t3, r2), this;
          }, kt.prototype.has = function(t3) {
            return this.__data__.has(t3);
          }, _t.prototype.clear = function() {
            this.__data__ = new wt(), this.size = 0;
          }, _t.prototype.delete = function(t3) {
            var e3 = this.__data__, n3 = e3.delete(t3);
            return this.size = e3.size, n3;
          }, _t.prototype.get = function(t3) {
            return this.__data__.get(t3);
          }, _t.prototype.has = function(t3) {
            return this.__data__.has(t3);
          }, _t.prototype.set = function(t3, e3) {
            var n3 = this.__data__;
            if (n3 instanceof wt) {
              var r3 = n3.__data__;
              if (!ht || r3.length < 199) return r3.push([t3, e3]), this.size = ++n3.size, this;
              n3 = this.__data__ = new qt(r3);
            }
            return n3.set(t3, e3), this.size = n3.size, this;
          };
          var Bt = lt ? function(t3) {
            return null == t3 ? [] : (t3 = Object(t3), function(e3, n3) {
              for (var r3 = -1, i2 = null == e3 ? 0 : e3.length, s2 = 0, o2 = []; ++r3 < i2; ) {
                var l2 = e3[r3];
                a2 = l2, it.call(t3, a2) && (o2[s2++] = l2);
              }
              var a2;
              return o2;
            }(lt(t3)));
          } : function() {
            return [];
          }, Mt = St;
          function Ut(t3, e3) {
            return !!(e3 = null == e3 ? o : e3) && ("number" == typeof t3 || S.test(t3)) && t3 > -1 && t3 % 1 == 0 && t3 < e3;
          }
          function Dt(t3) {
            if (null != t3) {
              try {
                return X.call(t3);
              } catch (t4) {
              }
              try {
                return t3 + "";
              } catch (t4) {
              }
            }
            return "";
          }
          function Pt(t3, e3) {
            return t3 === e3 || t3 != t3 && e3 != e3;
          }
          (ut && Mt(new ut(new ArrayBuffer(1))) != _ || ht && Mt(new ht()) != g || dt && Mt(dt.resolve()) != v || ft && Mt(new ft()) != N || pt && Mt(new pt()) != q) && (Mt = function(t3) {
            var e3 = St(t3), n3 = e3 == y ? t3.constructor : void 0, r3 = n3 ? Dt(n3) : "";
            if (r3) switch (r3) {
              case mt:
                return _;
              case bt:
                return g;
              case yt:
                return v;
              case vt:
                return N;
              case At:
                return q;
            }
            return e3;
          });
          var zt = Ot(/* @__PURE__ */ function() {
            return arguments;
          }()) ? Ot : function(t3) {
            return Wt(t3) && Q.call(t3, "callee") && !it.call(t3, "callee");
          }, Ft = Array.isArray, Ht = at || function() {
            return false;
          };
          function $t(t3) {
            if (!Kt(t3)) return false;
            var e3 = St(t3);
            return e3 == f || e3 == p || e3 == c || e3 == A;
          }
          function Vt(t3) {
            return "number" == typeof t3 && t3 > -1 && t3 % 1 == 0 && t3 <= o;
          }
          function Kt(t3) {
            var e3 = typeof t3;
            return null != t3 && ("object" == e3 || "function" == e3);
          }
          function Wt(t3) {
            return null != t3 && "object" == typeof t3;
          }
          var Zt = D ? /* @__PURE__ */ function(t3) {
            return function(e3) {
              return t3(e3);
            };
          }(D) : function(t3) {
            return Wt(t3) && Vt(t3.length) && !!O[St(t3)];
          };
          function Gt(t3) {
            return null != (e3 = t3) && Vt(e3.length) && !$t(e3) ? function(t4, e4) {
              var n3 = Ft(t4), r3 = !n3 && zt(t4), i2 = !n3 && !r3 && Ht(t4), s2 = !n3 && !r3 && !i2 && Zt(t4), o2 = n3 || r3 || i2 || s2, l2 = o2 ? function(t5, e5) {
                for (var n4 = -1, r4 = Array(t5); ++n4 < t5; ) r4[n4] = e5(n4);
                return r4;
              }(t4.length, String) : [], a2 = l2.length;
              for (var c2 in t4) !e4 && !Q.call(t4, c2) || o2 && ("length" == c2 || i2 && ("offset" == c2 || "parent" == c2) || s2 && ("buffer" == c2 || "byteLength" == c2 || "byteOffset" == c2) || Ut(c2, a2)) || l2.push(c2);
              return l2;
            }(t3) : function(t4) {
              if (n3 = (e4 = t4) && e4.constructor, e4 !== ("function" == typeof n3 && n3.prototype || Z)) return ct(t4);
              var e4, n3, r3 = [];
              for (var i2 in Object(t4)) Q.call(t4, i2) && "constructor" != i2 && r3.push(i2);
              return r3;
            }(t3);
            var e3;
          }
          t2.exports = function(t3, e3) {
            return Tt(t3, e3);
          };
        }, 1270: function(t2, e2, n2) {
          "use strict";
          Object.defineProperty(e2, "__esModule", { value: true });
          const r2 = n2(9629), i = n2(4162);
          var s;
          !function(t3) {
            t3.compose = function(t4 = {}, e3 = {}, n3 = false) {
              "object" != typeof t4 && (t4 = {}), "object" != typeof e3 && (e3 = {});
              let i2 = r2(e3);
              n3 || (i2 = Object.keys(i2).reduce((t5, e4) => (null != i2[e4] && (t5[e4] = i2[e4]), t5), {}));
              for (const n4 in t4) void 0 !== t4[n4] && void 0 === e3[n4] && (i2[n4] = t4[n4]);
              return Object.keys(i2).length > 0 ? i2 : void 0;
            }, t3.diff = function(t4 = {}, e3 = {}) {
              "object" != typeof t4 && (t4 = {}), "object" != typeof e3 && (e3 = {});
              const n3 = Object.keys(t4).concat(Object.keys(e3)).reduce((n4, r3) => (i(t4[r3], e3[r3]) || (n4[r3] = void 0 === e3[r3] ? null : e3[r3]), n4), {});
              return Object.keys(n3).length > 0 ? n3 : void 0;
            }, t3.invert = function(t4 = {}, e3 = {}) {
              t4 = t4 || {};
              const n3 = Object.keys(e3).reduce((n4, r3) => (e3[r3] !== t4[r3] && void 0 !== t4[r3] && (n4[r3] = e3[r3]), n4), {});
              return Object.keys(t4).reduce((n4, r3) => (t4[r3] !== e3[r3] && void 0 === e3[r3] && (n4[r3] = null), n4), n3);
            }, t3.transform = function(t4, e3, n3 = false) {
              if ("object" != typeof t4) return e3;
              if ("object" != typeof e3) return;
              if (!n3) return e3;
              const r3 = Object.keys(e3).reduce((n4, r4) => (void 0 === t4[r4] && (n4[r4] = e3[r4]), n4), {});
              return Object.keys(r3).length > 0 ? r3 : void 0;
            };
          }(s || (s = {})), e2.default = s;
        }, 5232: function(t2, e2, n2) {
          "use strict";
          Object.defineProperty(e2, "__esModule", { value: true }), e2.AttributeMap = e2.OpIterator = e2.Op = void 0;
          const r2 = n2(5090), i = n2(9629), s = n2(4162), o = n2(1270);
          e2.AttributeMap = o.default;
          const l = n2(4123);
          e2.Op = l.default;
          const a = n2(7033);
          e2.OpIterator = a.default;
          const c = String.fromCharCode(0), u = (t3, e3) => {
            if ("object" != typeof t3 || null === t3) throw new Error("cannot retain a " + typeof t3);
            if ("object" != typeof e3 || null === e3) throw new Error("cannot retain a " + typeof e3);
            const n3 = Object.keys(t3)[0];
            if (!n3 || n3 !== Object.keys(e3)[0]) throw new Error(`embed types not matched: ${n3} != ${Object.keys(e3)[0]}`);
            return [n3, t3[n3], e3[n3]];
          };
          class h {
            constructor(t3) {
              Array.isArray(t3) ? this.ops = t3 : null != t3 && Array.isArray(t3.ops) ? this.ops = t3.ops : this.ops = [];
            }
            static registerEmbed(t3, e3) {
              this.handlers[t3] = e3;
            }
            static unregisterEmbed(t3) {
              delete this.handlers[t3];
            }
            static getHandler(t3) {
              const e3 = this.handlers[t3];
              if (!e3) throw new Error(`no handlers for embed type "${t3}"`);
              return e3;
            }
            insert(t3, e3) {
              const n3 = {};
              return "string" == typeof t3 && 0 === t3.length ? this : (n3.insert = t3, null != e3 && "object" == typeof e3 && Object.keys(e3).length > 0 && (n3.attributes = e3), this.push(n3));
            }
            delete(t3) {
              return t3 <= 0 ? this : this.push({ delete: t3 });
            }
            retain(t3, e3) {
              if ("number" == typeof t3 && t3 <= 0) return this;
              const n3 = { retain: t3 };
              return null != e3 && "object" == typeof e3 && Object.keys(e3).length > 0 && (n3.attributes = e3), this.push(n3);
            }
            push(t3) {
              let e3 = this.ops.length, n3 = this.ops[e3 - 1];
              if (t3 = i(t3), "object" == typeof n3) {
                if ("number" == typeof t3.delete && "number" == typeof n3.delete) return this.ops[e3 - 1] = { delete: n3.delete + t3.delete }, this;
                if ("number" == typeof n3.delete && null != t3.insert && (e3 -= 1, n3 = this.ops[e3 - 1], "object" != typeof n3)) return this.ops.unshift(t3), this;
                if (s(t3.attributes, n3.attributes)) {
                  if ("string" == typeof t3.insert && "string" == typeof n3.insert) return this.ops[e3 - 1] = { insert: n3.insert + t3.insert }, "object" == typeof t3.attributes && (this.ops[e3 - 1].attributes = t3.attributes), this;
                  if ("number" == typeof t3.retain && "number" == typeof n3.retain) return this.ops[e3 - 1] = { retain: n3.retain + t3.retain }, "object" == typeof t3.attributes && (this.ops[e3 - 1].attributes = t3.attributes), this;
                }
              }
              return e3 === this.ops.length ? this.ops.push(t3) : this.ops.splice(e3, 0, t3), this;
            }
            chop() {
              const t3 = this.ops[this.ops.length - 1];
              return t3 && "number" == typeof t3.retain && !t3.attributes && this.ops.pop(), this;
            }
            filter(t3) {
              return this.ops.filter(t3);
            }
            forEach(t3) {
              this.ops.forEach(t3);
            }
            map(t3) {
              return this.ops.map(t3);
            }
            partition(t3) {
              const e3 = [], n3 = [];
              return this.forEach((r3) => {
                (t3(r3) ? e3 : n3).push(r3);
              }), [e3, n3];
            }
            reduce(t3, e3) {
              return this.ops.reduce(t3, e3);
            }
            changeLength() {
              return this.reduce((t3, e3) => e3.insert ? t3 + l.default.length(e3) : e3.delete ? t3 - e3.delete : t3, 0);
            }
            length() {
              return this.reduce((t3, e3) => t3 + l.default.length(e3), 0);
            }
            slice(t3 = 0, e3 = 1 / 0) {
              const n3 = [], r3 = new a.default(this.ops);
              let i2 = 0;
              for (; i2 < e3 && r3.hasNext(); ) {
                let s2;
                i2 < t3 ? s2 = r3.next(t3 - i2) : (s2 = r3.next(e3 - i2), n3.push(s2)), i2 += l.default.length(s2);
              }
              return new h(n3);
            }
            compose(t3) {
              const e3 = new a.default(this.ops), n3 = new a.default(t3.ops), r3 = [], i2 = n3.peek();
              if (null != i2 && "number" == typeof i2.retain && null == i2.attributes) {
                let t4 = i2.retain;
                for (; "insert" === e3.peekType() && e3.peekLength() <= t4; ) t4 -= e3.peekLength(), r3.push(e3.next());
                i2.retain - t4 > 0 && n3.next(i2.retain - t4);
              }
              const l2 = new h(r3);
              for (; e3.hasNext() || n3.hasNext(); ) if ("insert" === n3.peekType()) l2.push(n3.next());
              else if ("delete" === e3.peekType()) l2.push(e3.next());
              else {
                const t4 = Math.min(e3.peekLength(), n3.peekLength()), r4 = e3.next(t4), i3 = n3.next(t4);
                if (i3.retain) {
                  const a2 = {};
                  if ("number" == typeof r4.retain) a2.retain = "number" == typeof i3.retain ? t4 : i3.retain;
                  else if ("number" == typeof i3.retain) null == r4.retain ? a2.insert = r4.insert : a2.retain = r4.retain;
                  else {
                    const t5 = null == r4.retain ? "insert" : "retain", [e4, n4, s2] = u(r4[t5], i3.retain), o2 = h.getHandler(e4);
                    a2[t5] = { [e4]: o2.compose(n4, s2, "retain" === t5) };
                  }
                  const c2 = o.default.compose(r4.attributes, i3.attributes, "number" == typeof r4.retain);
                  if (c2 && (a2.attributes = c2), l2.push(a2), !n3.hasNext() && s(l2.ops[l2.ops.length - 1], a2)) {
                    const t5 = new h(e3.rest());
                    return l2.concat(t5).chop();
                  }
                } else "number" == typeof i3.delete && ("number" == typeof r4.retain || "object" == typeof r4.retain && null !== r4.retain) && l2.push(i3);
              }
              return l2.chop();
            }
            concat(t3) {
              const e3 = new h(this.ops.slice());
              return t3.ops.length > 0 && (e3.push(t3.ops[0]), e3.ops = e3.ops.concat(t3.ops.slice(1))), e3;
            }
            diff(t3, e3) {
              if (this.ops === t3.ops) return new h();
              const n3 = [this, t3].map((e4) => e4.map((n4) => {
                if (null != n4.insert) return "string" == typeof n4.insert ? n4.insert : c;
                throw new Error("diff() called " + (e4 === t3 ? "on" : "with") + " non-document");
              }).join("")), i2 = new h(), l2 = r2(n3[0], n3[1], e3, true), u2 = new a.default(this.ops), d = new a.default(t3.ops);
              return l2.forEach((t4) => {
                let e4 = t4[1].length;
                for (; e4 > 0; ) {
                  let n4 = 0;
                  switch (t4[0]) {
                    case r2.INSERT:
                      n4 = Math.min(d.peekLength(), e4), i2.push(d.next(n4));
                      break;
                    case r2.DELETE:
                      n4 = Math.min(e4, u2.peekLength()), u2.next(n4), i2.delete(n4);
                      break;
                    case r2.EQUAL:
                      n4 = Math.min(u2.peekLength(), d.peekLength(), e4);
                      const t5 = u2.next(n4), l3 = d.next(n4);
                      s(t5.insert, l3.insert) ? i2.retain(n4, o.default.diff(t5.attributes, l3.attributes)) : i2.push(l3).delete(n4);
                  }
                  e4 -= n4;
                }
              }), i2.chop();
            }
            eachLine(t3, e3 = "\n") {
              const n3 = new a.default(this.ops);
              let r3 = new h(), i2 = 0;
              for (; n3.hasNext(); ) {
                if ("insert" !== n3.peekType()) return;
                const s2 = n3.peek(), o2 = l.default.length(s2) - n3.peekLength(), a2 = "string" == typeof s2.insert ? s2.insert.indexOf(e3, o2) - o2 : -1;
                if (a2 < 0) r3.push(n3.next());
                else if (a2 > 0) r3.push(n3.next(a2));
                else {
                  if (false === t3(r3, n3.next(1).attributes || {}, i2)) return;
                  i2 += 1, r3 = new h();
                }
              }
              r3.length() > 0 && t3(r3, {}, i2);
            }
            invert(t3) {
              const e3 = new h();
              return this.reduce((n3, r3) => {
                if (r3.insert) e3.delete(l.default.length(r3));
                else {
                  if ("number" == typeof r3.retain && null == r3.attributes) return e3.retain(r3.retain), n3 + r3.retain;
                  if (r3.delete || "number" == typeof r3.retain) {
                    const i2 = r3.delete || r3.retain;
                    return t3.slice(n3, n3 + i2).forEach((t4) => {
                      r3.delete ? e3.push(t4) : r3.retain && r3.attributes && e3.retain(l.default.length(t4), o.default.invert(r3.attributes, t4.attributes));
                    }), n3 + i2;
                  }
                  if ("object" == typeof r3.retain && null !== r3.retain) {
                    const i2 = t3.slice(n3, n3 + 1), s2 = new a.default(i2.ops).next(), [l2, c2, d] = u(r3.retain, s2.insert), f = h.getHandler(l2);
                    return e3.retain({ [l2]: f.invert(c2, d) }, o.default.invert(r3.attributes, s2.attributes)), n3 + 1;
                  }
                }
                return n3;
              }, 0), e3.chop();
            }
            transform(t3, e3 = false) {
              if (e3 = !!e3, "number" == typeof t3) return this.transformPosition(t3, e3);
              const n3 = t3, r3 = new a.default(this.ops), i2 = new a.default(n3.ops), s2 = new h();
              for (; r3.hasNext() || i2.hasNext(); ) if ("insert" !== r3.peekType() || !e3 && "insert" === i2.peekType()) if ("insert" === i2.peekType()) s2.push(i2.next());
              else {
                const t4 = Math.min(r3.peekLength(), i2.peekLength()), n4 = r3.next(t4), l2 = i2.next(t4);
                if (n4.delete) continue;
                if (l2.delete) s2.push(l2);
                else {
                  const r4 = n4.retain, i3 = l2.retain;
                  let a2 = "object" == typeof i3 && null !== i3 ? i3 : t4;
                  if ("object" == typeof r4 && null !== r4 && "object" == typeof i3 && null !== i3) {
                    const t5 = Object.keys(r4)[0];
                    if (t5 === Object.keys(i3)[0]) {
                      const n5 = h.getHandler(t5);
                      n5 && (a2 = { [t5]: n5.transform(r4[t5], i3[t5], e3) });
                    }
                  }
                  s2.retain(a2, o.default.transform(n4.attributes, l2.attributes, e3));
                }
              }
              else s2.retain(l.default.length(r3.next()));
              return s2.chop();
            }
            transformPosition(t3, e3 = false) {
              e3 = !!e3;
              const n3 = new a.default(this.ops);
              let r3 = 0;
              for (; n3.hasNext() && r3 <= t3; ) {
                const i2 = n3.peekLength(), s2 = n3.peekType();
                n3.next(), "delete" !== s2 ? ("insert" === s2 && (r3 < t3 || !e3) && (t3 += i2), r3 += i2) : t3 -= Math.min(i2, t3 - r3);
              }
              return t3;
            }
          }
          h.Op = l.default, h.OpIterator = a.default, h.AttributeMap = o.default, h.handlers = {}, e2.default = h, t2.exports = h, t2.exports.default = h;
        }, 4123: function(t2, e2) {
          "use strict";
          var n2;
          Object.defineProperty(e2, "__esModule", { value: true }), function(t3) {
            t3.length = function(t4) {
              return "number" == typeof t4.delete ? t4.delete : "number" == typeof t4.retain ? t4.retain : "object" == typeof t4.retain && null !== t4.retain ? 1 : "string" == typeof t4.insert ? t4.insert.length : 1;
            };
          }(n2 || (n2 = {})), e2.default = n2;
        }, 7033: function(t2, e2, n2) {
          "use strict";
          Object.defineProperty(e2, "__esModule", { value: true });
          const r2 = n2(4123);
          e2.default = class {
            constructor(t3) {
              this.ops = t3, this.index = 0, this.offset = 0;
            }
            hasNext() {
              return this.peekLength() < 1 / 0;
            }
            next(t3) {
              t3 || (t3 = 1 / 0);
              const e3 = this.ops[this.index];
              if (e3) {
                const n3 = this.offset, i = r2.default.length(e3);
                if (t3 >= i - n3 ? (t3 = i - n3, this.index += 1, this.offset = 0) : this.offset += t3, "number" == typeof e3.delete) return { delete: t3 };
                {
                  const r3 = {};
                  return e3.attributes && (r3.attributes = e3.attributes), "number" == typeof e3.retain ? r3.retain = t3 : "object" == typeof e3.retain && null !== e3.retain ? r3.retain = e3.retain : "string" == typeof e3.insert ? r3.insert = e3.insert.substr(n3, t3) : r3.insert = e3.insert, r3;
                }
              }
              return { retain: 1 / 0 };
            }
            peek() {
              return this.ops[this.index];
            }
            peekLength() {
              return this.ops[this.index] ? r2.default.length(this.ops[this.index]) - this.offset : 1 / 0;
            }
            peekType() {
              const t3 = this.ops[this.index];
              return t3 ? "number" == typeof t3.delete ? "delete" : "number" == typeof t3.retain || "object" == typeof t3.retain && null !== t3.retain ? "retain" : "insert" : "retain";
            }
            rest() {
              if (this.hasNext()) {
                if (0 === this.offset) return this.ops.slice(this.index);
                {
                  const t3 = this.offset, e3 = this.index, n3 = this.next(), r3 = this.ops.slice(this.index);
                  return this.offset = t3, this.index = e3, [n3].concat(r3);
                }
              }
              return [];
            }
          };
        }, 8820: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return l;
          } });
          var r2 = n2(8138), i = function(t3, e3) {
            for (var n3 = t3.length; n3--; ) if ((0, r2.A)(t3[n3][0], e3)) return n3;
            return -1;
          }, s = Array.prototype.splice;
          function o(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          o.prototype.clear = function() {
            this.__data__ = [], this.size = 0;
          }, o.prototype.delete = function(t3) {
            var e3 = this.__data__, n3 = i(e3, t3);
            return !(n3 < 0 || (n3 == e3.length - 1 ? e3.pop() : s.call(e3, n3, 1), --this.size, 0));
          }, o.prototype.get = function(t3) {
            var e3 = this.__data__, n3 = i(e3, t3);
            return n3 < 0 ? void 0 : e3[n3][1];
          }, o.prototype.has = function(t3) {
            return i(this.__data__, t3) > -1;
          }, o.prototype.set = function(t3, e3) {
            var n3 = this.__data__, r3 = i(n3, t3);
            return r3 < 0 ? (++this.size, n3.push([t3, e3])) : n3[r3][1] = e3, this;
          };
          var l = o;
        }, 2461: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(2281), i = n2(5507), s = (0, r2.A)(i.A, "Map");
          e2.A = s;
        }, 3558: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return d;
          } });
          var r2 = (0, n2(2281).A)(Object, "create"), i = Object.prototype.hasOwnProperty, s = Object.prototype.hasOwnProperty;
          function o(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          o.prototype.clear = function() {
            this.__data__ = r2 ? r2(null) : {}, this.size = 0;
          }, o.prototype.delete = function(t3) {
            var e3 = this.has(t3) && delete this.__data__[t3];
            return this.size -= e3 ? 1 : 0, e3;
          }, o.prototype.get = function(t3) {
            var e3 = this.__data__;
            if (r2) {
              var n3 = e3[t3];
              return "__lodash_hash_undefined__" === n3 ? void 0 : n3;
            }
            return i.call(e3, t3) ? e3[t3] : void 0;
          }, o.prototype.has = function(t3) {
            var e3 = this.__data__;
            return r2 ? void 0 !== e3[t3] : s.call(e3, t3);
          }, o.prototype.set = function(t3, e3) {
            var n3 = this.__data__;
            return this.size += this.has(t3) ? 0 : 1, n3[t3] = r2 && void 0 === e3 ? "__lodash_hash_undefined__" : e3, this;
          };
          var l = o, a = n2(8820), c = n2(2461), u = function(t3, e3) {
            var n3, r3, i2 = t3.__data__;
            return ("string" == (r3 = typeof (n3 = e3)) || "number" == r3 || "symbol" == r3 || "boolean" == r3 ? "__proto__" !== n3 : null === n3) ? i2["string" == typeof e3 ? "string" : "hash"] : i2.map;
          };
          function h(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.clear(); ++e3 < n3; ) {
              var r3 = t3[e3];
              this.set(r3[0], r3[1]);
            }
          }
          h.prototype.clear = function() {
            this.size = 0, this.__data__ = { hash: new l(), map: new (c.A || a.A)(), string: new l() };
          }, h.prototype.delete = function(t3) {
            var e3 = u(this, t3).delete(t3);
            return this.size -= e3 ? 1 : 0, e3;
          }, h.prototype.get = function(t3) {
            return u(this, t3).get(t3);
          }, h.prototype.has = function(t3) {
            return u(this, t3).has(t3);
          }, h.prototype.set = function(t3, e3) {
            var n3 = u(this, t3), r3 = n3.size;
            return n3.set(t3, e3), this.size += n3.size == r3 ? 0 : 1, this;
          };
          var d = h;
        }, 2673: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return l;
          } });
          var r2 = n2(8820), i = n2(2461), s = n2(3558);
          function o(t3) {
            var e3 = this.__data__ = new r2.A(t3);
            this.size = e3.size;
          }
          o.prototype.clear = function() {
            this.__data__ = new r2.A(), this.size = 0;
          }, o.prototype.delete = function(t3) {
            var e3 = this.__data__, n3 = e3.delete(t3);
            return this.size = e3.size, n3;
          }, o.prototype.get = function(t3) {
            return this.__data__.get(t3);
          }, o.prototype.has = function(t3) {
            return this.__data__.has(t3);
          }, o.prototype.set = function(t3, e3) {
            var n3 = this.__data__;
            if (n3 instanceof r2.A) {
              var o2 = n3.__data__;
              if (!i.A || o2.length < 199) return o2.push([t3, e3]), this.size = ++n3.size, this;
              n3 = this.__data__ = new s.A(o2);
            }
            return n3.set(t3, e3), this.size = n3.size, this;
          };
          var l = o;
        }, 439: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(5507).A.Symbol;
          e2.A = r2;
        }, 7218: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(5507).A.Uint8Array;
          e2.A = r2;
        }, 6753: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return c;
          } });
          var r2 = n2(8412), i = n2(723), s = n2(776), o = n2(3767), l = n2(5755), a = Object.prototype.hasOwnProperty, c = function(t3, e3) {
            var n3 = (0, i.A)(t3), c2 = !n3 && (0, r2.A)(t3), u = !n3 && !c2 && (0, s.A)(t3), h = !n3 && !c2 && !u && (0, l.A)(t3), d = n3 || c2 || u || h, f = d ? function(t4, e4) {
              for (var n4 = -1, r3 = Array(t4); ++n4 < t4; ) r3[n4] = e4(n4);
              return r3;
            }(t3.length, String) : [], p = f.length;
            for (var g in t3) !e3 && !a.call(t3, g) || d && ("length" == g || u && ("offset" == g || "parent" == g) || h && ("buffer" == g || "byteLength" == g || "byteOffset" == g) || (0, o.A)(g, p)) || f.push(g);
            return f;
          };
        }, 802: function(t2, e2) {
          "use strict";
          e2.A = function(t3, e3) {
            for (var n2 = -1, r2 = e3.length, i = t3.length; ++n2 < r2; ) t3[i + n2] = e3[n2];
            return t3;
          };
        }, 6437: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6770), i = n2(8138), s = Object.prototype.hasOwnProperty;
          e2.A = function(t3, e3, n3) {
            var o = t3[e3];
            s.call(t3, e3) && (0, i.A)(o, n3) && (void 0 !== n3 || e3 in t3) || (0, r2.A)(t3, e3, n3);
          };
        }, 6770: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(7889);
          e2.A = function(t3, e3, n3) {
            "__proto__" == e3 && r2.A ? (0, r2.A)(t3, e3, { configurable: true, enumerable: true, value: n3, writable: true }) : t3[e3] = n3;
          };
        }, 1381: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(802), i = n2(723);
          e2.A = function(t3, e3, n3) {
            var s = e3(t3);
            return (0, i.A)(t3) ? s : (0, r2.A)(s, n3(t3));
          };
        }, 2159: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return u;
          } });
          var r2 = n2(439), i = Object.prototype, s = i.hasOwnProperty, o = i.toString, l = r2.A ? r2.A.toStringTag : void 0, a = Object.prototype.toString, c = r2.A ? r2.A.toStringTag : void 0, u = function(t3) {
            return null == t3 ? void 0 === t3 ? "[object Undefined]" : "[object Null]" : c && c in Object(t3) ? function(t4) {
              var e3 = s.call(t4, l), n3 = t4[l];
              try {
                t4[l] = void 0;
                var r3 = true;
              } catch (t5) {
              }
              var i2 = o.call(t4);
              return r3 && (e3 ? t4[l] = n3 : delete t4[l]), i2;
            }(t3) : function(t4) {
              return a.call(t4);
            }(t3);
          };
        }, 5771: function(t2, e2) {
          "use strict";
          e2.A = function(t3) {
            return function(e3) {
              return t3(e3);
            };
          };
        }, 2899: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(7218);
          e2.A = function(t3) {
            var e3 = new t3.constructor(t3.byteLength);
            return new r2.A(e3).set(new r2.A(t3)), e3;
          };
        }, 3812: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(5507), i = "object" == typeof exports && exports && !exports.nodeType && exports, s = i && "object" == typeof module && module && !module.nodeType && module, o = s && s.exports === i ? r2.A.Buffer : void 0, l = o ? o.allocUnsafe : void 0;
          e2.A = function(t3, e3) {
            if (e3) return t3.slice();
            var n3 = t3.length, r3 = l ? l(n3) : new t3.constructor(n3);
            return t3.copy(r3), r3;
          };
        }, 1827: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(2899);
          e2.A = function(t3, e3) {
            var n3 = e3 ? (0, r2.A)(t3.buffer) : t3.buffer;
            return new t3.constructor(n3, t3.byteOffset, t3.length);
          };
        }, 4405: function(t2, e2) {
          "use strict";
          e2.A = function(t3, e3) {
            var n2 = -1, r2 = t3.length;
            for (e3 || (e3 = Array(r2)); ++n2 < r2; ) e3[n2] = t3[n2];
            return e3;
          };
        }, 9601: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(6437), i = n2(6770);
          e2.A = function(t3, e3, n3, s) {
            var o = !n3;
            n3 || (n3 = {});
            for (var l = -1, a = e3.length; ++l < a; ) {
              var c = e3[l], u = s ? s(n3[c], t3[c], c, n3, t3) : void 0;
              void 0 === u && (u = t3[c]), o ? (0, i.A)(n3, c, u) : (0, r2.A)(n3, c, u);
            }
            return n3;
          };
        }, 7889: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(2281), i = function() {
            try {
              var t3 = (0, r2.A)(Object, "defineProperty");
              return t3({}, "", {}), t3;
            } catch (t4) {
            }
          }();
          e2.A = i;
        }, 9646: function(t2, e2) {
          "use strict";
          var n2 = "object" == typeof global && global && global.Object === Object && global;
          e2.A = n2;
        }, 2816: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(1381), i = n2(9844), s = n2(3169);
          e2.A = function(t3) {
            return (0, r2.A)(t3, s.A, i.A);
          };
        }, 2281: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return m;
          } });
          var r2, i = n2(7572), s = n2(5507).A["__core-js_shared__"], o = (r2 = /[^.]+$/.exec(s && s.keys && s.keys.IE_PROTO || "")) ? "Symbol(src)_1." + r2 : "", l = n2(659), a = n2(1543), c = /^\[object .+?Constructor\]$/, u = Function.prototype, h = Object.prototype, d = u.toString, f = h.hasOwnProperty, p = RegExp("^" + d.call(f).replace(/[\\^$.*+?()[\]{}|]/g, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"), g = function(t3) {
            return !(!(0, l.A)(t3) || (e3 = t3, o && o in e3)) && ((0, i.A)(t3) ? p : c).test((0, a.A)(t3));
            var e3;
          }, m = function(t3, e3) {
            var n3 = function(t4, e4) {
              return null == t4 ? void 0 : t4[e4];
            }(t3, e3);
            return g(n3) ? n3 : void 0;
          };
        }, 8769: function(t2, e2, n2) {
          "use strict";
          var r2 = (0, n2(2217).A)(Object.getPrototypeOf, Object);
          e2.A = r2;
        }, 9844: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return o;
          } });
          var r2 = n2(6935), i = Object.prototype.propertyIsEnumerable, s = Object.getOwnPropertySymbols, o = s ? function(t3) {
            return null == t3 ? [] : (t3 = Object(t3), function(t4, e3) {
              for (var n3 = -1, r3 = null == t4 ? 0 : t4.length, i2 = 0, s2 = []; ++n3 < r3; ) {
                var o2 = t4[n3];
                e3(o2, n3, t4) && (s2[i2++] = o2);
              }
              return s2;
            }(s(t3), function(e3) {
              return i.call(t3, e3);
            }));
          } : r2.A;
        }, 7995: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return E;
          } });
          var r2 = n2(2281), i = n2(5507), s = (0, r2.A)(i.A, "DataView"), o = n2(2461), l = (0, r2.A)(i.A, "Promise"), a = (0, r2.A)(i.A, "Set"), c = (0, r2.A)(i.A, "WeakMap"), u = n2(2159), h = n2(1543), d = "[object Map]", f = "[object Promise]", p = "[object Set]", g = "[object WeakMap]", m = "[object DataView]", b = (0, h.A)(s), y = (0, h.A)(o.A), v = (0, h.A)(l), A = (0, h.A)(a), x = (0, h.A)(c), N = u.A;
          (s && N(new s(new ArrayBuffer(1))) != m || o.A && N(new o.A()) != d || l && N(l.resolve()) != f || a && N(new a()) != p || c && N(new c()) != g) && (N = function(t3) {
            var e3 = (0, u.A)(t3), n3 = "[object Object]" == e3 ? t3.constructor : void 0, r3 = n3 ? (0, h.A)(n3) : "";
            if (r3) switch (r3) {
              case b:
                return m;
              case y:
                return d;
              case v:
                return f;
              case A:
                return p;
              case x:
                return g;
            }
            return e3;
          });
          var E = N;
        }, 1683: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return a;
          } });
          var r2 = n2(659), i = Object.create, s = /* @__PURE__ */ function() {
            function t3() {
            }
            return function(e3) {
              if (!(0, r2.A)(e3)) return {};
              if (i) return i(e3);
              t3.prototype = e3;
              var n3 = new t3();
              return t3.prototype = void 0, n3;
            };
          }(), o = n2(8769), l = n2(501), a = function(t3) {
            return "function" != typeof t3.constructor || (0, l.A)(t3) ? {} : s((0, o.A)(t3));
          };
        }, 3767: function(t2, e2) {
          "use strict";
          var n2 = /^(?:0|[1-9]\d*)$/;
          e2.A = function(t3, e3) {
            var r2 = typeof t3;
            return !!(e3 = null == e3 ? 9007199254740991 : e3) && ("number" == r2 || "symbol" != r2 && n2.test(t3)) && t3 > -1 && t3 % 1 == 0 && t3 < e3;
          };
        }, 501: function(t2, e2) {
          "use strict";
          var n2 = Object.prototype;
          e2.A = function(t3) {
            var e3 = t3 && t3.constructor;
            return t3 === ("function" == typeof e3 && e3.prototype || n2);
          };
        }, 8795: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(9646), i = "object" == typeof exports && exports && !exports.nodeType && exports, s = i && "object" == typeof module && module && !module.nodeType && module, o = s && s.exports === i && r2.A.process, l = function() {
            try {
              return s && s.require && s.require("util").types || o && o.binding && o.binding("util");
            } catch (t3) {
            }
          }();
          e2.A = l;
        }, 2217: function(t2, e2) {
          "use strict";
          e2.A = function(t3, e3) {
            return function(n2) {
              return t3(e3(n2));
            };
          };
        }, 5507: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(9646), i = "object" == typeof self && self && self.Object === Object && self, s = r2.A || i || Function("return this")();
          e2.A = s;
        }, 1543: function(t2, e2) {
          "use strict";
          var n2 = Function.prototype.toString;
          e2.A = function(t3) {
            if (null != t3) {
              try {
                return n2.call(t3);
              } catch (t4) {
              }
              try {
                return t3 + "";
              } catch (t4) {
              }
            }
            return "";
          };
        }, 3707: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return H;
          } });
          var r2 = n2(2673), i = n2(6437), s = n2(9601), o = n2(3169), l = n2(2624), a = n2(3812), c = n2(4405), u = n2(9844), h = n2(802), d = n2(8769), f = n2(6935), p = Object.getOwnPropertySymbols ? function(t3) {
            for (var e3 = []; t3; ) (0, h.A)(e3, (0, u.A)(t3)), t3 = (0, d.A)(t3);
            return e3;
          } : f.A, g = n2(2816), m = n2(1381), b = function(t3) {
            return (0, m.A)(t3, l.A, p);
          }, y = n2(7995), v = Object.prototype.hasOwnProperty, A = n2(2899), x = /\w*$/, N = n2(439), E = N.A ? N.A.prototype : void 0, w = E ? E.valueOf : void 0, q = n2(1827), k = function(t3, e3, n3) {
            var r3, i2, s2, o2 = t3.constructor;
            switch (e3) {
              case "[object ArrayBuffer]":
                return (0, A.A)(t3);
              case "[object Boolean]":
              case "[object Date]":
                return new o2(+t3);
              case "[object DataView]":
                return function(t4, e4) {
                  var n4 = e4 ? (0, A.A)(t4.buffer) : t4.buffer;
                  return new t4.constructor(n4, t4.byteOffset, t4.byteLength);
                }(t3, n3);
              case "[object Float32Array]":
              case "[object Float64Array]":
              case "[object Int8Array]":
              case "[object Int16Array]":
              case "[object Int32Array]":
              case "[object Uint8Array]":
              case "[object Uint8ClampedArray]":
              case "[object Uint16Array]":
              case "[object Uint32Array]":
                return (0, q.A)(t3, n3);
              case "[object Map]":
              case "[object Set]":
                return new o2();
              case "[object Number]":
              case "[object String]":
                return new o2(t3);
              case "[object RegExp]":
                return (s2 = new (i2 = t3).constructor(i2.source, x.exec(i2))).lastIndex = i2.lastIndex, s2;
              case "[object Symbol]":
                return r3 = t3, w ? Object(w.call(r3)) : {};
            }
          }, _ = n2(1683), L = n2(723), S = n2(776), O = n2(7948), T = n2(5771), j = n2(8795), C = j.A && j.A.isMap, R = C ? (0, T.A)(C) : function(t3) {
            return (0, O.A)(t3) && "[object Map]" == (0, y.A)(t3);
          }, I = n2(659), B = j.A && j.A.isSet, M = B ? (0, T.A)(B) : function(t3) {
            return (0, O.A)(t3) && "[object Set]" == (0, y.A)(t3);
          }, U = "[object Arguments]", D = "[object Function]", P = "[object Object]", z = {};
          z[U] = z["[object Array]"] = z["[object ArrayBuffer]"] = z["[object DataView]"] = z["[object Boolean]"] = z["[object Date]"] = z["[object Float32Array]"] = z["[object Float64Array]"] = z["[object Int8Array]"] = z["[object Int16Array]"] = z["[object Int32Array]"] = z["[object Map]"] = z["[object Number]"] = z[P] = z["[object RegExp]"] = z["[object Set]"] = z["[object String]"] = z["[object Symbol]"] = z["[object Uint8Array]"] = z["[object Uint8ClampedArray]"] = z["[object Uint16Array]"] = z["[object Uint32Array]"] = true, z["[object Error]"] = z[D] = z["[object WeakMap]"] = false;
          var F = function t3(e3, n3, h2, d2, f2, m2) {
            var A2, x2 = 1 & n3, N2 = 2 & n3, E2 = 4 & n3;
            if (h2 && (A2 = f2 ? h2(e3, d2, f2, m2) : h2(e3)), void 0 !== A2) return A2;
            if (!(0, I.A)(e3)) return e3;
            var w2 = (0, L.A)(e3);
            if (w2) {
              if (A2 = function(t4) {
                var e4 = t4.length, n4 = new t4.constructor(e4);
                return e4 && "string" == typeof t4[0] && v.call(t4, "index") && (n4.index = t4.index, n4.input = t4.input), n4;
              }(e3), !x2) return (0, c.A)(e3, A2);
            } else {
              var q2 = (0, y.A)(e3), O2 = q2 == D || "[object GeneratorFunction]" == q2;
              if ((0, S.A)(e3)) return (0, a.A)(e3, x2);
              if (q2 == P || q2 == U || O2 && !f2) {
                if (A2 = N2 || O2 ? {} : (0, _.A)(e3), !x2) return N2 ? function(t4, e4) {
                  return (0, s.A)(t4, p(t4), e4);
                }(e3, function(t4, e4) {
                  return t4 && (0, s.A)(e4, (0, l.A)(e4), t4);
                }(A2, e3)) : function(t4, e4) {
                  return (0, s.A)(t4, (0, u.A)(t4), e4);
                }(e3, function(t4, e4) {
                  return t4 && (0, s.A)(e4, (0, o.A)(e4), t4);
                }(A2, e3));
              } else {
                if (!z[q2]) return f2 ? e3 : {};
                A2 = k(e3, q2, x2);
              }
            }
            m2 || (m2 = new r2.A());
            var T2 = m2.get(e3);
            if (T2) return T2;
            m2.set(e3, A2), M(e3) ? e3.forEach(function(r3) {
              A2.add(t3(r3, n3, h2, r3, e3, m2));
            }) : R(e3) && e3.forEach(function(r3, i2) {
              A2.set(i2, t3(r3, n3, h2, i2, e3, m2));
            });
            var j2 = E2 ? N2 ? b : g.A : N2 ? l.A : o.A, C2 = w2 ? void 0 : j2(e3);
            return function(t4, e4) {
              for (var n4 = -1, r3 = null == t4 ? 0 : t4.length; ++n4 < r3 && false !== e4(t4[n4], n4, t4); ) ;
            }(C2 || e3, function(r3, s2) {
              C2 && (r3 = e3[s2 = r3]), (0, i.A)(A2, s2, t3(r3, n3, h2, s2, e3, m2));
            }), A2;
          }, H = function(t3) {
            return F(t3, 5);
          };
        }, 8138: function(t2, e2) {
          "use strict";
          e2.A = function(t3, e3) {
            return t3 === e3 || t3 != t3 && e3 != e3;
          };
        }, 8412: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return u;
          } });
          var r2 = n2(2159), i = n2(7948), s = function(t3) {
            return (0, i.A)(t3) && "[object Arguments]" == (0, r2.A)(t3);
          }, o = Object.prototype, l = o.hasOwnProperty, a = o.propertyIsEnumerable, c = s(/* @__PURE__ */ function() {
            return arguments;
          }()) ? s : function(t3) {
            return (0, i.A)(t3) && l.call(t3, "callee") && !a.call(t3, "callee");
          }, u = c;
        }, 723: function(t2, e2) {
          "use strict";
          var n2 = Array.isArray;
          e2.A = n2;
        }, 3628: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(7572), i = n2(1628);
          e2.A = function(t3) {
            return null != t3 && (0, i.A)(t3.length) && !(0, r2.A)(t3);
          };
        }, 776: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return l;
          } });
          var r2 = n2(5507), i = "object" == typeof exports && exports && !exports.nodeType && exports, s = i && "object" == typeof module && module && !module.nodeType && module, o = s && s.exports === i ? r2.A.Buffer : void 0, l = (o ? o.isBuffer : void 0) || function() {
            return false;
          };
        }, 5123: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return S;
          } });
          var r2 = n2(2673), i = n2(3558);
          function s(t3) {
            var e3 = -1, n3 = null == t3 ? 0 : t3.length;
            for (this.__data__ = new i.A(); ++e3 < n3; ) this.add(t3[e3]);
          }
          s.prototype.add = s.prototype.push = function(t3) {
            return this.__data__.set(t3, "__lodash_hash_undefined__"), this;
          }, s.prototype.has = function(t3) {
            return this.__data__.has(t3);
          };
          var o = s, l = function(t3, e3) {
            for (var n3 = -1, r3 = null == t3 ? 0 : t3.length; ++n3 < r3; ) if (e3(t3[n3], n3, t3)) return true;
            return false;
          }, a = function(t3, e3, n3, r3, i2, s2) {
            var a2 = 1 & n3, c2 = t3.length, u2 = e3.length;
            if (c2 != u2 && !(a2 && u2 > c2)) return false;
            var h2 = s2.get(t3), d2 = s2.get(e3);
            if (h2 && d2) return h2 == e3 && d2 == t3;
            var f2 = -1, p2 = true, g2 = 2 & n3 ? new o() : void 0;
            for (s2.set(t3, e3), s2.set(e3, t3); ++f2 < c2; ) {
              var m2 = t3[f2], b2 = e3[f2];
              if (r3) var y2 = a2 ? r3(b2, m2, f2, e3, t3, s2) : r3(m2, b2, f2, t3, e3, s2);
              if (void 0 !== y2) {
                if (y2) continue;
                p2 = false;
                break;
              }
              if (g2) {
                if (!l(e3, function(t4, e4) {
                  if (o2 = e4, !g2.has(o2) && (m2 === t4 || i2(m2, t4, n3, r3, s2))) return g2.push(e4);
                  var o2;
                })) {
                  p2 = false;
                  break;
                }
              } else if (m2 !== b2 && !i2(m2, b2, n3, r3, s2)) {
                p2 = false;
                break;
              }
            }
            return s2.delete(t3), s2.delete(e3), p2;
          }, c = n2(439), u = n2(7218), h = n2(8138), d = function(t3) {
            var e3 = -1, n3 = Array(t3.size);
            return t3.forEach(function(t4, r3) {
              n3[++e3] = [r3, t4];
            }), n3;
          }, f = function(t3) {
            var e3 = -1, n3 = Array(t3.size);
            return t3.forEach(function(t4) {
              n3[++e3] = t4;
            }), n3;
          }, p = c.A ? c.A.prototype : void 0, g = p ? p.valueOf : void 0, m = n2(2816), b = Object.prototype.hasOwnProperty, y = n2(7995), v = n2(723), A = n2(776), x = n2(5755), N = "[object Arguments]", E = "[object Array]", w = "[object Object]", q = Object.prototype.hasOwnProperty, k = function(t3, e3, n3, i2, s2, o2) {
            var l2 = (0, v.A)(t3), c2 = (0, v.A)(e3), p2 = l2 ? E : (0, y.A)(t3), k2 = c2 ? E : (0, y.A)(e3), _2 = (p2 = p2 == N ? w : p2) == w, L2 = (k2 = k2 == N ? w : k2) == w, S2 = p2 == k2;
            if (S2 && (0, A.A)(t3)) {
              if (!(0, A.A)(e3)) return false;
              l2 = true, _2 = false;
            }
            if (S2 && !_2) return o2 || (o2 = new r2.A()), l2 || (0, x.A)(t3) ? a(t3, e3, n3, i2, s2, o2) : function(t4, e4, n4, r3, i3, s3, o3) {
              switch (n4) {
                case "[object DataView]":
                  if (t4.byteLength != e4.byteLength || t4.byteOffset != e4.byteOffset) return false;
                  t4 = t4.buffer, e4 = e4.buffer;
                case "[object ArrayBuffer]":
                  return !(t4.byteLength != e4.byteLength || !s3(new u.A(t4), new u.A(e4)));
                case "[object Boolean]":
                case "[object Date]":
                case "[object Number]":
                  return (0, h.A)(+t4, +e4);
                case "[object Error]":
                  return t4.name == e4.name && t4.message == e4.message;
                case "[object RegExp]":
                case "[object String]":
                  return t4 == e4 + "";
                case "[object Map]":
                  var l3 = d;
                case "[object Set]":
                  var c3 = 1 & r3;
                  if (l3 || (l3 = f), t4.size != e4.size && !c3) return false;
                  var p3 = o3.get(t4);
                  if (p3) return p3 == e4;
                  r3 |= 2, o3.set(t4, e4);
                  var m2 = a(l3(t4), l3(e4), r3, i3, s3, o3);
                  return o3.delete(t4), m2;
                case "[object Symbol]":
                  if (g) return g.call(t4) == g.call(e4);
              }
              return false;
            }(t3, e3, p2, n3, i2, s2, o2);
            if (!(1 & n3)) {
              var O = _2 && q.call(t3, "__wrapped__"), T = L2 && q.call(e3, "__wrapped__");
              if (O || T) {
                var j = O ? t3.value() : t3, C = T ? e3.value() : e3;
                return o2 || (o2 = new r2.A()), s2(j, C, n3, i2, o2);
              }
            }
            return !!S2 && (o2 || (o2 = new r2.A()), function(t4, e4, n4, r3, i3, s3) {
              var o3 = 1 & n4, l3 = (0, m.A)(t4), a2 = l3.length;
              if (a2 != (0, m.A)(e4).length && !o3) return false;
              for (var c3 = a2; c3--; ) {
                var u2 = l3[c3];
                if (!(o3 ? u2 in e4 : b.call(e4, u2))) return false;
              }
              var h2 = s3.get(t4), d2 = s3.get(e4);
              if (h2 && d2) return h2 == e4 && d2 == t4;
              var f2 = true;
              s3.set(t4, e4), s3.set(e4, t4);
              for (var p3 = o3; ++c3 < a2; ) {
                var g2 = t4[u2 = l3[c3]], y2 = e4[u2];
                if (r3) var v2 = o3 ? r3(y2, g2, u2, e4, t4, s3) : r3(g2, y2, u2, t4, e4, s3);
                if (!(void 0 === v2 ? g2 === y2 || i3(g2, y2, n4, r3, s3) : v2)) {
                  f2 = false;
                  break;
                }
                p3 || (p3 = "constructor" == u2);
              }
              if (f2 && !p3) {
                var A2 = t4.constructor, x2 = e4.constructor;
                A2 == x2 || !("constructor" in t4) || !("constructor" in e4) || "function" == typeof A2 && A2 instanceof A2 && "function" == typeof x2 && x2 instanceof x2 || (f2 = false);
              }
              return s3.delete(t4), s3.delete(e4), f2;
            }(t3, e3, n3, i2, s2, o2));
          }, _ = n2(7948), L = function t3(e3, n3, r3, i2, s2) {
            return e3 === n3 || (null == e3 || null == n3 || !(0, _.A)(e3) && !(0, _.A)(n3) ? e3 != e3 && n3 != n3 : k(e3, n3, r3, i2, t3, s2));
          }, S = function(t3, e3) {
            return L(t3, e3);
          };
        }, 7572: function(t2, e2, n2) {
          "use strict";
          var r2 = n2(2159), i = n2(659);
          e2.A = function(t3) {
            if (!(0, i.A)(t3)) return false;
            var e3 = (0, r2.A)(t3);
            return "[object Function]" == e3 || "[object GeneratorFunction]" == e3 || "[object AsyncFunction]" == e3 || "[object Proxy]" == e3;
          };
        }, 1628: function(t2, e2) {
          "use strict";
          e2.A = function(t3) {
            return "number" == typeof t3 && t3 > -1 && t3 % 1 == 0 && t3 <= 9007199254740991;
          };
        }, 659: function(t2, e2) {
          "use strict";
          e2.A = function(t3) {
            var e3 = typeof t3;
            return null != t3 && ("object" == e3 || "function" == e3);
          };
        }, 7948: function(t2, e2) {
          "use strict";
          e2.A = function(t3) {
            return null != t3 && "object" == typeof t3;
          };
        }, 5755: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return u;
          } });
          var r2 = n2(2159), i = n2(1628), s = n2(7948), o = {};
          o["[object Float32Array]"] = o["[object Float64Array]"] = o["[object Int8Array]"] = o["[object Int16Array]"] = o["[object Int32Array]"] = o["[object Uint8Array]"] = o["[object Uint8ClampedArray]"] = o["[object Uint16Array]"] = o["[object Uint32Array]"] = true, o["[object Arguments]"] = o["[object Array]"] = o["[object ArrayBuffer]"] = o["[object Boolean]"] = o["[object DataView]"] = o["[object Date]"] = o["[object Error]"] = o["[object Function]"] = o["[object Map]"] = o["[object Number]"] = o["[object Object]"] = o["[object RegExp]"] = o["[object Set]"] = o["[object String]"] = o["[object WeakMap]"] = false;
          var l = n2(5771), a = n2(8795), c = a.A && a.A.isTypedArray, u = c ? (0, l.A)(c) : function(t3) {
            return (0, s.A)(t3) && (0, i.A)(t3.length) && !!o[(0, r2.A)(t3)];
          };
        }, 3169: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return a;
          } });
          var r2 = n2(6753), i = n2(501), s = (0, n2(2217).A)(Object.keys, Object), o = Object.prototype.hasOwnProperty, l = n2(3628), a = function(t3) {
            return (0, l.A)(t3) ? (0, r2.A)(t3) : function(t4) {
              if (!(0, i.A)(t4)) return s(t4);
              var e3 = [];
              for (var n3 in Object(t4)) o.call(t4, n3) && "constructor" != n3 && e3.push(n3);
              return e3;
            }(t3);
          };
        }, 2624: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return c;
          } });
          var r2 = n2(6753), i = n2(659), s = n2(501), o = Object.prototype.hasOwnProperty, l = function(t3) {
            if (!(0, i.A)(t3)) return function(t4) {
              var e4 = [];
              if (null != t4) for (var n4 in Object(t4)) e4.push(n4);
              return e4;
            }(t3);
            var e3 = (0, s.A)(t3), n3 = [];
            for (var r3 in t3) ("constructor" != r3 || !e3 && o.call(t3, r3)) && n3.push(r3);
            return n3;
          }, a = n2(3628), c = function(t3) {
            return (0, a.A)(t3) ? (0, r2.A)(t3, true) : l(t3);
          };
        }, 8347: function(t2, e2, n2) {
          "use strict";
          n2.d(e2, { A: function() {
            return $;
          } });
          var r2, i, s, o, l = n2(2673), a = n2(6770), c = n2(8138), u = function(t3, e3, n3) {
            (void 0 !== n3 && !(0, c.A)(t3[e3], n3) || void 0 === n3 && !(e3 in t3)) && (0, a.A)(t3, e3, n3);
          }, h = function(t3, e3, n3) {
            for (var r3 = -1, i2 = Object(t3), s2 = n3(t3), o2 = s2.length; o2--; ) {
              var l2 = s2[++r3];
              if (false === e3(i2[l2], l2, i2)) break;
            }
            return t3;
          }, d = n2(3812), f = n2(1827), p = n2(4405), g = n2(1683), m = n2(8412), b = n2(723), y = n2(3628), v = n2(7948), A = n2(776), x = n2(7572), N = n2(659), E = n2(2159), w = n2(8769), q = Function.prototype, k = Object.prototype, _ = q.toString, L = k.hasOwnProperty, S = _.call(Object), O = n2(5755), T = function(t3, e3) {
            if (("constructor" !== e3 || "function" != typeof t3[e3]) && "__proto__" != e3) return t3[e3];
          }, j = n2(9601), C = n2(2624), R = function(t3, e3, n3, r3, i2, s2, o2) {
            var l2, a2 = T(t3, n3), c2 = T(e3, n3), h2 = o2.get(c2);
            if (h2) u(t3, n3, h2);
            else {
              var q2 = s2 ? s2(a2, c2, n3 + "", t3, e3, o2) : void 0, k2 = void 0 === q2;
              if (k2) {
                var R2 = (0, b.A)(c2), I2 = !R2 && (0, A.A)(c2), B2 = !R2 && !I2 && (0, O.A)(c2);
                q2 = c2, R2 || I2 || B2 ? (0, b.A)(a2) ? q2 = a2 : (l2 = a2, (0, v.A)(l2) && (0, y.A)(l2) ? q2 = (0, p.A)(a2) : I2 ? (k2 = false, q2 = (0, d.A)(c2, true)) : B2 ? (k2 = false, q2 = (0, f.A)(c2, true)) : q2 = []) : function(t4) {
                  if (!(0, v.A)(t4) || "[object Object]" != (0, E.A)(t4)) return false;
                  var e4 = (0, w.A)(t4);
                  if (null === e4) return true;
                  var n4 = L.call(e4, "constructor") && e4.constructor;
                  return "function" == typeof n4 && n4 instanceof n4 && _.call(n4) == S;
                }(c2) || (0, m.A)(c2) ? (q2 = a2, (0, m.A)(a2) ? q2 = function(t4) {
                  return (0, j.A)(t4, (0, C.A)(t4));
                }(a2) : (0, N.A)(a2) && !(0, x.A)(a2) || (q2 = (0, g.A)(c2))) : k2 = false;
              }
              k2 && (o2.set(c2, q2), i2(q2, c2, r3, s2, o2), o2.delete(c2)), u(t3, n3, q2);
            }
          }, I = function t3(e3, n3, r3, i2, s2) {
            e3 !== n3 && h(n3, function(o2, a2) {
              if (s2 || (s2 = new l.A()), (0, N.A)(o2)) R(e3, n3, a2, r3, t3, i2, s2);
              else {
                var c2 = i2 ? i2(T(e3, a2), o2, a2 + "", e3, n3, s2) : void 0;
                void 0 === c2 && (c2 = o2), u(e3, a2, c2);
              }
            }, C.A);
          }, B = function(t3) {
            return t3;
          }, M = Math.max, U = n2(7889), D = U.A ? function(t3, e3) {
            return (0, U.A)(t3, "toString", { configurable: true, enumerable: false, value: (n3 = e3, function() {
              return n3;
            }), writable: true });
            var n3;
          } : B, P = Date.now, z = (r2 = D, i = 0, s = 0, function() {
            var t3 = P(), e3 = 16 - (t3 - s);
            if (s = t3, e3 > 0) {
              if (++i >= 800) return arguments[0];
            } else i = 0;
            return r2.apply(void 0, arguments);
          }), F = function(t3, e3) {
            return z(function(t4, e4, n3) {
              return e4 = M(void 0 === e4 ? t4.length - 1 : e4, 0), function() {
                for (var r3 = arguments, i2 = -1, s2 = M(r3.length - e4, 0), o2 = Array(s2); ++i2 < s2; ) o2[i2] = r3[e4 + i2];
                i2 = -1;
                for (var l2 = Array(e4 + 1); ++i2 < e4; ) l2[i2] = r3[i2];
                return l2[e4] = n3(o2), function(t5, e5, n4) {
                  switch (n4.length) {
                    case 0:
                      return t5.call(e5);
                    case 1:
                      return t5.call(e5, n4[0]);
                    case 2:
                      return t5.call(e5, n4[0], n4[1]);
                    case 3:
                      return t5.call(e5, n4[0], n4[1], n4[2]);
                  }
                  return t5.apply(e5, n4);
                }(t4, this, l2);
              };
            }(t3, e3, B), t3 + "");
          }, H = n2(3767), $ = (o = function(t3, e3, n3) {
            I(t3, e3, n3);
          }, F(function(t3, e3) {
            var n3 = -1, r3 = e3.length, i2 = r3 > 1 ? e3[r3 - 1] : void 0, s2 = r3 > 2 ? e3[2] : void 0;
            for (i2 = o.length > 3 && "function" == typeof i2 ? (r3--, i2) : void 0, s2 && function(t4, e4, n4) {
              if (!(0, N.A)(n4)) return false;
              var r4 = typeof e4;
              return !!("number" == r4 ? (0, y.A)(n4) && (0, H.A)(e4, n4.length) : "string" == r4 && e4 in n4) && (0, c.A)(n4[e4], t4);
            }(e3[0], e3[1], s2) && (i2 = r3 < 3 ? void 0 : i2, r3 = 1), t3 = Object(t3); ++n3 < r3; ) {
              var l2 = e3[n3];
              l2 && o(t3, l2, n3);
            }
            return t3;
          }));
        }, 6935: function(t2, e2) {
          "use strict";
          e2.A = function() {
            return [];
          };
        }, 6003: function(t2, e2, n2) {
          "use strict";
          n2.r(e2), n2.d(e2, { Attributor: function() {
            return i;
          }, AttributorStore: function() {
            return d;
          }, BlockBlot: function() {
            return w;
          }, ClassAttributor: function() {
            return c;
          }, ContainerBlot: function() {
            return k;
          }, EmbedBlot: function() {
            return _;
          }, InlineBlot: function() {
            return N;
          }, LeafBlot: function() {
            return m;
          }, ParentBlot: function() {
            return A;
          }, Registry: function() {
            return l;
          }, Scope: function() {
            return r2;
          }, ScrollBlot: function() {
            return O;
          }, StyleAttributor: function() {
            return h;
          }, TextBlot: function() {
            return j;
          } });
          var r2 = ((t3) => (t3[t3.TYPE = 3] = "TYPE", t3[t3.LEVEL = 12] = "LEVEL", t3[t3.ATTRIBUTE = 13] = "ATTRIBUTE", t3[t3.BLOT = 14] = "BLOT", t3[t3.INLINE = 7] = "INLINE", t3[t3.BLOCK = 11] = "BLOCK", t3[t3.BLOCK_BLOT = 10] = "BLOCK_BLOT", t3[t3.INLINE_BLOT = 6] = "INLINE_BLOT", t3[t3.BLOCK_ATTRIBUTE = 9] = "BLOCK_ATTRIBUTE", t3[t3.INLINE_ATTRIBUTE = 5] = "INLINE_ATTRIBUTE", t3[t3.ANY = 15] = "ANY", t3))(r2 || {});
          class i {
            constructor(t3, e3, n3 = {}) {
              this.attrName = t3, this.keyName = e3;
              const i2 = r2.TYPE & r2.ATTRIBUTE;
              this.scope = null != n3.scope ? n3.scope & r2.LEVEL | i2 : r2.ATTRIBUTE, null != n3.whitelist && (this.whitelist = n3.whitelist);
            }
            static keys(t3) {
              return Array.from(t3.attributes).map((t4) => t4.name);
            }
            add(t3, e3) {
              return !!this.canAdd(t3, e3) && (t3.setAttribute(this.keyName, e3), true);
            }
            canAdd(t3, e3) {
              return null == this.whitelist || ("string" == typeof e3 ? this.whitelist.indexOf(e3.replace(/["']/g, "")) > -1 : this.whitelist.indexOf(e3) > -1);
            }
            remove(t3) {
              t3.removeAttribute(this.keyName);
            }
            value(t3) {
              const e3 = t3.getAttribute(this.keyName);
              return this.canAdd(t3, e3) && e3 ? e3 : "";
            }
          }
          class s extends Error {
            constructor(t3) {
              super(t3 = "[Parchment] " + t3), this.message = t3, this.name = this.constructor.name;
            }
          }
          const o = class t3 {
            constructor() {
              this.attributes = {}, this.classes = {}, this.tags = {}, this.types = {};
            }
            static find(t4, e3 = false) {
              if (null == t4) return null;
              if (this.blots.has(t4)) return this.blots.get(t4) || null;
              if (e3) {
                let n3 = null;
                try {
                  n3 = t4.parentNode;
                } catch {
                  return null;
                }
                return this.find(n3, e3);
              }
              return null;
            }
            create(e3, n3, r3) {
              const i2 = this.query(n3);
              if (null == i2) throw new s(`Unable to create ${n3} blot`);
              const o2 = i2, l2 = n3 instanceof Node || n3.nodeType === Node.TEXT_NODE ? n3 : o2.create(r3), a2 = new o2(e3, l2, r3);
              return t3.blots.set(a2.domNode, a2), a2;
            }
            find(e3, n3 = false) {
              return t3.find(e3, n3);
            }
            query(t4, e3 = r2.ANY) {
              let n3;
              return "string" == typeof t4 ? n3 = this.types[t4] || this.attributes[t4] : t4 instanceof Text || t4.nodeType === Node.TEXT_NODE ? n3 = this.types.text : "number" == typeof t4 ? t4 & r2.LEVEL & r2.BLOCK ? n3 = this.types.block : t4 & r2.LEVEL & r2.INLINE && (n3 = this.types.inline) : t4 instanceof Element && ((t4.getAttribute("class") || "").split(/\s+/).some((t5) => (n3 = this.classes[t5], !!n3)), n3 = n3 || this.tags[t4.tagName]), null == n3 ? null : "scope" in n3 && e3 & r2.LEVEL & n3.scope && e3 & r2.TYPE & n3.scope ? n3 : null;
            }
            register(...t4) {
              return t4.map((t5) => {
                const e3 = "blotName" in t5, n3 = "attrName" in t5;
                if (!e3 && !n3) throw new s("Invalid definition");
                if (e3 && "abstract" === t5.blotName) throw new s("Cannot register abstract class");
                const r3 = e3 ? t5.blotName : n3 ? t5.attrName : void 0;
                return this.types[r3] = t5, n3 ? "string" == typeof t5.keyName && (this.attributes[t5.keyName] = t5) : e3 && (t5.className && (this.classes[t5.className] = t5), t5.tagName && (Array.isArray(t5.tagName) ? t5.tagName = t5.tagName.map((t6) => t6.toUpperCase()) : t5.tagName = t5.tagName.toUpperCase(), (Array.isArray(t5.tagName) ? t5.tagName : [t5.tagName]).forEach((e4) => {
                  (null == this.tags[e4] || null == t5.className) && (this.tags[e4] = t5);
                }))), t5;
              });
            }
          };
          o.blots = /* @__PURE__ */ new WeakMap();
          let l = o;
          function a(t3, e3) {
            return (t3.getAttribute("class") || "").split(/\s+/).filter((t4) => 0 === t4.indexOf(`${e3}-`));
          }
          const c = class extends i {
            static keys(t3) {
              return (t3.getAttribute("class") || "").split(/\s+/).map((t4) => t4.split("-").slice(0, -1).join("-"));
            }
            add(t3, e3) {
              return !!this.canAdd(t3, e3) && (this.remove(t3), t3.classList.add(`${this.keyName}-${e3}`), true);
            }
            remove(t3) {
              a(t3, this.keyName).forEach((e3) => {
                t3.classList.remove(e3);
              }), 0 === t3.classList.length && t3.removeAttribute("class");
            }
            value(t3) {
              const e3 = (a(t3, this.keyName)[0] || "").slice(this.keyName.length + 1);
              return this.canAdd(t3, e3) ? e3 : "";
            }
          };
          function u(t3) {
            const e3 = t3.split("-"), n3 = e3.slice(1).map((t4) => t4[0].toUpperCase() + t4.slice(1)).join("");
            return e3[0] + n3;
          }
          const h = class extends i {
            static keys(t3) {
              return (t3.getAttribute("style") || "").split(";").map((t4) => t4.split(":")[0].trim());
            }
            add(t3, e3) {
              return !!this.canAdd(t3, e3) && (t3.style[u(this.keyName)] = e3, true);
            }
            remove(t3) {
              t3.style[u(this.keyName)] = "", t3.getAttribute("style") || t3.removeAttribute("style");
            }
            value(t3) {
              const e3 = t3.style[u(this.keyName)];
              return this.canAdd(t3, e3) ? e3 : "";
            }
          }, d = class {
            constructor(t3) {
              this.attributes = {}, this.domNode = t3, this.build();
            }
            attribute(t3, e3) {
              e3 ? t3.add(this.domNode, e3) && (null != t3.value(this.domNode) ? this.attributes[t3.attrName] = t3 : delete this.attributes[t3.attrName]) : (t3.remove(this.domNode), delete this.attributes[t3.attrName]);
            }
            build() {
              this.attributes = {};
              const t3 = l.find(this.domNode);
              if (null == t3) return;
              const e3 = i.keys(this.domNode), n3 = c.keys(this.domNode), s2 = h.keys(this.domNode);
              e3.concat(n3).concat(s2).forEach((e4) => {
                const n4 = t3.scroll.query(e4, r2.ATTRIBUTE);
                n4 instanceof i && (this.attributes[n4.attrName] = n4);
              });
            }
            copy(t3) {
              Object.keys(this.attributes).forEach((e3) => {
                const n3 = this.attributes[e3].value(this.domNode);
                t3.format(e3, n3);
              });
            }
            move(t3) {
              this.copy(t3), Object.keys(this.attributes).forEach((t4) => {
                this.attributes[t4].remove(this.domNode);
              }), this.attributes = {};
            }
            values() {
              return Object.keys(this.attributes).reduce((t3, e3) => (t3[e3] = this.attributes[e3].value(this.domNode), t3), {});
            }
          }, f = class {
            constructor(t3, e3) {
              this.scroll = t3, this.domNode = e3, l.blots.set(e3, this), this.prev = null, this.next = null;
            }
            static create(t3) {
              if (null == this.tagName) throw new s("Blot definition missing tagName");
              let e3, n3;
              return Array.isArray(this.tagName) ? ("string" == typeof t3 ? (n3 = t3.toUpperCase(), parseInt(n3, 10).toString() === n3 && (n3 = parseInt(n3, 10))) : "number" == typeof t3 && (n3 = t3), e3 = "number" == typeof n3 ? document.createElement(this.tagName[n3 - 1]) : n3 && this.tagName.indexOf(n3) > -1 ? document.createElement(n3) : document.createElement(this.tagName[0])) : e3 = document.createElement(this.tagName), this.className && e3.classList.add(this.className), e3;
            }
            get statics() {
              return this.constructor;
            }
            attach() {
            }
            clone() {
              const t3 = this.domNode.cloneNode(false);
              return this.scroll.create(t3);
            }
            detach() {
              null != this.parent && this.parent.removeChild(this), l.blots.delete(this.domNode);
            }
            deleteAt(t3, e3) {
              this.isolate(t3, e3).remove();
            }
            formatAt(t3, e3, n3, i2) {
              const s2 = this.isolate(t3, e3);
              if (null != this.scroll.query(n3, r2.BLOT) && i2) s2.wrap(n3, i2);
              else if (null != this.scroll.query(n3, r2.ATTRIBUTE)) {
                const t4 = this.scroll.create(this.statics.scope);
                s2.wrap(t4), t4.format(n3, i2);
              }
            }
            insertAt(t3, e3, n3) {
              const r3 = null == n3 ? this.scroll.create("text", e3) : this.scroll.create(e3, n3), i2 = this.split(t3);
              this.parent.insertBefore(r3, i2 || void 0);
            }
            isolate(t3, e3) {
              const n3 = this.split(t3);
              if (null == n3) throw new Error("Attempt to isolate at end");
              return n3.split(e3), n3;
            }
            length() {
              return 1;
            }
            offset(t3 = this.parent) {
              return null == this.parent || this === t3 ? 0 : this.parent.children.offset(this) + this.parent.offset(t3);
            }
            optimize(t3) {
              this.statics.requiredContainer && !(this.parent instanceof this.statics.requiredContainer) && this.wrap(this.statics.requiredContainer.blotName);
            }
            remove() {
              null != this.domNode.parentNode && this.domNode.parentNode.removeChild(this.domNode), this.detach();
            }
            replaceWith(t3, e3) {
              const n3 = "string" == typeof t3 ? this.scroll.create(t3, e3) : t3;
              return null != this.parent && (this.parent.insertBefore(n3, this.next || void 0), this.remove()), n3;
            }
            split(t3, e3) {
              return 0 === t3 ? this : this.next;
            }
            update(t3, e3) {
            }
            wrap(t3, e3) {
              const n3 = "string" == typeof t3 ? this.scroll.create(t3, e3) : t3;
              if (null != this.parent && this.parent.insertBefore(n3, this.next || void 0), "function" != typeof n3.appendChild) throw new s(`Cannot wrap ${t3}`);
              return n3.appendChild(this), n3;
            }
          };
          f.blotName = "abstract";
          let p = f;
          const g = class extends p {
            static value(t3) {
              return true;
            }
            index(t3, e3) {
              return this.domNode === t3 || this.domNode.compareDocumentPosition(t3) & Node.DOCUMENT_POSITION_CONTAINED_BY ? Math.min(e3, 1) : -1;
            }
            position(t3, e3) {
              let n3 = Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);
              return t3 > 0 && (n3 += 1), [this.parent.domNode, n3];
            }
            value() {
              return { [this.statics.blotName]: this.statics.value(this.domNode) || true };
            }
          };
          g.scope = r2.INLINE_BLOT;
          const m = g;
          class b {
            constructor() {
              this.head = null, this.tail = null, this.length = 0;
            }
            append(...t3) {
              if (this.insertBefore(t3[0], null), t3.length > 1) {
                const e3 = t3.slice(1);
                this.append(...e3);
              }
            }
            at(t3) {
              const e3 = this.iterator();
              let n3 = e3();
              for (; n3 && t3 > 0; ) t3 -= 1, n3 = e3();
              return n3;
            }
            contains(t3) {
              const e3 = this.iterator();
              let n3 = e3();
              for (; n3; ) {
                if (n3 === t3) return true;
                n3 = e3();
              }
              return false;
            }
            indexOf(t3) {
              const e3 = this.iterator();
              let n3 = e3(), r3 = 0;
              for (; n3; ) {
                if (n3 === t3) return r3;
                r3 += 1, n3 = e3();
              }
              return -1;
            }
            insertBefore(t3, e3) {
              null != t3 && (this.remove(t3), t3.next = e3, null != e3 ? (t3.prev = e3.prev, null != e3.prev && (e3.prev.next = t3), e3.prev = t3, e3 === this.head && (this.head = t3)) : null != this.tail ? (this.tail.next = t3, t3.prev = this.tail, this.tail = t3) : (t3.prev = null, this.head = this.tail = t3), this.length += 1);
            }
            offset(t3) {
              let e3 = 0, n3 = this.head;
              for (; null != n3; ) {
                if (n3 === t3) return e3;
                e3 += n3.length(), n3 = n3.next;
              }
              return -1;
            }
            remove(t3) {
              this.contains(t3) && (null != t3.prev && (t3.prev.next = t3.next), null != t3.next && (t3.next.prev = t3.prev), t3 === this.head && (this.head = t3.next), t3 === this.tail && (this.tail = t3.prev), this.length -= 1);
            }
            iterator(t3 = this.head) {
              return () => {
                const e3 = t3;
                return null != t3 && (t3 = t3.next), e3;
              };
            }
            find(t3, e3 = false) {
              const n3 = this.iterator();
              let r3 = n3();
              for (; r3; ) {
                const i2 = r3.length();
                if (t3 < i2 || e3 && t3 === i2 && (null == r3.next || 0 !== r3.next.length())) return [r3, t3];
                t3 -= i2, r3 = n3();
              }
              return [null, 0];
            }
            forEach(t3) {
              const e3 = this.iterator();
              let n3 = e3();
              for (; n3; ) t3(n3), n3 = e3();
            }
            forEachAt(t3, e3, n3) {
              if (e3 <= 0) return;
              const [r3, i2] = this.find(t3);
              let s2 = t3 - i2;
              const o2 = this.iterator(r3);
              let l2 = o2();
              for (; l2 && s2 < t3 + e3; ) {
                const r4 = l2.length();
                t3 > s2 ? n3(l2, t3 - s2, Math.min(e3, s2 + r4 - t3)) : n3(l2, 0, Math.min(r4, t3 + e3 - s2)), s2 += r4, l2 = o2();
              }
            }
            map(t3) {
              return this.reduce((e3, n3) => (e3.push(t3(n3)), e3), []);
            }
            reduce(t3, e3) {
              const n3 = this.iterator();
              let r3 = n3();
              for (; r3; ) e3 = t3(e3, r3), r3 = n3();
              return e3;
            }
          }
          function y(t3, e3) {
            const n3 = e3.find(t3);
            if (n3) return n3;
            try {
              return e3.create(t3);
            } catch {
              const n4 = e3.create(r2.INLINE);
              return Array.from(t3.childNodes).forEach((t4) => {
                n4.domNode.appendChild(t4);
              }), t3.parentNode && t3.parentNode.replaceChild(n4.domNode, t3), n4.attach(), n4;
            }
          }
          const v = class t3 extends p {
            constructor(t4, e3) {
              super(t4, e3), this.uiNode = null, this.build();
            }
            appendChild(t4) {
              this.insertBefore(t4);
            }
            attach() {
              super.attach(), this.children.forEach((t4) => {
                t4.attach();
              });
            }
            attachUI(e3) {
              null != this.uiNode && this.uiNode.remove(), this.uiNode = e3, t3.uiClass && this.uiNode.classList.add(t3.uiClass), this.uiNode.setAttribute("contenteditable", "false"), this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);
            }
            build() {
              this.children = new b(), Array.from(this.domNode.childNodes).filter((t4) => t4 !== this.uiNode).reverse().forEach((t4) => {
                try {
                  const e3 = y(t4, this.scroll);
                  this.insertBefore(e3, this.children.head || void 0);
                } catch (t5) {
                  if (t5 instanceof s) return;
                  throw t5;
                }
              });
            }
            deleteAt(t4, e3) {
              if (0 === t4 && e3 === this.length()) return this.remove();
              this.children.forEachAt(t4, e3, (t5, e4, n3) => {
                t5.deleteAt(e4, n3);
              });
            }
            descendant(e3, n3 = 0) {
              const [r3, i2] = this.children.find(n3);
              return null == e3.blotName && e3(r3) || null != e3.blotName && r3 instanceof e3 ? [r3, i2] : r3 instanceof t3 ? r3.descendant(e3, i2) : [null, -1];
            }
            descendants(e3, n3 = 0, r3 = Number.MAX_VALUE) {
              let i2 = [], s2 = r3;
              return this.children.forEachAt(n3, r3, (n4, r4, o2) => {
                (null == e3.blotName && e3(n4) || null != e3.blotName && n4 instanceof e3) && i2.push(n4), n4 instanceof t3 && (i2 = i2.concat(n4.descendants(e3, r4, s2))), s2 -= o2;
              }), i2;
            }
            detach() {
              this.children.forEach((t4) => {
                t4.detach();
              }), super.detach();
            }
            enforceAllowedChildren() {
              let e3 = false;
              this.children.forEach((n3) => {
                e3 || this.statics.allowedChildren.some((t4) => n3 instanceof t4) || (n3.statics.scope === r2.BLOCK_BLOT ? (null != n3.next && this.splitAfter(n3), null != n3.prev && this.splitAfter(n3.prev), n3.parent.unwrap(), e3 = true) : n3 instanceof t3 ? n3.unwrap() : n3.remove());
              });
            }
            formatAt(t4, e3, n3, r3) {
              this.children.forEachAt(t4, e3, (t5, e4, i2) => {
                t5.formatAt(e4, i2, n3, r3);
              });
            }
            insertAt(t4, e3, n3) {
              const [r3, i2] = this.children.find(t4);
              if (r3) r3.insertAt(i2, e3, n3);
              else {
                const t5 = null == n3 ? this.scroll.create("text", e3) : this.scroll.create(e3, n3);
                this.appendChild(t5);
              }
            }
            insertBefore(t4, e3) {
              null != t4.parent && t4.parent.children.remove(t4);
              let n3 = null;
              this.children.insertBefore(t4, e3 || null), t4.parent = this, null != e3 && (n3 = e3.domNode), (this.domNode.parentNode !== t4.domNode || this.domNode.nextSibling !== n3) && this.domNode.insertBefore(t4.domNode, n3), t4.attach();
            }
            length() {
              return this.children.reduce((t4, e3) => t4 + e3.length(), 0);
            }
            moveChildren(t4, e3) {
              this.children.forEach((n3) => {
                t4.insertBefore(n3, e3);
              });
            }
            optimize(t4) {
              if (super.optimize(t4), this.enforceAllowedChildren(), null != this.uiNode && this.uiNode !== this.domNode.firstChild && this.domNode.insertBefore(this.uiNode, this.domNode.firstChild), 0 === this.children.length) if (null != this.statics.defaultChild) {
                const t5 = this.scroll.create(this.statics.defaultChild.blotName);
                this.appendChild(t5);
              } else this.remove();
            }
            path(e3, n3 = false) {
              const [r3, i2] = this.children.find(e3, n3), s2 = [[this, e3]];
              return r3 instanceof t3 ? s2.concat(r3.path(i2, n3)) : (null != r3 && s2.push([r3, i2]), s2);
            }
            removeChild(t4) {
              this.children.remove(t4);
            }
            replaceWith(e3, n3) {
              const r3 = "string" == typeof e3 ? this.scroll.create(e3, n3) : e3;
              return r3 instanceof t3 && this.moveChildren(r3), super.replaceWith(r3);
            }
            split(t4, e3 = false) {
              if (!e3) {
                if (0 === t4) return this;
                if (t4 === this.length()) return this.next;
              }
              const n3 = this.clone();
              return this.parent && this.parent.insertBefore(n3, this.next || void 0), this.children.forEachAt(t4, this.length(), (t5, r3, i2) => {
                const s2 = t5.split(r3, e3);
                null != s2 && n3.appendChild(s2);
              }), n3;
            }
            splitAfter(t4) {
              const e3 = this.clone();
              for (; null != t4.next; ) e3.appendChild(t4.next);
              return this.parent && this.parent.insertBefore(e3, this.next || void 0), e3;
            }
            unwrap() {
              this.parent && this.moveChildren(this.parent, this.next || void 0), this.remove();
            }
            update(t4, e3) {
              const n3 = [], r3 = [];
              t4.forEach((t5) => {
                t5.target === this.domNode && "childList" === t5.type && (n3.push(...t5.addedNodes), r3.push(...t5.removedNodes));
              }), r3.forEach((t5) => {
                if (null != t5.parentNode && "IFRAME" !== t5.tagName && document.body.compareDocumentPosition(t5) & Node.DOCUMENT_POSITION_CONTAINED_BY) return;
                const e4 = this.scroll.find(t5);
                null != e4 && (null == e4.domNode.parentNode || e4.domNode.parentNode === this.domNode) && e4.detach();
              }), n3.filter((t5) => t5.parentNode === this.domNode && t5 !== this.uiNode).sort((t5, e4) => t5 === e4 ? 0 : t5.compareDocumentPosition(e4) & Node.DOCUMENT_POSITION_FOLLOWING ? 1 : -1).forEach((t5) => {
                let e4 = null;
                null != t5.nextSibling && (e4 = this.scroll.find(t5.nextSibling));
                const n4 = y(t5, this.scroll);
                (n4.next !== e4 || null == n4.next) && (null != n4.parent && n4.parent.removeChild(this), this.insertBefore(n4, e4 || void 0));
              }), this.enforceAllowedChildren();
            }
          };
          v.uiClass = "";
          const A = v, x = class t3 extends A {
            static create(t4) {
              return super.create(t4);
            }
            static formats(e3, n3) {
              const r3 = n3.query(t3.blotName);
              if (null == r3 || e3.tagName !== r3.tagName) {
                if ("string" == typeof this.tagName) return true;
                if (Array.isArray(this.tagName)) return e3.tagName.toLowerCase();
              }
            }
            constructor(t4, e3) {
              super(t4, e3), this.attributes = new d(this.domNode);
            }
            format(e3, n3) {
              if (e3 !== this.statics.blotName || n3) {
                const t4 = this.scroll.query(e3, r2.INLINE);
                if (null == t4) return;
                t4 instanceof i ? this.attributes.attribute(t4, n3) : n3 && (e3 !== this.statics.blotName || this.formats()[e3] !== n3) && this.replaceWith(e3, n3);
              } else this.children.forEach((e4) => {
                e4 instanceof t3 || (e4 = e4.wrap(t3.blotName, true)), this.attributes.copy(e4);
              }), this.unwrap();
            }
            formats() {
              const t4 = this.attributes.values(), e3 = this.statics.formats(this.domNode, this.scroll);
              return null != e3 && (t4[this.statics.blotName] = e3), t4;
            }
            formatAt(t4, e3, n3, i2) {
              null != this.formats()[n3] || this.scroll.query(n3, r2.ATTRIBUTE) ? this.isolate(t4, e3).format(n3, i2) : super.formatAt(t4, e3, n3, i2);
            }
            optimize(e3) {
              super.optimize(e3);
              const n3 = this.formats();
              if (0 === Object.keys(n3).length) return this.unwrap();
              const r3 = this.next;
              r3 instanceof t3 && r3.prev === this && function(t4, e4) {
                if (Object.keys(t4).length !== Object.keys(e4).length) return false;
                for (const n4 in t4) if (t4[n4] !== e4[n4]) return false;
                return true;
              }(n3, r3.formats()) && (r3.moveChildren(this), r3.remove());
            }
            replaceWith(t4, e3) {
              const n3 = super.replaceWith(t4, e3);
              return this.attributes.copy(n3), n3;
            }
            update(t4, e3) {
              super.update(t4, e3), t4.some((t5) => t5.target === this.domNode && "attributes" === t5.type) && this.attributes.build();
            }
            wrap(e3, n3) {
              const r3 = super.wrap(e3, n3);
              return r3 instanceof t3 && this.attributes.move(r3), r3;
            }
          };
          x.allowedChildren = [x, m], x.blotName = "inline", x.scope = r2.INLINE_BLOT, x.tagName = "SPAN";
          const N = x, E = class t3 extends A {
            static create(t4) {
              return super.create(t4);
            }
            static formats(e3, n3) {
              const r3 = n3.query(t3.blotName);
              if (null == r3 || e3.tagName !== r3.tagName) {
                if ("string" == typeof this.tagName) return true;
                if (Array.isArray(this.tagName)) return e3.tagName.toLowerCase();
              }
            }
            constructor(t4, e3) {
              super(t4, e3), this.attributes = new d(this.domNode);
            }
            format(e3, n3) {
              const s2 = this.scroll.query(e3, r2.BLOCK);
              null != s2 && (s2 instanceof i ? this.attributes.attribute(s2, n3) : e3 !== this.statics.blotName || n3 ? n3 && (e3 !== this.statics.blotName || this.formats()[e3] !== n3) && this.replaceWith(e3, n3) : this.replaceWith(t3.blotName));
            }
            formats() {
              const t4 = this.attributes.values(), e3 = this.statics.formats(this.domNode, this.scroll);
              return null != e3 && (t4[this.statics.blotName] = e3), t4;
            }
            formatAt(t4, e3, n3, i2) {
              null != this.scroll.query(n3, r2.BLOCK) ? this.format(n3, i2) : super.formatAt(t4, e3, n3, i2);
            }
            insertAt(t4, e3, n3) {
              if (null == n3 || null != this.scroll.query(e3, r2.INLINE)) super.insertAt(t4, e3, n3);
              else {
                const r3 = this.split(t4);
                if (null == r3) throw new Error("Attempt to insertAt after block boundaries");
                {
                  const t5 = this.scroll.create(e3, n3);
                  r3.parent.insertBefore(t5, r3);
                }
              }
            }
            replaceWith(t4, e3) {
              const n3 = super.replaceWith(t4, e3);
              return this.attributes.copy(n3), n3;
            }
            update(t4, e3) {
              super.update(t4, e3), t4.some((t5) => t5.target === this.domNode && "attributes" === t5.type) && this.attributes.build();
            }
          };
          E.blotName = "block", E.scope = r2.BLOCK_BLOT, E.tagName = "P", E.allowedChildren = [N, E, m];
          const w = E, q = class extends A {
            checkMerge() {
              return null !== this.next && this.next.statics.blotName === this.statics.blotName;
            }
            deleteAt(t3, e3) {
              super.deleteAt(t3, e3), this.enforceAllowedChildren();
            }
            formatAt(t3, e3, n3, r3) {
              super.formatAt(t3, e3, n3, r3), this.enforceAllowedChildren();
            }
            insertAt(t3, e3, n3) {
              super.insertAt(t3, e3, n3), this.enforceAllowedChildren();
            }
            optimize(t3) {
              super.optimize(t3), this.children.length > 0 && null != this.next && this.checkMerge() && (this.next.moveChildren(this), this.next.remove());
            }
          };
          q.blotName = "container", q.scope = r2.BLOCK_BLOT;
          const k = q, _ = class extends m {
            static formats(t3, e3) {
            }
            format(t3, e3) {
              super.formatAt(0, this.length(), t3, e3);
            }
            formatAt(t3, e3, n3, r3) {
              0 === t3 && e3 === this.length() ? this.format(n3, r3) : super.formatAt(t3, e3, n3, r3);
            }
            formats() {
              return this.statics.formats(this.domNode, this.scroll);
            }
          }, L = { attributes: true, characterData: true, characterDataOldValue: true, childList: true, subtree: true }, S = class extends A {
            constructor(t3, e3) {
              super(null, e3), this.registry = t3, this.scroll = this, this.build(), this.observer = new MutationObserver((t4) => {
                this.update(t4);
              }), this.observer.observe(this.domNode, L), this.attach();
            }
            create(t3, e3) {
              return this.registry.create(this, t3, e3);
            }
            find(t3, e3 = false) {
              const n3 = this.registry.find(t3, e3);
              return n3 ? n3.scroll === this ? n3 : e3 ? this.find(n3.scroll.domNode.parentNode, true) : null : null;
            }
            query(t3, e3 = r2.ANY) {
              return this.registry.query(t3, e3);
            }
            register(...t3) {
              return this.registry.register(...t3);
            }
            build() {
              null != this.scroll && super.build();
            }
            detach() {
              super.detach(), this.observer.disconnect();
            }
            deleteAt(t3, e3) {
              this.update(), 0 === t3 && e3 === this.length() ? this.children.forEach((t4) => {
                t4.remove();
              }) : super.deleteAt(t3, e3);
            }
            formatAt(t3, e3, n3, r3) {
              this.update(), super.formatAt(t3, e3, n3, r3);
            }
            insertAt(t3, e3, n3) {
              this.update(), super.insertAt(t3, e3, n3);
            }
            optimize(t3 = [], e3 = {}) {
              super.optimize(e3);
              const n3 = e3.mutationsMap || /* @__PURE__ */ new WeakMap();
              let r3 = Array.from(this.observer.takeRecords());
              for (; r3.length > 0; ) t3.push(r3.pop());
              const i2 = (t4, e4 = true) => {
                null == t4 || t4 === this || null != t4.domNode.parentNode && (n3.has(t4.domNode) || n3.set(t4.domNode, []), e4 && i2(t4.parent));
              }, s2 = (t4) => {
                n3.has(t4.domNode) && (t4 instanceof A && t4.children.forEach(s2), n3.delete(t4.domNode), t4.optimize(e3));
              };
              let o2 = t3;
              for (let e4 = 0; o2.length > 0; e4 += 1) {
                if (e4 >= 100) throw new Error("[Parchment] Maximum optimize iterations reached");
                for (o2.forEach((t4) => {
                  const e5 = this.find(t4.target, true);
                  null != e5 && (e5.domNode === t4.target && ("childList" === t4.type ? (i2(this.find(t4.previousSibling, false)), Array.from(t4.addedNodes).forEach((t5) => {
                    const e6 = this.find(t5, false);
                    i2(e6, false), e6 instanceof A && e6.children.forEach((t6) => {
                      i2(t6, false);
                    });
                  })) : "attributes" === t4.type && i2(e5.prev)), i2(e5));
                }), this.children.forEach(s2), o2 = Array.from(this.observer.takeRecords()), r3 = o2.slice(); r3.length > 0; ) t3.push(r3.pop());
              }
            }
            update(t3, e3 = {}) {
              t3 = t3 || this.observer.takeRecords();
              const n3 = /* @__PURE__ */ new WeakMap();
              t3.map((t4) => {
                const e4 = this.find(t4.target, true);
                return null == e4 ? null : n3.has(e4.domNode) ? (n3.get(e4.domNode).push(t4), null) : (n3.set(e4.domNode, [t4]), e4);
              }).forEach((t4) => {
                null != t4 && t4 !== this && n3.has(t4.domNode) && t4.update(n3.get(t4.domNode) || [], e3);
              }), e3.mutationsMap = n3, n3.has(this.domNode) && super.update(n3.get(this.domNode), e3), this.optimize(t3, e3);
            }
          };
          S.blotName = "scroll", S.defaultChild = w, S.allowedChildren = [w, k], S.scope = r2.BLOCK_BLOT, S.tagName = "DIV";
          const O = S, T = class t3 extends m {
            static create(t4) {
              return document.createTextNode(t4);
            }
            static value(t4) {
              return t4.data;
            }
            constructor(t4, e3) {
              super(t4, e3), this.text = this.statics.value(this.domNode);
            }
            deleteAt(t4, e3) {
              this.domNode.data = this.text = this.text.slice(0, t4) + this.text.slice(t4 + e3);
            }
            index(t4, e3) {
              return this.domNode === t4 ? e3 : -1;
            }
            insertAt(t4, e3, n3) {
              null == n3 ? (this.text = this.text.slice(0, t4) + e3 + this.text.slice(t4), this.domNode.data = this.text) : super.insertAt(t4, e3, n3);
            }
            length() {
              return this.text.length;
            }
            optimize(e3) {
              super.optimize(e3), this.text = this.statics.value(this.domNode), 0 === this.text.length ? this.remove() : this.next instanceof t3 && this.next.prev === this && (this.insertAt(this.length(), this.next.value()), this.next.remove());
            }
            position(t4, e3 = false) {
              return [this.domNode, t4];
            }
            split(t4, e3 = false) {
              if (!e3) {
                if (0 === t4) return this;
                if (t4 === this.length()) return this.next;
              }
              const n3 = this.scroll.create(this.domNode.splitText(t4));
              return this.parent.insertBefore(n3, this.next || void 0), this.text = this.statics.value(this.domNode), n3;
            }
            update(t4, e3) {
              t4.some((t5) => "characterData" === t5.type && t5.target === this.domNode) && (this.text = this.statics.value(this.domNode));
            }
            value() {
              return this.text;
            }
          };
          T.blotName = "text", T.scope = r2.INLINE_BLOT;
          const j = T;
        } }, e = {};
        function n(r2) {
          var i = e[r2];
          if (void 0 !== i) return i.exports;
          var s = e[r2] = { id: r2, loaded: false, exports: {} };
          return t[r2](s, s.exports, n), s.loaded = true, s.exports;
        }
        n.n = function(t2) {
          var e2 = t2 && t2.__esModule ? function() {
            return t2.default;
          } : function() {
            return t2;
          };
          return n.d(e2, { a: e2 }), e2;
        }, n.d = function(t2, e2) {
          for (var r2 in e2) n.o(e2, r2) && !n.o(t2, r2) && Object.defineProperty(t2, r2, { enumerable: true, get: e2[r2] });
        }, n.g = function() {
          if ("object" == typeof globalThis) return globalThis;
          try {
            return this || new Function("return this")();
          } catch (t2) {
            if ("object" == typeof window) return window;
          }
        }(), n.o = function(t2, e2) {
          return Object.prototype.hasOwnProperty.call(t2, e2);
        }, n.r = function(t2) {
          "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t2, "__esModule", { value: true });
        }, n.nmd = function(t2) {
          return t2.paths = [], t2.children || (t2.children = []), t2;
        };
        var r = {};
        return function() {
          "use strict";
          n.d(r, { default: function() {
            return It;
          } });
          var t2 = n(3729), e2 = n(8276), i = n(7912), s = n(6003);
          class o extends s.ClassAttributor {
            add(t3, e3) {
              let n2 = 0;
              if ("+1" === e3 || "-1" === e3) {
                const r2 = this.value(t3) || 0;
                n2 = "+1" === e3 ? r2 + 1 : r2 - 1;
              } else "number" == typeof e3 && (n2 = e3);
              return 0 === n2 ? (this.remove(t3), true) : super.add(t3, n2.toString());
            }
            canAdd(t3, e3) {
              return super.canAdd(t3, e3) || super.canAdd(t3, parseInt(e3, 10));
            }
            value(t3) {
              return parseInt(super.value(t3), 10) || void 0;
            }
          }
          var l = new o("indent", "ql-indent", { scope: s.Scope.BLOCK, whitelist: [1, 2, 3, 4, 5, 6, 7, 8] }), a = n(9698);
          class c extends a.Ay {
            static blotName = "blockquote";
            static tagName = "blockquote";
          }
          var u = c;
          class h extends a.Ay {
            static blotName = "header";
            static tagName = ["H1", "H2", "H3", "H4", "H5", "H6"];
            static formats(t3) {
              return this.tagName.indexOf(t3.tagName) + 1;
            }
          }
          var d = h, f = n(580), p = n(6142);
          class g extends f.A {
          }
          g.blotName = "list-container", g.tagName = "OL";
          class m extends a.Ay {
            static create(t3) {
              const e3 = super.create();
              return e3.setAttribute("data-list", t3), e3;
            }
            static formats(t3) {
              return t3.getAttribute("data-list") || void 0;
            }
            static register() {
              p.Ay.register(g);
            }
            constructor(t3, e3) {
              super(t3, e3);
              const n2 = e3.ownerDocument.createElement("span"), r2 = (n3) => {
                if (!t3.isEnabled()) return;
                const r3 = this.statics.formats(e3, t3);
                "checked" === r3 ? (this.format("list", "unchecked"), n3.preventDefault()) : "unchecked" === r3 && (this.format("list", "checked"), n3.preventDefault());
              };
              n2.addEventListener("mousedown", r2), n2.addEventListener("touchstart", r2), this.attachUI(n2);
            }
            format(t3, e3) {
              t3 === this.statics.blotName && e3 ? this.domNode.setAttribute("data-list", e3) : super.format(t3, e3);
            }
          }
          m.blotName = "list", m.tagName = "LI", g.allowedChildren = [m], m.requiredContainer = g;
          var b = n(9541), y = n(8638), v = n(6772), A = n(664), x = n(4850);
          class N extends x.A {
            static blotName = "bold";
            static tagName = ["STRONG", "B"];
            static create() {
              return super.create();
            }
            static formats() {
              return true;
            }
            optimize(t3) {
              super.optimize(t3), this.domNode.tagName !== this.statics.tagName[0] && this.replaceWith(this.statics.blotName);
            }
          }
          var E = N;
          class w extends x.A {
            static blotName = "link";
            static tagName = "A";
            static SANITIZED_URL = "about:blank";
            static PROTOCOL_WHITELIST = ["http", "https", "mailto", "tel", "sms"];
            static create(t3) {
              const e3 = super.create(t3);
              return e3.setAttribute("href", this.sanitize(t3)), e3.setAttribute("rel", "noopener noreferrer"), e3.setAttribute("target", "_blank"), e3;
            }
            static formats(t3) {
              return t3.getAttribute("href");
            }
            static sanitize(t3) {
              return q(t3, this.PROTOCOL_WHITELIST) ? t3 : this.SANITIZED_URL;
            }
            format(t3, e3) {
              t3 === this.statics.blotName && e3 ? this.domNode.setAttribute("href", this.constructor.sanitize(e3)) : super.format(t3, e3);
            }
          }
          function q(t3, e3) {
            const n2 = document.createElement("a");
            n2.href = t3;
            const r2 = n2.href.slice(0, n2.href.indexOf(":"));
            return e3.indexOf(r2) > -1;
          }
          class k extends x.A {
            static blotName = "script";
            static tagName = ["SUB", "SUP"];
            static create(t3) {
              return "super" === t3 ? document.createElement("sup") : "sub" === t3 ? document.createElement("sub") : super.create(t3);
            }
            static formats(t3) {
              return "SUB" === t3.tagName ? "sub" : "SUP" === t3.tagName ? "super" : void 0;
            }
          }
          var _ = k;
          class L extends x.A {
            static blotName = "underline";
            static tagName = "U";
          }
          var S = L, O = n(746);
          class T extends O.A {
            static blotName = "formula";
            static className = "ql-formula";
            static tagName = "SPAN";
            static create(t3) {
              if (null == window.katex) throw new Error("Formula module requires KaTeX.");
              const e3 = super.create(t3);
              return "string" == typeof t3 && (window.katex.render(t3, e3, { throwOnError: false, errorColor: "#f00" }), e3.setAttribute("data-value", t3)), e3;
            }
            static value(t3) {
              return t3.getAttribute("data-value");
            }
            html() {
              const { formula: t3 } = this.value();
              return `<span>${t3}</span>`;
            }
          }
          var j = T;
          const C = ["alt", "height", "width"];
          class R extends s.EmbedBlot {
            static blotName = "image";
            static tagName = "IMG";
            static create(t3) {
              const e3 = super.create(t3);
              return "string" == typeof t3 && e3.setAttribute("src", this.sanitize(t3)), e3;
            }
            static formats(t3) {
              return C.reduce((e3, n2) => (t3.hasAttribute(n2) && (e3[n2] = t3.getAttribute(n2)), e3), {});
            }
            static match(t3) {
              return /\.(jpe?g|gif|png)$/.test(t3) || /^data:image\/.+;base64/.test(t3);
            }
            static sanitize(t3) {
              return q(t3, ["http", "https", "data"]) ? t3 : "//:0";
            }
            static value(t3) {
              return t3.getAttribute("src");
            }
            format(t3, e3) {
              C.indexOf(t3) > -1 ? e3 ? this.domNode.setAttribute(t3, e3) : this.domNode.removeAttribute(t3) : super.format(t3, e3);
            }
          }
          var I = R;
          const B = ["height", "width"];
          class M extends a.zo {
            static blotName = "video";
            static className = "ql-video";
            static tagName = "IFRAME";
            static create(t3) {
              const e3 = super.create(t3);
              return e3.setAttribute("frameborder", "0"), e3.setAttribute("allowfullscreen", "true"), e3.setAttribute("src", this.sanitize(t3)), e3;
            }
            static formats(t3) {
              return B.reduce((e3, n2) => (t3.hasAttribute(n2) && (e3[n2] = t3.getAttribute(n2)), e3), {});
            }
            static sanitize(t3) {
              return w.sanitize(t3);
            }
            static value(t3) {
              return t3.getAttribute("src");
            }
            format(t3, e3) {
              B.indexOf(t3) > -1 ? e3 ? this.domNode.setAttribute(t3, e3) : this.domNode.removeAttribute(t3) : super.format(t3, e3);
            }
            html() {
              const { video: t3 } = this.value();
              return `<a href="${t3}">${t3}</a>`;
            }
          }
          var U = M, D = n(9404), P = n(5232), z = n.n(P), F = n(4266), H = n(3036), $ = n(4541), V = n(5508), K = n(584);
          const W = new s.ClassAttributor("code-token", "hljs", { scope: s.Scope.INLINE });
          class Z extends x.A {
            static formats(t3, e3) {
              for (; null != t3 && t3 !== e3.domNode; ) {
                if (t3.classList && t3.classList.contains(D.Ay.className)) return super.formats(t3, e3);
                t3 = t3.parentNode;
              }
            }
            constructor(t3, e3, n2) {
              super(t3, e3, n2), W.add(this.domNode, n2);
            }
            format(t3, e3) {
              t3 !== Z.blotName ? super.format(t3, e3) : e3 ? W.add(this.domNode, e3) : (W.remove(this.domNode), this.domNode.classList.remove(this.statics.className));
            }
            optimize() {
              super.optimize(...arguments), W.value(this.domNode) || this.unwrap();
            }
          }
          Z.blotName = "code-token", Z.className = "ql-token";
          class G extends D.Ay {
            static create(t3) {
              const e3 = super.create(t3);
              return "string" == typeof t3 && e3.setAttribute("data-language", t3), e3;
            }
            static formats(t3) {
              return t3.getAttribute("data-language") || "plain";
            }
            static register() {
            }
            format(t3, e3) {
              t3 === this.statics.blotName && e3 ? this.domNode.setAttribute("data-language", e3) : super.format(t3, e3);
            }
            replaceWith(t3, e3) {
              return this.formatAt(0, this.length(), Z.blotName, false), super.replaceWith(t3, e3);
            }
          }
          class X extends D.EJ {
            attach() {
              super.attach(), this.forceNext = false, this.scroll.emitMount(this);
            }
            format(t3, e3) {
              t3 === G.blotName && (this.forceNext = true, this.children.forEach((n2) => {
                n2.format(t3, e3);
              }));
            }
            formatAt(t3, e3, n2, r2) {
              n2 === G.blotName && (this.forceNext = true), super.formatAt(t3, e3, n2, r2);
            }
            highlight(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              if (null == this.children.head) return;
              const n2 = `${Array.from(this.domNode.childNodes).filter((t4) => t4 !== this.uiNode).map((t4) => t4.textContent).join("\n")}
`, r2 = G.formats(this.children.head.domNode);
              if (e3 || this.forceNext || this.cachedText !== n2) {
                if (n2.trim().length > 0 || null == this.cachedText) {
                  const e4 = this.children.reduce((t4, e5) => t4.concat((0, a.mG)(e5, false)), new (z())()), i2 = t3(n2, r2);
                  e4.diff(i2).reduce((t4, e5) => {
                    let { retain: n3, attributes: r3 } = e5;
                    return n3 ? (r3 && Object.keys(r3).forEach((e6) => {
                      [G.blotName, Z.blotName].includes(e6) && this.formatAt(t4, n3, e6, r3[e6]);
                    }), t4 + n3) : t4;
                  }, 0);
                }
                this.cachedText = n2, this.forceNext = false;
              }
            }
            html(t3, e3) {
              const [n2] = this.children.find(t3);
              return `<pre data-language="${n2 ? G.formats(n2.domNode) : "plain"}">
${(0, V.X)(this.code(t3, e3))}
</pre>`;
            }
            optimize(t3) {
              if (super.optimize(t3), null != this.parent && null != this.children.head && null != this.uiNode) {
                const t4 = G.formats(this.children.head.domNode);
                t4 !== this.uiNode.value && (this.uiNode.value = t4);
              }
            }
          }
          X.allowedChildren = [G], G.requiredContainer = X, G.allowedChildren = [Z, $.A, V.A, H.A];
          class Q extends F.A {
            static register() {
              p.Ay.register(Z, true), p.Ay.register(G, true), p.Ay.register(X, true);
            }
            constructor(t3, e3) {
              if (super(t3, e3), null == this.options.hljs) throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");
              this.languages = this.options.languages.reduce((t4, e4) => {
                let { key: n2 } = e4;
                return t4[n2] = true, t4;
              }, {}), this.highlightBlot = this.highlightBlot.bind(this), this.initListener(), this.initTimer();
            }
            initListener() {
              this.quill.on(p.Ay.events.SCROLL_BLOT_MOUNT, (t3) => {
                if (!(t3 instanceof X)) return;
                const e3 = this.quill.root.ownerDocument.createElement("select");
                this.options.languages.forEach((t4) => {
                  let { key: n2, label: r2 } = t4;
                  const i2 = e3.ownerDocument.createElement("option");
                  i2.textContent = r2, i2.setAttribute("value", n2), e3.appendChild(i2);
                }), e3.addEventListener("change", () => {
                  t3.format(G.blotName, e3.value), this.quill.root.focus(), this.highlight(t3, true);
                }), null == t3.uiNode && (t3.attachUI(e3), t3.children.head && (e3.value = G.formats(t3.children.head.domNode)));
              });
            }
            initTimer() {
              let t3 = null;
              this.quill.on(p.Ay.events.SCROLL_OPTIMIZE, () => {
                t3 && clearTimeout(t3), t3 = setTimeout(() => {
                  this.highlight(), t3 = null;
                }, this.options.interval);
              });
            }
            highlight() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : null, e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              if (this.quill.selection.composing) return;
              this.quill.update(p.Ay.sources.USER);
              const n2 = this.quill.getSelection();
              (null == t3 ? this.quill.scroll.descendants(X) : [t3]).forEach((t4) => {
                t4.highlight(this.highlightBlot, e3);
              }), this.quill.update(p.Ay.sources.SILENT), null != n2 && this.quill.setSelection(n2, p.Ay.sources.SILENT);
            }
            highlightBlot(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "plain";
              if (e3 = this.languages[e3] ? e3 : "plain", "plain" === e3) return (0, V.X)(t3).split("\n").reduce((t4, n3, r2) => (0 !== r2 && t4.insert("\n", { [D.Ay.blotName]: e3 }), t4.insert(n3)), new (z())());
              const n2 = this.quill.root.ownerDocument.createElement("div");
              return n2.classList.add(D.Ay.className), n2.innerHTML = ((t4, e4, n3) => {
                if ("string" == typeof t4.versionString) {
                  const r2 = t4.versionString.split(".")[0];
                  if (parseInt(r2, 10) >= 11) return t4.highlight(n3, { language: e4 }).value;
                }
                return t4.highlight(e4, n3).value;
              })(this.options.hljs, e3, t3), (0, K.hV)(this.quill.scroll, n2, [(t4, e4) => {
                const n3 = W.value(t4);
                return n3 ? e4.compose(new (z())().retain(e4.length(), { [Z.blotName]: n3 })) : e4;
              }], [(t4, n3) => t4.data.split("\n").reduce((t5, n4, r2) => (0 !== r2 && t5.insert("\n", { [D.Ay.blotName]: e3 }), t5.insert(n4)), n3)], /* @__PURE__ */ new WeakMap());
            }
          }
          Q.DEFAULTS = { hljs: window.hljs, interval: 1e3, languages: [{ key: "plain", label: "Plain" }, { key: "bash", label: "Bash" }, { key: "cpp", label: "C++" }, { key: "cs", label: "C#" }, { key: "css", label: "CSS" }, { key: "diff", label: "Diff" }, { key: "xml", label: "HTML/XML" }, { key: "java", label: "Java" }, { key: "javascript", label: "JavaScript" }, { key: "markdown", label: "Markdown" }, { key: "php", label: "PHP" }, { key: "python", label: "Python" }, { key: "ruby", label: "Ruby" }, { key: "sql", label: "SQL" }] };
          class J extends a.Ay {
            static blotName = "table";
            static tagName = "TD";
            static create(t3) {
              const e3 = super.create();
              return t3 ? e3.setAttribute("data-row", t3) : e3.setAttribute("data-row", nt()), e3;
            }
            static formats(t3) {
              if (t3.hasAttribute("data-row")) return t3.getAttribute("data-row");
            }
            cellOffset() {
              return this.parent ? this.parent.children.indexOf(this) : -1;
            }
            format(t3, e3) {
              t3 === J.blotName && e3 ? this.domNode.setAttribute("data-row", e3) : super.format(t3, e3);
            }
            row() {
              return this.parent;
            }
            rowOffset() {
              return this.row() ? this.row().rowOffset() : -1;
            }
            table() {
              return this.row() && this.row().table();
            }
          }
          class Y extends f.A {
            static blotName = "table-row";
            static tagName = "TR";
            checkMerge() {
              if (super.checkMerge() && null != this.next.children.head) {
                const t3 = this.children.head.formats(), e3 = this.children.tail.formats(), n2 = this.next.children.head.formats(), r2 = this.next.children.tail.formats();
                return t3.table === e3.table && t3.table === n2.table && t3.table === r2.table;
              }
              return false;
            }
            optimize(t3) {
              super.optimize(t3), this.children.forEach((t4) => {
                if (null == t4.next) return;
                const e3 = t4.formats(), n2 = t4.next.formats();
                if (e3.table !== n2.table) {
                  const e4 = this.splitAfter(t4);
                  e4 && e4.optimize(), this.prev && this.prev.optimize();
                }
              });
            }
            rowOffset() {
              return this.parent ? this.parent.children.indexOf(this) : -1;
            }
            table() {
              return this.parent && this.parent.parent;
            }
          }
          class tt extends f.A {
            static blotName = "table-body";
            static tagName = "TBODY";
          }
          class et extends f.A {
            static blotName = "table-container";
            static tagName = "TABLE";
            balanceCells() {
              const t3 = this.descendants(Y), e3 = t3.reduce((t4, e4) => Math.max(e4.children.length, t4), 0);
              t3.forEach((t4) => {
                new Array(e3 - t4.children.length).fill(0).forEach(() => {
                  let e4;
                  null != t4.children.head && (e4 = J.formats(t4.children.head.domNode));
                  const n2 = this.scroll.create(J.blotName, e4);
                  t4.appendChild(n2), n2.optimize();
                });
              });
            }
            cells(t3) {
              return this.rows().map((e3) => e3.children.at(t3));
            }
            deleteColumn(t3) {
              const [e3] = this.descendant(tt);
              null != e3 && null != e3.children.head && e3.children.forEach((e4) => {
                const n2 = e4.children.at(t3);
                null != n2 && n2.remove();
              });
            }
            insertColumn(t3) {
              const [e3] = this.descendant(tt);
              null != e3 && null != e3.children.head && e3.children.forEach((e4) => {
                const n2 = e4.children.at(t3), r2 = J.formats(e4.children.head.domNode), i2 = this.scroll.create(J.blotName, r2);
                e4.insertBefore(i2, n2);
              });
            }
            insertRow(t3) {
              const [e3] = this.descendant(tt);
              if (null == e3 || null == e3.children.head) return;
              const n2 = nt(), r2 = this.scroll.create(Y.blotName);
              e3.children.head.children.forEach(() => {
                const t4 = this.scroll.create(J.blotName, n2);
                r2.appendChild(t4);
              });
              const i2 = e3.children.at(t3);
              e3.insertBefore(r2, i2);
            }
            rows() {
              const t3 = this.children.head;
              return null == t3 ? [] : t3.children.map((t4) => t4);
            }
          }
          function nt() {
            return `row-${Math.random().toString(36).slice(2, 6)}`;
          }
          et.allowedChildren = [tt], tt.requiredContainer = et, tt.allowedChildren = [Y], Y.requiredContainer = tt, Y.allowedChildren = [J], J.requiredContainer = Y;
          class rt extends F.A {
            static register() {
              p.Ay.register(J), p.Ay.register(Y), p.Ay.register(tt), p.Ay.register(et);
            }
            constructor() {
              super(...arguments), this.listenBalanceCells();
            }
            balanceTables() {
              this.quill.scroll.descendants(et).forEach((t3) => {
                t3.balanceCells();
              });
            }
            deleteColumn() {
              const [t3, , e3] = this.getTable();
              null != e3 && (t3.deleteColumn(e3.cellOffset()), this.quill.update(p.Ay.sources.USER));
            }
            deleteRow() {
              const [, t3] = this.getTable();
              null != t3 && (t3.remove(), this.quill.update(p.Ay.sources.USER));
            }
            deleteTable() {
              const [t3] = this.getTable();
              if (null == t3) return;
              const e3 = t3.offset();
              t3.remove(), this.quill.update(p.Ay.sources.USER), this.quill.setSelection(e3, p.Ay.sources.SILENT);
            }
            getTable() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.quill.getSelection();
              if (null == t3) return [null, null, null, -1];
              const [e3, n2] = this.quill.getLine(t3.index);
              if (null == e3 || e3.statics.blotName !== J.blotName) return [null, null, null, -1];
              const r2 = e3.parent;
              return [r2.parent.parent, r2, e3, n2];
            }
            insertColumn(t3) {
              const e3 = this.quill.getSelection();
              if (!e3) return;
              const [n2, r2, i2] = this.getTable(e3);
              if (null == i2) return;
              const s2 = i2.cellOffset();
              n2.insertColumn(s2 + t3), this.quill.update(p.Ay.sources.USER);
              let o2 = r2.rowOffset();
              0 === t3 && (o2 += 1), this.quill.setSelection(e3.index + o2, e3.length, p.Ay.sources.SILENT);
            }
            insertColumnLeft() {
              this.insertColumn(0);
            }
            insertColumnRight() {
              this.insertColumn(1);
            }
            insertRow(t3) {
              const e3 = this.quill.getSelection();
              if (!e3) return;
              const [n2, r2, i2] = this.getTable(e3);
              if (null == i2) return;
              const s2 = r2.rowOffset();
              n2.insertRow(s2 + t3), this.quill.update(p.Ay.sources.USER), t3 > 0 ? this.quill.setSelection(e3, p.Ay.sources.SILENT) : this.quill.setSelection(e3.index + r2.children.length, e3.length, p.Ay.sources.SILENT);
            }
            insertRowAbove() {
              this.insertRow(0);
            }
            insertRowBelow() {
              this.insertRow(1);
            }
            insertTable(t3, e3) {
              const n2 = this.quill.getSelection();
              if (null == n2) return;
              const r2 = new Array(t3).fill(0).reduce((t4) => {
                const n3 = new Array(e3).fill("\n").join("");
                return t4.insert(n3, { table: nt() });
              }, new (z())().retain(n2.index));
              this.quill.updateContents(r2, p.Ay.sources.USER), this.quill.setSelection(n2.index, p.Ay.sources.SILENT), this.balanceTables();
            }
            listenBalanceCells() {
              this.quill.on(p.Ay.events.SCROLL_OPTIMIZE, (t3) => {
                t3.some((t4) => !!["TD", "TR", "TBODY", "TABLE"].includes(t4.target.tagName) && (this.quill.once(p.Ay.events.TEXT_CHANGE, (t5, e3, n2) => {
                  n2 === p.Ay.sources.USER && this.balanceTables();
                }), true));
              });
            }
          }
          var it = rt;
          const st = (0, n(6078).A)("quill:toolbar");
          class ot extends F.A {
            constructor(t3, e3) {
              if (super(t3, e3), Array.isArray(this.options.container)) {
                const e4 = document.createElement("div");
                e4.setAttribute("role", "toolbar"), function(t4, e5) {
                  Array.isArray(e5[0]) || (e5 = [e5]), e5.forEach((e6) => {
                    const n2 = document.createElement("span");
                    n2.classList.add("ql-formats"), e6.forEach((t5) => {
                      if ("string" == typeof t5) lt(n2, t5);
                      else {
                        const e7 = Object.keys(t5)[0], r2 = t5[e7];
                        Array.isArray(r2) ? function(t6, e8, n3) {
                          const r3 = document.createElement("select");
                          r3.classList.add(`ql-${e8}`), n3.forEach((t7) => {
                            const e9 = document.createElement("option");
                            false !== t7 ? e9.setAttribute("value", String(t7)) : e9.setAttribute("selected", "selected"), r3.appendChild(e9);
                          }), t6.appendChild(r3);
                        }(n2, e7, r2) : lt(n2, e7, r2);
                      }
                    }), t4.appendChild(n2);
                  });
                }(e4, this.options.container), t3.container?.parentNode?.insertBefore(e4, t3.container), this.container = e4;
              } else "string" == typeof this.options.container ? this.container = document.querySelector(this.options.container) : this.container = this.options.container;
              this.container instanceof HTMLElement ? (this.container.classList.add("ql-toolbar"), this.controls = [], this.handlers = {}, this.options.handlers && Object.keys(this.options.handlers).forEach((t4) => {
                const e4 = this.options.handlers?.[t4];
                e4 && this.addHandler(t4, e4);
              }), Array.from(this.container.querySelectorAll("button, select")).forEach((t4) => {
                this.attach(t4);
              }), this.quill.on(p.Ay.events.EDITOR_CHANGE, () => {
                const [t4] = this.quill.selection.getRange();
                this.update(t4);
              })) : st.error("Container required for toolbar", this.options);
            }
            addHandler(t3, e3) {
              this.handlers[t3] = e3;
            }
            attach(t3) {
              let e3 = Array.from(t3.classList).find((t4) => 0 === t4.indexOf("ql-"));
              if (!e3) return;
              if (e3 = e3.slice(3), "BUTTON" === t3.tagName && t3.setAttribute("type", "button"), null == this.handlers[e3] && null == this.quill.scroll.query(e3)) return void st.warn("ignoring attaching to nonexistent format", e3, t3);
              const n2 = "SELECT" === t3.tagName ? "change" : "click";
              t3.addEventListener(n2, (n3) => {
                let r2;
                if ("SELECT" === t3.tagName) {
                  if (t3.selectedIndex < 0) return;
                  const e4 = t3.options[t3.selectedIndex];
                  r2 = !e4.hasAttribute("selected") && (e4.value || false);
                } else r2 = !t3.classList.contains("ql-active") && (t3.value || !t3.hasAttribute("value")), n3.preventDefault();
                this.quill.focus();
                const [i2] = this.quill.selection.getRange();
                if (null != this.handlers[e3]) this.handlers[e3].call(this, r2);
                else if (this.quill.scroll.query(e3).prototype instanceof s.EmbedBlot) {
                  if (r2 = prompt(`Enter ${e3}`), !r2) return;
                  this.quill.updateContents(new (z())().retain(i2.index).delete(i2.length).insert({ [e3]: r2 }), p.Ay.sources.USER);
                } else this.quill.format(e3, r2, p.Ay.sources.USER);
                this.update(i2);
              }), this.controls.push([e3, t3]);
            }
            update(t3) {
              const e3 = null == t3 ? {} : this.quill.getFormat(t3);
              this.controls.forEach((n2) => {
                const [r2, i2] = n2;
                if ("SELECT" === i2.tagName) {
                  let n3 = null;
                  if (null == t3) n3 = null;
                  else if (null == e3[r2]) n3 = i2.querySelector("option[selected]");
                  else if (!Array.isArray(e3[r2])) {
                    let t4 = e3[r2];
                    "string" == typeof t4 && (t4 = t4.replace(/"/g, '\\"')), n3 = i2.querySelector(`option[value="${t4}"]`);
                  }
                  null == n3 ? (i2.value = "", i2.selectedIndex = -1) : n3.selected = true;
                } else if (null == t3) i2.classList.remove("ql-active"), i2.setAttribute("aria-pressed", "false");
                else if (i2.hasAttribute("value")) {
                  const t4 = e3[r2], n3 = t4 === i2.getAttribute("value") || null != t4 && t4.toString() === i2.getAttribute("value") || null == t4 && !i2.getAttribute("value");
                  i2.classList.toggle("ql-active", n3), i2.setAttribute("aria-pressed", n3.toString());
                } else {
                  const t4 = null != e3[r2];
                  i2.classList.toggle("ql-active", t4), i2.setAttribute("aria-pressed", t4.toString());
                }
              });
            }
          }
          function lt(t3, e3, n2) {
            const r2 = document.createElement("button");
            r2.setAttribute("type", "button"), r2.classList.add(`ql-${e3}`), r2.setAttribute("aria-pressed", "false"), null != n2 ? (r2.value = n2, r2.setAttribute("aria-label", `${e3}: ${n2}`)) : r2.setAttribute("aria-label", e3), t3.appendChild(r2);
          }
          ot.DEFAULTS = {}, ot.DEFAULTS = { container: null, handlers: { clean() {
            const t3 = this.quill.getSelection();
            if (null != t3) if (0 === t3.length) {
              const t4 = this.quill.getFormat();
              Object.keys(t4).forEach((t5) => {
                null != this.quill.scroll.query(t5, s.Scope.INLINE) && this.quill.format(t5, false, p.Ay.sources.USER);
              });
            } else this.quill.removeFormat(t3.index, t3.length, p.Ay.sources.USER);
          }, direction(t3) {
            const { align: e3 } = this.quill.getFormat();
            "rtl" === t3 && null == e3 ? this.quill.format("align", "right", p.Ay.sources.USER) : t3 || "right" !== e3 || this.quill.format("align", false, p.Ay.sources.USER), this.quill.format("direction", t3, p.Ay.sources.USER);
          }, indent(t3) {
            const e3 = this.quill.getSelection(), n2 = this.quill.getFormat(e3), r2 = parseInt(n2.indent || 0, 10);
            if ("+1" === t3 || "-1" === t3) {
              let e4 = "+1" === t3 ? 1 : -1;
              "rtl" === n2.direction && (e4 *= -1), this.quill.format("indent", r2 + e4, p.Ay.sources.USER);
            }
          }, link(t3) {
            true === t3 && (t3 = prompt("Enter link URL:")), this.quill.format("link", t3, p.Ay.sources.USER);
          }, list(t3) {
            const e3 = this.quill.getSelection(), n2 = this.quill.getFormat(e3);
            "check" === t3 ? "checked" === n2.list || "unchecked" === n2.list ? this.quill.format("list", false, p.Ay.sources.USER) : this.quill.format("list", "unchecked", p.Ay.sources.USER) : this.quill.format("list", t3, p.Ay.sources.USER);
          } } };
          const at = '<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>';
          var ct = { align: { "": '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>', center: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>', right: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>', justify: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>' }, background: '<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>', blockquote: '<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>', bold: '<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>', clean: '<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>', code: at, "code-block": at, color: '<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>', direction: { "": '<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>', rtl: '<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>' }, formula: '<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>', header: { 1: '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>', 2: '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>', 3: '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>', 4: '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>', 5: '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>', 6: '<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>' }, italic: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>', image: '<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>', indent: { "+1": '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>', "-1": '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>' }, link: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>', list: { bullet: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>', check: '<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>', ordered: '<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>' }, script: { sub: '<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>', super: '<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>' }, strike: '<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>', table: '<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>', underline: '<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>', video: '<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>' };
          let ut = 0;
          function ht(t3, e3) {
            t3.setAttribute(e3, `${!("true" === t3.getAttribute(e3))}`);
          }
          var dt = class {
            constructor(t3) {
              this.select = t3, this.container = document.createElement("span"), this.buildPicker(), this.select.style.display = "none", this.select.parentNode.insertBefore(this.container, this.select), this.label.addEventListener("mousedown", () => {
                this.togglePicker();
              }), this.label.addEventListener("keydown", (t4) => {
                switch (t4.key) {
                  case "Enter":
                    this.togglePicker();
                    break;
                  case "Escape":
                    this.escape(), t4.preventDefault();
                }
              }), this.select.addEventListener("change", this.update.bind(this));
            }
            togglePicker() {
              this.container.classList.toggle("ql-expanded"), ht(this.label, "aria-expanded"), ht(this.options, "aria-hidden");
            }
            buildItem(t3) {
              const e3 = document.createElement("span");
              e3.tabIndex = "0", e3.setAttribute("role", "button"), e3.classList.add("ql-picker-item");
              const n2 = t3.getAttribute("value");
              return n2 && e3.setAttribute("data-value", n2), t3.textContent && e3.setAttribute("data-label", t3.textContent), e3.addEventListener("click", () => {
                this.selectItem(e3, true);
              }), e3.addEventListener("keydown", (t4) => {
                switch (t4.key) {
                  case "Enter":
                    this.selectItem(e3, true), t4.preventDefault();
                    break;
                  case "Escape":
                    this.escape(), t4.preventDefault();
                }
              }), e3;
            }
            buildLabel() {
              const t3 = document.createElement("span");
              return t3.classList.add("ql-picker-label"), t3.innerHTML = '<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>', t3.tabIndex = "0", t3.setAttribute("role", "button"), t3.setAttribute("aria-expanded", "false"), this.container.appendChild(t3), t3;
            }
            buildOptions() {
              const t3 = document.createElement("span");
              t3.classList.add("ql-picker-options"), t3.setAttribute("aria-hidden", "true"), t3.tabIndex = "-1", t3.id = `ql-picker-options-${ut}`, ut += 1, this.label.setAttribute("aria-controls", t3.id), this.options = t3, Array.from(this.select.options).forEach((e3) => {
                const n2 = this.buildItem(e3);
                t3.appendChild(n2), true === e3.selected && this.selectItem(n2);
              }), this.container.appendChild(t3);
            }
            buildPicker() {
              Array.from(this.select.attributes).forEach((t3) => {
                this.container.setAttribute(t3.name, t3.value);
              }), this.container.classList.add("ql-picker"), this.label = this.buildLabel(), this.buildOptions();
            }
            escape() {
              this.close(), setTimeout(() => this.label.focus(), 1);
            }
            close() {
              this.container.classList.remove("ql-expanded"), this.label.setAttribute("aria-expanded", "false"), this.options.setAttribute("aria-hidden", "true");
            }
            selectItem(t3) {
              let e3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
              const n2 = this.container.querySelector(".ql-selected");
              t3 !== n2 && (null != n2 && n2.classList.remove("ql-selected"), null != t3 && (t3.classList.add("ql-selected"), this.select.selectedIndex = Array.from(t3.parentNode.children).indexOf(t3), t3.hasAttribute("data-value") ? this.label.setAttribute("data-value", t3.getAttribute("data-value")) : this.label.removeAttribute("data-value"), t3.hasAttribute("data-label") ? this.label.setAttribute("data-label", t3.getAttribute("data-label")) : this.label.removeAttribute("data-label"), e3 && (this.select.dispatchEvent(new Event("change")), this.close())));
            }
            update() {
              let t3;
              if (this.select.selectedIndex > -1) {
                const e4 = this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];
                t3 = this.select.options[this.select.selectedIndex], this.selectItem(e4);
              } else this.selectItem(null);
              const e3 = null != t3 && t3 !== this.select.querySelector("option[selected]");
              this.label.classList.toggle("ql-active", e3);
            }
          }, ft = class extends dt {
            constructor(t3, e3) {
              super(t3), this.label.innerHTML = e3, this.container.classList.add("ql-color-picker"), Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0, 7).forEach((t4) => {
                t4.classList.add("ql-primary");
              });
            }
            buildItem(t3) {
              const e3 = super.buildItem(t3);
              return e3.style.backgroundColor = t3.getAttribute("value") || "", e3;
            }
            selectItem(t3, e3) {
              super.selectItem(t3, e3);
              const n2 = this.label.querySelector(".ql-color-label"), r2 = t3 && t3.getAttribute("data-value") || "";
              n2 && ("line" === n2.tagName ? n2.style.stroke = r2 : n2.style.fill = r2);
            }
          }, pt = class extends dt {
            constructor(t3, e3) {
              super(t3), this.container.classList.add("ql-icon-picker"), Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach((t4) => {
                t4.innerHTML = e3[t4.getAttribute("data-value") || ""];
              }), this.defaultItem = this.container.querySelector(".ql-selected"), this.selectItem(this.defaultItem);
            }
            selectItem(t3, e3) {
              super.selectItem(t3, e3);
              const n2 = t3 || this.defaultItem;
              if (null != n2) {
                if (this.label.innerHTML === n2.innerHTML) return;
                this.label.innerHTML = n2.innerHTML;
              }
            }
          }, gt = class {
            constructor(t3, e3) {
              this.quill = t3, this.boundsContainer = e3 || document.body, this.root = t3.addContainer("ql-tooltip"), this.root.innerHTML = this.constructor.TEMPLATE, ((t4) => {
                const { overflowY: e4 } = getComputedStyle(t4, null);
                return "visible" !== e4 && "clip" !== e4;
              })(this.quill.root) && this.quill.root.addEventListener("scroll", () => {
                this.root.style.marginTop = -1 * this.quill.root.scrollTop + "px";
              }), this.hide();
            }
            hide() {
              this.root.classList.add("ql-hidden");
            }
            position(t3) {
              const e3 = t3.left + t3.width / 2 - this.root.offsetWidth / 2, n2 = t3.bottom + this.quill.root.scrollTop;
              this.root.style.left = `${e3}px`, this.root.style.top = `${n2}px`, this.root.classList.remove("ql-flip");
              const r2 = this.boundsContainer.getBoundingClientRect(), i2 = this.root.getBoundingClientRect();
              let s2 = 0;
              if (i2.right > r2.right && (s2 = r2.right - i2.right, this.root.style.left = `${e3 + s2}px`), i2.left < r2.left && (s2 = r2.left - i2.left, this.root.style.left = `${e3 + s2}px`), i2.bottom > r2.bottom) {
                const e4 = i2.bottom - i2.top, r3 = t3.bottom - t3.top + e4;
                this.root.style.top = n2 - r3 + "px", this.root.classList.add("ql-flip");
              }
              return s2;
            }
            show() {
              this.root.classList.remove("ql-editing"), this.root.classList.remove("ql-hidden");
            }
          }, mt = n(8347), bt = n(5374), yt = n(9609);
          const vt = [false, "center", "right", "justify"], At = ["#000000", "#e60000", "#ff9900", "#ffff00", "#008a00", "#0066cc", "#9933ff", "#ffffff", "#facccc", "#ffebcc", "#ffffcc", "#cce8cc", "#cce0f5", "#ebd6ff", "#bbbbbb", "#f06666", "#ffc266", "#ffff66", "#66b966", "#66a3e0", "#c285ff", "#888888", "#a10000", "#b26b00", "#b2b200", "#006100", "#0047b2", "#6b24b2", "#444444", "#5c0000", "#663d00", "#666600", "#003700", "#002966", "#3d1466"], xt = [false, "serif", "monospace"], Nt = ["1", "2", "3", false], Et = ["small", false, "large", "huge"];
          class wt extends yt.A {
            constructor(t3, e3) {
              super(t3, e3);
              const n2 = (e4) => {
                document.body.contains(t3.root) ? (null == this.tooltip || this.tooltip.root.contains(e4.target) || document.activeElement === this.tooltip.textbox || this.quill.hasFocus() || this.tooltip.hide(), null != this.pickers && this.pickers.forEach((t4) => {
                  t4.container.contains(e4.target) || t4.close();
                })) : document.body.removeEventListener("click", n2);
              };
              t3.emitter.listenDOM("click", document.body, n2);
            }
            addModule(t3) {
              const e3 = super.addModule(t3);
              return "toolbar" === t3 && this.extendToolbar(e3), e3;
            }
            buildButtons(t3, e3) {
              Array.from(t3).forEach((t4) => {
                (t4.getAttribute("class") || "").split(/\s+/).forEach((n2) => {
                  if (n2.startsWith("ql-") && (n2 = n2.slice(3), null != e3[n2])) if ("direction" === n2) t4.innerHTML = e3[n2][""] + e3[n2].rtl;
                  else if ("string" == typeof e3[n2]) t4.innerHTML = e3[n2];
                  else {
                    const r2 = t4.value || "";
                    null != r2 && e3[n2][r2] && (t4.innerHTML = e3[n2][r2]);
                  }
                });
              });
            }
            buildPickers(t3, e3) {
              this.pickers = Array.from(t3).map((t4) => {
                if (t4.classList.contains("ql-align") && (null == t4.querySelector("option") && kt(t4, vt), "object" == typeof e3.align)) return new pt(t4, e3.align);
                if (t4.classList.contains("ql-background") || t4.classList.contains("ql-color")) {
                  const n2 = t4.classList.contains("ql-background") ? "background" : "color";
                  return null == t4.querySelector("option") && kt(t4, At, "background" === n2 ? "#ffffff" : "#000000"), new ft(t4, e3[n2]);
                }
                return null == t4.querySelector("option") && (t4.classList.contains("ql-font") ? kt(t4, xt) : t4.classList.contains("ql-header") ? kt(t4, Nt) : t4.classList.contains("ql-size") && kt(t4, Et)), new dt(t4);
              }), this.quill.on(bt.A.events.EDITOR_CHANGE, () => {
                this.pickers.forEach((t4) => {
                  t4.update();
                });
              });
            }
          }
          wt.DEFAULTS = (0, mt.A)({}, yt.A.DEFAULTS, { modules: { toolbar: { handlers: { formula() {
            this.quill.theme.tooltip.edit("formula");
          }, image() {
            let t3 = this.container.querySelector("input.ql-image[type=file]");
            null == t3 && (t3 = document.createElement("input"), t3.setAttribute("type", "file"), t3.setAttribute("accept", this.quill.uploader.options.mimetypes.join(", ")), t3.classList.add("ql-image"), t3.addEventListener("change", () => {
              const e3 = this.quill.getSelection(true);
              this.quill.uploader.upload(e3, t3.files), t3.value = "";
            }), this.container.appendChild(t3)), t3.click();
          }, video() {
            this.quill.theme.tooltip.edit("video");
          } } } } });
          class qt extends gt {
            constructor(t3, e3) {
              super(t3, e3), this.textbox = this.root.querySelector('input[type="text"]'), this.listen();
            }
            listen() {
              this.textbox.addEventListener("keydown", (t3) => {
                "Enter" === t3.key ? (this.save(), t3.preventDefault()) : "Escape" === t3.key && (this.cancel(), t3.preventDefault());
              });
            }
            cancel() {
              this.hide(), this.restoreFocus();
            }
            edit() {
              let t3 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "link", e3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null;
              if (this.root.classList.remove("ql-hidden"), this.root.classList.add("ql-editing"), null == this.textbox) return;
              null != e3 ? this.textbox.value = e3 : t3 !== this.root.getAttribute("data-mode") && (this.textbox.value = "");
              const n2 = this.quill.getBounds(this.quill.selection.savedRange);
              null != n2 && this.position(n2), this.textbox.select(), this.textbox.setAttribute("placeholder", this.textbox.getAttribute(`data-${t3}`) || ""), this.root.setAttribute("data-mode", t3);
            }
            restoreFocus() {
              this.quill.focus({ preventScroll: true });
            }
            save() {
              let { value: t3 } = this.textbox;
              switch (this.root.getAttribute("data-mode")) {
                case "link": {
                  const { scrollTop: e3 } = this.quill.root;
                  this.linkRange ? (this.quill.formatText(this.linkRange, "link", t3, bt.A.sources.USER), delete this.linkRange) : (this.restoreFocus(), this.quill.format("link", t3, bt.A.sources.USER)), this.quill.root.scrollTop = e3;
                  break;
                }
                case "video":
                  t3 = function(t4) {
                    let e3 = t4.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/) || t4.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);
                    return e3 ? `${e3[1] || "https"}://www.youtube.com/embed/${e3[2]}?showinfo=0` : (e3 = t4.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/)) ? `${e3[1] || "https"}://player.vimeo.com/video/${e3[2]}/` : t4;
                  }(t3);
                case "formula": {
                  if (!t3) break;
                  const e3 = this.quill.getSelection(true);
                  if (null != e3) {
                    const n2 = e3.index + e3.length;
                    this.quill.insertEmbed(n2, this.root.getAttribute("data-mode"), t3, bt.A.sources.USER), "formula" === this.root.getAttribute("data-mode") && this.quill.insertText(n2 + 1, " ", bt.A.sources.USER), this.quill.setSelection(n2 + 2, bt.A.sources.USER);
                  }
                  break;
                }
              }
              this.textbox.value = "", this.hide();
            }
          }
          function kt(t3, e3) {
            let n2 = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
            e3.forEach((e4) => {
              const r2 = document.createElement("option");
              e4 === n2 ? r2.setAttribute("selected", "selected") : r2.setAttribute("value", String(e4)), t3.appendChild(r2);
            });
          }
          var _t = n(8298);
          const Lt = [["bold", "italic", "link"], [{ header: 1 }, { header: 2 }, "blockquote"]];
          class St extends qt {
            static TEMPLATE = ['<span class="ql-tooltip-arrow"></span>', '<div class="ql-tooltip-editor">', '<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">', '<a class="ql-close"></a>', "</div>"].join("");
            constructor(t3, e3) {
              super(t3, e3), this.quill.on(bt.A.events.EDITOR_CHANGE, (t4, e4, n2, r2) => {
                if (t4 === bt.A.events.SELECTION_CHANGE) if (null != e4 && e4.length > 0 && r2 === bt.A.sources.USER) {
                  this.show(), this.root.style.left = "0px", this.root.style.width = "", this.root.style.width = `${this.root.offsetWidth}px`;
                  const t5 = this.quill.getLines(e4.index, e4.length);
                  if (1 === t5.length) {
                    const t6 = this.quill.getBounds(e4);
                    null != t6 && this.position(t6);
                  } else {
                    const n3 = t5[t5.length - 1], r3 = this.quill.getIndex(n3), i2 = Math.min(n3.length() - 1, e4.index + e4.length - r3), s2 = this.quill.getBounds(new _t.Q(r3, i2));
                    null != s2 && this.position(s2);
                  }
                } else document.activeElement !== this.textbox && this.quill.hasFocus() && this.hide();
              });
            }
            listen() {
              super.listen(), this.root.querySelector(".ql-close").addEventListener("click", () => {
                this.root.classList.remove("ql-editing");
              }), this.quill.on(bt.A.events.SCROLL_OPTIMIZE, () => {
                setTimeout(() => {
                  if (this.root.classList.contains("ql-hidden")) return;
                  const t3 = this.quill.getSelection();
                  if (null != t3) {
                    const e3 = this.quill.getBounds(t3);
                    null != e3 && this.position(e3);
                  }
                }, 1);
              });
            }
            cancel() {
              this.show();
            }
            position(t3) {
              const e3 = super.position(t3), n2 = this.root.querySelector(".ql-tooltip-arrow");
              return n2.style.marginLeft = "", 0 !== e3 && (n2.style.marginLeft = -1 * e3 - n2.offsetWidth / 2 + "px"), e3;
            }
          }
          class Ot extends wt {
            constructor(t3, e3) {
              null != e3.modules.toolbar && null == e3.modules.toolbar.container && (e3.modules.toolbar.container = Lt), super(t3, e3), this.quill.container.classList.add("ql-bubble");
            }
            extendToolbar(t3) {
              this.tooltip = new St(this.quill, this.options.bounds), null != t3.container && (this.tooltip.root.appendChild(t3.container), this.buildButtons(t3.container.querySelectorAll("button"), ct), this.buildPickers(t3.container.querySelectorAll("select"), ct));
            }
          }
          Ot.DEFAULTS = (0, mt.A)({}, wt.DEFAULTS, { modules: { toolbar: { handlers: { link(t3) {
            t3 ? this.quill.theme.tooltip.edit() : this.quill.format("link", false, p.Ay.sources.USER);
          } } } } });
          const Tt = [[{ header: ["1", "2", "3", false] }], ["bold", "italic", "underline", "link"], [{ list: "ordered" }, { list: "bullet" }], ["clean"]];
          class jt extends qt {
            static TEMPLATE = ['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>', '<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">', '<a class="ql-action"></a>', '<a class="ql-remove"></a>'].join("");
            preview = this.root.querySelector("a.ql-preview");
            listen() {
              super.listen(), this.root.querySelector("a.ql-action").addEventListener("click", (t3) => {
                this.root.classList.contains("ql-editing") ? this.save() : this.edit("link", this.preview.textContent), t3.preventDefault();
              }), this.root.querySelector("a.ql-remove").addEventListener("click", (t3) => {
                if (null != this.linkRange) {
                  const t4 = this.linkRange;
                  this.restoreFocus(), this.quill.formatText(t4, "link", false, bt.A.sources.USER), delete this.linkRange;
                }
                t3.preventDefault(), this.hide();
              }), this.quill.on(bt.A.events.SELECTION_CHANGE, (t3, e3, n2) => {
                if (null != t3) {
                  if (0 === t3.length && n2 === bt.A.sources.USER) {
                    const [e4, n3] = this.quill.scroll.descendant(w, t3.index);
                    if (null != e4) {
                      this.linkRange = new _t.Q(t3.index - n3, e4.length());
                      const r2 = w.formats(e4.domNode);
                      this.preview.textContent = r2, this.preview.setAttribute("href", r2), this.show();
                      const i2 = this.quill.getBounds(this.linkRange);
                      return void (null != i2 && this.position(i2));
                    }
                  } else delete this.linkRange;
                  this.hide();
                }
              });
            }
            show() {
              super.show(), this.root.removeAttribute("data-mode");
            }
          }
          class Ct extends wt {
            constructor(t3, e3) {
              null != e3.modules.toolbar && null == e3.modules.toolbar.container && (e3.modules.toolbar.container = Tt), super(t3, e3), this.quill.container.classList.add("ql-snow");
            }
            extendToolbar(t3) {
              null != t3.container && (t3.container.classList.add("ql-snow"), this.buildButtons(t3.container.querySelectorAll("button"), ct), this.buildPickers(t3.container.querySelectorAll("select"), ct), this.tooltip = new jt(this.quill, this.options.bounds), t3.container.querySelector(".ql-link") && this.quill.keyboard.addBinding({ key: "k", shortKey: true }, (e3, n2) => {
                t3.handlers.link.call(t3, !n2.format.link);
              }));
            }
          }
          Ct.DEFAULTS = (0, mt.A)({}, wt.DEFAULTS, { modules: { toolbar: { handlers: { link(t3) {
            if (t3) {
              const t4 = this.quill.getSelection();
              if (null == t4 || 0 === t4.length) return;
              let e3 = this.quill.getText(t4);
              /^\S+@\S+\.\S+$/.test(e3) && 0 !== e3.indexOf("mailto:") && (e3 = `mailto:${e3}`);
              const { tooltip: n2 } = this.quill.theme;
              n2.edit("link", e3);
            } else this.quill.format("link", false, p.Ay.sources.USER);
          } } } } });
          var Rt = Ct;
          t2.default.register({ "attributors/attribute/direction": i.Mc, "attributors/class/align": e2.qh, "attributors/class/background": b.l, "attributors/class/color": y.g3, "attributors/class/direction": i.sY, "attributors/class/font": v.q, "attributors/class/size": A.U, "attributors/style/align": e2.Hu, "attributors/style/background": b.s, "attributors/style/color": y.JM, "attributors/style/direction": i.VL, "attributors/style/font": v.z, "attributors/style/size": A.r }, true), t2.default.register({ "formats/align": e2.qh, "formats/direction": i.sY, "formats/indent": l, "formats/background": b.s, "formats/color": y.JM, "formats/font": v.q, "formats/size": A.U, "formats/blockquote": u, "formats/code-block": D.Ay, "formats/header": d, "formats/list": m, "formats/bold": E, "formats/code": D.Cy, "formats/italic": class extends E {
            static blotName = "italic";
            static tagName = ["EM", "I"];
          }, "formats/link": w, "formats/script": _, "formats/strike": class extends E {
            static blotName = "strike";
            static tagName = ["S", "STRIKE"];
          }, "formats/underline": S, "formats/formula": j, "formats/image": I, "formats/video": U, "modules/syntax": Q, "modules/table": it, "modules/toolbar": ot, "themes/bubble": Ot, "themes/snow": Rt, "ui/icons": ct, "ui/picker": dt, "ui/icon-picker": pt, "ui/color-picker": ft, "ui/tooltip": gt }, true);
          var It = t2.default;
        }(), r.default;
      }();
    });
  }
});
export default require_quill();
/*! Bundled license information:

quill/dist/quill.js:
  (*! For license information please see quill.js.LICENSE.txt *)
*/
//# sourceMappingURL=quill_dist_quill.js.map

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

/* Text truncation for card content */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}


/* Dark mode support */
:host-context(.dark-theme) .bg-white {
  background-color: var(--background-dark);
}

:host-context(.dark-theme) .text-dark {
  color: var(--text-light);
}

:host-context(.dark-theme) .border-gray {
  border-color: var(--hover-dark-gray);
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* EditorJS Content Styling with ::ng-deep for proper encapsulation */
::ng-deep .editorjs-content h1 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content p {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: var(--text-dark);
}

::ng-deep .editorjs-content ul,
::ng-deep .editorjs-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content ul {
  list-style-type: disc;
}

::ng-deep .editorjs-content ol {
  list-style-type: decimal;
}

::ng-deep .editorjs-content li {
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content blockquote {
  border-left: 4px solid var(--primary-purple);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--text-medium-gray);
}

::ng-deep .editorjs-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

::ng-deep .editorjs-content pre {
  background-color: var(--background-light-gray);
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1rem 0;
}

::ng-deep .editorjs-content code {
  font-family: monospace;
  font-size: 0.9rem;
  color: var(--text-dark);
}

::ng-deep .editorjs-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

::ng-deep .editorjs-content table td,
::ng-deep .editorjs-content table th {
  border: 1px solid var(--hover-blue-gray);
  padding: 0.5rem;
  color: var(--text-dark);
}

/* Checklist styling */
::ng-deep .editorjs-content .checklist-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

::ng-deep .editorjs-content .checklist-item-checkbox {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
  border-radius: 0.25rem;
  border: 1px solid var(--hover-blue-gray);
  display: flex;
  align-items: center;
  justify-content: center;
}

::ng-deep .editorjs-content .checklist-item-checkbox.checked {
  background-color: var(--primary-purple);
  border-color: var(--primary-purple);
}

::ng-deep .editorjs-content .checklist-item-text {
  color: var(--text-dark);
}

::ng-deep .editorjs-content .checklist-item.checked .checklist-item-text {
  text-decoration: line-through;
  color: var(--text-medium-gray);
}

/* Dark mode support for EditorJS content */
:host-context(.dark-theme) ::ng-deep .editorjs-content h1,
:host-context(.dark-theme) ::ng-deep .editorjs-content h2,
:host-context(.dark-theme) ::ng-deep .editorjs-content h3,
:host-context(.dark-theme) ::ng-deep .editorjs-content h4,
:host-context(.dark-theme) ::ng-deep .editorjs-content p,
:host-context(.dark-theme) ::ng-deep .editorjs-content li,
:host-context(.dark-theme) ::ng-deep .editorjs-content code,
:host-context(.dark-theme) ::ng-deep .editorjs-content table td,
:host-context(.dark-theme) ::ng-deep .editorjs-content table th,
:host-context(.dark-theme) ::ng-deep .editorjs-content .checklist-item-text {
  color: var(--text-light);
}

:host-context(.dark-theme) ::ng-deep .editorjs-content pre {
  background-color: var(--background-dark);
}

:host-context(.dark-theme) ::ng-deep .editorjs-content blockquote {
  color: var(--text-light-gray);
}

:host-context(.dark-theme) ::ng-deep .editorjs-content table td,
:host-context(.dark-theme) ::ng-deep .editorjs-content table th {
  border-color: var(--hover-dark-gray);
}

:host-context(.dark-theme) ::ng-deep .editorjs-content .checklist-item-checkbox {
  border-color: var(--hover-dark-gray);
}

:host-context(.dark-theme) ::ng-deep .editorjs-content .checklist-item-checkbox.checked {
  background-color: var(--primary-purple);
  border-color: var(--primary-purple);
}

:host-context(.dark-theme) ::ng-deep .editorjs-content .checklist-item.checked .checklist-item-text {
  color: var(--text-medium-gray);
}

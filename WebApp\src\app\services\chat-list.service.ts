import { Injectable } from '@angular/core';
import { ChatServiceProxy, ChatHistoryDto } from '../../shared/service-proxies/service-proxies';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ChatHistoryItem {
  id: number;
  title: string;
  timestamp?: Date;
  lastMessage?: string;
}

@Injectable({
  providedIn: 'root',
})
export class ChatListService {
  groupedChats: any = {
    Today: [],
    Yesterday: [],
    'Last 7 Days': [],
    'Last 30 Days': [],
    Older: [],
  };

  // BehaviorSubject to store the current chat history
  private currentChatHistorySubject = new BehaviorSubject<ChatHistoryDto | null>(null);

  // Observable to expose the current chat history
  currentChatHistory$ = this.currentChatHistorySubject.asObservable();

  // Flag to indicate if we have a cached chat history
  private hasCachedHistory = false;

  chatList: any = [];
  chatId: any = 0;

  /**
   * Updates the current chat history in the service
   * @param chatHistory The chat history to store
   */
  updateCurrentChatHistory(chatHistory: ChatHistoryDto | null): void {
    this.currentChatHistorySubject.next(chatHistory);
    this.hasCachedHistory = !!chatHistory;
  }

  /**
   * Checks if we have a cached chat history
   */
  hasCurrentChatHistory(): boolean {
    return this.hasCachedHistory;
  }

  /**
   * Gets the current chat history value
   */
  getCurrentChatHistory(): ChatHistoryDto | null {
    return this.currentChatHistorySubject.value;
  }

  /**
   * Clears the current chat history
   */
  clearCurrentChatHistory(): void {
    this.currentChatHistorySubject.next(null);
    this.hasCachedHistory = false;
  }
  groupChatsByDate() {
    const now = new Date();

    // Reset groupedChats before pushing new data
    this.groupedChats = {
      Today: [],
      Yesterday: [],
      'Last 7 Days': [],
      'Last 30 Days': [],
      Older: [],
    };

    this.chatList.forEach((chat: any) => {
      const createdDate = new Date(chat.createdDate);

      if (isNaN(createdDate.getTime())) {
        return; // Skip invalid date
      }

      const diffTime = now.getTime() - createdDate.getTime();
      const diffDays = diffTime / (1000 * 3600 * 24);

      if (diffDays < 1) {
        this.groupedChats['Today'].push(chat);
      } else if (diffDays < 2) {
        this.groupedChats['Yesterday'].push(chat);
      } else if (diffDays < 8) {
        this.groupedChats['Last 7 Days'].push(chat);
      } else if (diffDays < 31) {
        this.groupedChats['Last 30 Days'].push(chat);
      } else {
        this.groupedChats['Older'].push(chat);
      }
    });
  }


}

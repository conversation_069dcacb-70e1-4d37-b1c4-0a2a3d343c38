#editorjs {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.editorWrapper {
  max-width: 1200px;
  margin: 20px auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 20px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.container {
  text-align: center;
  margin: 20px;
}

button {
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background: #fff;
  cursor: pointer;
}

button:hover {
  background: #f0f0f0;
}

.editor-wrapper {
  position: relative;
}

.editor-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  padding: 1rem;
  color: #999;
  pointer-events: none;
}

.editor-container {
  min-height: 300px;
}

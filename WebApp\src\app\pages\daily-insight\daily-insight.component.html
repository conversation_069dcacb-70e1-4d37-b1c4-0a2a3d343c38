<div class="flex flex-col min-h-[calc(100vh-65px)] p-6 max-w-full mx-auto bg-[var(--background-light-gray)]">
  <!-- Page Title Section -->
  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
    <div>
      <h1 class="text-2xl font-semibold text-[var(--text-dark)] mb-2">Daily Insights</h1>
      <p class="text-[var(--text-medium-gray)] dark:text-white mt-2">Welcome back! Here's your personalized dashboard with the
        latest information from your AI agents.</p>
    </div>

    <!-- Search and Actions Section -->
    <div class="flex flex-col sm:flex-row items-center gap-3 w-full sm:w-auto">
      <!-- Search Input -->
      <div class="relative w-full sm:w-64 flex items-center">
        <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-3 pointer-events-none">
          <i class="ri-search-line text-[var(--text-medium-gray)]"></i>
        </div>
        <input
          type="text"
          placeholder="Search insights..."
          [(ngModel)]="searchQuery"
          (ngModelChange)="filterInsights()"
          class="w-full h-10 px-4 py-2 pl-10 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
      >
      </div>

      <!-- Quick Actions -->
      <div class="flex gap-2">
        <button (click)="goToChat()"
          class="flex items-center gap-2 px-3 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-colors text-sm">
          <i class="ri-chat-new-line"></i>
          <span>New Chat</span>
        </button>
        <button (click)="showAddAgentModal()"
          class="flex items-center gap-2 px-3 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-colors text-sm">
          <i class="ri-add-line"></i>
          <span>Add Agent</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Spinner for initial load -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-16 bg-[var(--background-white)] rounded-lg shadow-sm">
    <div class="flex flex-col items-center">
      <div class="w-12 h-12 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
        <i class="ri-loader-4-line text-[var(--primary-purple)] text-2xl animate-spin"></i>
      </div>
      <p class="text-[var(--text-medium-gray)]">Loading insights...</p>
    </div>
  </div>


  <!-- Main Content - Card Grid Layout -->
  <div *ngIf="!isLoading && !isSearching" class="w-full">
    <!-- No Results Message -->
    <div *ngIf="searchQuery && filteredInsights.length === 0" class="flex items-center justify-center py-16 bg-[var(--background-white)] rounded-lg shadow-sm mb-4">
      <div class="flex flex-col items-center">
        <div class="w-12 h-12 rounded-full bg-[var(--secondary-purple)] bg-opacity-20 flex items-center justify-center mb-4">
          <i class="ri-search-line text-[var(--primary-purple)] text-2xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-[var(--text-dark)] mb-2">No Results Found</h3>
        <p class="text-[var(--text-medium-gray)] max-w-md text-center">We couldn't find any insights matching your search for "{{searchQuery}}"</p>
        <button (click)="searchQuery = ''; filterInsights()" class="mt-4 px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-colors text-sm">
          Clear Search
        </button>
      </div>
    </div>

    <div class="card-container grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Insight Card -->
      <div *ngFor="let insight of filteredInsights; let i = index"
        class="card-wrapper bg-[var(--background-white)] rounded-md border border-[var(--hover-blue-gray)] overflow-hidden hover:shadow-sm group transition-all duration-300 animate-fadeIn relative h-full flex flex-col"
        [ngClass]="{'expanded-card': insight.expanded}"
        [ngStyle]="{'animation-delay': (i * 0.05) + 's'}">

        <!-- Card Header with Title and Action Buttons -->
        <div class="p-5 flex justify-between items-start">
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-[var(--primary-purple)] flex items-center justify-center flex-shrink-0 mr-3">
              <i class="ri-robot-line text-white"></i>
            </div>
            <h2 class="text-lg font-semibold text-[var(--text-dark)] truncate max-w-[180px]">{{insight.title}}</h2>
          </div>

          <!-- Action Buttons (Top Right) - Hidden until hover -->
          <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button (click)="runAgentNow(insight.id!); $event.stopPropagation();"
              class="text-[var(--primary-purple)] hover:text-[var(--primary-purple-dark)] transition-colors p-2 rounded hover:bg-[var(--secondary-purple)] hover:bg-opacity-20 flex items-center justify-center"
              title="Refresh insight" [disabled]="insight.isLoading">
              <i class="ri-refresh-line text-lg" [class.animate-spin]="insight.isLoading"></i>
            </button>
            <button (click)="deleteAgentInsight(insight.id!); $event.stopPropagation();"
              class="text-[var(--text-medium-gray)] hover:text-red-500 transition-colors p-2 rounded hover:bg-red-100 hover:bg-opacity-20 flex items-center justify-center"
              title="Delete insight" [disabled]="insight.isLoading">
              <i class="ri-delete-bin-line text-lg"></i>
            </button>
          </div>
        </div>

        <!-- Card Content -->
        <div class="px-5 pb-5 flex-grow">
          <!-- Loading indicator overlay -->
          <div *ngIf="insight.isLoading"
            class="flex items-center justify-center py-2 mb-2 text-[var(--text-medium-gray)]">
            <div class="flex gap-1 items-center">
              <span
                class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.3s]"></span>
              <span
                class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.15s]"></span>
              <span class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce"></span>
              <span class="ml-2 text-xs">Refreshing...</span>
            </div>
          </div>

          <!-- Content Area -->
          <div class="bg-[var(--background-light-gray)] rounded p-4 h-full">
            <!-- Always show collapsed content view -->
            <div class="relative">
              <div class="text-[var(--text-dark)] whitespace-pre-line leading-relaxed overflow-hidden insight-content"
                style="max-height: 200px;">
                <pre
                  class="font-sans text-[var(--text-dark)] whitespace-pre-line leading-relaxed overflow-hidden bg-transparent p-0 m-0 border-0 text-sm">{{insight.content | slice:0:350}}<span *ngIf="insight.content.length > 350">...</span></pre>
              </div>
              <div *ngIf="insight.content.length > 350"
                class="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-[var(--background-light-gray)] to-transparent">
              </div>
            </div>

            <!-- Show More/Less button -->
            <div *ngIf="insight.content && insight.content.length > 350" class="mt-2 text-right">
              <button (click)="toggleExpand(insight); $event.stopPropagation();" [disabled]="insight.isLoading"
                class="text-[var(--primary-purple)] hover:text-[var(--primary-purple-dark)] text-xs font-medium px-2 py-1 rounded hover:bg-[var(--secondary-purple)] hover:bg-opacity-20 transition-colors inline-flex items-center gap-1 z-10 relative">
                {{insight.expanded ? 'Show Less' : 'Show More'}}
                <i class="ri-arrow-{{insight.expanded ? 'up' : 'down'}}-s-line"></i>
              </button>
            </div>

            <!-- Expanded content overlay (absolute positioned) -->
            <div *ngIf="insight.expanded" class="expanded-content-overlay animate-fadeIn">
              <div class="flex justify-between items-start mb-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-[var(--primary-purple)] flex items-center justify-center flex-shrink-0 mr-3">
                    <i class="ri-robot-line text-white"></i>
                  </div>
                  <h2 class="text-lg font-semibold text-[var(--text-dark)]">{{insight.title}}</h2>
                </div>
                <button (click)="toggleExpand(insight); $event.stopPropagation();"
                  class="close-button text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] p-2 rounded-full hover:bg-[var(--hover-blue-gray)] transition-colors">
                  <i class="ri-close-line text-xl"></i>
                </button>
              </div>

              <div class="text-[var(--text-dark)] leading-relaxed overflow-auto insight-content mb-4">
                <pre class="font-sans text-[var(--text-dark)] whitespace-pre-line leading-relaxed overflow-hidden bg-transparent p-0 m-0 border-0 text-sm">{{insight.content}}</pre>
              </div>

              <div class="mt-4 pt-4 border-t border-[var(--hover-blue-gray)] border-opacity-30 flex justify-between items-center">
                <div class="text-xs text-[var(--text-medium-gray)] flex items-center gap-1">
                  <span>By <span class="text-[var(--primary-purple)] font-medium">{{insight.agentName}}</span></span>
                </div>
                <button (click)="toggleExpand(insight); $event.stopPropagation();"
                  class="text-[var(--primary-purple)] hover:text-[var(--primary-purple-dark)] text-xs font-medium px-3 py-1.5 rounded hover:bg-[var(--secondary-purple)] hover:bg-opacity-20 transition-colors inline-flex items-center gap-1">
                  Show Less
                  <i class="ri-arrow-up-s-line"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Card Footer -->
        <div class="px-5 py-4 mt-auto border-t border-[var(--hover-blue-gray)] border-opacity-30 flex justify-between items-center">
          <div class="text-xs text-[var(--text-medium-gray)] flex items-center gap-1">
            <span>By <span class="text-[var(--primary-purple)] font-medium">{{insight.agentName}}</span></span>
          </div>
          <p *ngIf="insight.lastRunAt" class="text-xs text-[var(--text-medium-gray)] flex items-center gap-1">
            <i class="ri-time-line"></i>
            {{insight.lastRunAt ? insight.lastRunAt.toFormat('yyyy-MM-dd') : 'Never'}}
          </p>
        </div>
      </div>

      <!-- Empty state as a card -->
      <div *ngIf="insights.length === 0 && !searchQuery" class="md:col-span-2 lg:col-span-3">
        <div class="p-10 text-center bg-[var(--background-white)] rounded-md border border-dashed border-[var(--hover-blue-gray)]">
          <div class="flex flex-col items-center justify-center py-8">
            <div
              class="w-20 h-20 rounded-full bg-[var(--secondary-purple)] bg-opacity-20 flex items-center justify-center mb-4">
              <i class="ri-robot-line text-4xl text-[var(--primary-purple)]"></i>
            </div>
            <h3 class="text-xl font-semibold text-[var(--text-dark)] mb-2">No Insights Available</h3>
            <p class="text-[var(--text-medium-gray)] max-w-md mx-auto mb-6">Get started by adding your first AI agent to
              generate daily insights tailored to your needs.</p>
            <button (click)="showAddAgentModal()"
              class="px-6 py-3 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-colors flex items-center gap-2">
              <i class="ri-add-line"></i>
              Add Your First Agent
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Agent Modal Template -->
<ng-template #addAgentModalTemplate>
  <div class="p-4 text-[var(--text-dark)]">
    <!-- Two-column layout for larger screens -->
    <div class="flex flex-col md:flex-row gap-6">
      <!-- Left column: Agent Discovery -->
      <div class="w-full md:w-1/2">
        <h3 class="text-lg font-semibold text-[var(--text-dark)] dark:text-white mb-4">Discover Agents</h3>

        <!-- Search bar -->
        <div class="relative mb-4">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-50"></i>
          </div>
          <input
            type="text"
            placeholder="Search for agents to add"
            [(ngModel)]="agentSearchQuery"
            (ngModelChange)="searchAgents()"
            class="w-full h-10 px-4 py-2 pl-10 text-sm font-normal text-[var(--text-dark)] dark:text-white bg-[var(--background-light-gray)] dark:bg-[#3a3f4b] border border-[var(--hover-blue-gray)] dark:border-none rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3" *ngIf="agentSearchQuery">
            <button
              (click)="agentSearchQuery = ''; searchAgents()"
              class="text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-50 hover:text-[var(--text-dark)] dark:hover:text-white transition-colors focus:outline-none"
            >
              <i class="ri-close-line"></i>
            </button>
          </div>
        </div>

        <!-- Agent categories -->
        <div class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <h4 class="text-sm font-medium text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-80">Recommended Agents</h4>
            <button *ngIf="selectedCategory"
              (click)="clearCategoryFilter()"
              class="text-xs text-[var(--primary-purple)] hover:underline flex items-center">
              <i class="ri-filter-off-line mr-1"></i> Clear Filter
            </button>
          </div>
          <div class="grid grid-cols-2 gap-2">
            <div *ngFor="let category of agentCategories"
              [class.bg-[var(--primary-purple)]]="selectedCategory === category"
              [class.bg-opacity-10]="selectedCategory === category"
              [class.dark:bg-[var(--primary-purple)]]="selectedCategory === category"
              [class.dark:bg-opacity-20]="selectedCategory === category"
              [class.border]="selectedCategory === category"
              [class.border-[var(--primary-purple)]]="selectedCategory === category"
              [class.dark:border-[var(--primary-purple)]]="selectedCategory === category"
              class="bg-[var(--background-light-gray)] dark:bg-[#3a3f4b] p-3 rounded-md flex items-center justify-between cursor-pointer hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4a4f5b] transition-colors agent-category"
              (click)="filterAgentsByCategory(category)">
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full bg-[var(--primary-purple)] flex items-center justify-center mr-2">
                  <i class="ri-robot-line text-white"></i>
                </div>
                <div>
                  <span class="text-sm font-medium text-[var(--text-dark)] dark:text-white">{{category}}</span>
                  <span class="text-xs text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-50 ml-1">({{categoryAgentCounts[category] || 0}})</span>
                </div>
              </div>

            </div>
          </div>
        </div>

        <!-- Agent list -->
        <div class="max-h-60 overflow-y-auto pr-1">
          <div *ngFor="let agent of filteredAvailableAgents"
            [class.bg-[var(--primary-purple)]]="newAgent.agentName === agent"
            [class.bg-opacity-10]="newAgent.agentName === agent"
            [class.dark:bg-[var(--primary-purple)]]="newAgent.agentName === agent"
            [class.dark:bg-opacity-20]="newAgent.agentName === agent"
            [class.border]="newAgent.agentName === agent"
            [class.border-[var(--primary-purple)]]="newAgent.agentName === agent"
            [class.dark:border-[var(--primary-purple)]]="newAgent.agentName === agent"
            class="bg-[var(--background-light-gray)] dark:bg-[#3a3f4b] p-3 rounded-md flex items-center justify-between mb-2 cursor-pointer hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4a4f5b] transition-colors agent-list-item"
            (click)="selectAgent(agent)">
            <div class="flex items-center">
              <div class="w-8 h-8 rounded-full bg-[var(--primary-purple)] flex items-center justify-center mr-2">
                <i class="ri-robot-line text-white"></i>
              </div>
              <span class="text-sm font-medium text-[var(--text-dark)] dark:text-white">{{agent}}</span>
            </div>
            <div>
              <i [class.ri-check-line]="newAgent.agentName === agent" [class.ri-add-line]="newAgent.agentName !== agent" class="text-[var(--primary-purple)]"></i>
            </div>
          </div>

          <!-- Empty state for search -->
          <div *ngIf="filteredAvailableAgents.length === 0 && agentSearchQuery" class="text-center py-4">
            <p class="text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-70">No agents found matching "{{agentSearchQuery}}"</p>
          </div>

          <!-- Empty state for category filter -->
          <div *ngIf="filteredAvailableAgents.length === 0 && selectedCategory && !agentSearchQuery" class="text-center py-4">
            <p class="text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-70">No agents found in the "{{selectedCategory}}" category</p>
            <button (click)="clearCategoryFilter()" class="mt-2 text-[var(--primary-purple)] hover:underline">
              Show all agents
            </button>
          </div>
        </div>
      </div>

      <!-- Right column: Agent Configuration -->
      <div class="w-full md:w-1/2">
        <h3 class="text-lg font-semibold text-[var(--text-dark)] dark:text-white mb-4">Configure Insight</h3>

        <!-- Selected agent display -->
        <div *ngIf="newAgent.agentName" class="bg-[var(--secondary-purple)] bg-opacity-20 dark:bg-[#00c39a] dark:bg-opacity-20 p-4 rounded-md flex items-center justify-between mb-4 border border-[var(--primary-purple)] border-opacity-30 dark:border-none">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full bg-[var(--primary-purple)] dark:bg-[#00c39a] flex items-center justify-center mr-3 shadow-sm">
              <i class="ri-robot-line text-white text-lg"></i>
            </div>
            <div>
              <span class="text-sm font-semibold text-[var(--text-dark)] dark:text-white block">{{newAgent.agentName}}</span>
              <span class="text-xs text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-70">Selected for your insight</span>
            </div>
          </div>
          <button (click)="newAgent.agentName = ''" class="text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-70 hover:text-[var(--text-dark)] dark:hover:text-white hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4a4f5b] p-1.5 rounded-full transition-all duration-200">
            <i class="ri-close-line text-lg"></i>
          </button>
        </div>

        <!-- No agent selected state -->
        <div *ngIf="!newAgent.agentName" class="bg-[var(--background-light-gray)] dark:bg-[#3a3f4b] p-6 rounded-md mb-4 text-center border border-dashed border-[var(--hover-blue-gray)] dark:border-[#4a4f5b]">
          <div class="w-12 h-12 rounded-full bg-[var(--hover-blue-gray)] dark:bg-[#4a4f5b] mx-auto flex items-center justify-center mb-3">
            <i class="ri-robot-line text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-50 text-2xl"></i>
          </div>
          <p class="text-[var(--text-dark)] dark:text-white mb-1">No Agent Selected</p>
          <p class="text-xs text-[var(--text-medium-gray)] dark:text-white dark:text-opacity-70">Browse and select an agent from the left panel</p>
          <div class="mt-3">
            <i class="ri-arrow-left-line text-[var(--primary-purple)]"></i>
          </div>
        </div>

        <!-- Title input -->
        <div class="mb-4">
          <label class="block text-[var(--text-dark)] dark:text-white font-medium mb-2">Title</label>
          <input [(ngModel)]="newAgent.title"
            class="w-full px-3 py-2 border border-[var(--hover-blue-gray)] dark:border-none rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] bg-[var(--background-white)] dark:bg-[#3a3f4b] text-[var(--text-dark)] dark:text-white transition-all duration-200"
            placeholder="Enter a title for this insight">
        </div>

        <!-- Prompt input -->
        <div class="mb-4">
          <label class="block text-[var(--text-dark)] dark:text-white font-medium mb-2">Prompt</label>
          <textarea [(ngModel)]="newAgent.prompt"
            class="w-full px-3 py-2 border border-[var(--hover-blue-gray)] dark:border-none rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] min-h-[100px] bg-[var(--background-white)] dark:bg-[#3a3f4b] text-[var(--text-dark)] dark:text-white transition-all duration-200"
            placeholder="Enter prompt for the agent (optional)"></textarea>
        </div>
      </div>
    </div>
  </div>

  <!-- Custom Modal Footer -->
  <div class="px-6 py-4  flex justify-end gap-3 border-t border-[var(--hover-blue-gray)] dark:border-[#3a3f4b]">
    <button
      (click)="modalService.closeAll()"
      class="px-5 py-2 bg-white dark:bg-[#3a3f4b] text-[var(--text-dark)] dark:text-white border border-[var(--hover-blue-gray)] dark:border-[#4a4f5b] rounded-md hover:bg-[var(--background-light-gray)] dark:hover:bg-[#4a4f5b] transition-colors">
      Cancel
    </button>
    <button
      (click)="addNewAgent()"
      [disabled]="!newAgent.agentName || !newAgent.title"
      [class.opacity-50]="!newAgent.agentName || !newAgent.title"
      class="px-5 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-colors flex items-center gap-2">
      <i *ngIf="isAddingAgent" class="ri-loader-4-line animate-spin"></i>
      Add Agent
    </button>
  </div>
</ng-template>

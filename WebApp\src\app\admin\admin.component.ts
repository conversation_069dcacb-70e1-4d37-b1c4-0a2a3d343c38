import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TogglingService } from '../toggling.service';
import { AuthService } from '../../shared/services/auth.service';

@Component({
  selector: 'app-admin',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet
  ],
  templateUrl: './admin.component.html',
  styleUrl: './admin.component.css',
})
export class AdminComponent {
  constructor(private authService: AuthService) { }

  togglingservice = inject(TogglingService)

  get isAdmin() {
    return this.authService.isAdmin();
  }
}

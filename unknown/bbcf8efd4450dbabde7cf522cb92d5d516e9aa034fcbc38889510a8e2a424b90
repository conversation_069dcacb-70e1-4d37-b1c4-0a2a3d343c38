{"runtime": "<PERSON><PERSON><PERSON>", "defaultVariables": null, "documentGenerator": {"fromDocument": {"url": "https://localhost:44314/swagger/v1/swagger.json", "output": null}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "{controller}ServiceProxy", "moduleName": "", "namespace": "", "typeScriptVersion": 4.7, "template": "Angular", "promiseType": "Promise", "httpClass": "HttpClient", "useSingletonProvider": false, "injectionTokenType": "InjectionToken", "rxJsVersion": 7.5, "dateTimeType": "<PERSON><PERSON>", "nullValue": "Undefined", "generateClientClasses": true, "generateClientInterfaces": false, "generateOptionalParameters": false, "exportTypes": true, "wrapDtoExceptions": false, "exceptionClass": "ApiException", "clientBaseClass": null, "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "MultipleClientsFromPathSegments", "markOptionalProperties": false, "generateCloneMethod": false, "typeStyle": "Class", "classTypes": [], "extendedClasses": [], "extensionCode": "service.extensions.ts", "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "baseUrlTokenName": "API_BASE_URL", "queryNullValue": "", "inlineNamedDictionaries": false, "inlineNamedAny": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": "../src/shared/service-proxies/service-proxies.ts"}, "openApiToCSharpClient": {"clientBaseClass": null, "configurationClass": null, "generateClientClasses": true, "generateClientInterfaces": false, "injectHttpClient": false, "disposeHttpClient": true, "protectedMethods": [], "generateExceptionClasses": true, "exceptionClass": "SwaggerException", "wrapDtoExceptions": true, "useHttpClientCreationMethod": false, "httpClientType": "System.Net.Http.HttpClient", "useHttpRequestMessageCreationMethod": false, "useBaseUrl": true, "generateBaseUrlProperty": true, "generateSyncMethods": false, "exposeJsonSerializerSettings": false, "clientClassAccessModifier": "public", "typeAccessModifier": "public", "generateContractsOutput": false, "contractsNamespace": null, "contractsOutputFilePath": null, "parameterDateTimeFormat": "s", "generateUpdateJsonSerializerSettingsMethod": true, "serializeTypeInformation": false, "queryNullValue": "", "className": "{controller}Client", "operationGenerationMode": "MultipleClientsFromOperationId", "additionalNamespaceUsages": [], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": true, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.ObjectModel.ObservableCollection", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "MyNamespace", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.ObjectModel.ObservableCollection", "arrayInstanceType": "System.Collections.ObjectModel.Collection", "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": "System.Collections.Generic.Dictionary", "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Inpc", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": null}, "openApiToCSharpController": {"controllerBaseClass": null, "controllerStyle": "Partial", "controllerTarget": "AspNet", "useCancellationToken": false, "useActionResultType": false, "generateModelValidationAttributes": false, "routeNamingStrategy": "None", "className": "{controller}", "operationGenerationMode": "MultipleClientsFromOperationId", "additionalNamespaceUsages": ["System.Web.Http"], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": true, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.ObjectModel.ObservableCollection", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "MyNamespace", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.Generic.IEnumerable", "arrayInstanceType": "System.Collections.ObjectModel.Collection", "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": "System.Collections.Generic.Dictionary", "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Inpc", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": null}}}
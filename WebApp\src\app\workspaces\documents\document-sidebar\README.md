# Document Sidebar Component

## Overview
The `DocumentSidebarComponent` is a standalone Angular component that provides sidebar functionality for the documents workspace. It has been extracted from the main `DocumentsComponent` to improve modularity and reusability.

## Features
- **Favorites Management**: Display and manage favorite documents
- **Recent Documents**: Show recently opened documents
- **Theme Support**: Dark/light theme integration
- **Responsive Design**: Adapts to different sidebar states (collapsed/narrow/expanded)
- **API Integration**: Connects to backend services for data management

## Inputs
- `workspaceName: string` - The current workspace name
- `isPublicRoute: boolean` - Whether this is a public route (notes vs documents)
- `isDragging: boolean` - Whether the splitter is being dragged
- `splitSizes: any` - Current split sizes for width indicator
- `sidebarState: 'collapsed' | 'narrow' | 'expanded'` - Current sidebar state

## Outputs
- `documentSelected: EventEmitter<any>` - Emitted when a document is selected
- `addDocumentClicked: EventEmitter<Event>` - Emitted when add document is clicked
- `showDocumentsListClicked: EventEmitter<void>` - Emitted when "View All" is clicked
- `gutterDoubleClick: EventEmitter<any>` - Emitted when gutter is double-clicked

## Usage
```html
<app-document-sidebar
  [workspaceName]="workspaceName"
  [isPublicRoute]="isPublicRoute"
  [isDragging]="isDragging"
  [splitSizes]="splitSizes"
  [sidebarState]="sidebarState"
  (documentSelected)="onDocumentSelected($event)"
  (addDocumentClicked)="onAddDocument($event)"
  (showDocumentsListClicked)="onShowDocumentsList()"
  (gutterDoubleClick)="onGutterDoubleClick($event)">
</app-document-sidebar>
```

## Dependencies
- `DocsServiceProxy` - For API calls
- `ThemeService` - For theme management
- `CommonModule` - For Angular common directives

## Styling
The component includes comprehensive CSS for:
- Theme-specific styling (dark/light mode)
- Responsive design
- Smooth animations and transitions
- Custom scrollbars
- Accessibility features

## API Methods
- `loadSidebarData()` - Loads favorites and recent documents
- `updateFavoritesAndRecent()` - Refreshes sidebar data from API
- `selectDocument(document)` - Handles document selection
- `toggleFavorite(document)` - Toggles favorite status
- `addToRecentDocuments(document)` - Tracks document opens

## Integration
This component is designed to work seamlessly with the main `DocumentsComponent` and maintains the same functionality as the original integrated sidebar while providing better separation of concerns.

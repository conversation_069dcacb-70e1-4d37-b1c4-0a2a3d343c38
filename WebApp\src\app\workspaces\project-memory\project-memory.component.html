<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">
  <!-- Main Container -->
  <div class="flex-1 px-6 py-4 overflow-hidden flex flex-col">
    <!-- Header -->
    <div class="sticky-header flex flex-col sm:flex-row justify-between mb-6 gap-4 bg-[var(--background-light-gray)] items-center">
      <div class="flex flex-col mb-4 sm:mb-0">
        <h1 class="text-2xl font-semibold text-[var(--text-dark)] flex items-center gap-2">
          <i class="ri-folder-line text-[var(--primary-purple)]"></i>
          <span class="animate-fadeIn">Project Memories</span>
          <span class="inline-flex items-center justify-center px-2 py-0.5 ml-2 rounded-full text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
            {{ filteredMemories.length }}
          </span>
        </h1>
        <p class="text-sm text-[var(--text-medium-gray)] mt-1">Organize and manage your project memories</p>
      </div>

      <div class="flex flex-col sm:flex-row items-center gap-3 w-full sm:w-auto">
        <!-- Add Button -->
        <button
          (click)="addProjectMemory()"
          class="w-full sm:w-auto h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md"
        >
          <i class="ri-add-line"></i>
          <span>Add Memory</span>
        </button>
      </div>
    </div>

    <!-- Memories Table - With Proper Y-Axis Scrolling -->
    <div class="flex-1 overflow-y-auto pr-1">
      <div class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-[var(--text-dark)] memory-table">
            <!-- Table Header - Sticky -->
            <thead>
              <tr class="sticky top-0 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] font-medium text-sm border-b border-[var(--hover-blue-gray)] z-10">
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Workspace</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Description</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Category</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-center">
                  <div class="flex items-center justify-center">
                    <span>Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let memory of paginatedMemories; let i = index"
                class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] transition-all duration-200 animate-fadeIn"
                [ngStyle]="{'animation-delay': (i * 0.05) + 's'}">
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{ memory.workspace }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="description-truncate">{{ memory.projectDescription }}</div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium bg-[var(--secondary-purple)] text-[var(--text-dark)]">
                      {{ memory.projectCategory }}
                    </span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center justify-center space-x-3">
                    <button
                      (click)="onUpdate(memory)"
                      class="action-button w-8 h-8 rounded-md bg-[#F0F0F0] hover:bg-[#E0E0E0] transition-all duration-200 flex items-center justify-center border-none"
                      title="Edit Memory"
                    >
                      <i class="ri-edit-line text-[var(--primary-purple)] text-base"></i>
                    </button>
                    <button
                      (click)="deleteMemory(memory.id)"
                      class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none"
                      title="Delete Memory"
                    >
                      <i class="ri-delete-bin-6-line text-red-500 text-base"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="paginatedMemories.length === 0">
                <td colspan="4" class="px-4 py-8 text-center text-[var(--text-medium-gray)]">
                  <div class="flex flex-col items-center justify-center">
                    <div class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4">
                      <i class="ri-folder-line text-3xl text-[var(--text-medium-gray)]"></i>
                    </div>
                    <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No memories found</h3>
                    <p class="text-[var(--text-medium-gray)] text-center max-w-md">
                      There are no project memories available. Click the "Add Memory" button to create one.
                    </p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    <!-- Pagination Controls -->
    <div class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-6 px-4 py-3 bg-[var(--background-white)] dark:bg-[var(--hover-blue-gray)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]" *ngIf="filteredMemories.length > 0">
      <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
        <ng-container *ngIf="filteredMemories.length > 0">
          <span>Showing</span>
          <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
          <span>to</span>
          <span class="font-medium text-[var(--text-dark)] mx-1">{{ Math.min(currentPage * pageSize, filteredMemories.length) }}</span>
          <span>of</span>
          <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredMemories.length }}</span>
          <span>memories</span>
        </ng-container>
        <ng-container *ngIf="filteredMemories.length === 0">
          <span>No memories to display</span>
        </ng-container>
      </div>

      <div class="flex items-center">
        <div class="hidden sm:flex items-center mr-6 space-x-2">
          <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
          <div class="relative">
            <select
              [(ngModel)]="pageSize"
              (change)="updatePagination()"
              class="appearance-none h-8 bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md text-sm px-3 pr-8 py-1 text-center focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)]"
            >
              <option [value]="5" class="text-center">5</option>
              <option [value]="10" class="text-center">10</option>
              <option [value]="20" class="text-center">20</option>
              <option [value]="50" class="text-center">50</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2 pointer-events-none">
              <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-1" *ngIf="totalPages > 0">
          <button
            (click)="goToPage(1)"
            [disabled]="currentPage === 1 || totalPages <= 1"
            class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            aria-label="First page"
          >
            <i class="ri-skip-back-mini-line"></i>
          </button>

          <button
            (click)="previousPage()"
            [disabled]="currentPage === 1 || totalPages <= 1"
            class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            aria-label="Previous page"
          >
            <i class="ri-arrow-left-s-line"></i>
          </button>

          <div class="flex items-center space-x-1">
            <button
              *ngIf="currentPage > 2 && totalPages > 3"
              (click)="goToPage(1)"
              class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            >
              1
            </button>

            <span *ngIf="currentPage > 3 && totalPages > 4" class="w-8 h-8 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

            <button
              *ngIf="currentPage > 1 && totalPages > 1"
              (click)="goToPage(currentPage - 1)"
              class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            >
              {{ currentPage - 1 }}
            </button>

            <button
              class="w-8 h-8 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none"
            >
              {{ currentPage }}
            </button>

            <button
              *ngIf="currentPage < totalPages && totalPages > 1"
              (click)="goToPage(currentPage + 1)"
              class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            >
              {{ currentPage + 1 }}
            </button>

            <span *ngIf="currentPage < totalPages - 2 && totalPages > 4" class="w-8 h-8 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

            <button
              *ngIf="currentPage < totalPages - 1 && totalPages > 3"
              (click)="goToPage(totalPages)"
              class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            >
              {{ totalPages }}
            </button>
          </div>

          <button
            (click)="nextPage()"
            [disabled]="currentPage === totalPages || totalPages <= 1"
            class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            aria-label="Next page"
          >
            <i class="ri-arrow-right-s-line"></i>
          </button>

          <button
            (click)="goToPage(totalPages)"
            [disabled]="currentPage === totalPages || totalPages <= 1"
            class="w-8 h-8 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] dark:bg-[#2D3748] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] dark:hover:bg-[#4A5568] transition-all duration-200"
            aria-label="Last page"
          >
            <i class="ri-skip-forward-mini-line"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Project Memory Dialog -->
<div *ngIf="showAddProjectMemoryDialog"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
  <div class="rounded-[var(--border-radius-large)] bg-[var(--background-white)] shadow-[var(--box-shadow)] w-full max-w-md mx-4 overflow-hidden transform transition-all duration-300 ease-in-out animate-scaleIn">
    <div class="p-6 pb-3">
      <h2 class="text-xl font-semibold text-[var(--text-dark)] flex items-center gap-2">
        <i class="ri-file-list-3-line text-[var(--primary-purple)]"></i>
        <span>{{isUpdating ? 'Update' : 'Add'}} Project Memory</span>
      </h2>
      <p class="text-sm text-[var(--text-medium-gray)] mt-1">Enter the details for your project memory</p>
    </div>
    <form #memoryForm="ngForm" class="px-6 pb-6 space-y-6">
      <!-- Category Field -->
      <div class="mb-4">
        <label for="projectCategory" class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
          <i class="ri-text-line mr-2 text-[var(--primary-purple)]"></i>Short Message
        </label>
        <input
          type="text"
          id="projectCategory"
          name="projectCategory"
          [(ngModel)]="projectMemory.projectCategory"
          #category="ngModel"
          required
          class="w-full p-3 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:placeholder-gray-400"
          placeholder="Enter a short name or description"
        />
        <div *ngIf="category.invalid && category.touched" class="text-red-500 text-xs mt-1 flex items-center">
          <i class="ri-error-warning-line mr-1"></i>Please enter a short message
        </div>
      </div>

      <!-- Description Field -->
      <div class="mb-4">
        <label for="description" class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
          <i class="ri-file-text-line mr-2 text-[var(--primary-purple)]"></i>Prompt
        </label>
        <textarea
          id="description"
          name="description"
          [(ngModel)]="projectMemory.projectDescription"
          #description="ngModel"
          required
          class="w-full p-3 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 min-h-[150px] resize-none dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:placeholder-gray-400"
          placeholder="Enter your prompt"
        ></textarea>
        <div *ngIf="description.invalid && description.touched" class="text-red-500 text-xs mt-1 flex items-center">
          <i class="ri-error-warning-line mr-1"></i>Please enter a prompt
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex justify-end space-x-4 pt-4 border-t border-[var(--hover-blue-gray)]">
        <button
          type="button"
          (click)="showAddProjectMemoryDialog = false"
          class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 outline-none border-none cursor-pointer hover:shadow-md flex items-center gap-2"
        >
          <i class="ri-close-line"></i>
          Cancel
        </button>

        @if (!isUpdating) {
        <button
          type="submit"
          (click)="addProjectMemory()"
          [disabled]="!memoryForm.form.valid"
          class="bg-[var(--primary-purple)] text-[var(--background-white)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-all duration-300 outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md"
        >
          <i class="ri-save-line"></i>
          Add
        </button>
        } @else {
        <button
          type="submit"
          (click)="editMemory()"
          [disabled]="!memoryForm.form.valid"
          class="bg-[var(--primary-purple)] text-[var(--background-white)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-all duration-300 outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md"
        >
          <i class="ri-save-line"></i>
          Update
        </button>
        }
      </div>
    </form>
  </div>
</div>

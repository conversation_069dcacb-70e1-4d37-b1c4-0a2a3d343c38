import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import EditorJS, { ToolConstructable } from '@editorjs/editorjs';

import Header from '@editorjs/header';
import Table from '@editorjs/table';
import List from '@editorjs/list';
import SimpleImage from '@editorjs/simple-image';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Warning from '@editorjs/warning';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import LinkTool from '@editorjs/link';
import Embed from '@editorjs/embed';

@Component({
  selector: 'app-my-note',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './my-note.component.html',
  styleUrl: './my-note.component.css'
})
export class MyNoteComponent implements OnInit, AfterViewInit {
  @ViewChild('editor', { read: ElementRef })
  editorElement!: ElementRef;

  private editor!: EditorJS;
  hasContent = false;
  isGenerating = false;
  aiPrompt = '';
  contentType = 'article';
  useCurrentContent = false;
  isLoading = false;
  isToogled = false;

  constructor() { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.initializeEditor();
  }

  private initializeEditor() {
    this.editor = new EditorJS({
      holder: 'editor',
      minHeight: 200,
      placeholder: 'Start writing or paste your content here...',
      onChange: () => {
        this.editor.save().then(data => {
          this.hasContent = data.blocks.length > 0;
        });
      },
      tools: {
        header: {
          class: Header as unknown as ToolConstructable,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4],
            defaultLevel: 1,
            placeholder: 'Enter a heading'
          }
        },
        image: {
          class: SimpleImage as unknown as ToolConstructable,
          config: {
            placeholder: 'Paste image URL or choose file',
            buttonContent: 'Choose an image',
            uploader: {
              uploadByFile(file: File): Promise<{ success: number; file: { url: string } }> {
                const formData = new FormData();
                formData.append('image', file);
                return fetch('https://localhost:44350/api/Upload/image', {
                  method: 'POST',
                  body: formData
                })
                .then(response => response.json())
                .then(data => ({
                  success: 1,
                  file: { url: data.url }
                }));
              },
              uploadByUrl(url: string) {
                return fetch('https://localhost:44350/api/Upload/url', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ url })
                })
                .then(response => response.json())
                .then(data => ({
                  success: 1,
                  file: { url: data.url }
                }));
              }
            }
          }
        },
        list: List as unknown as ToolConstructable,
        checklist: Checklist as unknown as ToolConstructable,
        quote: Quote as unknown as ToolConstructable,
        warning: Warning as unknown as ToolConstructable,
        marker: Marker as unknown as ToolConstructable,
        code: CodeTool as unknown as ToolConstructable,
        delimiter: Delimiter as unknown as ToolConstructable,
        inlineCode: InlineCode as unknown as ToolConstructable,
        linkTool: LinkTool as unknown as ToolConstructable,
        embed: Embed as unknown as ToolConstructable,
        table: Table as unknown as ToolConstructable
      },
      defaultBlock: 'paragraph'
    });
  }

  private async getMarkdownContent(): Promise<string> {
    if (!this.editor) return '';
    const data = await this.editor.save();
    return data.blocks.map(block => block.data.text).join('\n');
  }

  async confirmGeneration() {
    if (!this.editor) return;
    this.isLoading = true;

    try {
      const currentContent = this.useCurrentContent ? await this.getMarkdownContent() : '';
      const response = await this.callAIService(this.aiPrompt, this.contentType, currentContent);

      if (this.useCurrentContent) {
        // Append to existing content
        await this.editor.blocks.insert('paragraph', {
          text: response
        });
      } else {
        // Replace content
        await this.editor.clear();
        await this.editor.blocks.insert('paragraph', {
          text: response
        });
      }

      this.isGenerating = false;
      this.useCurrentContent = false;
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      this.isLoading = false;
    }
  }

  cancelGeneration() {
    this.isGenerating = false;
    this.aiPrompt = '';
  }

  clearContent() {
    if (this.editor) {
      this.editor.clear();
    }
  }

  private async callAIService(prompt: string, type: string, context: string = ''): Promise<string> {
    // Implement your AI service call here
    const payload = {
      prompt,
      type,
      context,
      useContext: this.useCurrentContent
    };
    // Replace with actual API call
    return `AI generated content based on ${context ? 'existing context' : 'new prompt'}`;
  }

  ngOnDestroy() {
    if (this.editor) {
      this.editor.destroy();
    }
  }
}

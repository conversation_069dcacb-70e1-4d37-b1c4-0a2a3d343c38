/* Daily Insights Sidebar Styles */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

/* Card hover effects */
.group:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Icon styles */
.ri-flashlight-line,
.ri-lightbulb-line {
  color: var(--primary-purple);
}

/* Section dividers */
.h-px {
  height: 1px;
  background-color: var(--hover-blue-gray);
  opacity: 0.5;
}

/* Card icon container */
.w-10.h-10.rounded-full {
  transition: all 0.3s ease;
}

/* Ensure proper scrolling */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: var(--text-medium-gray) transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: var(--text-medium-gray);
  border-radius: 4px;
}

/* Ensure proper height calculation */
.max-h-\[calc\(100vh-74px\)\] {
  max-height: calc(100vh - 74px);
}

/* Tooltip styles */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.custom-tooltip {
  visibility: hidden;
  position: absolute;
  background-color: var(--background-light-gray);
  color: var(--text-dark);
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--hover-blue-gray);
}

.sidebar-tooltip {
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
}

.sidebar-tooltip::before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--hover-blue-gray) transparent transparent transparent;
}

/* Refresh button tooltip - appears at the bottom */
.refresh-tooltip {
  top: 125%;
  left: 50%;
  transform: translateX(-50%);
}

.refresh-tooltip::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent var(--hover-blue-gray) transparent;
}

.tooltip-container:hover .custom-tooltip {
  visibility: visible;
  opacity: 1;
}

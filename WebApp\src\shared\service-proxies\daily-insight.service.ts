// import { Injectable, Inject, Optional } from '@angular/core';
// import { HttpClient } from '@angular/common/http';
// import { Observable } from 'rxjs';
// import { API_BASE_URL } from './service-proxies';

// @Injectable({
//   providedIn: 'root'
// })
// export class DailyInsightService {
//   private http: HttpClient;
//   private baseUrl: string;

//   constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
//     this.http = http;
//     this.baseUrl = baseUrl ?? "";
//   }

//   /**
//    * Get all daily insight agents for the current user
//    */
//   getAllAgents(): Observable<DailyInsightAgentResponseDto[]> {
//     return this.http.get<DailyInsightAgentResponseDto[]>(`${this.baseUrl}/api/DailyInsight/GetAll`);
//   }

//   /**
//    * Create a new daily insight agent
//    */
//   createAgent(agent: DailyInsightAgentDto): Observable<DailyInsightAgentResponseDto> {
//     return this.http.post<DailyInsightAgentResponseDto>(`${this.baseUrl}/api/DailyInsight/Create`, agent);
//   }

//   /**
//    * Delete a daily insight agent
//    */
//   deleteAgent(id: number): Observable<ResponseMessage> {
//     return this.http.delete<ResponseMessage>(`${this.baseUrl}/api/DailyInsight/Delete/${id}`);
//   }

//   /**
//    * Run a daily insight agent now
//    */
//   runAgentNow(id: number): Observable<ResponseMessage> {
//     return this.http.post<ResponseMessage>(`${this.baseUrl}/api/DailyInsight/RunNow/${id}`, {});
//   }
// }

// export interface DailyInsightAgentDto {
//   agentName: string;
//   parameters: string;
//   title: string;
// }

// export interface DailyInsightAgentResponseDto {
//   id: number;
//   agentName: string;
//   parameters: string;
//   title: string;
//   createdAt: Date;
//   lastRunAt?: Date;
//   lastResponse?: string;
// }

// export interface ResponseMessage {
//   isError: boolean;
//   message: string;
// }

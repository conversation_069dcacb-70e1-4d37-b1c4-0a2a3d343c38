import { CommonModule, Location } from '@angular/common';
import { Component, inject, OnInit, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import {
  AiServiceProxy,
  DocsServiceProxy,
  FileParameter,
  ViewDocsDto,
  FileServiceProxy,
  ResponseMessage,
  CreateDocsDto,
} from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import EditorJS, { ToolConstructable } from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import Table from '@editorjs/table';
import ImageTool from '@editorjs/image';

@Component({
  selector: 'app-add-or-edit-document',
  standalone: true,
  imports: [FormsModule, CommonModule, ServiceProxyModule],
  templateUrl: './add-or-edit-document.component.html',
  styleUrl: './add-or-edit-document.component.css',
})
export class AddOrEditDocumentComponent implements OnInit {
  @Input() docToEdit: any;
  @Input() isNew: boolean = false;

  @Output() saveComplete = new EventEmitter<any>();
  @Output() cancelEdit = new EventEmitter<void>();

  isEditMode: boolean = true;
  documentData: ViewDocsDto = new ViewDocsDto();
  documentId: any | null = null;
  workspaceName: string | null = null;
  fileToDelete: any = [];
  selectedFiles: File[] = [];
  private editor: EditorJS | null = null;
  private editorInitialized = false;
  private pendingContent: any = null;

  constructor(
    private route: ActivatedRoute,
    private docsService: DocsServiceProxy,
    private router: Router,
    private fileService: FileServiceProxy,
  ) { }

  ngOnInit(): void {
    this.documentId = this.route.snapshot.paramMap.get('DocId');
    this.workspaceName = this.router.url.split('/')[2];
    this.documentData.workspaceName = this.workspaceName || '';
    this.documentData.files = [];

    this.isEditMode = true;

    if (this.documentId && this.documentId != '0') {
      this.loadDocument();
    }
  }

  // Add back button functionality
  goBack() {
    this.cancelEdit.emit();
  }
  ngAfterViewInit() {
    if (!this.editor) {
      setTimeout(() => {
        this.initializeEditor();
      }, 0);
    }
  }

  private initializeEditor() {
    if (this.editor) return;

    const editorElement = document.getElementById('editor');
    if (!editorElement) {
      console.error('Element with ID "editor" is missing.');
      return;
    }

    this.editor = new EditorJS({
      holder: 'editor',
      minHeight: 200,
      placeholder: 'Write your description here...',
      onChange: () => {
        this.editor?.save().then(data => {
          this.documentData.content = JSON.stringify(data); // Save content as JSON
        });
      },
      onReady: () => {
        this.editorInitialized = true;
        if (this.pendingContent) {
          this.updateEditorContent(this.pendingContent);
          this.pendingContent = null;
        }
      },
      tools: {
        header: {
          class: Header as unknown as ToolConstructable,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4],
            defaultLevel: 1
          }
        },
        list: List as unknown as ToolConstructable,
        checklist: Checklist as unknown as ToolConstructable,
        quote: Quote as unknown as ToolConstructable,
        marker: Marker as unknown as ToolConstructable,
        code: CodeTool as unknown as ToolConstructable,
        delimiter: Delimiter as unknown as ToolConstructable,
        inlineCode: InlineCode as unknown as ToolConstructable,
        table: Table as unknown as ToolConstructable,
        image: {
          class: ImageTool as unknown as { new(): any },
          config: {
            uploader: {
              uploadByFile: (file: File) => {
                return new Promise((resolve, reject) => {
                  const reader = new FileReader();
                  reader.onload = () => {
                    resolve({
                      success: 1,
                      file: {
                        url: reader.result as string
                      }
                    });
                  };
                  reader.onerror = () => {
                    reject({
                      success: 0,
                      message: 'Could not read file'
                    });
                  };
                  reader.readAsDataURL(file);
                });
              }
            }
          }
        }
      }
    });
  }

  private async updateEditorContent(newContent: any) {
    try {
      if (!this.editor) {
        console.warn('Editor not initialized yet');
        return;
      }

      await this.editor.isReady;
      await this.editor.blocks.clear();
      await this.editor.render(newContent);
    } catch (error) {
      console.error('Error updating editor content:', error);
    }
  }

  private async saveEditorContent() {
    if (!this.editor) return;

    try {
      const editorData = await this.editor.save();
      this.documentData.content = JSON.stringify(editorData); // Save content as JSON
    } catch (error) {
      console.error('Error saving editor content:', error);
    }
  }

  async loadDocument() {
    if (this.documentId) {
      try {
        const result = await firstValueFrom(this.docsService.getById(this.documentId));
        this.documentData = result;
        this.documentData.content = result.content;

        // Handle files with proper type checking
        if (result.files && Array.isArray(result.files)) {
          // Use 'any' type for the files array to avoid TypeScript errors
          (this.documentData as any).files = result.files.map((file: any) => ({
            fileName: file.fileName,
            filePath: file.filePath,
            description: file.description || 'No description available',
            file: null, // No raw file on load, only for new uploads
          }));
        } else {
          (this.documentData as any).files = [];
        }

        console.log('Edit mode enabled for document ID:', this.documentData);

        // Parse content if it exists
        if (result.content) {
          try {
            const parsedContent = JSON.parse(result.content);
            if (this.editorInitialized) {
              this.updateEditorContent(parsedContent); // Render content into the editor
            } else {
              this.pendingContent = parsedContent; // Store content to render later
            }
          } catch (error) {
            console.error('Error parsing document content:', error);
          }
        }
      } catch (error) {
        console.error('Error loading document:', error);
      }
    }
  }

  onFileSelect(event: any) {
    const files: FileList = event.target.files;
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileType = file.type.startsWith('image')
        ? 'image'
        : file.type === 'application/pdf'
          ? 'pdf'
          : null;

      if (!fileType) {
        alert('Only images and PDF files are allowed.');
        continue;
      }

      // Check for duplicates by name
      const fileName = file.name;
      const isDuplicate = this.selectedFiles.some(existingFile => existingFile.name === fileName);

      if (!isDuplicate) {
        this.selectedFiles.push(file);

        // Also add to documentData.files for display purposes
        const fileData: any = {
          fileName: file.name,
          filePath: fileType === 'image' ? URL.createObjectURL(file) : '', // Temporary URL for images
          description: `Description for ${file.name}`, // Default description
          file: file, // Store the raw File object for upload
        };
        this.documentData.files?.push(fileData);
        console.log('Added file:', fileData);
      }
    }

    // Clear the input value to allow selecting the same file again
    event.target.value = '';
  }

  removeFile(index: number) {
    const removedFile = this.documentData.files?.splice(index, 1)[0];

    // Add to files to delete list if it's an existing file
    if (removedFile?.fileName) {
      this.fileToDelete.push(removedFile.fileName);

      // Also remove from selectedFiles array if it exists there
      const fileIndex = this.selectedFiles.findIndex(f => f.name === removedFile.fileName);
      if (fileIndex !== -1) {
        this.selectedFiles.splice(fileIndex, 1);
      }
    }
  }
  location = inject(Location);

  async saveDocument() {
    await this.saveEditorContent();
    if (!this.documentData.title || !this.documentData.content) {
      alert('Please fill in title and description.');
      return;
    }

    this.documentData.workspaceName = this.workspaceName || '';

    // Format content properly
    let contentToSave = this.documentData.content || '';
    try {
      const parsedContent = JSON.parse(contentToSave);
      if (!parsedContent.title && !parsedContent.desc) {
        contentToSave = JSON.stringify({
          title: this.documentData.title,
          desc: contentToSave
        });
      }
    } catch (e) {
      contentToSave = JSON.stringify({
        title: this.documentData.title,
        desc: contentToSave
      });
    }

    try {
      // Step 1: Upload files first
      let fileToAddResponse: ResponseMessage = new ResponseMessage();

      if (this.selectedFiles.length > 0) {
        // Convert files to FileParameter array
        const fileParams: FileParameter[] = this.selectedFiles.map(file => ({
          data: file,
          fileName: file.name
        }));

        // Upload files using Promise
        try {
          fileToAddResponse = await firstValueFrom(this.fileService.upload(undefined, fileParams));
          console.log('Files uploaded successfully:', fileToAddResponse);
        } catch (error) {
          console.error('Error uploading files:', error);
          // Continue with empty file response
        }
      }

      // Step 2: Create the document DTO
      const docsCreate = new CreateDocsDto({
        id: this.documentId ? parseInt(this.documentId) : 0,
        title: this.documentData.title,
        content: contentToSave,
        workspaceName: this.workspaceName || '',
        filesToAdd: fileToAddResponse.message,
        filesToDelete: this.fileToDelete
      });

      console.log('Saving document:', docsCreate);

      // Step 3: Save the document using Promise
      const result = await firstValueFrom(this.docsService.createOrUpdate(docsCreate));
      console.log('Document saved:', result);

      // Handle success
      this.documentData = new ViewDocsDto();
      this.isEditMode = false;
      this.location.back();
      this.saveComplete.emit(result);

      // Clear file selections
      this.selectedFiles = [];
      this.fileToDelete = [];
    } catch (error) {
      console.error('Error saving document:', error);
    }
  }

  onCancel() {
    this.cancelEdit.emit();
  }
}

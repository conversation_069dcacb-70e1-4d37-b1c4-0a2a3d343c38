import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NotesService, Note } from '../services/notes.service';
import { MarkdownModule } from 'ngx-markdown';
import { ActivatedRoute, Router } from '@angular/router';

interface ChatMessage {
  text: string;
  isUser: boolean;
  timestamp: Date;
  hasCodeBlock?: boolean;
  codeLanguage?: string;
  code?: string;
}

@Component({
  selector: 'app-document-list',
  imports: [CommonModule, FormsModule, MarkdownModule],
  templateUrl: './document-list.component.html',
  styleUrl: './document-list.component.css',
  standalone: true,
})
export class DocumentListComponent implements OnInit {
  @ViewChild('chatContainer') private chatContainer!: ElementRef;
  @ViewChild('chatInput') private chatInput!: ElementRef;
  @ViewChild('editor') private editorElement!: ElementRef;

  isToogled = false;
  wordCount = 0;
  markdownContent = '';
  isSaving = false;
  isLoading = false;
  isGenerating = false;
  generateModalVisible = false;
  aiPrompt = '';
  contentType = 'article';
  useCurrentContent = false;
  chatMessages: ChatMessage[] = [
    {
      text: "Hello! How can I help you today?",
      isUser: false,
      timestamp: new Date(),
    }
  ];
  selectedOption = 'article';
  documents: Note[] = [];
  // Add title property
  noteTitle: string = '';
  currentNoteId: number | null = null;

  isDocsOpen = false;  // Set default to false to hide docs section
  showDeleteModal = false;
  noteToDelete: number | null = null;
  sessionId: string = '';
  hasContent = false;
  isjournal = false;

  // Pagination properties
  currentPage = 1;
  totalPages = 1;
  pageSize = 15;

  constructor(private notesService: NotesService, private router: Router, private route: ActivatedRoute) { }

  ngOnInit() {
    this.sessionId = crypto.randomUUID();
    const currentUrl = this.router.url;
    if (currentUrl.includes('journal')) {
      this.isjournal = true;
      console.log('Journal detected in URL');
    }
    this.loadNotes();
  }

  createNewDocument() {
    this.router.navigate(['/editor']);
  }

  private loadNotes() {
    this.isLoading = true;
    this.notesService.getAllNotes().subscribe({
      next: (notes) => {
        this.documents = notes;
        this.totalPages = Math.ceil(this.documents.length / this.pageSize);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading notes:', error);
        this.isLoading = false;
      }
    });
  }

  get paginatedDocuments(): Note[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.documents.slice(startIndex, endIndex);
  }

  deleteDocument(id: number, event: MouseEvent) {
    event.stopPropagation();
    this.noteToDelete = id;
    this.showDeleteModal = true;
  }

  editDocument(doc: Note, event: MouseEvent) {
    event.stopPropagation();
    this.router.navigate(['editor', doc.id]);
  }

  viewDocument(doc: Note, event: MouseEvent) {
    event.stopPropagation();
    this.router.navigate(['document', doc.id]);
  }

  toggleDocs() {
    this.isDocsOpen = !this.isDocsOpen;
  }

  async confirmDelete() {
    if (this.noteToDelete) {
      try {
        await this.notesService.deleteNote(this.noteToDelete).toPromise();
        this.loadNotes();
      } catch (error) {
        console.error('Error deleting document:', error);
      } finally {
        this.showDeleteModal = false;
        this.noteToDelete = null;
      }
    }
  }

  // Pagination methods
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }
}

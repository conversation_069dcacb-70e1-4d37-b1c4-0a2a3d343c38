<div class="min-h-screen bg-[#1A1A1A] text-white">
  <div class="flex">
    <!-- Left Sidebar -->
    <div class="w-48 border-r border-[#2A2A2A] px-4 py-2">
      <div class="space-y-1 *:no-underline transition-all">
        <div class="flex items-center gap-2   text-gray-400 py-1 cursor-pointer" (click)="setTab('leaderboard')"
          [class]="tabs === 'leaderboard' ? 'text-white' : ''">
          <i class="ri-trophy-line"></i>
          <span class="text-sm cursor-pointer">Leaderboard</span>
        </div>
        <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1 cursor-pointer"
          (click)="setTab('feedbacks')" [class]="tabs === 'feedbacks' ? 'text-white' : ''">
          <i class="ri-feedback-line"></i>
          <span class="text-sm cursor-pointer">Feedbacks</span>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1" *ngIf="tabs === 'leaderboard'">
      <div class="p-6">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div class="flex items-center gap-2">
            <h1 class="text-xl  text-white">Leaderboard</h1>
            <span class="bg-[#2A2A2A] px-2 py-0.5 rounded text-sm">3</span>
          </div>
          <div class="relative">
            <input type="text" placeholder="Search"
              class="bg-[#2A2A2A] px-4 py-1.5 rounded pl-9 text-sm w-64 focus:outline-none border-none">
            <i class="ri-search-line absolute left-3 top-2 text-gray-400"></i>
          </div>
        </div>

        <!-- Table -->
        <div class="bg-[#1F1F1F] rounded-lg overflow-hidden">
          <table class="w-full">
            <thead class="bg-[#262626] text-xs text-gray-400">
              <tr>
                <th class="text-left py-3 px-4 font-medium">RK</th>
                <th class="text-left py-3 px-4 font-medium">MODEL</th>
                <th class="text-right py-3 px-4 font-medium">RATING</th>
                <th class="text-right py-3 px-4 font-medium">WON</th>
                <th class="text-right py-3 px-4 font-medium">LOST</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-[#2A2A2A]">
              <tr *ngFor="let model of models" class="hover:bg-[#2A2A2A]">
                <td class="py-3 px-4 text-gray-400">-</td>
                <td class="py-3 px-4">
                  <div class="flex items-center gap-2">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                      <i class="ri-ai-generate text-sm"></i>
                    </div>
                    <span>{{model.name}}</span>
                  </div>
                </td>
                <td class="py-3 px-4 text-right text-gray-400">-</td>
                <td class="py-3 px-4 text-right text-green-500">-</td>
                <td class="py-3 px-4 text-right text-red-500">-</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Footer Note -->
        <div class="mt-4 text-xs text-gray-500">
          <p>The evaluation leaderboard is based on the Elo rating system and is updated in real-time.</p>
          <p>The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.
          </p>
        </div>
      </div>
    </div>
    <div class="flex flex-col h-full w-full" *ngIf="tabs === 'feedbacks'">
      <!-- Header -->
      <div class="flex justify-between items-center border-b border-[#2A2A2A]">
        <div class="flex items-center gap-2">
          <h1 class="text-xl font-medium  text-white">Evaluations</h1>
          <span class="bg-[#2A2A2A] px-2 py-0.5 rounded text-xs">0</span>
        </div>
        <div class="flex items-center gap-3">
          <div class="relative">
            <input type="text" placeholder="Search"
              class="bg-[#2A2A2A] w-64 text-white outline-none border-none p-2 rounded-md pl-9 text-sm">
            <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
          </div>
          <button
            class="bg-transparent text-white rounded-md text-sm hover:text-gray-200 transition-colors border-none outline-none cursor-pointer">
            <i class="ri-add-line mr-1"></i>
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <div class="flex-1 flex flex-col items-center justify-center p-8 text-center">
        <h2 class="text-xl font-medium mb-2  text-white">No evaluations yet</h2>
        <p class="text-[#8C8C8C] text-sm mb-6">Create an evaluation to compare model performance.</p>
        <button class="bg-white text-black px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
          Create Evaluation
        </button>
      </div>
    </div>
  </div>
</div>

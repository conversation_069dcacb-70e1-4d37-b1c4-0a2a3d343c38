{"version": 3, "sources": ["../../../../../node_modules/quill/dist/quill.js"], "sourcesContent": ["/*! For license information please see quill.js.LICENSE.txt */\n!function(t,e){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define([],e):\"object\"==typeof exports?exports.Quill=e():t.Quill=e()}(self,(function(){return function(){var t={9698:function(t,e,n){\"use strict\";n.d(e,{Ay:function(){return c},Ji:function(){return d},mG:function(){return h},zo:function(){return u}});var r=n(6003),i=n(5232),s=n.n(i),o=n(3036),l=n(4850),a=n(5508);class c extends r.BlockBlot{cache={};delta(){return null==this.cache.delta&&(this.cache.delta=h(this)),this.cache.delta}deleteAt(t,e){super.deleteAt(t,e),this.cache={}}formatAt(t,e,n,i){e<=0||(this.scroll.query(n,r.Scope.BLOCK)?t+e===this.length()&&this.format(n,i):super.formatAt(t,Math.min(e,this.length()-t-1),n,i),this.cache={})}insertAt(t,e,n){if(null!=n)return super.insertAt(t,e,n),void(this.cache={});if(0===e.length)return;const r=e.split(\"\\n\"),i=r.shift();i.length>0&&(t<this.length()-1||null==this.children.tail?super.insertAt(Math.min(t,this.length()-1),i):this.children.tail.insertAt(this.children.tail.length(),i),this.cache={});let s=this;r.reduce(((t,e)=>(s=s.split(t,!0),s.insertAt(0,e),e.length)),t+i.length)}insertBefore(t,e){const{head:n}=this.children;super.insertBefore(t,e),n instanceof o.A&&n.remove(),this.cache={}}length(){return null==this.cache.length&&(this.cache.length=super.length()+1),this.cache.length}moveChildren(t,e){super.moveChildren(t,e),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){const e=this.clone();return 0===t?(this.parent.insertBefore(e,this),this):(this.parent.insertBefore(e,this.next),e)}const n=super.split(t,e);return this.cache={},n}}c.blotName=\"block\",c.tagName=\"P\",c.defaultChild=o.A,c.allowedChildren=[o.A,l.A,r.EmbedBlot,a.A];class u extends r.EmbedBlot{attach(){super.attach(),this.attributes=new r.AttributorStore(this.domNode)}delta(){return(new(s())).insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const n=this.scroll.query(t,r.Scope.BLOCK_ATTRIBUTE);null!=n&&this.attributes.attribute(n,e)}formatAt(t,e,n,r){this.format(n,r)}insertAt(t,e,n){if(null!=n)return void super.insertAt(t,e,n);const r=e.split(\"\\n\"),i=r.pop(),s=r.map((t=>{const e=this.scroll.create(c.blotName);return e.insertAt(0,t),e})),o=this.split(t);s.forEach((t=>{this.parent.insertBefore(t,o)})),i&&this.parent.insertBefore(this.scroll.create(\"text\",i),o)}}function h(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return t.descendants(r.LeafBlot).reduce(((t,n)=>0===n.length()?t:t.insert(n.value(),d(n,{},e))),new(s())).insert(\"\\n\",d(t))}function d(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return null==t?e:(\"formats\"in t&&\"function\"==typeof t.formats&&(e={...e,...t.formats()},n&&delete e[\"code-token\"]),null==t.parent||\"scroll\"===t.parent.statics.blotName||t.parent.statics.scope!==t.statics.scope?e:d(t.parent,e,n))}u.scope=r.Scope.BLOCK_BLOT},3036:function(t,e,n){\"use strict\";var r=n(6003);class i extends r.EmbedBlot{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return\"\"}}i.blotName=\"break\",i.tagName=\"BR\",e.A=i},580:function(t,e,n){\"use strict\";var r=n(6003);class i extends r.ContainerBlot{}e.A=i},4541:function(t,e,n){\"use strict\";var r=n(6003),i=n(5508);class s extends r.EmbedBlot{static blotName=\"cursor\";static className=\"ql-cursor\";static tagName=\"span\";static CONTENTS=\"\\ufeff\";static value(){}constructor(t,e,n){super(t,e),this.selection=n,this.textNode=document.createTextNode(s.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){null!=this.parent&&this.parent.removeChild(this)}format(t,e){if(0!==this.savedLength)return void super.format(t,e);let n=this,i=0;for(;null!=n&&n.statics.scope!==r.Scope.BLOCK_BLOT;)i+=n.offset(n.parent),n=n.parent;null!=n&&(this.savedLength=s.CONTENTS.length,n.optimize(),n.formatAt(i,s.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||null==this.parent)return null;const t=this.selection.getNativeRange();for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof i.A?this.prev:null,n=e?e.length():0,r=this.next instanceof i.A?this.next:null,o=r?r.text:\"\",{textNode:l}=this,a=l.data.split(s.CONTENTS).join(\"\");let c;if(l.data=s.CONTENTS,e)c=e,(a||r)&&(e.insertAt(e.length(),a+o),r&&r.remove());else if(r)c=r,r.insertAt(0,a);else{const t=document.createTextNode(a);c=this.scroll.create(t),this.parent.insertBefore(c,this)}if(this.remove(),t){const i=(t,i)=>e&&t===e.domNode?i:t===l?n+i-1:r&&t===r.domNode?n+a.length+i:null,s=i(t.start.node,t.start.offset),o=i(t.end.node,t.end.offset);if(null!==s&&null!==o)return{startNode:c.domNode,startOffset:s,endNode:c.domNode,endOffset:o}}return null}update(t,e){if(t.some((t=>\"characterData\"===t.type&&t.target===this.textNode))){const t=this.restore();t&&(e.range=t)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(\"A\"===e.domNode.tagName){this.savedLength=s.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return\"\"}}e.A=s},746:function(t,e,n){\"use strict\";var r=n(6003),i=n(5508);const s=\"\\ufeff\";class o extends r.EmbedBlot{constructor(t,e){super(t,e),this.contentNode=document.createElement(\"span\"),this.contentNode.setAttribute(\"contenteditable\",\"false\"),Array.from(this.domNode.childNodes).forEach((t=>{this.contentNode.appendChild(t)})),this.leftGuard=document.createTextNode(s),this.rightGuard=document.createTextNode(s),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e,n=null;const r=t.data.split(s).join(\"\");if(t===this.leftGuard)if(this.prev instanceof i.A){const t=this.prev.length();this.prev.insertAt(t,r),n={startNode:this.prev.domNode,startOffset:t+r.length}}else e=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(e),this),n={startNode:e,startOffset:r.length};else t===this.rightGuard&&(this.next instanceof i.A?(this.next.insertAt(0,r),n={startNode:this.next.domNode,startOffset:r.length}):(e=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(e),this.next),n={startNode:e,startOffset:r.length}));return t.data=s,n}update(t,e){t.forEach((t=>{if(\"characterData\"===t.type&&(t.target===this.leftGuard||t.target===this.rightGuard)){const n=this.restore(t.target);n&&(e.range=n)}}))}}e.A=o},4850:function(t,e,n){\"use strict\";var r=n(6003),i=n(3036),s=n(5508);class o extends r.InlineBlot{static allowedChildren=[o,i.A,r.EmbedBlot,s.A];static order=[\"cursor\",\"inline\",\"link\",\"underline\",\"strike\",\"italic\",\"bold\",\"script\",\"code\"];static compare(t,e){const n=o.order.indexOf(t),r=o.order.indexOf(e);return n>=0||r>=0?n-r:t===e?0:t<e?-1:1}formatAt(t,e,n,i){if(o.compare(this.statics.blotName,n)<0&&this.scroll.query(n,r.Scope.BLOT)){const r=this.isolate(t,e);i&&r.wrap(n,i)}else super.formatAt(t,e,n,i)}optimize(t){if(super.optimize(t),this.parent instanceof o&&o.compare(this.statics.blotName,this.parent.statics.blotName)>0){const t=this.parent.isolate(this.offset(),this.length());this.moveChildren(t),t.wrap(this)}}}e.A=o},5508:function(t,e,n){\"use strict\";n.d(e,{A:function(){return i},X:function(){return o}});var r=n(6003);class i extends r.TextBlot{}const s={\"&\":\"&amp;\",\"<\":\"&lt;\",\">\":\"&gt;\",'\"':\"&quot;\",\"'\":\"&#39;\"};function o(t){return t.replace(/[&<>\"']/g,(t=>s[t]))}},3729:function(t,e,n){\"use strict\";n.d(e,{default:function(){return R}});var r=n(6142),i=n(9698),s=n(3036),o=n(580),l=n(4541),a=n(746),c=n(4850),u=n(6003),h=n(5232),d=n.n(h),f=n(5374);function p(t){return t instanceof i.Ay||t instanceof i.zo}function g(t){return\"function\"==typeof t.updateContent}class m extends u.ScrollBlot{static blotName=\"scroll\";static className=\"ql-editor\";static tagName=\"DIV\";static defaultChild=i.Ay;static allowedChildren=[i.Ay,i.zo,o.A];constructor(t,e,n){let{emitter:r}=n;super(t,e),this.emitter=r,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener(\"dragstart\",(t=>this.handleDragStart(t)))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(f.A.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(f.A.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(f.A.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[n,r]=this.line(t),[o]=this.line(t+e);if(super.deleteAt(t,e),null!=o&&n!==o&&r>0){if(n instanceof i.zo||o instanceof i.zo)return void this.optimize();const t=o.children.head instanceof s.A?null:o.children.head;n.moveChildren(o,t),n.remove()}this.optimize()}enable(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.domNode.setAttribute(\"contenteditable\",t?\"true\":\"false\")}formatAt(t,e,n,r){super.formatAt(t,e,n,r),this.optimize()}insertAt(t,e,n){if(t>=this.length())if(null==n||null==this.scroll.query(e,u.Scope.BLOCK)){const t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t),null==n&&e.endsWith(\"\\n\")?t.insertAt(0,e.slice(0,-1),n):t.insertAt(0,e,n)}else{const t=this.scroll.create(e,n);this.appendChild(t)}else super.insertAt(t,e,n);this.optimize()}insertBefore(t,e){if(t.statics.scope===u.Scope.INLINE_BLOT){const n=this.scroll.create(this.statics.defaultChild.blotName);n.appendChild(t),super.insertBefore(n,e)}else super.insertBefore(t,e)}insertContents(t,e){const n=this.deltaToRenderBlocks(e.concat((new(d())).insert(\"\\n\"))),r=n.pop();if(null==r)return;this.batchStart();const s=n.shift();if(s){const e=\"block\"===s.type&&(0===s.delta.length()||!this.descendant(i.zo,t)[0]&&t<this.length()),n=\"block\"===s.type?s.delta:(new(d())).insert({[s.key]:s.value});b(this,t,n);const r=\"block\"===s.type?1:0,o=t+n.length()+r;e&&this.insertAt(o-1,\"\\n\");const l=(0,i.Ji)(this.line(t)[0]),a=h.AttributeMap.diff(l,s.attributes)||{};Object.keys(a).forEach((t=>{this.formatAt(o-1,1,t,a[t])})),t=o}let[o,l]=this.children.find(t);n.length&&(o&&(o=o.split(l),l=0),n.forEach((t=>{if(\"block\"===t.type)b(this.createBlock(t.attributes,o||void 0),0,t.delta);else{const e=this.create(t.key,t.value);this.insertBefore(e,o||void 0),Object.keys(t.attributes).forEach((n=>{e.format(n,t.attributes[n])}))}}))),\"block\"===r.type&&r.delta.length()&&b(this,o?o.offset(o.scroll)+l:this.length(),r.delta),this.batchEnd(),this.optimize()}isEnabled(){return\"true\"===this.domNode.getAttribute(\"contenteditable\")}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[n,r]=e;return n instanceof u.LeafBlot?[n,r]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(p,t)}lines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;const n=(t,e,r)=>{let i=[],s=r;return t.children.forEachAt(e,r,((t,e,r)=>{p(t)?i.push(t):t instanceof u.ContainerBlot&&(i=i.concat(n(t,e,s))),s-=r})),i};return n(this,t,e)}optimize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(f.A.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch)return void(Array.isArray(t)&&(this.batch=this.batch.concat(t)));let e=f.A.sources.USER;\"string\"==typeof t&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),(t=t.filter((t=>{let{target:e}=t;const n=this.find(e,!0);return n&&!g(n)}))).length>0&&this.emitter.emit(f.A.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(f.A.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,n){const[r]=this.descendant((t=>t instanceof i.zo),t);r&&r.statics.blotName===e&&g(r)&&r.updateContent(n)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let n=new(d());return t.forEach((t=>{const r=t?.insert;if(r)if(\"string\"==typeof r){const i=r.split(\"\\n\");i.slice(0,-1).forEach((r=>{n.insert(r,t.attributes),e.push({type:\"block\",delta:n,attributes:t.attributes??{}}),n=new(d())}));const s=i[i.length-1];s&&n.insert(s,t.attributes)}else{const i=Object.keys(r)[0];if(!i)return;this.query(i,u.Scope.INLINE)?n.push(t):(n.length()&&e.push({type:\"block\",delta:n,attributes:{}}),n=new(d()),e.push({type:\"blockEmbed\",key:i,value:r[i],attributes:t.attributes??{}}))}})),n.length()&&e.push({type:\"block\",delta:n,attributes:{}}),e}createBlock(t,e){let n;const r={};Object.entries(t).forEach((t=>{let[e,i]=t;null!=this.query(e,u.Scope.BLOCK&u.Scope.BLOT)?n=e:r[e]=i}));const i=this.create(n||this.statics.defaultChild.blotName,n?t[n]:void 0);this.insertBefore(i,e||void 0);const s=i.length();return Object.entries(r).forEach((t=>{let[e,n]=t;i.formatAt(0,s,e,n)})),i}}function b(t,e,n){n.reduce(((e,n)=>{const r=h.Op.length(n);let s=n.attributes||{};if(null!=n.insert)if(\"string\"==typeof n.insert){const r=n.insert;t.insertAt(e,r);const[o]=t.descendant(u.LeafBlot,e),l=(0,i.Ji)(o);s=h.AttributeMap.diff(l,s)||{}}else if(\"object\"==typeof n.insert){const r=Object.keys(n.insert)[0];if(null==r)return e;if(t.insertAt(e,r,n.insert[r]),null!=t.scroll.query(r,u.Scope.INLINE)){const[n]=t.descendant(u.LeafBlot,e),r=(0,i.Ji)(n);s=h.AttributeMap.diff(r,s)||{}}}return Object.keys(s).forEach((n=>{t.formatAt(e,r,n,s[n])})),e+r}),e)}var y=m,v=n(5508),A=n(584),x=n(4266);class N extends x.A{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,e){super(t,e),this.quill.on(r.Ay.events.EDITOR_CHANGE,((t,e,n,i)=>{t===r.Ay.events.SELECTION_CHANGE?e&&i!==r.Ay.sources.SILENT&&(this.currentRange=e):t===r.Ay.events.TEXT_CHANGE&&(this.ignoreChange||(this.options.userOnly&&i!==r.Ay.sources.USER?this.transform(e):this.record(e,n)),this.currentRange=w(this.currentRange,e))})),this.quill.keyboard.addBinding({key:\"z\",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:[\"z\",\"Z\"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:\"y\",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener(\"beforeinput\",(t=>{\"historyUndo\"===t.inputType?(this.undo(),t.preventDefault()):\"historyRedo\"===t.inputType&&(this.redo(),t.preventDefault())}))}change(t,e){if(0===this.stack[t].length)return;const n=this.stack[t].pop();if(!n)return;const i=this.quill.getContents(),s=n.delta.invert(i);this.stack[e].push({delta:s,range:w(n.range,s)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n.delta,r.Ay.sources.USER),this.ignoreChange=!1,this.restoreSelection(n)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,e){if(0===t.ops.length)return;this.stack.redo=[];let n=t.invert(e),r=this.currentRange;const i=Date.now();if(this.lastRecorded+this.options.delay>i&&this.stack.undo.length>0){const t=this.stack.undo.pop();t&&(n=n.compose(t.delta),r=t.range)}else this.lastRecorded=i;0!==n.length()&&(this.stack.undo.push({delta:n,range:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change(\"redo\",\"undo\")}transform(t){E(this.stack.undo,t),E(this.stack.redo,t)}undo(){this.change(\"undo\",\"redo\")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,r.Ay.sources.USER);else{const e=function(t,e){const n=e.reduce(((t,e)=>t+(e.delete||0)),0);let r=e.length()-n;return function(t,e){const n=e.ops[e.ops.length-1];return null!=n&&(null!=n.insert?\"string\"==typeof n.insert&&n.insert.endsWith(\"\\n\"):null!=n.attributes&&Object.keys(n.attributes).some((e=>null!=t.query(e,u.Scope.BLOCK))))}(t,e)&&(r-=1),r}(this.quill.scroll,t.delta);this.quill.setSelection(e,r.Ay.sources.USER)}}}function E(t,e){let n=e;for(let e=t.length-1;e>=0;e-=1){const r=t[e];t[e]={delta:n.transform(r.delta,!0),range:r.range&&w(r.range,n)},n=r.delta.transform(n),0===t[e].delta.length()&&t.splice(e,1)}}function w(t,e){if(!t)return t;const n=e.transformPosition(t.index);return{index:n,length:e.transformPosition(t.index+t.length)-n}}var q=n(8123);class k extends x.A{constructor(t,e){super(t,e),t.root.addEventListener(\"drop\",(e=>{e.preventDefault();let n=null;if(document.caretRangeFromPoint)n=document.caretRangeFromPoint(e.clientX,e.clientY);else if(document.caretPositionFromPoint){const t=document.caretPositionFromPoint(e.clientX,e.clientY);n=document.createRange(),n.setStart(t.offsetNode,t.offset),n.setEnd(t.offsetNode,t.offset)}const r=n&&t.selection.normalizeNative(n);if(r){const n=t.selection.normalizedToRange(r);e.dataTransfer?.files&&this.upload(n,e.dataTransfer.files)}}))}upload(t,e){const n=[];Array.from(e).forEach((t=>{t&&this.options.mimetypes?.includes(t.type)&&n.push(t)})),n.length>0&&this.options.handler.call(this,t,n)}}k.DEFAULTS={mimetypes:[\"image/png\",\"image/jpeg\"],handler(t,e){if(!this.quill.scroll.query(\"image\"))return;const n=e.map((t=>new Promise((e=>{const n=new FileReader;n.onload=()=>{e(n.result)},n.readAsDataURL(t)}))));Promise.all(n).then((e=>{const n=e.reduce(((t,e)=>t.insert({image:e})),(new(d())).retain(t.index).delete(t.length));this.quill.updateContents(n,f.A.sources.USER),this.quill.setSelection(t.index+e.length,f.A.sources.SILENT)}))}};var _=k;const L=[\"insertText\",\"insertReplacementText\"];class S extends x.A{constructor(t,e){super(t,e),t.root.addEventListener(\"beforeinput\",(t=>{this.handleBeforeInput(t)})),/Android/i.test(navigator.userAgent)||t.on(r.Ay.events.COMPOSITION_BEFORE_START,(()=>{this.handleCompositionStart()}))}deleteRange(t){(0,q.Xo)({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";if(0===t.length)return!1;if(e){const n=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents((new(d())).retain(t.index).insert(e,n),r.Ay.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,r.Ay.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!L.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||!0===e.collapsed)return;const n=function(t){return\"string\"==typeof t.data?t.data:t.dataTransfer?.types.includes(\"text/plain\")?t.dataTransfer.getData(\"text/plain\"):null}(t);if(null==n)return;const r=this.quill.selection.normalizeNative(e),i=r?this.quill.selection.normalizedToRange(r):null;i&&this.replaceText(i,n)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}var O=S;const T=/Mac/i.test(navigator.platform);class j extends x.A{isListening=!1;selectionChangeDeadline=0;constructor(t,e){super(t,e),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:[\"ArrowLeft\",\"ArrowRight\"],offset:0,shiftKey:null,handler(t,e){let{line:n,event:i}=e;if(!(n instanceof u.ParentBlot&&n.uiNode))return!0;const s=\"rtl\"===getComputedStyle(n.domNode).direction;return!!(s&&\"ArrowRight\"!==i.key||!s&&\"ArrowLeft\"!==i.key)||(this.quill.setSelection(t.index-1,t.length+(i.shiftKey?1:0),r.Ay.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener(\"keydown\",(t=>{!t.defaultPrevented&&(t=>\"ArrowLeft\"===t.key||\"ArrowRight\"===t.key||\"ArrowUp\"===t.key||\"ArrowDown\"===t.key||\"Home\"===t.key||!(!T||\"a\"!==t.key||!0!==t.ctrlKey))(t)&&this.ensureListeningToSelectionChange()}))}ensureListeningToSelectionChange(){this.selectionChangeDeadline=Date.now()+100,this.isListening||(this.isListening=!0,document.addEventListener(\"selectionchange\",(()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()}),{once:!0}))}handleSelectionChange(){const t=document.getSelection();if(!t)return;const e=t.getRangeAt(0);if(!0!==e.collapsed||0!==e.startOffset)return;const n=this.quill.scroll.find(e.startContainer);if(!(n instanceof u.ParentBlot&&n.uiNode))return;const r=document.createRange();r.setStartAfter(n.uiNode),r.setEndAfter(n.uiNode),t.removeAllRanges(),t.addRange(r)}}var C=j;r.Ay.register({\"blots/block\":i.Ay,\"blots/block/embed\":i.zo,\"blots/break\":s.A,\"blots/container\":o.A,\"blots/cursor\":l.A,\"blots/embed\":a.A,\"blots/inline\":c.A,\"blots/scroll\":y,\"blots/text\":v.A,\"modules/clipboard\":A.Ay,\"modules/history\":N,\"modules/keyboard\":q.Ay,\"modules/uploader\":_,\"modules/input\":O,\"modules/uiNode\":C});var R=r.Ay},5374:function(t,e,n){\"use strict\";n.d(e,{A:function(){return o}});var r=n(8920),i=n(7356);const s=(0,n(6078).A)(\"quill:events\");[\"selectionchange\",\"mousedown\",\"mouseup\",\"click\"].forEach((t=>{document.addEventListener(t,(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];Array.from(document.querySelectorAll(\".ql-container\")).forEach((t=>{const n=i.A.get(t);n&&n.emitter&&n.emitter.handleDOM(...e)}))}))}));var o=class extends r{static events={EDITOR_CHANGE:\"editor-change\",SCROLL_BEFORE_UPDATE:\"scroll-before-update\",SCROLL_BLOT_MOUNT:\"scroll-blot-mount\",SCROLL_BLOT_UNMOUNT:\"scroll-blot-unmount\",SCROLL_OPTIMIZE:\"scroll-optimize\",SCROLL_UPDATE:\"scroll-update\",SCROLL_EMBED_UPDATE:\"scroll-embed-update\",SELECTION_CHANGE:\"selection-change\",TEXT_CHANGE:\"text-change\",COMPOSITION_BEFORE_START:\"composition-before-start\",COMPOSITION_START:\"composition-start\",COMPOSITION_BEFORE_END:\"composition-before-end\",COMPOSITION_END:\"composition-end\"};static sources={API:\"api\",SILENT:\"silent\",USER:\"user\"};constructor(){super(),this.domListeners={},this.on(\"error\",s.error)}emit(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return s.log.call(s,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];(this.domListeners[t.type]||[]).forEach((e=>{let{node:r,handler:i}=e;(t.target===r||r.contains(t.target))&&i(t,...n)}))}listenDOM(t,e,n){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:n})}}},7356:function(t,e){\"use strict\";e.A=new WeakMap},6078:function(t,e){\"use strict\";const n=[\"error\",\"warn\",\"log\",\"info\"];let r=\"warn\";function i(t){if(r&&n.indexOf(t)<=n.indexOf(r)){for(var e=arguments.length,i=new Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];console[t](...i)}}function s(t){return n.reduce(((e,n)=>(e[n]=i.bind(console,n,t),e)),{})}s.level=t=>{r=t},i.level=s.level,e.A=s},4266:function(t,e){\"use strict\";e.A=class{static DEFAULTS={};constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.quill=t,this.options=e}}},6142:function(t,e,n){\"use strict\";n.d(e,{Ay:function(){return I}});var r=n(8347),i=n(6003),s=n(5232),o=n.n(s),l=n(3707),a=n(5123),c=n(9698),u=n(3036),h=n(4541),d=n(5508),f=n(8298);const p=/^[ -~]*$/;function g(t,e,n){if(0===t.length){const[t]=y(n.pop());return e<=0?`</li></${t}>`:`</li></${t}>${g([],e-1,n)}`}const[{child:r,offset:i,length:s,indent:o,type:l},...a]=t,[c,u]=y(l);if(o>e)return n.push(l),o===e+1?`<${c}><li${u}>${m(r,i,s)}${g(a,o,n)}`:`<${c}><li>${g(t,e+1,n)}`;const h=n[n.length-1];if(o===e&&l===h)return`</li><li${u}>${m(r,i,s)}${g(a,o,n)}`;const[d]=y(n.pop());return`</li></${d}>${g(t,e-1,n)}`}function m(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(\"html\"in t&&\"function\"==typeof t.html)return t.html(e,n);if(t instanceof d.A)return(0,d.X)(t.value().slice(e,e+n)).replaceAll(\" \",\"&nbsp;\");if(t instanceof i.ParentBlot){if(\"list-container\"===t.statics.blotName){const r=[];return t.children.forEachAt(e,n,((t,e,n)=>{const i=\"formats\"in t&&\"function\"==typeof t.formats?t.formats():{};r.push({child:t,offset:e,length:n,indent:i.indent||0,type:i.list})})),g(r,-1,[])}const i=[];if(t.children.forEachAt(e,n,((t,e,n)=>{i.push(m(t,e,n))})),r||\"list\"===t.statics.blotName)return i.join(\"\");const{outerHTML:s,innerHTML:o}=t.domNode,[l,a]=s.split(`>${o}<`);return\"<table\"===l?`<table style=\"border: 1px solid #000;\">${i.join(\"\")}<${a}`:`${l}>${i.join(\"\")}<${a}`}return t.domNode instanceof Element?t.domNode.outerHTML:\"\"}function b(t,e){return Object.keys(e).reduce(((n,r)=>{if(null==t[r])return n;const i=e[r];return i===t[r]?n[r]=i:Array.isArray(i)?i.indexOf(t[r])<0?n[r]=i.concat([t[r]]):n[r]=i:n[r]=[i,t[r]],n}),{})}function y(t){const e=\"ordered\"===t?\"ol\":\"ul\";switch(t){case\"checked\":return[e,' data-list=\"checked\"'];case\"unchecked\":return[e,' data-list=\"unchecked\"'];default:return[e,\"\"]}}function v(t){return t.reduce(((t,e)=>{if(\"string\"==typeof e.insert){const n=e.insert.replace(/\\r\\n/g,\"\\n\").replace(/\\r/g,\"\\n\");return t.insert(n,e.attributes)}return t.push(e)}),new(o()))}function A(t,e){let{index:n,length:r}=t;return new f.Q(n+e,r)}var x=class{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const n=v(t),l=new(o());return function(t){const e=[];return t.forEach((t=>{\"string\"==typeof t.insert?t.insert.split(\"\\n\").forEach(((n,r)=>{r&&e.push({insert:\"\\n\",attributes:t.attributes}),n&&e.push({insert:n,attributes:t.attributes})})):e.push(t)})),e}(n.ops.slice()).reduce(((t,n)=>{const o=s.Op.length(n);let a=n.attributes||{},u=!1,h=!1;if(null!=n.insert){if(l.retain(o),\"string\"==typeof n.insert){const o=n.insert;h=!o.endsWith(\"\\n\")&&(e<=t||!!this.scroll.descendant(c.zo,t)[0]),this.scroll.insertAt(t,o);const[l,u]=this.scroll.line(t);let d=(0,r.A)({},(0,c.Ji)(l));if(l instanceof c.Ay){const[t]=l.descendant(i.LeafBlot,u);t&&(d=(0,r.A)(d,(0,c.Ji)(t)))}a=s.AttributeMap.diff(d,a)||{}}else if(\"object\"==typeof n.insert){const o=Object.keys(n.insert)[0];if(null==o)return t;const l=null!=this.scroll.query(o,i.Scope.INLINE);if(l)(e<=t||this.scroll.descendant(c.zo,t)[0])&&(h=!0);else if(t>0){const[e,n]=this.scroll.descendant(i.LeafBlot,t-1);e instanceof d.A?\"\\n\"!==e.value()[n]&&(u=!0):e instanceof i.EmbedBlot&&e.statics.scope===i.Scope.INLINE_BLOT&&(u=!0)}if(this.scroll.insertAt(t,o,n.insert[o]),l){const[e]=this.scroll.descendant(i.LeafBlot,t);if(e){const t=(0,r.A)({},(0,c.Ji)(e));a=s.AttributeMap.diff(t,a)||{}}}}e+=o}else if(l.push(n),null!==n.retain&&\"object\"==typeof n.retain){const e=Object.keys(n.retain)[0];if(null==e)return t;this.scroll.updateEmbedAt(t,e,n.retain[e])}Object.keys(a).forEach((e=>{this.scroll.formatAt(t,o,e,a[e])}));const f=u?1:0,p=h?1:0;return e+=f+p,l.retain(f),l.delete(p),t+o+f+p}),0),l.reduce(((t,e)=>\"number\"==typeof e.delete?(this.scroll.deleteAt(t,e.delete),t):t+s.Op.length(e)),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(n)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update((new(o())).retain(t).delete(e))}formatLine(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(n).forEach((r=>{this.scroll.lines(t,Math.max(e,1)).forEach((t=>{t.format(r,n[r])}))})),this.scroll.optimize();const r=(new(o())).retain(t).retain(e,(0,l.A)(n));return this.update(r)}formatText(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(n).forEach((r=>{this.scroll.formatAt(t,e,r,n[r])}));const r=(new(o())).retain(t).retain(e,(0,l.A)(n));return this.update(r)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce(((t,e)=>t.concat(e.delta())),new(o()))}getFormat(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[],r=[];0===e?this.scroll.path(t).forEach((t=>{const[e]=t;e instanceof c.Ay?n.push(e):e instanceof i.LeafBlot&&r.push(e)})):(n=this.scroll.lines(t,e),r=this.scroll.descendants(i.LeafBlot,t,e));const[s,o]=[n,r].map((t=>{const e=t.shift();if(null==e)return{};let n=(0,c.Ji)(e);for(;Object.keys(n).length>0;){const e=t.shift();if(null==e)return n;n=b((0,c.Ji)(e),n)}return n}));return{...s,...o}}getHTML(t,e){const[n,r]=this.scroll.line(t);if(n){const i=n.length();return n.length()>=r+e&&(0!==r||e!==i)?m(n,r,e,!0):m(this.scroll,t,e,!0)}return\"\"}getText(t,e){return this.getContents(t,e).filter((t=>\"string\"==typeof t.insert)).map((t=>t.insert)).join(\"\")}insertContents(t,e){const n=v(e),r=(new(o())).retain(t).concat(n);return this.scroll.insertContents(t,n),this.update(r)}insertEmbed(t,e,n){return this.scroll.insertAt(t,e,n),this.update((new(o())).retain(t).insert({[e]:n}))}insertText(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\\r\\n/g,\"\\n\").replace(/\\r/g,\"\\n\"),this.scroll.insertAt(t,e),Object.keys(n).forEach((r=>{this.scroll.formatAt(t,e.length,r,n[r])})),this.update((new(o())).retain(t).insert(e,(0,l.A)(n)))}isBlank(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if(t?.statics.blotName!==c.Ay.blotName)return!1;const e=t;return!(e.children.length>1)&&e.children.head instanceof u.A}removeFormat(t,e){const n=this.getText(t,e),[r,i]=this.scroll.line(t+e);let s=0,l=new(o());null!=r&&(s=r.length()-i,l=r.delta().slice(i,i+s-1).insert(\"\\n\"));const a=this.getContents(t,e+s).diff((new(o())).insert(n).concat(l)),c=(new(o())).retain(t).concat(a);return this.applyDelta(c)}update(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r=this.delta;if(1===e.length&&\"characterData\"===e[0].type&&e[0].target.data.match(p)&&this.scroll.find(e[0].target)){const i=this.scroll.find(e[0].target),s=(0,c.Ji)(i),l=i.offset(this.scroll),a=e[0].oldValue.replace(h.A.CONTENTS,\"\"),u=(new(o())).insert(a),d=(new(o())).insert(i.value()),f=n&&{oldRange:A(n.oldRange,-l),newRange:A(n.newRange,-l)};t=(new(o())).retain(l).concat(u.diff(d,f)).reduce(((t,e)=>e.insert?t.insert(e.insert,s):t.push(e)),new(o())),this.delta=r.compose(t)}else this.delta=this.getDelta(),t&&(0,a.A)(r.compose(t),this.delta)||(t=r.diff(this.delta,n));return t}},N=n(5374),E=n(7356),w=n(6078),q=n(4266),k=n(746),_=class{isComposing=!1;constructor(t,e){this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener(\"compositionstart\",(t=>{this.isComposing||this.handleCompositionStart(t)})),this.scroll.domNode.addEventListener(\"compositionend\",(t=>{this.isComposing&&queueMicrotask((()=>{this.handleCompositionEnd(t)}))}))}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;!e||e instanceof k.A||(this.emitter.emit(N.A.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(N.A.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(N.A.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(N.A.events.COMPOSITION_END,t),this.isComposing=!1}},L=n(9609);const S=t=>{const e=t.getBoundingClientRect(),n=\"offsetWidth\"in t&&Math.abs(e.width)/t.offsetWidth||1,r=\"offsetHeight\"in t&&Math.abs(e.height)/t.offsetHeight||1;return{top:e.top,right:e.left+t.clientWidth*n,bottom:e.top+t.clientHeight*r,left:e.left}},O=t=>{const e=parseInt(t,10);return Number.isNaN(e)?0:e},T=(t,e,n,r,i,s)=>t<n&&e>r?0:t<n?-(n-t+i):e>r?e-t>r-n?t+i-n:e-r+s:0;const j=[\"block\",\"break\",\"cursor\",\"inline\",\"scroll\",\"text\"];const C=(0,w.A)(\"quill\"),R=new i.Registry;i.ParentBlot.uiClass=\"ql-ui\";class I{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:\"\",readOnly:!1,registry:R,theme:\"default\"};static events=N.A.events;static sources=N.A.sources;static version=\"2.0.3\";static imports={delta:o(),parchment:i,\"core/module\":q.A,\"core/theme\":L.A};static debug(t){!0===t&&(t=\"log\"),w.A.level(t)}static find(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return E.A.get(t)||R.find(t,e)}static import(t){return null==this.imports[t]&&C.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(\"string\"!=typeof(arguments.length<=0?void 0:arguments[0])){const t=arguments.length<=0?void 0:arguments[0],e=!!(arguments.length<=1?void 0:arguments[1]),n=\"attrName\"in t?t.attrName:t.blotName;\"string\"==typeof n?this.register(`formats/${n}`,t,e):Object.keys(t).forEach((n=>{this.register(n,t[n],e)}))}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],n=!!(arguments.length<=2?void 0:arguments[2]);null==this.imports[t]||n||C.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith(\"blots/\")||t.startsWith(\"formats/\"))&&e&&\"boolean\"!=typeof e&&\"abstract\"!==e.blotName&&R.register(e),\"function\"==typeof e.register&&e.register(R)}}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.options=function(t,e){const n=B(t);if(!n)throw new Error(\"Invalid Quill container\");const s=!e.theme||e.theme===I.DEFAULTS.theme?L.A:I.import(`themes/${e.theme}`);if(!s)throw new Error(`Invalid theme ${e.theme}. Did you register it?`);const{modules:o,...l}=I.DEFAULTS,{modules:a,...c}=s.DEFAULTS;let u=M(e.modules);null!=u&&u.toolbar&&u.toolbar.constructor!==Object&&(u={...u,toolbar:{container:u.toolbar}});const h=(0,r.A)({},M(o),M(a),u),d={...l,...U(c),...U(e)};let f=e.registry;return f?e.formats&&C.warn('Ignoring \"formats\" option because \"registry\" is specified'):f=e.formats?((t,e,n)=>{const r=new i.Registry;return j.forEach((t=>{const n=e.query(t);n&&r.register(n)})),t.forEach((t=>{let i=e.query(t);i||n.error(`Cannot register \"${t}\" specified in \"formats\" config. Are you sure it was registered?`);let s=0;for(;i;)if(r.register(i),i=\"blotName\"in i?i.requiredContainer??null:null,s+=1,s>100){n.error(`Cycle detected in registering blot requiredContainer: \"${t}\"`);break}})),r})(e.formats,d.registry,C):d.registry,{...d,registry:f,container:n,theme:s,modules:Object.entries(h).reduce(((t,e)=>{let[n,i]=e;if(!i)return t;const s=I.import(`modules/${n}`);return null==s?(C.error(`Cannot load ${n} module. Are you sure you registered it?`),t):{...t,[n]:(0,r.A)({},s.DEFAULTS||{},i)}}),{}),bounds:B(d.bounds)}}(t,e),this.container=this.options.container,null==this.container)return void C.error(\"Invalid Quill container\",t);this.options.debug&&I.debug(this.options.debug);const n=this.container.innerHTML.trim();this.container.classList.add(\"ql-container\"),this.container.innerHTML=\"\",E.A.set(this.container,this),this.root=this.addContainer(\"ql-editor\"),this.root.classList.add(\"ql-blank\"),this.emitter=new N.A;const s=i.ScrollBlot.blotName,l=this.options.registry.query(s);if(!l||!(\"blotName\"in l))throw new Error(`Cannot initialize Quill without \"${s}\" blot`);if(this.scroll=new l(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new x(this.scroll),this.selection=new f.A(this.scroll,this.emitter),this.composition=new _(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule(\"keyboard\"),this.clipboard=this.theme.addModule(\"clipboard\"),this.history=this.theme.addModule(\"history\"),this.uploader=this.theme.addModule(\"uploader\"),this.theme.addModule(\"input\"),this.theme.addModule(\"uiNode\"),this.theme.init(),this.emitter.on(N.A.events.EDITOR_CHANGE,(t=>{t===N.A.events.TEXT_CHANGE&&this.root.classList.toggle(\"ql-blank\",this.editor.isBlank())})),this.emitter.on(N.A.events.SCROLL_UPDATE,((t,e)=>{const n=this.selection.lastRange,[r]=this.selection.getRange(),i=n&&r?{oldRange:n,newRange:r}:void 0;D.call(this,(()=>this.editor.update(null,e,i)),t)})),this.emitter.on(N.A.events.SCROLL_EMBED_UPDATE,((t,e)=>{const n=this.selection.lastRange,[r]=this.selection.getRange(),i=n&&r?{oldRange:n,newRange:r}:void 0;D.call(this,(()=>{const n=(new(o())).retain(t.offset(this)).retain({[t.statics.blotName]:e});return this.editor.update(n,[],i)}),I.sources.USER)})),n){const t=this.clipboard.convert({html:`${n}<p><br></p>`,text:\"\\n\"});this.setContents(t)}this.history.clear(),this.options.placeholder&&this.root.setAttribute(\"data-placeholder\",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(\"string\"==typeof t){const e=t;(t=document.createElement(\"div\")).classList.add(e)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,n){return[t,e,,n]=P(t,e,n),D.call(this,(()=>this.editor.deleteText(t,e)),n,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(t),this.container.classList.toggle(\"ql-disabled\",!t)}focus(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:N.A.sources.API;return D.call(this,(()=>{const n=this.getSelection(!0);let r=new(o());if(null==n)return r;if(this.scroll.query(t,i.Scope.BLOCK))r=this.editor.formatLine(n.index,n.length,{[t]:e});else{if(0===n.length)return this.selection.format(t,e),r;r=this.editor.formatText(n.index,n.length,{[t]:e})}return this.setSelection(n,N.A.sources.SILENT),r}),n)}formatLine(t,e,n,r,i){let s;return[t,e,s,i]=P(t,e,n,r,i),D.call(this,(()=>this.editor.formatLine(t,e,s)),i,t,0)}formatText(t,e,n,r,i){let s;return[t,e,s,i]=P(t,e,n,r,i),D.call(this,(()=>this.editor.formatText(t,e,s)),i,t,0)}getBounds(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=null;if(n=\"number\"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length),!n)return null;const r=this.container.getBoundingClientRect();return{bottom:n.bottom-r.top,height:n.height,left:n.left-r.left,right:n.right-r.left,top:n.top-r.top,width:n.width}}getContents(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t;return[t,e]=P(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return\"number\"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return\"number\"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return\"number\"==typeof t&&(e=e??this.getLength()-t),[t,e]=P(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return\"number\"==typeof t&&(e=e??this.getLength()-t),[t,e]=P(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:I.sources.API;return D.call(this,(()=>this.editor.insertEmbed(t,e,n)),r,t)}insertText(t,e,n,r,i){let s;return[t,,s,i]=P(t,0,n,r,i),D.call(this,(()=>this.editor.insertText(t,e,s)),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,n){return[t,e,,n]=P(t,e,n),D.call(this,(()=>this.editor.removeFormat(t,e)),n,t)}scrollRectIntoView(t){((t,e)=>{const n=t.ownerDocument;let r=e,i=t;for(;i;){const t=i===n.body,e=t?{top:0,right:window.visualViewport?.width??n.documentElement.clientWidth,bottom:window.visualViewport?.height??n.documentElement.clientHeight,left:0}:S(i),o=getComputedStyle(i),l=T(r.left,r.right,e.left,e.right,O(o.scrollPaddingLeft),O(o.scrollPaddingRight)),a=T(r.top,r.bottom,e.top,e.bottom,O(o.scrollPaddingTop),O(o.scrollPaddingBottom));if(l||a)if(t)n.defaultView?.scrollBy(l,a);else{const{scrollLeft:t,scrollTop:e}=i;a&&(i.scrollTop+=a),l&&(i.scrollLeft+=l);const n=i.scrollLeft-t,s=i.scrollTop-e;r={left:r.left-n,top:r.top-s,right:r.right-n,bottom:r.bottom-s}}i=t||\"fixed\"===o.position?null:(s=i).parentElement||s.getRootNode().host||null}var s})(this.root,t)}scrollIntoView(){console.warn(\"Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead.\"),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:N.A.sources.API;return D.call(this,(()=>{t=new(o())(t);const e=this.getLength(),n=this.editor.deleteText(0,e),r=this.editor.insertContents(0,t),i=this.editor.deleteText(this.getLength()-1,1);return n.compose(r).compose(i)}),e)}setSelection(t,e,n){null==t?this.selection.setRange(null,e||I.sources.API):([t,e,,n]=P(t,e,n),this.selection.setRange(new f.Q(Math.max(0,t),e),n),n!==N.A.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:N.A.sources.API;const n=(new(o())).insert(t);return this.setContents(n,e)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N.A.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:N.A.sources.API;return D.call(this,(()=>(t=new(o())(t),this.editor.applyDelta(t))),e,!0)}}function B(t){return\"string\"==typeof t?document.querySelector(t):t}function M(t){return Object.entries(t??{}).reduce(((t,e)=>{let[n,r]=e;return{...t,[n]:!0===r?{}:r}}),{})}function U(t){return Object.fromEntries(Object.entries(t).filter((t=>void 0!==t[1])))}function D(t,e,n,r){if(!this.isEnabled()&&e===N.A.sources.USER&&!this.allowReadOnlyEdits)return new(o());let i=null==n?null:this.getSelection();const s=this.editor.delta,l=t();if(null!=i&&(!0===n&&(n=i.index),null==r?i=z(i,l,e):0!==r&&(i=z(i,n,r,e)),this.setSelection(i,N.A.sources.SILENT)),l.length()>0){const t=[N.A.events.TEXT_CHANGE,l,s,e];this.emitter.emit(N.A.events.EDITOR_CHANGE,...t),e!==N.A.sources.SILENT&&this.emitter.emit(...t)}return l}function P(t,e,n,r,i){let s={};return\"number\"==typeof t.index&&\"number\"==typeof t.length?\"number\"!=typeof e?(i=r,r=n,n=e,e=t.length,t=t.index):(e=t.length,t=t.index):\"number\"!=typeof e&&(i=r,r=n,n=e,e=0),\"object\"==typeof n?(s=n,i=r):\"string\"==typeof n&&(null!=r?s[n]=r:i=n),[t,e,s,i=i||N.A.sources.API]}function z(t,e,n,r){const i=\"number\"==typeof n?n:0;if(null==t)return null;let s,o;return e&&\"function\"==typeof e.transformPosition?[s,o]=[t.index,t.index+t.length].map((t=>e.transformPosition(t,r!==N.A.sources.USER))):[s,o]=[t.index,t.index+t.length].map((t=>t<e||t===e&&r===N.A.sources.USER?t:i>=0?t+i:Math.max(e,t+i))),new f.Q(s,o-s)}},8298:function(t,e,n){\"use strict\";n.d(e,{Q:function(){return a}});var r=n(6003),i=n(5123),s=n(3707),o=n(5374);const l=(0,n(6078).A)(\"quill:selection\");class a{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.index=t,this.length=e}}function c(t,e){try{e.parentNode}catch(t){return!1}return t.contains(e)}e.A=class{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create(\"cursor\",this),this.savedRange=new a(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM(\"selectionchange\",document,(()=>{this.mouseDown||this.composing||setTimeout(this.update.bind(this,o.A.sources.USER),1)})),this.emitter.on(o.A.events.SCROLL_BEFORE_UPDATE,(()=>{if(!this.hasFocus())return;const t=this.getNativeRange();null!=t&&t.start.node!==this.cursor.textNode&&this.emitter.once(o.A.events.SCROLL_UPDATE,((e,n)=>{try{this.root.contains(t.start.node)&&this.root.contains(t.end.node)&&this.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset);const r=n.some((t=>\"characterData\"===t.type||\"childList\"===t.type||\"attributes\"===t.type&&t.target===this.root));this.update(r?o.A.sources.SILENT:e)}catch(t){}}))})),this.emitter.on(o.A.events.SCROLL_OPTIMIZE,((t,e)=>{if(e.range){const{startNode:t,startOffset:n,endNode:r,endOffset:i}=e.range;this.setNativeRange(t,n,r,i),this.update(o.A.sources.SILENT)}})),this.update(o.A.sources.SILENT)}handleComposition(){this.emitter.on(o.A.events.COMPOSITION_BEFORE_START,(()=>{this.composing=!0})),this.emitter.on(o.A.events.COMPOSITION_END,(()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout((()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}),1)}}))}handleDragging(){this.emitter.listenDOM(\"mousedown\",document.body,(()=>{this.mouseDown=!0})),this.emitter.listenDOM(\"mouseup\",document.body,(()=>{this.mouseDown=!1,this.update(o.A.sources.USER)}))}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const n=this.getNativeRange();if(null!=n&&n.native.collapsed&&!this.scroll.query(t,r.Scope.BLOCK)){if(n.start.node!==this.cursor.textNode){const t=this.scroll.find(n.start.node,!1);if(null==t)return;if(t instanceof r.LeafBlot){const e=t.split(n.start.offset);t.parent.insertBefore(this.cursor,e)}else t.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=this.scroll.length();let r;t=Math.min(t,n-1),e=Math.min(t+e,n-1)-t;let[i,s]=this.scroll.leaf(t);if(null==i)return null;if(e>0&&s===i.length()){const[e]=this.scroll.leaf(t+1);if(e){const[n]=this.scroll.line(t),[r]=this.scroll.line(t+1);n===r&&(i=e,s=0)}}[r,s]=i.position(s,!0);const o=document.createRange();if(e>0)return o.setStart(r,s),[i,s]=this.scroll.leaf(t+e),null==i?null:([r,s]=i.position(s,!0),o.setEnd(r,s),o.getBoundingClientRect());let l,a=\"left\";if(r instanceof Text){if(!r.data.length)return null;s<r.data.length?(o.setStart(r,s),o.setEnd(r,s+1)):(o.setStart(r,s-1),o.setEnd(r,s),a=\"right\"),l=o.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;l=i.domNode.getBoundingClientRect(),s>0&&(a=\"right\")}return{bottom:l.top+l.height,height:l.height,left:l[a],right:l[a],top:l.top,width:0}}getNativeRange(){const t=document.getSelection();if(null==t||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(null==e)return null;const n=this.normalizeNative(e);return l.info(\"getNativeRange\",n),n}getRange(){const t=this.scroll.domNode;if(\"isConnected\"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return null==e?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||null!=document.activeElement&&c(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const n=e.map((t=>{const[e,n]=t,i=this.scroll.find(e,!0),s=i.offset(this.scroll);return 0===n?s:i instanceof r.LeafBlot?s+i.index(e,n):s+i.length()})),i=Math.min(Math.max(...n),this.scroll.length()-1),s=Math.min(i,...n);return new a(s,i-s)}normalizeNative(t){if(!c(this.root,t.startContainer)||!t.collapsed&&!c(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach((t=>{let{node:e,offset:n}=t;for(;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>n)e=e.childNodes[n],n=0;else{if(e.childNodes.length!==n)break;e=e.lastChild,n=e instanceof Text?e.data.length:e.childNodes.length>0?e.childNodes.length:e.childNodes.length+1}t.node=e,t.offset=n})),e}rangeToNative(t){const e=this.scroll.length(),n=(t,n)=>{t=Math.min(e-1,t);const[r,i]=this.scroll.leaf(t);return r?r.position(i,n):[null,-1]};return[...n(t.index,!1),...n(t.index+t.length,!0)]}setNativeRange(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(l.info(\"setNativeRange\",t,e,n,r),null!=t&&(null==this.root.parentNode||null==t.parentNode||null==n.parentNode))return;const s=document.getSelection();if(null!=s)if(null!=t){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:o}=this.getNativeRange()||{};if(null==o||i||t!==o.startContainer||e!==o.startOffset||n!==o.endContainer||r!==o.endOffset){t instanceof Element&&\"BR\"===t.tagName&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),n instanceof Element&&\"BR\"===n.tagName&&(r=Array.from(n.parentNode.childNodes).indexOf(n),n=n.parentNode);const i=document.createRange();i.setStart(t,e),i.setEnd(n,r),s.removeAllRanges(),s.addRange(i)}}else s.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o.A.sources.API;if(\"string\"==typeof e&&(n=e,e=!1),l.info(\"setRange\",t),null!=t){const n=this.rangeToNative(t);this.setNativeRange(...n,e)}else this.setNativeRange(null);this.update(n)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.A.sources.USER;const e=this.lastRange,[n,r]=this.getRange();if(this.lastRange=n,this.lastNative=r,null!=this.lastRange&&(this.savedRange=this.lastRange),!(0,i.A)(e,this.lastRange)){if(!this.composing&&null!=r&&r.native.collapsed&&r.start.node!==this.cursor.textNode){const t=this.cursor.restore();t&&this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}const n=[o.A.events.SELECTION_CHANGE,(0,s.A)(this.lastRange),(0,s.A)(e),t];this.emitter.emit(o.A.events.EDITOR_CHANGE,...n),t!==o.A.sources.SILENT&&this.emitter.emit(...n)}}}},9609:function(t,e){\"use strict\";class n{static DEFAULTS={modules:{}};static themes={default:n};modules={};constructor(t,e){this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach((t=>{null==this.modules[t]&&this.addModule(t)}))}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}e.A=n},8276:function(t,e,n){\"use strict\";n.d(e,{Hu:function(){return l},gS:function(){return s},qh:function(){return o}});var r=n(6003);const i={scope:r.Scope.BLOCK,whitelist:[\"right\",\"center\",\"justify\"]},s=new r.Attributor(\"align\",\"align\",i),o=new r.ClassAttributor(\"align\",\"ql-align\",i),l=new r.StyleAttributor(\"align\",\"text-align\",i)},9541:function(t,e,n){\"use strict\";n.d(e,{l:function(){return s},s:function(){return o}});var r=n(6003),i=n(8638);const s=new r.ClassAttributor(\"background\",\"ql-bg\",{scope:r.Scope.INLINE}),o=new i.a2(\"background\",\"background-color\",{scope:r.Scope.INLINE})},9404:function(t,e,n){\"use strict\";n.d(e,{Ay:function(){return h},Cy:function(){return d},EJ:function(){return u}});var r=n(9698),i=n(3036),s=n(4541),o=n(4850),l=n(5508),a=n(580),c=n(6142);class u extends a.A{static create(t){const e=super.create(t);return e.setAttribute(\"spellcheck\",\"false\"),e}code(t,e){return this.children.map((t=>t.length()<=1?\"\":t.domNode.innerText)).join(\"\\n\").slice(t,t+e)}html(t,e){return`<pre>\\n${(0,l.X)(this.code(t,e))}\\n</pre>`}}class h extends r.Ay{static TAB=\"  \";static register(){c.Ay.register(u)}}class d extends o.A{}d.blotName=\"code\",d.tagName=\"CODE\",h.blotName=\"code-block\",h.className=\"ql-code-block\",h.tagName=\"DIV\",u.blotName=\"code-block-container\",u.className=\"ql-code-block-container\",u.tagName=\"DIV\",u.allowedChildren=[h],h.allowedChildren=[l.A,i.A,s.A],h.requiredContainer=u},8638:function(t,e,n){\"use strict\";n.d(e,{JM:function(){return o},a2:function(){return i},g3:function(){return s}});var r=n(6003);class i extends r.StyleAttributor{value(t){let e=super.value(t);return e.startsWith(\"rgb(\")?(e=e.replace(/^[^\\d]+/,\"\").replace(/[^\\d]+$/,\"\"),`#${e.split(\",\").map((t=>`00${parseInt(t,10).toString(16)}`.slice(-2))).join(\"\")}`):e}}const s=new r.ClassAttributor(\"color\",\"ql-color\",{scope:r.Scope.INLINE}),o=new i(\"color\",\"color\",{scope:r.Scope.INLINE})},7912:function(t,e,n){\"use strict\";n.d(e,{Mc:function(){return s},VL:function(){return l},sY:function(){return o}});var r=n(6003);const i={scope:r.Scope.BLOCK,whitelist:[\"rtl\"]},s=new r.Attributor(\"direction\",\"dir\",i),o=new r.ClassAttributor(\"direction\",\"ql-direction\",i),l=new r.StyleAttributor(\"direction\",\"direction\",i)},6772:function(t,e,n){\"use strict\";n.d(e,{q:function(){return s},z:function(){return l}});var r=n(6003);const i={scope:r.Scope.INLINE,whitelist:[\"serif\",\"monospace\"]},s=new r.ClassAttributor(\"font\",\"ql-font\",i);class o extends r.StyleAttributor{value(t){return super.value(t).replace(/[\"']/g,\"\")}}const l=new o(\"font\",\"font-family\",i)},664:function(t,e,n){\"use strict\";n.d(e,{U:function(){return i},r:function(){return s}});var r=n(6003);const i=new r.ClassAttributor(\"size\",\"ql-size\",{scope:r.Scope.INLINE,whitelist:[\"small\",\"large\",\"huge\"]}),s=new r.StyleAttributor(\"size\",\"font-size\",{scope:r.Scope.INLINE,whitelist:[\"10px\",\"18px\",\"32px\"]})},584:function(t,e,n){\"use strict\";n.d(e,{Ay:function(){return S},hV:function(){return I}});var r=n(6003),i=n(5232),s=n.n(i),o=n(9698),l=n(6078),a=n(4266),c=n(6142),u=n(8276),h=n(9541),d=n(9404),f=n(8638),p=n(7912),g=n(6772),m=n(664),b=n(8123);const y=/font-weight:\\s*normal/,v=[\"P\",\"OL\",\"UL\"],A=t=>t&&v.includes(t.tagName),x=/\\bmso-list:[^;]*ignore/i,N=/\\bmso-list:[^;]*\\bl(\\d+)/i,E=/\\bmso-list:[^;]*\\blevel(\\d+)/i,w=[function(t){\"urn:schemas-microsoft-com:office:word\"===t.documentElement.getAttribute(\"xmlns:w\")&&(t=>{const e=Array.from(t.querySelectorAll(\"[style*=mso-list]\")),n=[],r=[];e.forEach((t=>{(t.getAttribute(\"style\")||\"\").match(x)?n.push(t):r.push(t)})),n.forEach((t=>t.parentNode?.removeChild(t)));const i=t.documentElement.innerHTML,s=r.map((t=>((t,e)=>{const n=t.getAttribute(\"style\"),r=n?.match(N);if(!r)return null;const i=Number(r[1]),s=n?.match(E),o=s?Number(s[1]):1,l=new RegExp(`@list l${i}:level${o}\\\\s*\\\\{[^\\\\}]*mso-level-number-format:\\\\s*([\\\\w-]+)`,\"i\"),a=e.match(l);return{id:i,indent:o,type:a&&\"bullet\"===a[1]?\"bullet\":\"ordered\",element:t}})(t,i))).filter((t=>t));for(;s.length;){const t=[];let e=s.shift();for(;e;)t.push(e),e=s.length&&s[0]?.element===e.element.nextElementSibling&&s[0].id===e.id?s.shift():null;const n=document.createElement(\"ul\");t.forEach((t=>{const e=document.createElement(\"li\");e.setAttribute(\"data-list\",t.type),t.indent>1&&e.setAttribute(\"class\",\"ql-indent-\"+(t.indent-1)),e.innerHTML=t.element.innerHTML,n.appendChild(e)}));const r=t[0]?.element,{parentNode:i}=r??{};r&&i?.replaceChild(n,r),t.slice(1).forEach((t=>{let{element:e}=t;i?.removeChild(e)}))}})(t)},function(t){t.querySelector('[id^=\"docs-internal-guid-\"]')&&((t=>{Array.from(t.querySelectorAll('b[style*=\"font-weight\"]')).filter((t=>t.getAttribute(\"style\")?.match(y))).forEach((e=>{const n=t.createDocumentFragment();n.append(...e.childNodes),e.parentNode?.replaceChild(n,e)}))})(t),(t=>{Array.from(t.querySelectorAll(\"br\")).filter((t=>A(t.previousElementSibling)&&A(t.nextElementSibling))).forEach((t=>{t.parentNode?.removeChild(t)}))})(t))}];const q=(0,l.A)(\"quill:clipboard\"),k=[[Node.TEXT_NODE,function(t,e,n){let r=t.data;if(\"O:P\"===t.parentElement?.tagName)return e.insert(r.trim());if(!R(t)){if(0===r.trim().length&&r.includes(\"\\n\")&&!function(t,e){return t.previousElementSibling&&t.nextElementSibling&&!j(t.previousElementSibling,e)&&!j(t.nextElementSibling,e)}(t,n))return e;r=r.replace(/[^\\S\\u00a0]/g,\" \"),r=r.replace(/ {2,}/g,\" \"),(null==t.previousSibling&&null!=t.parentElement&&j(t.parentElement,n)||t.previousSibling instanceof Element&&j(t.previousSibling,n))&&(r=r.replace(/^ /,\"\")),(null==t.nextSibling&&null!=t.parentElement&&j(t.parentElement,n)||t.nextSibling instanceof Element&&j(t.nextSibling,n))&&(r=r.replace(/ $/,\"\")),r=r.replaceAll(\" \",\" \")}return e.insert(r)}],[Node.TEXT_NODE,M],[\"br\",function(t,e){return T(e,\"\\n\")||e.insert(\"\\n\"),e}],[Node.ELEMENT_NODE,M],[Node.ELEMENT_NODE,function(t,e,n){const i=n.query(t);if(null==i)return e;if(i.prototype instanceof r.EmbedBlot){const e={},r=i.value(t);if(null!=r)return e[i.blotName]=r,(new(s())).insert(e,i.formats(t,n))}else if(i.prototype instanceof r.BlockBlot&&!T(e,\"\\n\")&&e.insert(\"\\n\"),\"blotName\"in i&&\"formats\"in i&&\"function\"==typeof i.formats)return O(e,i.blotName,i.formats(t,n),n);return e}],[Node.ELEMENT_NODE,function(t,e,n){const i=r.Attributor.keys(t),s=r.ClassAttributor.keys(t),o=r.StyleAttributor.keys(t),l={};return i.concat(s).concat(o).forEach((e=>{let i=n.query(e,r.Scope.ATTRIBUTE);null!=i&&(l[i.attrName]=i.value(t),l[i.attrName])||(i=_[e],null==i||i.attrName!==e&&i.keyName!==e||(l[i.attrName]=i.value(t)||void 0),i=L[e],null==i||i.attrName!==e&&i.keyName!==e||(i=L[e],l[i.attrName]=i.value(t)||void 0))})),Object.entries(l).reduce(((t,e)=>{let[r,i]=e;return O(t,r,i,n)}),e)}],[Node.ELEMENT_NODE,function(t,e,n){const r={},i=t.style||{};return\"italic\"===i.fontStyle&&(r.italic=!0),\"underline\"===i.textDecoration&&(r.underline=!0),\"line-through\"===i.textDecoration&&(r.strike=!0),(i.fontWeight?.startsWith(\"bold\")||parseInt(i.fontWeight,10)>=700)&&(r.bold=!0),e=Object.entries(r).reduce(((t,e)=>{let[r,i]=e;return O(t,r,i,n)}),e),parseFloat(i.textIndent||0)>0?(new(s())).insert(\"\\t\").concat(e):e}],[\"li\",function(t,e,n){const r=n.query(t);if(null==r||\"list\"!==r.blotName||!T(e,\"\\n\"))return e;let i=-1,o=t.parentNode;for(;null!=o;)[\"OL\",\"UL\"].includes(o.tagName)&&(i+=1),o=o.parentNode;return i<=0?e:e.reduce(((t,e)=>e.insert?e.attributes&&\"number\"==typeof e.attributes.indent?t.push(e):t.insert(e.insert,{indent:i,...e.attributes||{}}):t),new(s()))}],[\"ol, ul\",function(t,e,n){const r=t;let i=\"OL\"===r.tagName?\"ordered\":\"bullet\";const s=r.getAttribute(\"data-checked\");return s&&(i=\"true\"===s?\"checked\":\"unchecked\"),O(e,\"list\",i,n)}],[\"pre\",function(t,e,n){const r=n.query(\"code-block\");return O(e,\"code-block\",!r||!(\"formats\"in r)||\"function\"!=typeof r.formats||r.formats(t,n),n)}],[\"tr\",function(t,e,n){const r=\"TABLE\"===t.parentElement?.tagName?t.parentElement:t.parentElement?.parentElement;return null!=r?O(e,\"table\",Array.from(r.querySelectorAll(\"tr\")).indexOf(t)+1,n):e}],[\"b\",B(\"bold\")],[\"i\",B(\"italic\")],[\"strike\",B(\"strike\")],[\"style\",function(){return new(s())}]],_=[u.gS,p.Mc].reduce(((t,e)=>(t[e.keyName]=e,t)),{}),L=[u.Hu,h.s,f.JM,p.VL,g.z,m.r].reduce(((t,e)=>(t[e.keyName]=e,t)),{});class S extends a.A{static DEFAULTS={matchers:[]};constructor(t,e){super(t,e),this.quill.root.addEventListener(\"copy\",(t=>this.onCaptureCopy(t,!1))),this.quill.root.addEventListener(\"cut\",(t=>this.onCaptureCopy(t,!0))),this.quill.root.addEventListener(\"paste\",this.onCapturePaste.bind(this)),this.matchers=[],k.concat(this.options.matchers??[]).forEach((t=>{let[e,n]=t;this.addMatcher(e,n)}))}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:n}=t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r[d.Ay.blotName])return(new(s())).insert(n||\"\",{[d.Ay.blotName]:r[d.Ay.blotName]});if(!e)return(new(s())).insert(n||\"\",r);const i=this.convertHTML(e);return T(i,\"\\n\")&&(null==i.ops[i.ops.length-1].attributes||r.table)?i.compose((new(s())).retain(i.length()-1).delete(1)):i}normalizeHTML(t){(t=>{t.documentElement&&w.forEach((e=>{e(t)}))})(t)}convertHTML(t){const e=(new DOMParser).parseFromString(t,\"text/html\");this.normalizeHTML(e);const n=e.body,r=new WeakMap,[i,s]=this.prepareMatching(n,r);return I(this.quill.scroll,n,i,s,r)}dangerouslyPasteHTML(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:c.Ay.sources.API;if(\"string\"==typeof t){const n=this.convert({html:t,text:\"\"});this.quill.setContents(n,e),this.quill.setSelection(0,c.Ay.sources.SILENT)}else{const r=this.convert({html:e,text:\"\"});this.quill.updateContents((new(s())).retain(t).concat(r),n),this.quill.setSelection(t+r.length(),c.Ay.sources.SILENT)}}onCaptureCopy(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.defaultPrevented)return;t.preventDefault();const[n]=this.quill.selection.getRange();if(null==n)return;const{html:r,text:i}=this.onCopy(n,e);t.clipboardData?.setData(\"text/plain\",i),t.clipboardData?.setData(\"text/html\",r),e&&(0,b.Xo)({range:n,quill:this.quill})}normalizeURIList(t){return t.split(/\\r?\\n/).filter((t=>\"#\"!==t[0])).join(\"\\n\")}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(null==e)return;const n=t.clipboardData?.getData(\"text/html\");let r=t.clipboardData?.getData(\"text/plain\");if(!n&&!r){const e=t.clipboardData?.getData(\"text/uri-list\");e&&(r=this.normalizeURIList(e))}const i=Array.from(t.clipboardData?.files||[]);if(!n&&i.length>0)this.quill.uploader.upload(e,i);else{if(n&&i.length>0){const t=(new DOMParser).parseFromString(n,\"text/html\");if(1===t.body.childElementCount&&\"IMG\"===t.body.firstElementChild?.tagName)return void this.quill.uploader.upload(e,i)}this.onPaste(e,{html:n,text:r})}}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:n,html:r}=e;const i=this.quill.getFormat(t.index),o=this.convert({text:n,html:r},i);q.log(\"onPaste\",o,{text:n,html:r});const l=(new(s())).retain(t.index).delete(t.length).concat(o);this.quill.updateContents(l,c.Ay.sources.USER),this.quill.setSelection(l.length()-t.length,c.Ay.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const n=[],r=[];return this.matchers.forEach((i=>{const[s,o]=i;switch(s){case Node.TEXT_NODE:r.push(o);break;case Node.ELEMENT_NODE:n.push(o);break;default:Array.from(t.querySelectorAll(s)).forEach((t=>{if(e.has(t)){const n=e.get(t);n?.push(o)}else e.set(t,[o])}))}})),[n,r]}}function O(t,e,n,r){return r.query(e)?t.reduce(((t,r)=>{if(!r.insert)return t;if(r.attributes&&r.attributes[e])return t.push(r);const i=n?{[e]:n}:{};return t.insert(r.insert,{...i,...r.attributes})}),new(s())):t}function T(t,e){let n=\"\";for(let r=t.ops.length-1;r>=0&&n.length<e.length;--r){const e=t.ops[r];if(\"string\"!=typeof e.insert)break;n=e.insert+n}return n.slice(-1*e.length)===e}function j(t,e){if(!(t instanceof Element))return!1;const n=e.query(t);return!(n&&n.prototype instanceof r.EmbedBlot)&&[\"address\",\"article\",\"blockquote\",\"canvas\",\"dd\",\"div\",\"dl\",\"dt\",\"fieldset\",\"figcaption\",\"figure\",\"footer\",\"form\",\"h1\",\"h2\",\"h3\",\"h4\",\"h5\",\"h6\",\"header\",\"iframe\",\"li\",\"main\",\"nav\",\"ol\",\"output\",\"p\",\"pre\",\"section\",\"table\",\"td\",\"tr\",\"ul\",\"video\"].includes(t.tagName.toLowerCase())}const C=new WeakMap;function R(t){return null!=t&&(C.has(t)||(\"PRE\"===t.tagName?C.set(t,!0):C.set(t,R(t.parentNode))),C.get(t))}function I(t,e,n,r,i){return e.nodeType===e.TEXT_NODE?r.reduce(((n,r)=>r(e,n,t)),new(s())):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce(((s,o)=>{let l=I(t,o,n,r,i);return o.nodeType===e.ELEMENT_NODE&&(l=n.reduce(((e,n)=>n(o,e,t)),l),l=(i.get(o)||[]).reduce(((e,n)=>n(o,e,t)),l)),s.concat(l)}),new(s())):new(s())}function B(t){return(e,n,r)=>O(n,t,!0,r)}function M(t,e,n){if(!T(e,\"\\n\")){if(j(t,n)&&(t.childNodes.length>0||t instanceof HTMLParagraphElement))return e.insert(\"\\n\");if(e.length()>0&&t.nextSibling){let r=t.nextSibling;for(;null!=r;){if(j(r,n))return e.insert(\"\\n\");const t=n.query(r);if(t&&t.prototype instanceof o.zo)return e.insert(\"\\n\");r=r.firstChild}}}return e}},8123:function(t,e,n){\"use strict\";n.d(e,{Ay:function(){return f},Xo:function(){return v}});var r=n(5123),i=n(3707),s=n(5232),o=n.n(s),l=n(6003),a=n(6142),c=n(6078),u=n(4266);const h=(0,c.A)(\"quill:keyboard\"),d=/Mac/i.test(navigator.platform)?\"metaKey\":\"ctrlKey\";class f extends u.A{static match(t,e){return![\"altKey\",\"ctrlKey\",\"metaKey\",\"shiftKey\"].some((n=>!!e[n]!==t[n]&&null!==e[n]))&&(e.key===t.key||e.key===t.which)}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach((t=>{this.options.bindings[t]&&this.addBinding(this.options.bindings[t])})),this.addBinding({key:\"Enter\",shiftKey:null},this.handleEnter),this.addBinding({key:\"Enter\",metaKey:null,ctrlKey:null,altKey:null},(()=>{})),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:\"Backspace\"},{collapsed:!0},this.handleBackspace),this.addBinding({key:\"Delete\"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:\"Backspace\"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:\"Delete\"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:\"Backspace\"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:\"Delete\"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:\"Backspace\",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=function(t){if(\"string\"==typeof t||\"number\"==typeof t)t={key:t};else{if(\"object\"!=typeof t)return null;t=(0,i.A)(t)}return t.shortKey&&(t[d]=t.shortKey,delete t.shortKey),t}(t);null!=r?(\"function\"==typeof e&&(e={handler:e}),\"function\"==typeof n&&(n={handler:n}),(Array.isArray(r.key)?r.key:[r.key]).forEach((t=>{const i={...r,key:t,...e,...n};this.bindings[i.key]=this.bindings[i.key]||[],this.bindings[i.key].push(i)}))):h.warn(\"Attempted to add invalid keyboard binding\",r)}listen(){this.quill.root.addEventListener(\"keydown\",(t=>{if(t.defaultPrevented||t.isComposing)return;if(229===t.keyCode&&(\"Enter\"===t.key||\"Backspace\"===t.key))return;const e=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter((e=>f.match(t,e)));if(0===e.length)return;const n=a.Ay.find(t.target,!0);if(n&&n.scroll!==this.quill.scroll)return;const i=this.quill.getSelection();if(null==i||!this.quill.hasFocus())return;const[s,o]=this.quill.getLine(i.index),[c,u]=this.quill.getLeaf(i.index),[h,d]=0===i.length?[c,u]:this.quill.getLeaf(i.index+i.length),p=c instanceof l.TextBlot?c.value().slice(0,u):\"\",g=h instanceof l.TextBlot?h.value().slice(d):\"\",m={collapsed:0===i.length,empty:0===i.length&&s.length()<=1,format:this.quill.getFormat(i),line:s,offset:o,prefix:p,suffix:g,event:t};e.some((t=>{if(null!=t.collapsed&&t.collapsed!==m.collapsed)return!1;if(null!=t.empty&&t.empty!==m.empty)return!1;if(null!=t.offset&&t.offset!==m.offset)return!1;if(Array.isArray(t.format)){if(t.format.every((t=>null==m.format[t])))return!1}else if(\"object\"==typeof t.format&&!Object.keys(t.format).every((e=>!0===t.format[e]?null!=m.format[e]:!1===t.format[e]?null==m.format[e]:(0,r.A)(t.format[e],m.format[e]))))return!1;return!(null!=t.prefix&&!t.prefix.test(m.prefix)||null!=t.suffix&&!t.suffix.test(m.suffix)||!0===t.handler.call(this,i,m,t))}))&&t.preventDefault()}))}handleBackspace(t,e){const n=/[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]$/.test(e.prefix)?2:1;if(0===t.index||this.quill.getLength()<=1)return;let r={};const[i]=this.quill.getLine(t.index);let l=(new(o())).retain(t.index-n).delete(n);if(0===e.offset){const[e]=this.quill.getLine(t.index-1);if(e&&!(\"block\"===e.statics.blotName&&e.length()<=1)){const e=i.formats(),n=this.quill.getFormat(t.index-1,1);if(r=s.AttributeMap.diff(e,n)||{},Object.keys(r).length>0){const e=(new(o())).retain(t.index+i.length()-2).retain(1,r);l=l.compose(e)}}}this.quill.updateContents(l,a.Ay.sources.USER),this.quill.focus()}handleDelete(t,e){const n=/^[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-n)return;let r={};const[i]=this.quill.getLine(t.index);let l=(new(o())).retain(t.index).delete(n);if(e.offset>=i.length()-1){const[e]=this.quill.getLine(t.index+1);if(e){const n=i.formats(),o=this.quill.getFormat(t.index,1);r=s.AttributeMap.diff(n,o)||{},Object.keys(r).length>0&&(l=l.retain(e.length()-1).retain(1,r))}}this.quill.updateContents(l,a.Ay.sources.USER),this.quill.focus()}handleDeleteRange(t){v({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const n=Object.keys(e.format).reduce(((t,n)=>(this.quill.scroll.query(n,l.Scope.BLOCK)&&!Array.isArray(e.format[n])&&(t[n]=e.format[n]),t)),{}),r=(new(o())).retain(t.index).delete(t.length).insert(\"\\n\",n);this.quill.updateContents(r,a.Ay.sources.USER),this.quill.setSelection(t.index+1,a.Ay.sources.SILENT),this.quill.focus()}}const p={bindings:{bold:b(\"bold\"),italic:b(\"italic\"),underline:b(\"underline\"),indent:{key:\"Tab\",format:[\"blockquote\",\"indent\",\"list\"],handler(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format(\"indent\",\"+1\",a.Ay.sources.USER),!1)}},outdent:{key:\"Tab\",shiftKey:!0,format:[\"blockquote\",\"indent\",\"list\"],handler(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format(\"indent\",\"-1\",a.Ay.sources.USER),!1)}},\"outdent backspace\":{key:\"Backspace\",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:[\"indent\",\"list\"],offset:0,handler(t,e){null!=e.format.indent?this.quill.format(\"indent\",\"-1\",a.Ay.sources.USER):null!=e.format.list&&this.quill.format(\"list\",!1,a.Ay.sources.USER)}},\"indent code-block\":g(!0),\"outdent code-block\":g(!1),\"remove tab\":{key:\"Tab\",shiftKey:!0,collapsed:!0,prefix:/\\t$/,handler(t){this.quill.deleteText(t.index-1,1,a.Ay.sources.USER)}},tab:{key:\"Tab\",handler(t,e){if(e.format.table)return!0;this.quill.history.cutoff();const n=(new(o())).retain(t.index).delete(t.length).insert(\"\\t\");return this.quill.updateContents(n,a.Ay.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,a.Ay.sources.SILENT),!1}},\"blockquote empty enter\":{key:\"Enter\",collapsed:!0,format:[\"blockquote\"],empty:!0,handler(){this.quill.format(\"blockquote\",!1,a.Ay.sources.USER)}},\"list empty enter\":{key:\"Enter\",collapsed:!0,format:[\"list\"],empty:!0,handler(t,e){const n={list:!1};e.format.indent&&(n.indent=!1),this.quill.formatLine(t.index,t.length,n,a.Ay.sources.USER)}},\"checklist enter\":{key:\"Enter\",collapsed:!0,format:{list:\"checked\"},handler(t){const[e,n]=this.quill.getLine(t.index),r={...e.formats(),list:\"checked\"},i=(new(o())).retain(t.index).insert(\"\\n\",r).retain(e.length()-n-1).retain(1,{list:\"unchecked\"});this.quill.updateContents(i,a.Ay.sources.USER),this.quill.setSelection(t.index+1,a.Ay.sources.SILENT),this.quill.scrollSelectionIntoView()}},\"header enter\":{key:\"Enter\",collapsed:!0,format:[\"header\"],suffix:/^$/,handler(t,e){const[n,r]=this.quill.getLine(t.index),i=(new(o())).retain(t.index).insert(\"\\n\",e.format).retain(n.length()-r-1).retain(1,{header:null});this.quill.updateContents(i,a.Ay.sources.USER),this.quill.setSelection(t.index+1,a.Ay.sources.SILENT),this.quill.scrollSelectionIntoView()}},\"table backspace\":{key:\"Backspace\",format:[\"table\"],collapsed:!0,offset:0,handler(){}},\"table delete\":{key:\"Delete\",format:[\"table\"],collapsed:!0,suffix:/^$/,handler(){}},\"table enter\":{key:\"Enter\",shiftKey:null,format:[\"table\"],handler(t){const e=this.quill.getModule(\"table\");if(e){const[n,r,i,s]=e.getTable(t),l=function(t,e,n,r){return null==e.prev&&null==e.next?null==n.prev&&null==n.next?0===r?-1:1:null==n.prev?-1:1:null==e.prev?-1:null==e.next?1:null}(0,r,i,s);if(null==l)return;let c=n.offset();if(l<0){const e=(new(o())).retain(c).insert(\"\\n\");this.quill.updateContents(e,a.Ay.sources.USER),this.quill.setSelection(t.index+1,t.length,a.Ay.sources.SILENT)}else if(l>0){c+=n.length();const t=(new(o())).retain(c).insert(\"\\n\");this.quill.updateContents(t,a.Ay.sources.USER),this.quill.setSelection(c,a.Ay.sources.USER)}}}},\"table tab\":{key:\"Tab\",shiftKey:null,format:[\"table\"],handler(t,e){const{event:n,line:r}=e,i=r.offset(this.quill.scroll);n.shiftKey?this.quill.setSelection(i-1,a.Ay.sources.USER):this.quill.setSelection(i+r.length(),a.Ay.sources.USER)}},\"list autofill\":{key:\" \",shiftKey:null,collapsed:!0,format:{\"code-block\":!1,blockquote:!1,table:!1},prefix:/^\\s*?(\\d+\\.|-|\\*|\\[ ?\\]|\\[x\\])$/,handler(t,e){if(null==this.quill.scroll.query(\"list\"))return!0;const{length:n}=e.prefix,[r,i]=this.quill.getLine(t.index);if(i>n)return!0;let s;switch(e.prefix.trim()){case\"[]\":case\"[ ]\":s=\"unchecked\";break;case\"[x]\":s=\"checked\";break;case\"-\":case\"*\":s=\"bullet\";break;default:s=\"ordered\"}this.quill.insertText(t.index,\" \",a.Ay.sources.USER),this.quill.history.cutoff();const l=(new(o())).retain(t.index-i).delete(n+1).retain(r.length()-2-i).retain(1,{list:s});return this.quill.updateContents(l,a.Ay.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-n,a.Ay.sources.SILENT),!1}},\"code exit\":{key:\"Enter\",collapsed:!0,format:[\"code-block\"],prefix:/^$/,suffix:/^\\s*$/,handler(t){const[e,n]=this.quill.getLine(t.index);let r=2,i=e;for(;null!=i&&i.length()<=1&&i.formats()[\"code-block\"];)if(i=i.prev,r-=1,r<=0){const r=(new(o())).retain(t.index+e.length()-n-2).retain(1,{\"code-block\":null}).delete(1);return this.quill.updateContents(r,a.Ay.sources.USER),this.quill.setSelection(t.index-1,a.Ay.sources.SILENT),!1}return!0}},\"embed left\":m(\"ArrowLeft\",!1),\"embed left shift\":m(\"ArrowLeft\",!0),\"embed right\":m(\"ArrowRight\",!1),\"embed right shift\":m(\"ArrowRight\",!0),\"table down\":y(!1),\"table up\":y(!0)}};function g(t){return{key:\"Tab\",shiftKey:!t,format:{\"code-block\":!0},handler(e,n){let{event:r}=n;const i=this.quill.scroll.query(\"code-block\"),{TAB:s}=i;if(0===e.length&&!r.shiftKey)return this.quill.insertText(e.index,s,a.Ay.sources.USER),void this.quill.setSelection(e.index+s.length,a.Ay.sources.SILENT);const o=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e);let{index:l,length:c}=e;o.forEach(((e,n)=>{t?(e.insertAt(0,s),0===n?l+=s.length:c+=s.length):e.domNode.textContent.startsWith(s)&&(e.deleteAt(0,s.length),0===n?l-=s.length:c-=s.length)})),this.quill.update(a.Ay.sources.USER),this.quill.setSelection(l,c,a.Ay.sources.SILENT)}}}function m(t,e){return{key:t,shiftKey:e,altKey:null,[\"ArrowLeft\"===t?\"prefix\":\"suffix\"]:/^$/,handler(n){let{index:r}=n;\"ArrowRight\"===t&&(r+=n.length+1);const[i]=this.quill.getLeaf(r);return!(i instanceof l.EmbedBlot&&(\"ArrowLeft\"===t?e?this.quill.setSelection(n.index-1,n.length+1,a.Ay.sources.USER):this.quill.setSelection(n.index-1,a.Ay.sources.USER):e?this.quill.setSelection(n.index,n.length+1,a.Ay.sources.USER):this.quill.setSelection(n.index+n.length+1,a.Ay.sources.USER),1))}}}function b(t){return{key:t[0],shortKey:!0,handler(e,n){this.quill.format(t,!n.format[t],a.Ay.sources.USER)}}}function y(t){return{key:t?\"ArrowUp\":\"ArrowDown\",collapsed:!0,format:[\"table\"],handler(e,n){const r=t?\"prev\":\"next\",i=n.line,s=i.parent[r];if(null!=s){if(\"table-row\"===s.statics.blotName){let t=s.children.head,e=i;for(;null!=e.prev;)e=e.prev,t=t.next;const r=t.offset(this.quill.scroll)+Math.min(n.offset,t.length()-1);this.quill.setSelection(r,0,a.Ay.sources.USER)}}else{const e=i.table()[r];null!=e&&(t?this.quill.setSelection(e.offset(this.quill.scroll)+e.length()-1,0,a.Ay.sources.USER):this.quill.setSelection(e.offset(this.quill.scroll),0,a.Ay.sources.USER))}return!1}}}function v(t){let{quill:e,range:n}=t;const r=e.getLines(n);let i={};if(r.length>1){const t=r[0].formats(),e=r[r.length-1].formats();i=s.AttributeMap.diff(e,t)||{}}e.deleteText(n,a.Ay.sources.USER),Object.keys(i).length>0&&e.formatLine(n.index,1,i,a.Ay.sources.USER),e.setSelection(n.index,a.Ay.sources.SILENT)}f.DEFAULTS=p},8920:function(t){\"use strict\";var e=Object.prototype.hasOwnProperty,n=\"~\";function r(){}function i(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function s(t,e,r,s,o){if(\"function\"!=typeof r)throw new TypeError(\"The listener must be a function\");var l=new i(r,s||t,o),a=n?n+e:e;return t._events[a]?t._events[a].fn?t._events[a]=[t._events[a],l]:t._events[a].push(l):(t._events[a]=l,t._eventsCount++),t}function o(t,e){0==--t._eventsCount?t._events=new r:delete t._events[e]}function l(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),l.prototype.eventNames=function(){var t,r,i=[];if(0===this._eventsCount)return i;for(r in t=this._events)e.call(t,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},l.prototype.listeners=function(t){var e=n?n+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,o=new Array(s);i<s;i++)o[i]=r[i].fn;return o},l.prototype.listenerCount=function(t){var e=n?n+t:t,r=this._events[e];return r?r.fn?1:r.length:0},l.prototype.emit=function(t,e,r,i,s,o){var l=n?n+t:t;if(!this._events[l])return!1;var a,c,u=this._events[l],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,r),!0;case 4:return u.fn.call(u.context,e,r,i),!0;case 5:return u.fn.call(u.context,e,r,i,s),!0;case 6:return u.fn.call(u.context,e,r,i,s,o),!0}for(c=1,a=new Array(h-1);c<h;c++)a[c-1]=arguments[c];u.fn.apply(u.context,a)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(t,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,e);break;case 3:u[c].fn.call(u[c].context,e,r);break;case 4:u[c].fn.call(u[c].context,e,r,i);break;default:if(!a)for(d=1,a=new Array(h-1);d<h;d++)a[d-1]=arguments[d];u[c].fn.apply(u[c].context,a)}}return!0},l.prototype.on=function(t,e,n){return s(this,t,e,n,!1)},l.prototype.once=function(t,e,n){return s(this,t,e,n,!0)},l.prototype.removeListener=function(t,e,r,i){var s=n?n+t:t;if(!this._events[s])return this;if(!e)return o(this,s),this;var l=this._events[s];if(l.fn)l.fn!==e||i&&!l.once||r&&l.context!==r||o(this,s);else{for(var a=0,c=[],u=l.length;a<u;a++)(l[a].fn!==e||i&&!l[a].once||r&&l[a].context!==r)&&c.push(l[a]);c.length?this._events[s]=1===c.length?c[0]:c:o(this,s)}return this},l.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&o(this,e)):(this._events=new r,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=n,l.EventEmitter=l,t.exports=l},5090:function(t){var e=-1,n=1,r=0;function i(t,g,m,b,y){if(t===g)return t?[[r,t]]:[];if(null!=m){var A=function(t,e,n){var r=\"number\"==typeof n?{index:n,length:0}:n.oldRange,i=\"number\"==typeof n?null:n.newRange,s=t.length,o=e.length;if(0===r.length&&(null===i||0===i.length)){var l=r.index,a=t.slice(0,l),c=t.slice(l),u=i?i.index:null,h=l+o-s;if((null===u||u===h)&&!(h<0||h>o)){var d=e.slice(0,h);if((g=e.slice(h))===c){var f=Math.min(l,h);if((b=a.slice(0,f))===(A=d.slice(0,f)))return v(b,a.slice(f),d.slice(f),c)}}if(null===u||u===l){var p=l,g=(d=e.slice(0,p),e.slice(p));if(d===a){var m=Math.min(s-p,o-p);if((y=c.slice(c.length-m))===(x=g.slice(g.length-m)))return v(a,c.slice(0,c.length-m),g.slice(0,g.length-m),y)}}}if(r.length>0&&i&&0===i.length){var b=t.slice(0,r.index),y=t.slice(r.index+r.length);if(!(o<(f=b.length)+(m=y.length))){var A=e.slice(0,f),x=e.slice(o-m);if(b===A&&y===x)return v(b,t.slice(f,s-m),e.slice(f,o-m),y)}}return null}(t,g,m);if(A)return A}var x=o(t,g),N=t.substring(0,x);x=a(t=t.substring(x),g=g.substring(x));var E=t.substring(t.length-x),w=function(t,l){var c;if(!t)return[[n,l]];if(!l)return[[e,t]];var u=t.length>l.length?t:l,h=t.length>l.length?l:t,d=u.indexOf(h);if(-1!==d)return c=[[n,u.substring(0,d)],[r,h],[n,u.substring(d+h.length)]],t.length>l.length&&(c[0][0]=c[2][0]=e),c;if(1===h.length)return[[e,t],[n,l]];var f=function(t,e){var n=t.length>e.length?t:e,r=t.length>e.length?e:t;if(n.length<4||2*r.length<n.length)return null;function i(t,e,n){for(var r,i,s,l,c=t.substring(n,n+Math.floor(t.length/4)),u=-1,h=\"\";-1!==(u=e.indexOf(c,u+1));){var d=o(t.substring(n),e.substring(u)),f=a(t.substring(0,n),e.substring(0,u));h.length<f+d&&(h=e.substring(u-f,u)+e.substring(u,u+d),r=t.substring(0,n-f),i=t.substring(n+d),s=e.substring(0,u-f),l=e.substring(u+d))}return 2*h.length>=t.length?[r,i,s,l,h]:null}var s,l,c,u,h,d=i(n,r,Math.ceil(n.length/4)),f=i(n,r,Math.ceil(n.length/2));return d||f?(s=f?d&&d[4].length>f[4].length?d:f:d,t.length>e.length?(l=s[0],c=s[1],u=s[2],h=s[3]):(u=s[0],h=s[1],l=s[2],c=s[3]),[l,c,u,h,s[4]]):null}(t,l);if(f){var p=f[0],g=f[1],m=f[2],b=f[3],y=f[4],v=i(p,m),A=i(g,b);return v.concat([[r,y]],A)}return function(t,r){for(var i=t.length,o=r.length,l=Math.ceil((i+o)/2),a=l,c=2*l,u=new Array(c),h=new Array(c),d=0;d<c;d++)u[d]=-1,h[d]=-1;u[a+1]=0,h[a+1]=0;for(var f=i-o,p=f%2!=0,g=0,m=0,b=0,y=0,v=0;v<l;v++){for(var A=-v+g;A<=v-m;A+=2){for(var x=a+A,N=(_=A===-v||A!==v&&u[x-1]<u[x+1]?u[x+1]:u[x-1]+1)-A;_<i&&N<o&&t.charAt(_)===r.charAt(N);)_++,N++;if(u[x]=_,_>i)m+=2;else if(N>o)g+=2;else if(p&&(q=a+f-A)>=0&&q<c&&-1!==h[q]&&_>=(w=i-h[q]))return s(t,r,_,N)}for(var E=-v+b;E<=v-y;E+=2){for(var w,q=a+E,k=(w=E===-v||E!==v&&h[q-1]<h[q+1]?h[q+1]:h[q-1]+1)-E;w<i&&k<o&&t.charAt(i-w-1)===r.charAt(o-k-1);)w++,k++;if(h[q]=w,w>i)y+=2;else if(k>o)b+=2;else if(!p){var _;if((x=a+f-E)>=0&&x<c&&-1!==u[x])if(N=a+(_=u[x])-x,_>=(w=i-w))return s(t,r,_,N)}}}return[[e,t],[n,r]]}(t,l)}(t=t.substring(0,t.length-x),g=g.substring(0,g.length-x));return N&&w.unshift([r,N]),E&&w.push([r,E]),p(w,y),b&&function(t){for(var i=!1,s=[],o=0,g=null,m=0,b=0,y=0,v=0,A=0;m<t.length;)t[m][0]==r?(s[o++]=m,b=v,y=A,v=0,A=0,g=t[m][1]):(t[m][0]==n?v+=t[m][1].length:A+=t[m][1].length,g&&g.length<=Math.max(b,y)&&g.length<=Math.max(v,A)&&(t.splice(s[o-1],0,[e,g]),t[s[o-1]+1][0]=n,o--,m=--o>0?s[o-1]:-1,b=0,y=0,v=0,A=0,g=null,i=!0)),m++;for(i&&p(t),function(t){function e(t,e){if(!t||!e)return 6;var n=t.charAt(t.length-1),r=e.charAt(0),i=n.match(c),s=r.match(c),o=i&&n.match(u),l=s&&r.match(u),a=o&&n.match(h),p=l&&r.match(h),g=a&&t.match(d),m=p&&e.match(f);return g||m?5:a||p?4:i&&!o&&l?3:o||l?2:i||s?1:0}for(var n=1;n<t.length-1;){if(t[n-1][0]==r&&t[n+1][0]==r){var i=t[n-1][1],s=t[n][1],o=t[n+1][1],l=a(i,s);if(l){var p=s.substring(s.length-l);i=i.substring(0,i.length-l),s=p+s.substring(0,s.length-l),o=p+o}for(var g=i,m=s,b=o,y=e(i,s)+e(s,o);s.charAt(0)===o.charAt(0);){i+=s.charAt(0),s=s.substring(1)+o.charAt(0),o=o.substring(1);var v=e(i,s)+e(s,o);v>=y&&(y=v,g=i,m=s,b=o)}t[n-1][1]!=g&&(g?t[n-1][1]=g:(t.splice(n-1,1),n--),t[n][1]=m,b?t[n+1][1]=b:(t.splice(n+1,1),n--))}n++}}(t),m=1;m<t.length;){if(t[m-1][0]==e&&t[m][0]==n){var x=t[m-1][1],N=t[m][1],E=l(x,N),w=l(N,x);E>=w?(E>=x.length/2||E>=N.length/2)&&(t.splice(m,0,[r,N.substring(0,E)]),t[m-1][1]=x.substring(0,x.length-E),t[m+1][1]=N.substring(E),m++):(w>=x.length/2||w>=N.length/2)&&(t.splice(m,0,[r,x.substring(0,w)]),t[m-1][0]=n,t[m-1][1]=N.substring(0,N.length-w),t[m+1][0]=e,t[m+1][1]=x.substring(w),m++),m++}m++}}(w),w}function s(t,e,n,r){var s=t.substring(0,n),o=e.substring(0,r),l=t.substring(n),a=e.substring(r),c=i(s,o),u=i(l,a);return c.concat(u)}function o(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(s,i)==e.substring(s,i)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return g(t.charCodeAt(i-1))&&i--,i}function l(t,e){var n=t.length,r=e.length;if(0==n||0==r)return 0;n>r?t=t.substring(n-r):n<r&&(e=e.substring(0,n));var i=Math.min(n,r);if(t==e)return i;for(var s=0,o=1;;){var l=t.substring(i-o),a=e.indexOf(l);if(-1==a)return s;o+=a,0!=a&&t.substring(i-o)!=e.substring(0,o)||(s=o,o++)}}function a(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(t.length-i,t.length-s)==e.substring(e.length-i,e.length-s)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return m(t.charCodeAt(t.length-i))&&i--,i}var c=/[^a-zA-Z0-9]/,u=/\\s/,h=/[\\r\\n]/,d=/\\n\\r?\\n$/,f=/^\\r?\\n\\r?\\n/;function p(t,i){t.push([r,\"\"]);for(var s,l=0,c=0,u=0,h=\"\",d=\"\";l<t.length;)if(l<t.length-1&&!t[l][1])t.splice(l,1);else switch(t[l][0]){case n:u++,d+=t[l][1],l++;break;case e:c++,h+=t[l][1],l++;break;case r:var f=l-u-c-1;if(i){if(f>=0&&y(t[f][1])){var g=t[f][1].slice(-1);if(t[f][1]=t[f][1].slice(0,-1),h=g+h,d=g+d,!t[f][1]){t.splice(f,1),l--;var m=f-1;t[m]&&t[m][0]===n&&(u++,d=t[m][1]+d,m--),t[m]&&t[m][0]===e&&(c++,h=t[m][1]+h,m--),f=m}}b(t[l][1])&&(g=t[l][1].charAt(0),t[l][1]=t[l][1].slice(1),h+=g,d+=g)}if(l<t.length-1&&!t[l][1]){t.splice(l,1);break}if(h.length>0||d.length>0){h.length>0&&d.length>0&&(0!==(s=o(d,h))&&(f>=0?t[f][1]+=d.substring(0,s):(t.splice(0,0,[r,d.substring(0,s)]),l++),d=d.substring(s),h=h.substring(s)),0!==(s=a(d,h))&&(t[l][1]=d.substring(d.length-s)+t[l][1],d=d.substring(0,d.length-s),h=h.substring(0,h.length-s)));var v=u+c;0===h.length&&0===d.length?(t.splice(l-v,v),l-=v):0===h.length?(t.splice(l-v,v,[n,d]),l=l-v+1):0===d.length?(t.splice(l-v,v,[e,h]),l=l-v+1):(t.splice(l-v,v,[e,h],[n,d]),l=l-v+2)}0!==l&&t[l-1][0]===r?(t[l-1][1]+=t[l][1],t.splice(l,1)):l++,u=0,c=0,h=\"\",d=\"\"}\"\"===t[t.length-1][1]&&t.pop();var A=!1;for(l=1;l<t.length-1;)t[l-1][0]===r&&t[l+1][0]===r&&(t[l][1].substring(t[l][1].length-t[l-1][1].length)===t[l-1][1]?(t[l][1]=t[l-1][1]+t[l][1].substring(0,t[l][1].length-t[l-1][1].length),t[l+1][1]=t[l-1][1]+t[l+1][1],t.splice(l-1,1),A=!0):t[l][1].substring(0,t[l+1][1].length)==t[l+1][1]&&(t[l-1][1]+=t[l+1][1],t[l][1]=t[l][1].substring(t[l+1][1].length)+t[l+1][1],t.splice(l+1,1),A=!0)),l++;A&&p(t,i)}function g(t){return t>=55296&&t<=56319}function m(t){return t>=56320&&t<=57343}function b(t){return m(t.charCodeAt(0))}function y(t){return g(t.charCodeAt(t.length-1))}function v(t,i,s,o){return y(t)||b(o)?null:function(t){for(var e=[],n=0;n<t.length;n++)t[n][1].length>0&&e.push(t[n]);return e}([[r,t],[e,i],[n,s],[r,o]])}function A(t,e,n,r){return i(t,e,n,r,!0)}A.INSERT=n,A.DELETE=e,A.EQUAL=r,t.exports=A},9629:function(t,e,n){t=n.nmd(t);var r=\"__lodash_hash_undefined__\",i=9007199254740991,s=\"[object Arguments]\",o=\"[object Boolean]\",l=\"[object Date]\",a=\"[object Function]\",c=\"[object GeneratorFunction]\",u=\"[object Map]\",h=\"[object Number]\",d=\"[object Object]\",f=\"[object Promise]\",p=\"[object RegExp]\",g=\"[object Set]\",m=\"[object String]\",b=\"[object Symbol]\",y=\"[object WeakMap]\",v=\"[object ArrayBuffer]\",A=\"[object DataView]\",x=\"[object Float32Array]\",N=\"[object Float64Array]\",E=\"[object Int8Array]\",w=\"[object Int16Array]\",q=\"[object Int32Array]\",k=\"[object Uint8Array]\",_=\"[object Uint8ClampedArray]\",L=\"[object Uint16Array]\",S=\"[object Uint32Array]\",O=/\\w*$/,T=/^\\[object .+?Constructor\\]$/,j=/^(?:0|[1-9]\\d*)$/,C={};C[s]=C[\"[object Array]\"]=C[v]=C[A]=C[o]=C[l]=C[x]=C[N]=C[E]=C[w]=C[q]=C[u]=C[h]=C[d]=C[p]=C[g]=C[m]=C[b]=C[k]=C[_]=C[L]=C[S]=!0,C[\"[object Error]\"]=C[a]=C[y]=!1;var R=\"object\"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,I=\"object\"==typeof self&&self&&self.Object===Object&&self,B=R||I||Function(\"return this\")(),M=e&&!e.nodeType&&e,U=M&&t&&!t.nodeType&&t,D=U&&U.exports===M;function P(t,e){return t.set(e[0],e[1]),t}function z(t,e){return t.add(e),t}function F(t,e,n,r){var i=-1,s=t?t.length:0;for(r&&s&&(n=t[++i]);++i<s;)n=e(n,t[i],i,t);return n}function H(t){var e=!1;if(null!=t&&\"function\"!=typeof t.toString)try{e=!!(t+\"\")}catch(t){}return e}function $(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function V(t,e){return function(n){return t(e(n))}}function K(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var W,Z=Array.prototype,G=Function.prototype,X=Object.prototype,Q=B[\"__core-js_shared__\"],J=(W=/[^.]+$/.exec(Q&&Q.keys&&Q.keys.IE_PROTO||\"\"))?\"Symbol(src)_1.\"+W:\"\",Y=G.toString,tt=X.hasOwnProperty,et=X.toString,nt=RegExp(\"^\"+Y.call(tt).replace(/[\\\\^$.*+?()[\\]{}|]/g,\"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g,\"$1.*?\")+\"$\"),rt=D?B.Buffer:void 0,it=B.Symbol,st=B.Uint8Array,ot=V(Object.getPrototypeOf,Object),lt=Object.create,at=X.propertyIsEnumerable,ct=Z.splice,ut=Object.getOwnPropertySymbols,ht=rt?rt.isBuffer:void 0,dt=V(Object.keys,Object),ft=Bt(B,\"DataView\"),pt=Bt(B,\"Map\"),gt=Bt(B,\"Promise\"),mt=Bt(B,\"Set\"),bt=Bt(B,\"WeakMap\"),yt=Bt(Object,\"create\"),vt=zt(ft),At=zt(pt),xt=zt(gt),Nt=zt(mt),Et=zt(bt),wt=it?it.prototype:void 0,qt=wt?wt.valueOf:void 0;function kt(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function _t(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Lt(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function St(t){this.__data__=new _t(t)}function Ot(t,e,n){var r=t[e];tt.call(t,e)&&Ft(r,n)&&(void 0!==n||e in t)||(t[e]=n)}function Tt(t,e){for(var n=t.length;n--;)if(Ft(t[n][0],e))return n;return-1}function jt(t,e,n,r,i,f,y){var T;if(r&&(T=f?r(t,i,f,y):r(t)),void 0!==T)return T;if(!Wt(t))return t;var j=Ht(t);if(j){if(T=function(t){var e=t.length,n=t.constructor(e);return e&&\"string\"==typeof t[0]&&tt.call(t,\"index\")&&(n.index=t.index,n.input=t.input),n}(t),!e)return function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}(t,T)}else{var R=Ut(t),I=R==a||R==c;if(Vt(t))return function(t,e){if(e)return t.slice();var n=new t.constructor(t.length);return t.copy(n),n}(t,e);if(R==d||R==s||I&&!f){if(H(t))return f?t:{};if(T=function(t){return\"function\"!=typeof t.constructor||Pt(t)?{}:Wt(e=ot(t))?lt(e):{};var e}(I?{}:t),!e)return function(t,e){return Rt(t,Mt(t),e)}(t,function(t,e){return t&&Rt(e,Zt(e),t)}(T,t))}else{if(!C[R])return f?t:{};T=function(t,e,n,r){var i,s=t.constructor;switch(e){case v:return Ct(t);case o:case l:return new s(+t);case A:return function(t,e){var n=e?Ct(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,r);case x:case N:case E:case w:case q:case k:case _:case L:case S:return function(t,e){var n=e?Ct(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}(t,r);case u:return function(t,e,n){return F(e?n($(t),!0):$(t),P,new t.constructor)}(t,r,n);case h:case m:return new s(t);case p:return function(t){var e=new t.constructor(t.source,O.exec(t));return e.lastIndex=t.lastIndex,e}(t);case g:return function(t,e,n){return F(e?n(K(t),!0):K(t),z,new t.constructor)}(t,r,n);case b:return i=t,qt?Object(qt.call(i)):{}}}(t,R,jt,e)}}y||(y=new St);var B=y.get(t);if(B)return B;if(y.set(t,T),!j)var M=n?function(t){return function(t,e,n){var r=e(t);return Ht(t)?r:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(r,n(t))}(t,Zt,Mt)}(t):Zt(t);return function(t,e){for(var n=-1,r=t?t.length:0;++n<r&&!1!==e(t[n],n););}(M||t,(function(i,s){M&&(i=t[s=i]),Ot(T,s,jt(i,e,n,r,s,t,y))})),T}function Ct(t){var e=new t.constructor(t.byteLength);return new st(e).set(new st(t)),e}function Rt(t,e,n,r){n||(n={});for(var i=-1,s=e.length;++i<s;){var o=e[i],l=r?r(n[o],t[o],o,n,t):void 0;Ot(n,o,void 0===l?t[o]:l)}return n}function It(t,e){var n,r,i=t.__data__;return(\"string\"==(r=typeof(n=e))||\"number\"==r||\"symbol\"==r||\"boolean\"==r?\"__proto__\"!==n:null===n)?i[\"string\"==typeof e?\"string\":\"hash\"]:i.map}function Bt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!Wt(t)||(e=t,J&&J in e))&&(Kt(t)||H(t)?nt:T).test(zt(t));var e}(n)?n:void 0}kt.prototype.clear=function(){this.__data__=yt?yt(null):{}},kt.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},kt.prototype.get=function(t){var e=this.__data__;if(yt){var n=e[t];return n===r?void 0:n}return tt.call(e,t)?e[t]:void 0},kt.prototype.has=function(t){var e=this.__data__;return yt?void 0!==e[t]:tt.call(e,t)},kt.prototype.set=function(t,e){return this.__data__[t]=yt&&void 0===e?r:e,this},_t.prototype.clear=function(){this.__data__=[]},_t.prototype.delete=function(t){var e=this.__data__,n=Tt(e,t);return!(n<0||(n==e.length-1?e.pop():ct.call(e,n,1),0))},_t.prototype.get=function(t){var e=this.__data__,n=Tt(e,t);return n<0?void 0:e[n][1]},_t.prototype.has=function(t){return Tt(this.__data__,t)>-1},_t.prototype.set=function(t,e){var n=this.__data__,r=Tt(n,t);return r<0?n.push([t,e]):n[r][1]=e,this},Lt.prototype.clear=function(){this.__data__={hash:new kt,map:new(pt||_t),string:new kt}},Lt.prototype.delete=function(t){return It(this,t).delete(t)},Lt.prototype.get=function(t){return It(this,t).get(t)},Lt.prototype.has=function(t){return It(this,t).has(t)},Lt.prototype.set=function(t,e){return It(this,t).set(t,e),this},St.prototype.clear=function(){this.__data__=new _t},St.prototype.delete=function(t){return this.__data__.delete(t)},St.prototype.get=function(t){return this.__data__.get(t)},St.prototype.has=function(t){return this.__data__.has(t)},St.prototype.set=function(t,e){var n=this.__data__;if(n instanceof _t){var r=n.__data__;if(!pt||r.length<199)return r.push([t,e]),this;n=this.__data__=new Lt(r)}return n.set(t,e),this};var Mt=ut?V(ut,Object):function(){return[]},Ut=function(t){return et.call(t)};function Dt(t,e){return!!(e=null==e?i:e)&&(\"number\"==typeof t||j.test(t))&&t>-1&&t%1==0&&t<e}function Pt(t){var e=t&&t.constructor;return t===(\"function\"==typeof e&&e.prototype||X)}function zt(t){if(null!=t){try{return Y.call(t)}catch(t){}try{return t+\"\"}catch(t){}}return\"\"}function Ft(t,e){return t===e||t!=t&&e!=e}(ft&&Ut(new ft(new ArrayBuffer(1)))!=A||pt&&Ut(new pt)!=u||gt&&Ut(gt.resolve())!=f||mt&&Ut(new mt)!=g||bt&&Ut(new bt)!=y)&&(Ut=function(t){var e=et.call(t),n=e==d?t.constructor:void 0,r=n?zt(n):void 0;if(r)switch(r){case vt:return A;case At:return u;case xt:return f;case Nt:return g;case Et:return y}return e});var Ht=Array.isArray;function $t(t){return null!=t&&function(t){return\"number\"==typeof t&&t>-1&&t%1==0&&t<=i}(t.length)&&!Kt(t)}var Vt=ht||function(){return!1};function Kt(t){var e=Wt(t)?et.call(t):\"\";return e==a||e==c}function Wt(t){var e=typeof t;return!!t&&(\"object\"==e||\"function\"==e)}function Zt(t){return $t(t)?function(t,e){var n=Ht(t)||function(t){return function(t){return function(t){return!!t&&\"object\"==typeof t}(t)&&$t(t)}(t)&&tt.call(t,\"callee\")&&(!at.call(t,\"callee\")||et.call(t)==s)}(t)?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],r=n.length,i=!!r;for(var o in t)!e&&!tt.call(t,o)||i&&(\"length\"==o||Dt(o,r))||n.push(o);return n}(t):function(t){if(!Pt(t))return dt(t);var e=[];for(var n in Object(t))tt.call(t,n)&&\"constructor\"!=n&&e.push(n);return e}(t)}t.exports=function(t){return jt(t,!0,!0)}},4162:function(t,e,n){t=n.nmd(t);var r=\"__lodash_hash_undefined__\",i=1,s=2,o=9007199254740991,l=\"[object Arguments]\",a=\"[object Array]\",c=\"[object AsyncFunction]\",u=\"[object Boolean]\",h=\"[object Date]\",d=\"[object Error]\",f=\"[object Function]\",p=\"[object GeneratorFunction]\",g=\"[object Map]\",m=\"[object Number]\",b=\"[object Null]\",y=\"[object Object]\",v=\"[object Promise]\",A=\"[object Proxy]\",x=\"[object RegExp]\",N=\"[object Set]\",E=\"[object String]\",w=\"[object Undefined]\",q=\"[object WeakMap]\",k=\"[object ArrayBuffer]\",_=\"[object DataView]\",L=/^\\[object .+?Constructor\\]$/,S=/^(?:0|[1-9]\\d*)$/,O={};O[\"[object Float32Array]\"]=O[\"[object Float64Array]\"]=O[\"[object Int8Array]\"]=O[\"[object Int16Array]\"]=O[\"[object Int32Array]\"]=O[\"[object Uint8Array]\"]=O[\"[object Uint8ClampedArray]\"]=O[\"[object Uint16Array]\"]=O[\"[object Uint32Array]\"]=!0,O[l]=O[a]=O[k]=O[u]=O[_]=O[h]=O[d]=O[f]=O[g]=O[m]=O[y]=O[x]=O[N]=O[E]=O[q]=!1;var T=\"object\"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,j=\"object\"==typeof self&&self&&self.Object===Object&&self,C=T||j||Function(\"return this\")(),R=e&&!e.nodeType&&e,I=R&&t&&!t.nodeType&&t,B=I&&I.exports===R,M=B&&T.process,U=function(){try{return M&&M.binding&&M.binding(\"util\")}catch(t){}}(),D=U&&U.isTypedArray;function P(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}function z(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function F(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var H,$,V,K=Array.prototype,W=Function.prototype,Z=Object.prototype,G=C[\"__core-js_shared__\"],X=W.toString,Q=Z.hasOwnProperty,J=(H=/[^.]+$/.exec(G&&G.keys&&G.keys.IE_PROTO||\"\"))?\"Symbol(src)_1.\"+H:\"\",Y=Z.toString,tt=RegExp(\"^\"+X.call(Q).replace(/[\\\\^$.*+?()[\\]{}|]/g,\"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g,\"$1.*?\")+\"$\"),et=B?C.Buffer:void 0,nt=C.Symbol,rt=C.Uint8Array,it=Z.propertyIsEnumerable,st=K.splice,ot=nt?nt.toStringTag:void 0,lt=Object.getOwnPropertySymbols,at=et?et.isBuffer:void 0,ct=($=Object.keys,V=Object,function(t){return $(V(t))}),ut=It(C,\"DataView\"),ht=It(C,\"Map\"),dt=It(C,\"Promise\"),ft=It(C,\"Set\"),pt=It(C,\"WeakMap\"),gt=It(Object,\"create\"),mt=Dt(ut),bt=Dt(ht),yt=Dt(dt),vt=Dt(ft),At=Dt(pt),xt=nt?nt.prototype:void 0,Nt=xt?xt.valueOf:void 0;function Et(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function wt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function qt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function kt(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new qt;++e<n;)this.add(t[e])}function _t(t){var e=this.__data__=new wt(t);this.size=e.size}function Lt(t,e){for(var n=t.length;n--;)if(Pt(t[n][0],e))return n;return-1}function St(t){return null==t?void 0===t?w:b:ot&&ot in Object(t)?function(t){var e=Q.call(t,ot),n=t[ot];try{t[ot]=void 0;var r=!0}catch(t){}var i=Y.call(t);return r&&(e?t[ot]=n:delete t[ot]),i}(t):function(t){return Y.call(t)}(t)}function Ot(t){return Wt(t)&&St(t)==l}function Tt(t,e,n,r,o){return t===e||(null==t||null==e||!Wt(t)&&!Wt(e)?t!=t&&e!=e:function(t,e,n,r,o,c){var f=Ft(t),p=Ft(e),b=f?a:Mt(t),v=p?a:Mt(e),A=(b=b==l?y:b)==y,w=(v=v==l?y:v)==y,q=b==v;if(q&&Ht(t)){if(!Ht(e))return!1;f=!0,A=!1}if(q&&!A)return c||(c=new _t),f||Zt(t)?jt(t,e,n,r,o,c):function(t,e,n,r,o,l,a){switch(n){case _:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case k:return!(t.byteLength!=e.byteLength||!l(new rt(t),new rt(e)));case u:case h:case m:return Pt(+t,+e);case d:return t.name==e.name&&t.message==e.message;case x:case E:return t==e+\"\";case g:var c=z;case N:var f=r&i;if(c||(c=F),t.size!=e.size&&!f)return!1;var p=a.get(t);if(p)return p==e;r|=s,a.set(t,e);var b=jt(c(t),c(e),r,o,l,a);return a.delete(t),b;case\"[object Symbol]\":if(Nt)return Nt.call(t)==Nt.call(e)}return!1}(t,e,b,n,r,o,c);if(!(n&i)){var L=A&&Q.call(t,\"__wrapped__\"),S=w&&Q.call(e,\"__wrapped__\");if(L||S){var O=L?t.value():t,T=S?e.value():e;return c||(c=new _t),o(O,T,n,r,c)}}return!!q&&(c||(c=new _t),function(t,e,n,r,s,o){var l=n&i,a=Ct(t),c=a.length;if(c!=Ct(e).length&&!l)return!1;for(var u=c;u--;){var h=a[u];if(!(l?h in e:Q.call(e,h)))return!1}var d=o.get(t);if(d&&o.get(e))return d==e;var f=!0;o.set(t,e),o.set(e,t);for(var p=l;++u<c;){var g=t[h=a[u]],m=e[h];if(r)var b=l?r(m,g,h,e,t,o):r(g,m,h,t,e,o);if(!(void 0===b?g===m||s(g,m,n,r,o):b)){f=!1;break}p||(p=\"constructor\"==h)}if(f&&!p){var y=t.constructor,v=e.constructor;y==v||!(\"constructor\"in t)||!(\"constructor\"in e)||\"function\"==typeof y&&y instanceof y&&\"function\"==typeof v&&v instanceof v||(f=!1)}return o.delete(t),o.delete(e),f}(t,e,n,r,o,c))}(t,e,n,r,Tt,o))}function jt(t,e,n,r,o,l){var a=n&i,c=t.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var h=l.get(t);if(h&&l.get(e))return h==e;var d=-1,f=!0,p=n&s?new kt:void 0;for(l.set(t,e),l.set(e,t);++d<c;){var g=t[d],m=e[d];if(r)var b=a?r(m,g,d,e,t,l):r(g,m,d,t,e,l);if(void 0!==b){if(b)continue;f=!1;break}if(p){if(!P(e,(function(t,e){if(i=e,!p.has(i)&&(g===t||o(g,t,n,r,l)))return p.push(e);var i}))){f=!1;break}}else if(g!==m&&!o(g,m,n,r,l)){f=!1;break}}return l.delete(t),l.delete(e),f}function Ct(t){return function(t,e,n){var r=e(t);return Ft(t)?r:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(r,n(t))}(t,Gt,Bt)}function Rt(t,e){var n,r,i=t.__data__;return(\"string\"==(r=typeof(n=e))||\"number\"==r||\"symbol\"==r||\"boolean\"==r?\"__proto__\"!==n:null===n)?i[\"string\"==typeof e?\"string\":\"hash\"]:i.map}function It(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!Kt(t)||function(t){return!!J&&J in t}(t))&&($t(t)?tt:L).test(Dt(t))}(n)?n:void 0}Et.prototype.clear=function(){this.__data__=gt?gt(null):{},this.size=0},Et.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Et.prototype.get=function(t){var e=this.__data__;if(gt){var n=e[t];return n===r?void 0:n}return Q.call(e,t)?e[t]:void 0},Et.prototype.has=function(t){var e=this.__data__;return gt?void 0!==e[t]:Q.call(e,t)},Et.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=gt&&void 0===e?r:e,this},wt.prototype.clear=function(){this.__data__=[],this.size=0},wt.prototype.delete=function(t){var e=this.__data__,n=Lt(e,t);return!(n<0||(n==e.length-1?e.pop():st.call(e,n,1),--this.size,0))},wt.prototype.get=function(t){var e=this.__data__,n=Lt(e,t);return n<0?void 0:e[n][1]},wt.prototype.has=function(t){return Lt(this.__data__,t)>-1},wt.prototype.set=function(t,e){var n=this.__data__,r=Lt(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},qt.prototype.clear=function(){this.size=0,this.__data__={hash:new Et,map:new(ht||wt),string:new Et}},qt.prototype.delete=function(t){var e=Rt(this,t).delete(t);return this.size-=e?1:0,e},qt.prototype.get=function(t){return Rt(this,t).get(t)},qt.prototype.has=function(t){return Rt(this,t).has(t)},qt.prototype.set=function(t,e){var n=Rt(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},kt.prototype.add=kt.prototype.push=function(t){return this.__data__.set(t,r),this},kt.prototype.has=function(t){return this.__data__.has(t)},_t.prototype.clear=function(){this.__data__=new wt,this.size=0},_t.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},_t.prototype.get=function(t){return this.__data__.get(t)},_t.prototype.has=function(t){return this.__data__.has(t)},_t.prototype.set=function(t,e){var n=this.__data__;if(n instanceof wt){var r=n.__data__;if(!ht||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new qt(r)}return n.set(t,e),this.size=n.size,this};var Bt=lt?function(t){return null==t?[]:(t=Object(t),function(e,n){for(var r=-1,i=null==e?0:e.length,s=0,o=[];++r<i;){var l=e[r];a=l,it.call(t,a)&&(o[s++]=l)}var a;return o}(lt(t)))}:function(){return[]},Mt=St;function Ut(t,e){return!!(e=null==e?o:e)&&(\"number\"==typeof t||S.test(t))&&t>-1&&t%1==0&&t<e}function Dt(t){if(null!=t){try{return X.call(t)}catch(t){}try{return t+\"\"}catch(t){}}return\"\"}function Pt(t,e){return t===e||t!=t&&e!=e}(ut&&Mt(new ut(new ArrayBuffer(1)))!=_||ht&&Mt(new ht)!=g||dt&&Mt(dt.resolve())!=v||ft&&Mt(new ft)!=N||pt&&Mt(new pt)!=q)&&(Mt=function(t){var e=St(t),n=e==y?t.constructor:void 0,r=n?Dt(n):\"\";if(r)switch(r){case mt:return _;case bt:return g;case yt:return v;case vt:return N;case At:return q}return e});var zt=Ot(function(){return arguments}())?Ot:function(t){return Wt(t)&&Q.call(t,\"callee\")&&!it.call(t,\"callee\")},Ft=Array.isArray,Ht=at||function(){return!1};function $t(t){if(!Kt(t))return!1;var e=St(t);return e==f||e==p||e==c||e==A}function Vt(t){return\"number\"==typeof t&&t>-1&&t%1==0&&t<=o}function Kt(t){var e=typeof t;return null!=t&&(\"object\"==e||\"function\"==e)}function Wt(t){return null!=t&&\"object\"==typeof t}var Zt=D?function(t){return function(e){return t(e)}}(D):function(t){return Wt(t)&&Vt(t.length)&&!!O[St(t)]};function Gt(t){return null!=(e=t)&&Vt(e.length)&&!$t(e)?function(t,e){var n=Ft(t),r=!n&&zt(t),i=!n&&!r&&Ht(t),s=!n&&!r&&!i&&Zt(t),o=n||r||i||s,l=o?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],a=l.length;for(var c in t)!e&&!Q.call(t,c)||o&&(\"length\"==c||i&&(\"offset\"==c||\"parent\"==c)||s&&(\"buffer\"==c||\"byteLength\"==c||\"byteOffset\"==c)||Ut(c,a))||l.push(c);return l}(t):function(t){if(n=(e=t)&&e.constructor,e!==(\"function\"==typeof n&&n.prototype||Z))return ct(t);var e,n,r=[];for(var i in Object(t))Q.call(t,i)&&\"constructor\"!=i&&r.push(i);return r}(t);var e}t.exports=function(t,e){return Tt(t,e)}},1270:function(t,e,n){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0});const r=n(9629),i=n(4162);var s;!function(t){t.compose=function(t={},e={},n=!1){\"object\"!=typeof t&&(t={}),\"object\"!=typeof e&&(e={});let i=r(e);n||(i=Object.keys(i).reduce(((t,e)=>(null!=i[e]&&(t[e]=i[e]),t)),{}));for(const n in t)void 0!==t[n]&&void 0===e[n]&&(i[n]=t[n]);return Object.keys(i).length>0?i:void 0},t.diff=function(t={},e={}){\"object\"!=typeof t&&(t={}),\"object\"!=typeof e&&(e={});const n=Object.keys(t).concat(Object.keys(e)).reduce(((n,r)=>(i(t[r],e[r])||(n[r]=void 0===e[r]?null:e[r]),n)),{});return Object.keys(n).length>0?n:void 0},t.invert=function(t={},e={}){t=t||{};const n=Object.keys(e).reduce(((n,r)=>(e[r]!==t[r]&&void 0!==t[r]&&(n[r]=e[r]),n)),{});return Object.keys(t).reduce(((n,r)=>(t[r]!==e[r]&&void 0===e[r]&&(n[r]=null),n)),n)},t.transform=function(t,e,n=!1){if(\"object\"!=typeof t)return e;if(\"object\"!=typeof e)return;if(!n)return e;const r=Object.keys(e).reduce(((n,r)=>(void 0===t[r]&&(n[r]=e[r]),n)),{});return Object.keys(r).length>0?r:void 0}}(s||(s={})),e.default=s},5232:function(t,e,n){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0}),e.AttributeMap=e.OpIterator=e.Op=void 0;const r=n(5090),i=n(9629),s=n(4162),o=n(1270);e.AttributeMap=o.default;const l=n(4123);e.Op=l.default;const a=n(7033);e.OpIterator=a.default;const c=String.fromCharCode(0),u=(t,e)=>{if(\"object\"!=typeof t||null===t)throw new Error(\"cannot retain a \"+typeof t);if(\"object\"!=typeof e||null===e)throw new Error(\"cannot retain a \"+typeof e);const n=Object.keys(t)[0];if(!n||n!==Object.keys(e)[0])throw new Error(`embed types not matched: ${n} != ${Object.keys(e)[0]}`);return[n,t[n],e[n]]};class h{constructor(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){const e=this.handlers[t];if(!e)throw new Error(`no handlers for embed type \"${t}\"`);return e}insert(t,e){const n={};return\"string\"==typeof t&&0===t.length?this:(n.insert=t,null!=e&&\"object\"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if(\"number\"==typeof t&&t<=0)return this;const n={retain:t};return null!=e&&\"object\"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n)}push(t){let e=this.ops.length,n=this.ops[e-1];if(t=i(t),\"object\"==typeof n){if(\"number\"==typeof t.delete&&\"number\"==typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if(\"number\"==typeof n.delete&&null!=t.insert&&(e-=1,n=this.ops[e-1],\"object\"!=typeof n))return this.ops.unshift(t),this;if(s(t.attributes,n.attributes)){if(\"string\"==typeof t.insert&&\"string\"==typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},\"object\"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if(\"number\"==typeof t.retain&&\"number\"==typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},\"object\"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){const t=this.ops[this.ops.length-1];return t&&\"number\"==typeof t.retain&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){const e=[],n=[];return this.forEach((r=>{(t(r)?e:n).push(r)})),[e,n]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce(((t,e)=>e.insert?t+l.default.length(e):e.delete?t-e.delete:t),0)}length(){return this.reduce(((t,e)=>t+l.default.length(e)),0)}slice(t=0,e=1/0){const n=[],r=new a.default(this.ops);let i=0;for(;i<e&&r.hasNext();){let s;i<t?s=r.next(t-i):(s=r.next(e-i),n.push(s)),i+=l.default.length(s)}return new h(n)}compose(t){const e=new a.default(this.ops),n=new a.default(t.ops),r=[],i=n.peek();if(null!=i&&\"number\"==typeof i.retain&&null==i.attributes){let t=i.retain;for(;\"insert\"===e.peekType()&&e.peekLength()<=t;)t-=e.peekLength(),r.push(e.next());i.retain-t>0&&n.next(i.retain-t)}const l=new h(r);for(;e.hasNext()||n.hasNext();)if(\"insert\"===n.peekType())l.push(n.next());else if(\"delete\"===e.peekType())l.push(e.next());else{const t=Math.min(e.peekLength(),n.peekLength()),r=e.next(t),i=n.next(t);if(i.retain){const a={};if(\"number\"==typeof r.retain)a.retain=\"number\"==typeof i.retain?t:i.retain;else if(\"number\"==typeof i.retain)null==r.retain?a.insert=r.insert:a.retain=r.retain;else{const t=null==r.retain?\"insert\":\"retain\",[e,n,s]=u(r[t],i.retain),o=h.getHandler(e);a[t]={[e]:o.compose(n,s,\"retain\"===t)}}const c=o.default.compose(r.attributes,i.attributes,\"number\"==typeof r.retain);if(c&&(a.attributes=c),l.push(a),!n.hasNext()&&s(l.ops[l.ops.length-1],a)){const t=new h(e.rest());return l.concat(t).chop()}}else\"number\"==typeof i.delete&&(\"number\"==typeof r.retain||\"object\"==typeof r.retain&&null!==r.retain)&&l.push(i)}return l.chop()}concat(t){const e=new h(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new h;const n=[this,t].map((e=>e.map((n=>{if(null!=n.insert)return\"string\"==typeof n.insert?n.insert:c;throw new Error(\"diff() called \"+(e===t?\"on\":\"with\")+\" non-document\")})).join(\"\"))),i=new h,l=r(n[0],n[1],e,!0),u=new a.default(this.ops),d=new a.default(t.ops);return l.forEach((t=>{let e=t[1].length;for(;e>0;){let n=0;switch(t[0]){case r.INSERT:n=Math.min(d.peekLength(),e),i.push(d.next(n));break;case r.DELETE:n=Math.min(e,u.peekLength()),u.next(n),i.delete(n);break;case r.EQUAL:n=Math.min(u.peekLength(),d.peekLength(),e);const t=u.next(n),l=d.next(n);s(t.insert,l.insert)?i.retain(n,o.default.diff(t.attributes,l.attributes)):i.push(l).delete(n)}e-=n}})),i.chop()}eachLine(t,e=\"\\n\"){const n=new a.default(this.ops);let r=new h,i=0;for(;n.hasNext();){if(\"insert\"!==n.peekType())return;const s=n.peek(),o=l.default.length(s)-n.peekLength(),a=\"string\"==typeof s.insert?s.insert.indexOf(e,o)-o:-1;if(a<0)r.push(n.next());else if(a>0)r.push(n.next(a));else{if(!1===t(r,n.next(1).attributes||{},i))return;i+=1,r=new h}}r.length()>0&&t(r,{},i)}invert(t){const e=new h;return this.reduce(((n,r)=>{if(r.insert)e.delete(l.default.length(r));else{if(\"number\"==typeof r.retain&&null==r.attributes)return e.retain(r.retain),n+r.retain;if(r.delete||\"number\"==typeof r.retain){const i=r.delete||r.retain;return t.slice(n,n+i).forEach((t=>{r.delete?e.push(t):r.retain&&r.attributes&&e.retain(l.default.length(t),o.default.invert(r.attributes,t.attributes))})),n+i}if(\"object\"==typeof r.retain&&null!==r.retain){const i=t.slice(n,n+1),s=new a.default(i.ops).next(),[l,c,d]=u(r.retain,s.insert),f=h.getHandler(l);return e.retain({[l]:f.invert(c,d)},o.default.invert(r.attributes,s.attributes)),n+1}}return n}),0),e.chop()}transform(t,e=!1){if(e=!!e,\"number\"==typeof t)return this.transformPosition(t,e);const n=t,r=new a.default(this.ops),i=new a.default(n.ops),s=new h;for(;r.hasNext()||i.hasNext();)if(\"insert\"!==r.peekType()||!e&&\"insert\"===i.peekType())if(\"insert\"===i.peekType())s.push(i.next());else{const t=Math.min(r.peekLength(),i.peekLength()),n=r.next(t),l=i.next(t);if(n.delete)continue;if(l.delete)s.push(l);else{const r=n.retain,i=l.retain;let a=\"object\"==typeof i&&null!==i?i:t;if(\"object\"==typeof r&&null!==r&&\"object\"==typeof i&&null!==i){const t=Object.keys(r)[0];if(t===Object.keys(i)[0]){const n=h.getHandler(t);n&&(a={[t]:n.transform(r[t],i[t],e)})}}s.retain(a,o.default.transform(n.attributes,l.attributes,e))}}else s.retain(l.default.length(r.next()));return s.chop()}transformPosition(t,e=!1){e=!!e;const n=new a.default(this.ops);let r=0;for(;n.hasNext()&&r<=t;){const i=n.peekLength(),s=n.peekType();n.next(),\"delete\"!==s?(\"insert\"===s&&(r<t||!e)&&(t+=i),r+=i):t-=Math.min(i,t-r)}return t}}h.Op=l.default,h.OpIterator=a.default,h.AttributeMap=o.default,h.handlers={},e.default=h,t.exports=h,t.exports.default=h},4123:function(t,e){\"use strict\";var n;Object.defineProperty(e,\"__esModule\",{value:!0}),function(t){t.length=function(t){return\"number\"==typeof t.delete?t.delete:\"number\"==typeof t.retain?t.retain:\"object\"==typeof t.retain&&null!==t.retain?1:\"string\"==typeof t.insert?t.insert.length:1}}(n||(n={})),e.default=n},7033:function(t,e,n){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0});const r=n(4123);e.default=class{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);const e=this.ops[this.index];if(e){const n=this.offset,i=r.default.length(e);if(t>=i-n?(t=i-n,this.index+=1,this.offset=0):this.offset+=t,\"number\"==typeof e.delete)return{delete:t};{const r={};return e.attributes&&(r.attributes=e.attributes),\"number\"==typeof e.retain?r.retain=t:\"object\"==typeof e.retain&&null!==e.retain?r.retain=e.retain:\"string\"==typeof e.insert?r.insert=e.insert.substr(n,t):r.insert=e.insert,r}}return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?r.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const t=this.ops[this.index];return t?\"number\"==typeof t.delete?\"delete\":\"number\"==typeof t.retain||\"object\"==typeof t.retain&&null!==t.retain?\"retain\":\"insert\":\"retain\"}rest(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);{const t=this.offset,e=this.index,n=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(r)}}return[]}}},8820:function(t,e,n){\"use strict\";n.d(e,{A:function(){return l}});var r=n(8138),i=function(t,e){for(var n=t.length;n--;)if((0,r.A)(t[n][0],e))return n;return-1},s=Array.prototype.splice;function o(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}o.prototype.clear=function(){this.__data__=[],this.size=0},o.prototype.delete=function(t){var e=this.__data__,n=i(e,t);return!(n<0||(n==e.length-1?e.pop():s.call(e,n,1),--this.size,0))},o.prototype.get=function(t){var e=this.__data__,n=i(e,t);return n<0?void 0:e[n][1]},o.prototype.has=function(t){return i(this.__data__,t)>-1},o.prototype.set=function(t,e){var n=this.__data__,r=i(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this};var l=o},2461:function(t,e,n){\"use strict\";var r=n(2281),i=n(5507),s=(0,r.A)(i.A,\"Map\");e.A=s},3558:function(t,e,n){\"use strict\";n.d(e,{A:function(){return d}});var r=(0,n(2281).A)(Object,\"create\"),i=Object.prototype.hasOwnProperty,s=Object.prototype.hasOwnProperty;function o(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}o.prototype.clear=function(){this.__data__=r?r(null):{},this.size=0},o.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},o.prototype.get=function(t){var e=this.__data__;if(r){var n=e[t];return\"__lodash_hash_undefined__\"===n?void 0:n}return i.call(e,t)?e[t]:void 0},o.prototype.has=function(t){var e=this.__data__;return r?void 0!==e[t]:s.call(e,t)},o.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=r&&void 0===e?\"__lodash_hash_undefined__\":e,this};var l=o,a=n(8820),c=n(2461),u=function(t,e){var n,r,i=t.__data__;return(\"string\"==(r=typeof(n=e))||\"number\"==r||\"symbol\"==r||\"boolean\"==r?\"__proto__\"!==n:null===n)?i[\"string\"==typeof e?\"string\":\"hash\"]:i.map};function h(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}h.prototype.clear=function(){this.size=0,this.__data__={hash:new l,map:new(c.A||a.A),string:new l}},h.prototype.delete=function(t){var e=u(this,t).delete(t);return this.size-=e?1:0,e},h.prototype.get=function(t){return u(this,t).get(t)},h.prototype.has=function(t){return u(this,t).has(t)},h.prototype.set=function(t,e){var n=u(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this};var d=h},2673:function(t,e,n){\"use strict\";n.d(e,{A:function(){return l}});var r=n(8820),i=n(2461),s=n(3558);function o(t){var e=this.__data__=new r.A(t);this.size=e.size}o.prototype.clear=function(){this.__data__=new r.A,this.size=0},o.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},o.prototype.get=function(t){return this.__data__.get(t)},o.prototype.has=function(t){return this.__data__.has(t)},o.prototype.set=function(t,e){var n=this.__data__;if(n instanceof r.A){var o=n.__data__;if(!i.A||o.length<199)return o.push([t,e]),this.size=++n.size,this;n=this.__data__=new s.A(o)}return n.set(t,e),this.size=n.size,this};var l=o},439:function(t,e,n){\"use strict\";var r=n(5507).A.Symbol;e.A=r},7218:function(t,e,n){\"use strict\";var r=n(5507).A.Uint8Array;e.A=r},6753:function(t,e,n){\"use strict\";n.d(e,{A:function(){return c}});var r=n(8412),i=n(723),s=n(776),o=n(3767),l=n(5755),a=Object.prototype.hasOwnProperty,c=function(t,e){var n=(0,i.A)(t),c=!n&&(0,r.A)(t),u=!n&&!c&&(0,s.A)(t),h=!n&&!c&&!u&&(0,l.A)(t),d=n||c||u||h,f=d?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],p=f.length;for(var g in t)!e&&!a.call(t,g)||d&&(\"length\"==g||u&&(\"offset\"==g||\"parent\"==g)||h&&(\"buffer\"==g||\"byteLength\"==g||\"byteOffset\"==g)||(0,o.A)(g,p))||f.push(g);return f}},802:function(t,e){\"use strict\";e.A=function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}},6437:function(t,e,n){\"use strict\";var r=n(6770),i=n(8138),s=Object.prototype.hasOwnProperty;e.A=function(t,e,n){var o=t[e];s.call(t,e)&&(0,i.A)(o,n)&&(void 0!==n||e in t)||(0,r.A)(t,e,n)}},6770:function(t,e,n){\"use strict\";var r=n(7889);e.A=function(t,e,n){\"__proto__\"==e&&r.A?(0,r.A)(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}},1381:function(t,e,n){\"use strict\";var r=n(802),i=n(723);e.A=function(t,e,n){var s=e(t);return(0,i.A)(t)?s:(0,r.A)(s,n(t))}},2159:function(t,e,n){\"use strict\";n.d(e,{A:function(){return u}});var r=n(439),i=Object.prototype,s=i.hasOwnProperty,o=i.toString,l=r.A?r.A.toStringTag:void 0,a=Object.prototype.toString,c=r.A?r.A.toStringTag:void 0,u=function(t){return null==t?void 0===t?\"[object Undefined]\":\"[object Null]\":c&&c in Object(t)?function(t){var e=s.call(t,l),n=t[l];try{t[l]=void 0;var r=!0}catch(t){}var i=o.call(t);return r&&(e?t[l]=n:delete t[l]),i}(t):function(t){return a.call(t)}(t)}},5771:function(t,e){\"use strict\";e.A=function(t){return function(e){return t(e)}}},2899:function(t,e,n){\"use strict\";var r=n(7218);e.A=function(t){var e=new t.constructor(t.byteLength);return new r.A(e).set(new r.A(t)),e}},3812:function(t,e,n){\"use strict\";var r=n(5507),i=\"object\"==typeof exports&&exports&&!exports.nodeType&&exports,s=i&&\"object\"==typeof module&&module&&!module.nodeType&&module,o=s&&s.exports===i?r.A.Buffer:void 0,l=o?o.allocUnsafe:void 0;e.A=function(t,e){if(e)return t.slice();var n=t.length,r=l?l(n):new t.constructor(n);return t.copy(r),r}},1827:function(t,e,n){\"use strict\";var r=n(2899);e.A=function(t,e){var n=e?(0,r.A)(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}},4405:function(t,e){\"use strict\";e.A=function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}},9601:function(t,e,n){\"use strict\";var r=n(6437),i=n(6770);e.A=function(t,e,n,s){var o=!n;n||(n={});for(var l=-1,a=e.length;++l<a;){var c=e[l],u=s?s(n[c],t[c],c,n,t):void 0;void 0===u&&(u=t[c]),o?(0,i.A)(n,c,u):(0,r.A)(n,c,u)}return n}},7889:function(t,e,n){\"use strict\";var r=n(2281),i=function(){try{var t=(0,r.A)(Object,\"defineProperty\");return t({},\"\",{}),t}catch(t){}}();e.A=i},9646:function(t,e){\"use strict\";var n=\"object\"==typeof global&&global&&global.Object===Object&&global;e.A=n},2816:function(t,e,n){\"use strict\";var r=n(1381),i=n(9844),s=n(3169);e.A=function(t){return(0,r.A)(t,s.A,i.A)}},2281:function(t,e,n){\"use strict\";n.d(e,{A:function(){return m}});var r,i=n(7572),s=n(5507).A[\"__core-js_shared__\"],o=(r=/[^.]+$/.exec(s&&s.keys&&s.keys.IE_PROTO||\"\"))?\"Symbol(src)_1.\"+r:\"\",l=n(659),a=n(1543),c=/^\\[object .+?Constructor\\]$/,u=Function.prototype,h=Object.prototype,d=u.toString,f=h.hasOwnProperty,p=RegExp(\"^\"+d.call(f).replace(/[\\\\^$.*+?()[\\]{}|]/g,\"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g,\"$1.*?\")+\"$\"),g=function(t){return!(!(0,l.A)(t)||(e=t,o&&o in e))&&((0,i.A)(t)?p:c).test((0,a.A)(t));var e},m=function(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return g(n)?n:void 0}},8769:function(t,e,n){\"use strict\";var r=(0,n(2217).A)(Object.getPrototypeOf,Object);e.A=r},9844:function(t,e,n){\"use strict\";n.d(e,{A:function(){return o}});var r=n(6935),i=Object.prototype.propertyIsEnumerable,s=Object.getOwnPropertySymbols,o=s?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var n=-1,r=null==t?0:t.length,i=0,s=[];++n<r;){var o=t[n];e(o,n,t)&&(s[i++]=o)}return s}(s(t),(function(e){return i.call(t,e)})))}:r.A},7995:function(t,e,n){\"use strict\";n.d(e,{A:function(){return E}});var r=n(2281),i=n(5507),s=(0,r.A)(i.A,\"DataView\"),o=n(2461),l=(0,r.A)(i.A,\"Promise\"),a=(0,r.A)(i.A,\"Set\"),c=(0,r.A)(i.A,\"WeakMap\"),u=n(2159),h=n(1543),d=\"[object Map]\",f=\"[object Promise]\",p=\"[object Set]\",g=\"[object WeakMap]\",m=\"[object DataView]\",b=(0,h.A)(s),y=(0,h.A)(o.A),v=(0,h.A)(l),A=(0,h.A)(a),x=(0,h.A)(c),N=u.A;(s&&N(new s(new ArrayBuffer(1)))!=m||o.A&&N(new o.A)!=d||l&&N(l.resolve())!=f||a&&N(new a)!=p||c&&N(new c)!=g)&&(N=function(t){var e=(0,u.A)(t),n=\"[object Object]\"==e?t.constructor:void 0,r=n?(0,h.A)(n):\"\";if(r)switch(r){case b:return m;case y:return d;case v:return f;case A:return p;case x:return g}return e});var E=N},1683:function(t,e,n){\"use strict\";n.d(e,{A:function(){return a}});var r=n(659),i=Object.create,s=function(){function t(){}return function(e){if(!(0,r.A)(e))return{};if(i)return i(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}(),o=n(8769),l=n(501),a=function(t){return\"function\"!=typeof t.constructor||(0,l.A)(t)?{}:s((0,o.A)(t))}},3767:function(t,e){\"use strict\";var n=/^(?:0|[1-9]\\d*)$/;e.A=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&(\"number\"==r||\"symbol\"!=r&&n.test(t))&&t>-1&&t%1==0&&t<e}},501:function(t,e){\"use strict\";var n=Object.prototype;e.A=function(t){var e=t&&t.constructor;return t===(\"function\"==typeof e&&e.prototype||n)}},8795:function(t,e,n){\"use strict\";var r=n(9646),i=\"object\"==typeof exports&&exports&&!exports.nodeType&&exports,s=i&&\"object\"==typeof module&&module&&!module.nodeType&&module,o=s&&s.exports===i&&r.A.process,l=function(){try{return s&&s.require&&s.require(\"util\").types||o&&o.binding&&o.binding(\"util\")}catch(t){}}();e.A=l},2217:function(t,e){\"use strict\";e.A=function(t,e){return function(n){return t(e(n))}}},5507:function(t,e,n){\"use strict\";var r=n(9646),i=\"object\"==typeof self&&self&&self.Object===Object&&self,s=r.A||i||Function(\"return this\")();e.A=s},1543:function(t,e){\"use strict\";var n=Function.prototype.toString;e.A=function(t){if(null!=t){try{return n.call(t)}catch(t){}try{return t+\"\"}catch(t){}}return\"\"}},3707:function(t,e,n){\"use strict\";n.d(e,{A:function(){return H}});var r=n(2673),i=n(6437),s=n(9601),o=n(3169),l=n(2624),a=n(3812),c=n(4405),u=n(9844),h=n(802),d=n(8769),f=n(6935),p=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)(0,h.A)(e,(0,u.A)(t)),t=(0,d.A)(t);return e}:f.A,g=n(2816),m=n(1381),b=function(t){return(0,m.A)(t,l.A,p)},y=n(7995),v=Object.prototype.hasOwnProperty,A=n(2899),x=/\\w*$/,N=n(439),E=N.A?N.A.prototype:void 0,w=E?E.valueOf:void 0,q=n(1827),k=function(t,e,n){var r,i,s,o=t.constructor;switch(e){case\"[object ArrayBuffer]\":return(0,A.A)(t);case\"[object Boolean]\":case\"[object Date]\":return new o(+t);case\"[object DataView]\":return function(t,e){var n=e?(0,A.A)(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,n);case\"[object Float32Array]\":case\"[object Float64Array]\":case\"[object Int8Array]\":case\"[object Int16Array]\":case\"[object Int32Array]\":case\"[object Uint8Array]\":case\"[object Uint8ClampedArray]\":case\"[object Uint16Array]\":case\"[object Uint32Array]\":return(0,q.A)(t,n);case\"[object Map]\":case\"[object Set]\":return new o;case\"[object Number]\":case\"[object String]\":return new o(t);case\"[object RegExp]\":return(s=new(i=t).constructor(i.source,x.exec(i))).lastIndex=i.lastIndex,s;case\"[object Symbol]\":return r=t,w?Object(w.call(r)):{}}},_=n(1683),L=n(723),S=n(776),O=n(7948),T=n(5771),j=n(8795),C=j.A&&j.A.isMap,R=C?(0,T.A)(C):function(t){return(0,O.A)(t)&&\"[object Map]\"==(0,y.A)(t)},I=n(659),B=j.A&&j.A.isSet,M=B?(0,T.A)(B):function(t){return(0,O.A)(t)&&\"[object Set]\"==(0,y.A)(t)},U=\"[object Arguments]\",D=\"[object Function]\",P=\"[object Object]\",z={};z[U]=z[\"[object Array]\"]=z[\"[object ArrayBuffer]\"]=z[\"[object DataView]\"]=z[\"[object Boolean]\"]=z[\"[object Date]\"]=z[\"[object Float32Array]\"]=z[\"[object Float64Array]\"]=z[\"[object Int8Array]\"]=z[\"[object Int16Array]\"]=z[\"[object Int32Array]\"]=z[\"[object Map]\"]=z[\"[object Number]\"]=z[P]=z[\"[object RegExp]\"]=z[\"[object Set]\"]=z[\"[object String]\"]=z[\"[object Symbol]\"]=z[\"[object Uint8Array]\"]=z[\"[object Uint8ClampedArray]\"]=z[\"[object Uint16Array]\"]=z[\"[object Uint32Array]\"]=!0,z[\"[object Error]\"]=z[D]=z[\"[object WeakMap]\"]=!1;var F=function t(e,n,h,d,f,m){var A,x=1&n,N=2&n,E=4&n;if(h&&(A=f?h(e,d,f,m):h(e)),void 0!==A)return A;if(!(0,I.A)(e))return e;var w=(0,L.A)(e);if(w){if(A=function(t){var e=t.length,n=new t.constructor(e);return e&&\"string\"==typeof t[0]&&v.call(t,\"index\")&&(n.index=t.index,n.input=t.input),n}(e),!x)return(0,c.A)(e,A)}else{var q=(0,y.A)(e),O=q==D||\"[object GeneratorFunction]\"==q;if((0,S.A)(e))return(0,a.A)(e,x);if(q==P||q==U||O&&!f){if(A=N||O?{}:(0,_.A)(e),!x)return N?function(t,e){return(0,s.A)(t,p(t),e)}(e,function(t,e){return t&&(0,s.A)(e,(0,l.A)(e),t)}(A,e)):function(t,e){return(0,s.A)(t,(0,u.A)(t),e)}(e,function(t,e){return t&&(0,s.A)(e,(0,o.A)(e),t)}(A,e))}else{if(!z[q])return f?e:{};A=k(e,q,x)}}m||(m=new r.A);var T=m.get(e);if(T)return T;m.set(e,A),M(e)?e.forEach((function(r){A.add(t(r,n,h,r,e,m))})):R(e)&&e.forEach((function(r,i){A.set(i,t(r,n,h,i,e,m))}));var j=E?N?b:g.A:N?l.A:o.A,C=w?void 0:j(e);return function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););}(C||e,(function(r,s){C&&(r=e[s=r]),(0,i.A)(A,s,t(r,n,h,s,e,m))})),A},H=function(t){return F(t,5)}},8138:function(t,e){\"use strict\";e.A=function(t,e){return t===e||t!=t&&e!=e}},8412:function(t,e,n){\"use strict\";n.d(e,{A:function(){return u}});var r=n(2159),i=n(7948),s=function(t){return(0,i.A)(t)&&\"[object Arguments]\"==(0,r.A)(t)},o=Object.prototype,l=o.hasOwnProperty,a=o.propertyIsEnumerable,c=s(function(){return arguments}())?s:function(t){return(0,i.A)(t)&&l.call(t,\"callee\")&&!a.call(t,\"callee\")},u=c},723:function(t,e){\"use strict\";var n=Array.isArray;e.A=n},3628:function(t,e,n){\"use strict\";var r=n(7572),i=n(1628);e.A=function(t){return null!=t&&(0,i.A)(t.length)&&!(0,r.A)(t)}},776:function(t,e,n){\"use strict\";n.d(e,{A:function(){return l}});var r=n(5507),i=\"object\"==typeof exports&&exports&&!exports.nodeType&&exports,s=i&&\"object\"==typeof module&&module&&!module.nodeType&&module,o=s&&s.exports===i?r.A.Buffer:void 0,l=(o?o.isBuffer:void 0)||function(){return!1}},5123:function(t,e,n){\"use strict\";n.d(e,{A:function(){return S}});var r=n(2673),i=n(3558);function s(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new i.A;++e<n;)this.add(t[e])}s.prototype.add=s.prototype.push=function(t){return this.__data__.set(t,\"__lodash_hash_undefined__\"),this},s.prototype.has=function(t){return this.__data__.has(t)};var o=s,l=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1},a=function(t,e,n,r,i,s){var a=1&n,c=t.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var h=s.get(t),d=s.get(e);if(h&&d)return h==e&&d==t;var f=-1,p=!0,g=2&n?new o:void 0;for(s.set(t,e),s.set(e,t);++f<c;){var m=t[f],b=e[f];if(r)var y=a?r(b,m,f,e,t,s):r(m,b,f,t,e,s);if(void 0!==y){if(y)continue;p=!1;break}if(g){if(!l(e,(function(t,e){if(o=e,!g.has(o)&&(m===t||i(m,t,n,r,s)))return g.push(e);var o}))){p=!1;break}}else if(m!==b&&!i(m,b,n,r,s)){p=!1;break}}return s.delete(t),s.delete(e),p},c=n(439),u=n(7218),h=n(8138),d=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n},f=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n},p=c.A?c.A.prototype:void 0,g=p?p.valueOf:void 0,m=n(2816),b=Object.prototype.hasOwnProperty,y=n(7995),v=n(723),A=n(776),x=n(5755),N=\"[object Arguments]\",E=\"[object Array]\",w=\"[object Object]\",q=Object.prototype.hasOwnProperty,k=function(t,e,n,i,s,o){var l=(0,v.A)(t),c=(0,v.A)(e),p=l?E:(0,y.A)(t),k=c?E:(0,y.A)(e),_=(p=p==N?w:p)==w,L=(k=k==N?w:k)==w,S=p==k;if(S&&(0,A.A)(t)){if(!(0,A.A)(e))return!1;l=!0,_=!1}if(S&&!_)return o||(o=new r.A),l||(0,x.A)(t)?a(t,e,n,i,s,o):function(t,e,n,r,i,s,o){switch(n){case\"[object DataView]\":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case\"[object ArrayBuffer]\":return!(t.byteLength!=e.byteLength||!s(new u.A(t),new u.A(e)));case\"[object Boolean]\":case\"[object Date]\":case\"[object Number]\":return(0,h.A)(+t,+e);case\"[object Error]\":return t.name==e.name&&t.message==e.message;case\"[object RegExp]\":case\"[object String]\":return t==e+\"\";case\"[object Map]\":var l=d;case\"[object Set]\":var c=1&r;if(l||(l=f),t.size!=e.size&&!c)return!1;var p=o.get(t);if(p)return p==e;r|=2,o.set(t,e);var m=a(l(t),l(e),r,i,s,o);return o.delete(t),m;case\"[object Symbol]\":if(g)return g.call(t)==g.call(e)}return!1}(t,e,p,n,i,s,o);if(!(1&n)){var O=_&&q.call(t,\"__wrapped__\"),T=L&&q.call(e,\"__wrapped__\");if(O||T){var j=O?t.value():t,C=T?e.value():e;return o||(o=new r.A),s(j,C,n,i,o)}}return!!S&&(o||(o=new r.A),function(t,e,n,r,i,s){var o=1&n,l=(0,m.A)(t),a=l.length;if(a!=(0,m.A)(e).length&&!o)return!1;for(var c=a;c--;){var u=l[c];if(!(o?u in e:b.call(e,u)))return!1}var h=s.get(t),d=s.get(e);if(h&&d)return h==e&&d==t;var f=!0;s.set(t,e),s.set(e,t);for(var p=o;++c<a;){var g=t[u=l[c]],y=e[u];if(r)var v=o?r(y,g,u,e,t,s):r(g,y,u,t,e,s);if(!(void 0===v?g===y||i(g,y,n,r,s):v)){f=!1;break}p||(p=\"constructor\"==u)}if(f&&!p){var A=t.constructor,x=e.constructor;A==x||!(\"constructor\"in t)||!(\"constructor\"in e)||\"function\"==typeof A&&A instanceof A&&\"function\"==typeof x&&x instanceof x||(f=!1)}return s.delete(t),s.delete(e),f}(t,e,n,i,s,o))},_=n(7948),L=function t(e,n,r,i,s){return e===n||(null==e||null==n||!(0,_.A)(e)&&!(0,_.A)(n)?e!=e&&n!=n:k(e,n,r,i,t,s))},S=function(t,e){return L(t,e)}},7572:function(t,e,n){\"use strict\";var r=n(2159),i=n(659);e.A=function(t){if(!(0,i.A)(t))return!1;var e=(0,r.A)(t);return\"[object Function]\"==e||\"[object GeneratorFunction]\"==e||\"[object AsyncFunction]\"==e||\"[object Proxy]\"==e}},1628:function(t,e){\"use strict\";e.A=function(t){return\"number\"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},659:function(t,e){\"use strict\";e.A=function(t){var e=typeof t;return null!=t&&(\"object\"==e||\"function\"==e)}},7948:function(t,e){\"use strict\";e.A=function(t){return null!=t&&\"object\"==typeof t}},5755:function(t,e,n){\"use strict\";n.d(e,{A:function(){return u}});var r=n(2159),i=n(1628),s=n(7948),o={};o[\"[object Float32Array]\"]=o[\"[object Float64Array]\"]=o[\"[object Int8Array]\"]=o[\"[object Int16Array]\"]=o[\"[object Int32Array]\"]=o[\"[object Uint8Array]\"]=o[\"[object Uint8ClampedArray]\"]=o[\"[object Uint16Array]\"]=o[\"[object Uint32Array]\"]=!0,o[\"[object Arguments]\"]=o[\"[object Array]\"]=o[\"[object ArrayBuffer]\"]=o[\"[object Boolean]\"]=o[\"[object DataView]\"]=o[\"[object Date]\"]=o[\"[object Error]\"]=o[\"[object Function]\"]=o[\"[object Map]\"]=o[\"[object Number]\"]=o[\"[object Object]\"]=o[\"[object RegExp]\"]=o[\"[object Set]\"]=o[\"[object String]\"]=o[\"[object WeakMap]\"]=!1;var l=n(5771),a=n(8795),c=a.A&&a.A.isTypedArray,u=c?(0,l.A)(c):function(t){return(0,s.A)(t)&&(0,i.A)(t.length)&&!!o[(0,r.A)(t)]}},3169:function(t,e,n){\"use strict\";n.d(e,{A:function(){return a}});var r=n(6753),i=n(501),s=(0,n(2217).A)(Object.keys,Object),o=Object.prototype.hasOwnProperty,l=n(3628),a=function(t){return(0,l.A)(t)?(0,r.A)(t):function(t){if(!(0,i.A)(t))return s(t);var e=[];for(var n in Object(t))o.call(t,n)&&\"constructor\"!=n&&e.push(n);return e}(t)}},2624:function(t,e,n){\"use strict\";n.d(e,{A:function(){return c}});var r=n(6753),i=n(659),s=n(501),o=Object.prototype.hasOwnProperty,l=function(t){if(!(0,i.A)(t))return function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}(t);var e=(0,s.A)(t),n=[];for(var r in t)(\"constructor\"!=r||!e&&o.call(t,r))&&n.push(r);return n},a=n(3628),c=function(t){return(0,a.A)(t)?(0,r.A)(t,!0):l(t)}},8347:function(t,e,n){\"use strict\";n.d(e,{A:function(){return $}});var r,i,s,o,l=n(2673),a=n(6770),c=n(8138),u=function(t,e,n){(void 0!==n&&!(0,c.A)(t[e],n)||void 0===n&&!(e in t))&&(0,a.A)(t,e,n)},h=function(t,e,n){for(var r=-1,i=Object(t),s=n(t),o=s.length;o--;){var l=s[++r];if(!1===e(i[l],l,i))break}return t},d=n(3812),f=n(1827),p=n(4405),g=n(1683),m=n(8412),b=n(723),y=n(3628),v=n(7948),A=n(776),x=n(7572),N=n(659),E=n(2159),w=n(8769),q=Function.prototype,k=Object.prototype,_=q.toString,L=k.hasOwnProperty,S=_.call(Object),O=n(5755),T=function(t,e){if((\"constructor\"!==e||\"function\"!=typeof t[e])&&\"__proto__\"!=e)return t[e]},j=n(9601),C=n(2624),R=function(t,e,n,r,i,s,o){var l,a=T(t,n),c=T(e,n),h=o.get(c);if(h)u(t,n,h);else{var q=s?s(a,c,n+\"\",t,e,o):void 0,k=void 0===q;if(k){var R=(0,b.A)(c),I=!R&&(0,A.A)(c),B=!R&&!I&&(0,O.A)(c);q=c,R||I||B?(0,b.A)(a)?q=a:(l=a,(0,v.A)(l)&&(0,y.A)(l)?q=(0,p.A)(a):I?(k=!1,q=(0,d.A)(c,!0)):B?(k=!1,q=(0,f.A)(c,!0)):q=[]):function(t){if(!(0,v.A)(t)||\"[object Object]\"!=(0,E.A)(t))return!1;var e=(0,w.A)(t);if(null===e)return!0;var n=L.call(e,\"constructor\")&&e.constructor;return\"function\"==typeof n&&n instanceof n&&_.call(n)==S}(c)||(0,m.A)(c)?(q=a,(0,m.A)(a)?q=function(t){return(0,j.A)(t,(0,C.A)(t))}(a):(0,N.A)(a)&&!(0,x.A)(a)||(q=(0,g.A)(c))):k=!1}k&&(o.set(c,q),i(q,c,r,s,o),o.delete(c)),u(t,n,q)}},I=function t(e,n,r,i,s){e!==n&&h(n,(function(o,a){if(s||(s=new l.A),(0,N.A)(o))R(e,n,a,r,t,i,s);else{var c=i?i(T(e,a),o,a+\"\",e,n,s):void 0;void 0===c&&(c=o),u(e,a,c)}}),C.A)},B=function(t){return t},M=Math.max,U=n(7889),D=U.A?function(t,e){return(0,U.A)(t,\"toString\",{configurable:!0,enumerable:!1,value:(n=e,function(){return n}),writable:!0});var n}:B,P=Date.now,z=(r=D,i=0,s=0,function(){var t=P(),e=16-(t-s);if(s=t,e>0){if(++i>=800)return arguments[0]}else i=0;return r.apply(void 0,arguments)}),F=function(t,e){return z(function(t,e,n){return e=M(void 0===e?t.length-1:e,0),function(){for(var r=arguments,i=-1,s=M(r.length-e,0),o=Array(s);++i<s;)o[i]=r[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=r[i];return l[e]=n(o),function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}(t,this,l)}}(t,e,B),t+\"\")},H=n(3767),$=(o=function(t,e,n){I(t,e,n)},F((function(t,e){var n=-1,r=e.length,i=r>1?e[r-1]:void 0,s=r>2?e[2]:void 0;for(i=o.length>3&&\"function\"==typeof i?(r--,i):void 0,s&&function(t,e,n){if(!(0,N.A)(n))return!1;var r=typeof e;return!!(\"number\"==r?(0,y.A)(n)&&(0,H.A)(e,n.length):\"string\"==r&&e in n)&&(0,c.A)(n[e],t)}(e[0],e[1],s)&&(i=r<3?void 0:i,r=1),t=Object(t);++n<r;){var l=e[n];l&&o(t,l,n)}return t})))},6935:function(t,e){\"use strict\";e.A=function(){return[]}},6003:function(t,e,n){\"use strict\";n.r(e),n.d(e,{Attributor:function(){return i},AttributorStore:function(){return d},BlockBlot:function(){return w},ClassAttributor:function(){return c},ContainerBlot:function(){return k},EmbedBlot:function(){return _},InlineBlot:function(){return N},LeafBlot:function(){return m},ParentBlot:function(){return A},Registry:function(){return l},Scope:function(){return r},ScrollBlot:function(){return O},StyleAttributor:function(){return h},TextBlot:function(){return j}});var r=(t=>(t[t.TYPE=3]=\"TYPE\",t[t.LEVEL=12]=\"LEVEL\",t[t.ATTRIBUTE=13]=\"ATTRIBUTE\",t[t.BLOT=14]=\"BLOT\",t[t.INLINE=7]=\"INLINE\",t[t.BLOCK=11]=\"BLOCK\",t[t.BLOCK_BLOT=10]=\"BLOCK_BLOT\",t[t.INLINE_BLOT=6]=\"INLINE_BLOT\",t[t.BLOCK_ATTRIBUTE=9]=\"BLOCK_ATTRIBUTE\",t[t.INLINE_ATTRIBUTE=5]=\"INLINE_ATTRIBUTE\",t[t.ANY=15]=\"ANY\",t))(r||{});class i{constructor(t,e,n={}){this.attrName=t,this.keyName=e;const i=r.TYPE&r.ATTRIBUTE;this.scope=null!=n.scope?n.scope&r.LEVEL|i:r.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}static keys(t){return Array.from(t.attributes).map((t=>t.name))}add(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)}canAdd(t,e){return null==this.whitelist||(\"string\"==typeof e?this.whitelist.indexOf(e.replace(/[\"']/g,\"\"))>-1:this.whitelist.indexOf(e)>-1)}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:\"\"}}class s extends Error{constructor(t){super(t=\"[Parchment] \"+t),this.message=t,this.name=this.constructor.name}}const o=class t{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let n=null;try{n=t.parentNode}catch{return null}return this.find(n,e)}return null}create(e,n,r){const i=this.query(n);if(null==i)throw new s(`Unable to create ${n} blot`);const o=i,l=n instanceof Node||n.nodeType===Node.TEXT_NODE?n:o.create(r),a=new o(e,l,r);return t.blots.set(a.domNode,a),a}find(e,n=!1){return t.find(e,n)}query(t,e=r.ANY){let n;return\"string\"==typeof t?n=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?n=this.types.text:\"number\"==typeof t?t&r.LEVEL&r.BLOCK?n=this.types.block:t&r.LEVEL&r.INLINE&&(n=this.types.inline):t instanceof Element&&((t.getAttribute(\"class\")||\"\").split(/\\s+/).some((t=>(n=this.classes[t],!!n))),n=n||this.tags[t.tagName]),null==n?null:\"scope\"in n&&e&r.LEVEL&n.scope&&e&r.TYPE&n.scope?n:null}register(...t){return t.map((t=>{const e=\"blotName\"in t,n=\"attrName\"in t;if(!e&&!n)throw new s(\"Invalid definition\");if(e&&\"abstract\"===t.blotName)throw new s(\"Cannot register abstract class\");const r=e?t.blotName:n?t.attrName:void 0;return this.types[r]=t,n?\"string\"==typeof t.keyName&&(this.attributes[t.keyName]=t):e&&(t.className&&(this.classes[t.className]=t),t.tagName&&(Array.isArray(t.tagName)?t.tagName=t.tagName.map((t=>t.toUpperCase())):t.tagName=t.tagName.toUpperCase(),(Array.isArray(t.tagName)?t.tagName:[t.tagName]).forEach((e=>{(null==this.tags[e]||null==t.className)&&(this.tags[e]=t)})))),t}))}};o.blots=new WeakMap;let l=o;function a(t,e){return(t.getAttribute(\"class\")||\"\").split(/\\s+/).filter((t=>0===t.indexOf(`${e}-`)))}const c=class extends i{static keys(t){return(t.getAttribute(\"class\")||\"\").split(/\\s+/).map((t=>t.split(\"-\").slice(0,-1).join(\"-\")))}add(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0)}remove(t){a(t,this.keyName).forEach((e=>{t.classList.remove(e)})),0===t.classList.length&&t.removeAttribute(\"class\")}value(t){const e=(a(t,this.keyName)[0]||\"\").slice(this.keyName.length+1);return this.canAdd(t,e)?e:\"\"}};function u(t){const e=t.split(\"-\"),n=e.slice(1).map((t=>t[0].toUpperCase()+t.slice(1))).join(\"\");return e[0]+n}const h=class extends i{static keys(t){return(t.getAttribute(\"style\")||\"\").split(\";\").map((t=>t.split(\":\")[0].trim()))}add(t,e){return!!this.canAdd(t,e)&&(t.style[u(this.keyName)]=e,!0)}remove(t){t.style[u(this.keyName)]=\"\",t.getAttribute(\"style\")||t.removeAttribute(\"style\")}value(t){const e=t.style[u(this.keyName)];return this.canAdd(t,e)?e:\"\"}},d=class{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=l.find(this.domNode);if(null==t)return;const e=i.keys(this.domNode),n=c.keys(this.domNode),s=h.keys(this.domNode);e.concat(n).concat(s).forEach((e=>{const n=t.scroll.query(e,r.ATTRIBUTE);n instanceof i&&(this.attributes[n.attrName]=n)}))}copy(t){Object.keys(this.attributes).forEach((e=>{const n=this.attributes[e].value(this.domNode);t.format(e,n)}))}move(t){this.copy(t),Object.keys(this.attributes).forEach((t=>{this.attributes[t].remove(this.domNode)})),this.attributes={}}values(){return Object.keys(this.attributes).reduce(((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t)),{})}},f=class{constructor(t,e){this.scroll=t,this.domNode=e,l.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(null==this.tagName)throw new s(\"Blot definition missing tagName\");let e,n;return Array.isArray(this.tagName)?(\"string\"==typeof t?(n=t.toUpperCase(),parseInt(n,10).toString()===n&&(n=parseInt(n,10))):\"number\"==typeof t&&(n=t),e=\"number\"==typeof n?document.createElement(this.tagName[n-1]):n&&this.tagName.indexOf(n)>-1?document.createElement(n):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){null!=this.parent&&this.parent.removeChild(this),l.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,n,i){const s=this.isolate(t,e);if(null!=this.scroll.query(n,r.BLOT)&&i)s.wrap(n,i);else if(null!=this.scroll.query(n,r.ATTRIBUTE)){const t=this.scroll.create(this.statics.scope);s.wrap(t),t.format(n,i)}}insertAt(t,e,n){const r=null==n?this.scroll.create(\"text\",e):this.scroll.create(e,n),i=this.split(t);this.parent.insertBefore(r,i||void 0)}isolate(t,e){const n=this.split(t);if(null==n)throw new Error(\"Attempt to isolate at end\");return n.split(e),n}length(){return 1}offset(t=this.parent){return null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const n=\"string\"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(n,this.next||void 0),this.remove()),n}split(t,e){return 0===t?this:this.next}update(t,e){}wrap(t,e){const n=\"string\"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(n,this.next||void 0),\"function\"!=typeof n.appendChild)throw new s(`Cannot wrap ${t}`);return n.appendChild(this),n}};f.blotName=\"abstract\";let p=f;const g=class extends p{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let n=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(n+=1),[this.parent.domNode,n]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};g.scope=r.INLINE_BLOT;const m=g;class b{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let n=e();for(;n&&t>0;)t-=1,n=e();return n}contains(t){const e=this.iterator();let n=e();for(;n;){if(n===t)return!0;n=e()}return!1}indexOf(t){const e=this.iterator();let n=e(),r=0;for(;n;){if(n===t)return r;r+=1,n=e()}return-1}insertBefore(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,n=this.head;for(;null!=n;){if(n===t)return e;e+=n.length(),n=n.next}return-1}remove(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return null!=t&&(t=t.next),e}}find(t,e=!1){const n=this.iterator();let r=n();for(;r;){const i=r.length();if(t<i||e&&t===i&&(null==r.next||0!==r.next.length()))return[r,t];t-=i,r=n()}return[null,0]}forEach(t){const e=this.iterator();let n=e();for(;n;)t(n),n=e()}forEachAt(t,e,n){if(e<=0)return;const[r,i]=this.find(t);let s=t-i;const o=this.iterator(r);let l=o();for(;l&&s<t+e;){const r=l.length();t>s?n(l,t-s,Math.min(e,s+r-t)):n(l,0,Math.min(r,t+e-s)),s+=r,l=o()}}map(t){return this.reduce(((e,n)=>(e.push(t(n)),e)),[])}reduce(t,e){const n=this.iterator();let r=n();for(;r;)e=t(e,r),r=n();return e}}function y(t,e){const n=e.find(t);if(n)return n;try{return e.create(t)}catch{const n=e.create(r.INLINE);return Array.from(t.childNodes).forEach((t=>{n.domNode.appendChild(t)})),t.parentNode&&t.parentNode.replaceChild(n.domNode,t),n.attach(),n}}const v=class t extends p{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach((t=>{t.attach()}))}attachUI(e){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=e,t.uiClass&&this.uiNode.classList.add(t.uiClass),this.uiNode.setAttribute(\"contenteditable\",\"false\"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new b,Array.from(this.domNode.childNodes).filter((t=>t!==this.uiNode)).reverse().forEach((t=>{try{const e=y(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(t){if(t instanceof s)return;throw t}}))}deleteAt(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,((t,e,n)=>{t.deleteAt(e,n)}))}descendant(e,n=0){const[r,i]=this.children.find(n);return null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e?[r,i]:r instanceof t?r.descendant(e,i):[null,-1]}descendants(e,n=0,r=Number.MAX_VALUE){let i=[],s=r;return this.children.forEachAt(n,r,((n,r,o)=>{(null==e.blotName&&e(n)||null!=e.blotName&&n instanceof e)&&i.push(n),n instanceof t&&(i=i.concat(n.descendants(e,r,s))),s-=o})),i}detach(){this.children.forEach((t=>{t.detach()})),super.detach()}enforceAllowedChildren(){let e=!1;this.children.forEach((n=>{e||this.statics.allowedChildren.some((t=>n instanceof t))||(n.statics.scope===r.BLOCK_BLOT?(null!=n.next&&this.splitAfter(n),null!=n.prev&&this.splitAfter(n.prev),n.parent.unwrap(),e=!0):n instanceof t?n.unwrap():n.remove())}))}formatAt(t,e,n,r){this.children.forEachAt(t,e,((t,e,i)=>{t.formatAt(e,i,n,r)}))}insertAt(t,e,n){const[r,i]=this.children.find(t);if(r)r.insertAt(i,e,n);else{const t=null==n?this.scroll.create(\"text\",e):this.scroll.create(e,n);this.appendChild(t)}}insertBefore(t,e){null!=t.parent&&t.parent.children.remove(t);let n=null;this.children.insertBefore(t,e||null),t.parent=this,null!=e&&(n=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==n)&&this.domNode.insertBefore(t.domNode,n),t.attach()}length(){return this.children.reduce(((t,e)=>t+e.length()),0)}moveChildren(t,e){this.children.forEach((n=>{t.insertBefore(n,e)}))}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){const t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t)}else this.remove()}path(e,n=!1){const[r,i]=this.children.find(e,n),s=[[this,e]];return r instanceof t?s.concat(r.path(i,n)):(null!=r&&s.push([r,i]),s)}removeChild(t){this.children.remove(t)}replaceWith(e,n){const r=\"string\"==typeof e?this.scroll.create(e,n):e;return r instanceof t&&this.moveChildren(r),super.replaceWith(r)}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}const n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(t,this.length(),((t,r,i)=>{const s=t.split(r,e);null!=s&&n.appendChild(s)})),n}splitAfter(t){const e=this.clone();for(;null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const n=[],r=[];t.forEach((t=>{t.target===this.domNode&&\"childList\"===t.type&&(n.push(...t.addedNodes),r.push(...t.removedNodes))})),r.forEach((t=>{if(null!=t.parentNode&&\"IFRAME\"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const e=this.scroll.find(t);null!=e&&(null==e.domNode.parentNode||e.domNode.parentNode===this.domNode)&&e.detach()})),n.filter((t=>t.parentNode===this.domNode&&t!==this.uiNode)).sort(((t,e)=>t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1)).forEach((t=>{let e=null;null!=t.nextSibling&&(e=this.scroll.find(t.nextSibling));const n=y(t,this.scroll);(n.next!==e||null==n.next)&&(null!=n.parent&&n.parent.removeChild(this),this.insertBefore(n,e||void 0))})),this.enforceAllowedChildren()}};v.uiClass=\"\";const A=v,x=class t extends A{static create(t){return super.create(t)}static formats(e,n){const r=n.query(t.blotName);if(null==r||e.tagName!==r.tagName){if(\"string\"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new d(this.domNode)}format(e,n){if(e!==this.statics.blotName||n){const t=this.scroll.query(e,r.INLINE);if(null==t)return;t instanceof i?this.attributes.attribute(t,n):n&&(e!==this.statics.blotName||this.formats()[e]!==n)&&this.replaceWith(e,n)}else this.children.forEach((e=>{e instanceof t||(e=e.wrap(t.blotName,!0)),this.attributes.copy(e)})),this.unwrap()}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,i){null!=this.formats()[n]||this.scroll.query(n,r.ATTRIBUTE)?this.isolate(t,e).format(n,i):super.formatAt(t,e,n,i)}optimize(e){super.optimize(e);const n=this.formats();if(0===Object.keys(n).length)return this.unwrap();const r=this.next;r instanceof t&&r.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t)if(t[n]!==e[n])return!1;return!0}(n,r.formats())&&(r.moveChildren(this),r.remove())}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some((t=>t.target===this.domNode&&\"attributes\"===t.type))&&this.attributes.build()}wrap(e,n){const r=super.wrap(e,n);return r instanceof t&&this.attributes.move(r),r}};x.allowedChildren=[x,m],x.blotName=\"inline\",x.scope=r.INLINE_BLOT,x.tagName=\"SPAN\";const N=x,E=class t extends A{static create(t){return super.create(t)}static formats(e,n){const r=n.query(t.blotName);if(null==r||e.tagName!==r.tagName){if(\"string\"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new d(this.domNode)}format(e,n){const s=this.scroll.query(e,r.BLOCK);null!=s&&(s instanceof i?this.attributes.attribute(s,n):e!==this.statics.blotName||n?n&&(e!==this.statics.blotName||this.formats()[e]!==n)&&this.replaceWith(e,n):this.replaceWith(t.blotName))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,i){null!=this.scroll.query(n,r.BLOCK)?this.format(n,i):super.formatAt(t,e,n,i)}insertAt(t,e,n){if(null==n||null!=this.scroll.query(e,r.INLINE))super.insertAt(t,e,n);else{const r=this.split(t);if(null==r)throw new Error(\"Attempt to insertAt after block boundaries\");{const t=this.scroll.create(e,n);r.parent.insertBefore(t,r)}}}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some((t=>t.target===this.domNode&&\"attributes\"===t.type))&&this.attributes.build()}};E.blotName=\"block\",E.scope=r.BLOCK_BLOT,E.tagName=\"P\",E.allowedChildren=[N,E,m];const w=E,q=class extends A{checkMerge(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,n,r){super.formatAt(t,e,n,r),this.enforceAllowedChildren()}insertAt(t,e,n){super.insertAt(t,e,n),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};q.blotName=\"container\",q.scope=r.BLOCK_BLOT;const k=q,_=class extends m{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,n,r){0===t&&e===this.length()?this.format(n,r):super.formatAt(t,e,n,r)}formats(){return this.statics.formats(this.domNode,this.scroll)}},L={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},S=class extends A{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver((t=>{this.update(t)})),this.observer.observe(this.domNode,L),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const n=this.registry.find(t,e);return n?n.scroll===this?n:e?this.find(n.scroll.domNode.parentNode,!0):null:null}query(t,e=r.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){null!=this.scroll&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),0===t&&e===this.length()?this.children.forEach((t=>{t.remove()})):super.deleteAt(t,e)}formatAt(t,e,n,r){this.update(),super.formatAt(t,e,n,r)}insertAt(t,e,n){this.update(),super.insertAt(t,e,n)}optimize(t=[],e={}){super.optimize(e);const n=e.mutationsMap||new WeakMap;let r=Array.from(this.observer.takeRecords());for(;r.length>0;)t.push(r.pop());const i=(t,e=!0)=>{null==t||t===this||null!=t.domNode.parentNode&&(n.has(t.domNode)||n.set(t.domNode,[]),e&&i(t.parent))},s=t=>{n.has(t.domNode)&&(t instanceof A&&t.children.forEach(s),n.delete(t.domNode),t.optimize(e))};let o=t;for(let e=0;o.length>0;e+=1){if(e>=100)throw new Error(\"[Parchment] Maximum optimize iterations reached\");for(o.forEach((t=>{const e=this.find(t.target,!0);null!=e&&(e.domNode===t.target&&(\"childList\"===t.type?(i(this.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach((t=>{const e=this.find(t,!1);i(e,!1),e instanceof A&&e.children.forEach((t=>{i(t,!1)}))}))):\"attributes\"===t.type&&i(e.prev)),i(e))})),this.children.forEach(s),o=Array.from(this.observer.takeRecords()),r=o.slice();r.length>0;)t.push(r.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const n=new WeakMap;t.map((t=>{const e=this.find(t.target,!0);return null==e?null:n.has(e.domNode)?(n.get(e.domNode).push(t),null):(n.set(e.domNode,[t]),e)})).forEach((t=>{null!=t&&t!==this&&n.has(t.domNode)&&t.update(n.get(t.domNode)||[],e)})),e.mutationsMap=n,n.has(this.domNode)&&super.update(n.get(this.domNode),e),this.optimize(t,e)}};S.blotName=\"scroll\",S.defaultChild=w,S.allowedChildren=[w,k],S.scope=r.BLOCK_BLOT,S.tagName=\"DIV\";const O=S,T=class t extends m{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,n){null==n?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,n)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof t&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}const n=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next||void 0),this.text=this.statics.value(this.domNode),n}update(t,e){t.some((t=>\"characterData\"===t.type&&t.target===this.domNode))&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};T.blotName=\"text\",T.scope=r.INLINE_BLOT;const j=T}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var s=e[r]={id:r,loaded:!1,exports:{}};return t[r](s,s.exports,n),s.loaded=!0,s.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(t){if(\"object\"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})},n.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t};var r={};return function(){\"use strict\";n.d(r,{default:function(){return It}});var t=n(3729),e=n(8276),i=n(7912),s=n(6003);class o extends s.ClassAttributor{add(t,e){let n=0;if(\"+1\"===e||\"-1\"===e){const r=this.value(t)||0;n=\"+1\"===e?r+1:r-1}else\"number\"==typeof e&&(n=e);return 0===n?(this.remove(t),!0):super.add(t,n.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}var l=new o(\"indent\",\"ql-indent\",{scope:s.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]}),a=n(9698);class c extends a.Ay{static blotName=\"blockquote\";static tagName=\"blockquote\"}var u=c;class h extends a.Ay{static blotName=\"header\";static tagName=[\"H1\",\"H2\",\"H3\",\"H4\",\"H5\",\"H6\"];static formats(t){return this.tagName.indexOf(t.tagName)+1}}var d=h,f=n(580),p=n(6142);class g extends f.A{}g.blotName=\"list-container\",g.tagName=\"OL\";class m extends a.Ay{static create(t){const e=super.create();return e.setAttribute(\"data-list\",t),e}static formats(t){return t.getAttribute(\"data-list\")||void 0}static register(){p.Ay.register(g)}constructor(t,e){super(t,e);const n=e.ownerDocument.createElement(\"span\"),r=n=>{if(!t.isEnabled())return;const r=this.statics.formats(e,t);\"checked\"===r?(this.format(\"list\",\"unchecked\"),n.preventDefault()):\"unchecked\"===r&&(this.format(\"list\",\"checked\"),n.preventDefault())};n.addEventListener(\"mousedown\",r),n.addEventListener(\"touchstart\",r),this.attachUI(n)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute(\"data-list\",e):super.format(t,e)}}m.blotName=\"list\",m.tagName=\"LI\",g.allowedChildren=[m],m.requiredContainer=g;var b=n(9541),y=n(8638),v=n(6772),A=n(664),x=n(4850);class N extends x.A{static blotName=\"bold\";static tagName=[\"STRONG\",\"B\"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}var E=N;class w extends x.A{static blotName=\"link\";static tagName=\"A\";static SANITIZED_URL=\"about:blank\";static PROTOCOL_WHITELIST=[\"http\",\"https\",\"mailto\",\"tel\",\"sms\"];static create(t){const e=super.create(t);return e.setAttribute(\"href\",this.sanitize(t)),e.setAttribute(\"rel\",\"noopener noreferrer\"),e.setAttribute(\"target\",\"_blank\"),e}static formats(t){return t.getAttribute(\"href\")}static sanitize(t){return q(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute(\"href\",this.constructor.sanitize(e)):super.format(t,e)}}function q(t,e){const n=document.createElement(\"a\");n.href=t;const r=n.href.slice(0,n.href.indexOf(\":\"));return e.indexOf(r)>-1}class k extends x.A{static blotName=\"script\";static tagName=[\"SUB\",\"SUP\"];static create(t){return\"super\"===t?document.createElement(\"sup\"):\"sub\"===t?document.createElement(\"sub\"):super.create(t)}static formats(t){return\"SUB\"===t.tagName?\"sub\":\"SUP\"===t.tagName?\"super\":void 0}}var _=k;class L extends x.A{static blotName=\"underline\";static tagName=\"U\"}var S=L,O=n(746);class T extends O.A{static blotName=\"formula\";static className=\"ql-formula\";static tagName=\"SPAN\";static create(t){if(null==window.katex)throw new Error(\"Formula module requires KaTeX.\");const e=super.create(t);return\"string\"==typeof t&&(window.katex.render(t,e,{throwOnError:!1,errorColor:\"#f00\"}),e.setAttribute(\"data-value\",t)),e}static value(t){return t.getAttribute(\"data-value\")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}var j=T;const C=[\"alt\",\"height\",\"width\"];class R extends s.EmbedBlot{static blotName=\"image\";static tagName=\"IMG\";static create(t){const e=super.create(t);return\"string\"==typeof t&&e.setAttribute(\"src\",this.sanitize(t)),e}static formats(t){return C.reduce(((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e)),{})}static match(t){return/\\.(jpe?g|gif|png)$/.test(t)||/^data:image\\/.+;base64/.test(t)}static sanitize(t){return q(t,[\"http\",\"https\",\"data\"])?t:\"//:0\"}static value(t){return t.getAttribute(\"src\")}format(t,e){C.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}var I=R;const B=[\"height\",\"width\"];class M extends a.zo{static blotName=\"video\";static className=\"ql-video\";static tagName=\"IFRAME\";static create(t){const e=super.create(t);return e.setAttribute(\"frameborder\",\"0\"),e.setAttribute(\"allowfullscreen\",\"true\"),e.setAttribute(\"src\",this.sanitize(t)),e}static formats(t){return B.reduce(((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e)),{})}static sanitize(t){return w.sanitize(t)}static value(t){return t.getAttribute(\"src\")}format(t,e){B.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href=\"${t}\">${t}</a>`}}var U=M,D=n(9404),P=n(5232),z=n.n(P),F=n(4266),H=n(3036),$=n(4541),V=n(5508),K=n(584);const W=new s.ClassAttributor(\"code-token\",\"hljs\",{scope:s.Scope.INLINE});class Z extends x.A{static formats(t,e){for(;null!=t&&t!==e.domNode;){if(t.classList&&t.classList.contains(D.Ay.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,n){super(t,e,n),W.add(this.domNode,n)}format(t,e){t!==Z.blotName?super.format(t,e):e?W.add(this.domNode,e):(W.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),W.value(this.domNode)||this.unwrap()}}Z.blotName=\"code-token\",Z.className=\"ql-token\";class G extends D.Ay{static create(t){const e=super.create(t);return\"string\"==typeof t&&e.setAttribute(\"data-language\",t),e}static formats(t){return t.getAttribute(\"data-language\")||\"plain\"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute(\"data-language\",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),Z.blotName,!1),super.replaceWith(t,e)}}class X extends D.EJ{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===G.blotName&&(this.forceNext=!0,this.children.forEach((n=>{n.format(t,e)})))}formatAt(t,e,n,r){n===G.blotName&&(this.forceNext=!0),super.formatAt(t,e,n,r)}highlight(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==this.children.head)return;const n=`${Array.from(this.domNode.childNodes).filter((t=>t!==this.uiNode)).map((t=>t.textContent)).join(\"\\n\")}\\n`,r=G.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==n){if(n.trim().length>0||null==this.cachedText){const e=this.children.reduce(((t,e)=>t.concat((0,a.mG)(e,!1))),new(z())),i=t(n,r);e.diff(i).reduce(((t,e)=>{let{retain:n,attributes:r}=e;return n?(r&&Object.keys(r).forEach((e=>{[G.blotName,Z.blotName].includes(e)&&this.formatAt(t,n,e,r[e])})),t+n):t}),0)}this.cachedText=n,this.forceNext=!1}}html(t,e){const[n]=this.children.find(t);return`<pre data-language=\"${n?G.formats(n.domNode):\"plain\"}\">\\n${(0,V.X)(this.code(t,e))}\\n</pre>`}optimize(t){if(super.optimize(t),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){const t=G.formats(this.children.head.domNode);t!==this.uiNode.value&&(this.uiNode.value=t)}}}X.allowedChildren=[G],G.requiredContainer=X,G.allowedChildren=[Z,$.A,V.A,H.A];class Q extends F.A{static register(){p.Ay.register(Z,!0),p.Ay.register(G,!0),p.Ay.register(X,!0)}constructor(t,e){if(super(t,e),null==this.options.hljs)throw new Error(\"Syntax module requires highlight.js. Please include the library on the page before Quill.\");this.languages=this.options.languages.reduce(((t,e)=>{let{key:n}=e;return t[n]=!0,t}),{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(p.Ay.events.SCROLL_BLOT_MOUNT,(t=>{if(!(t instanceof X))return;const e=this.quill.root.ownerDocument.createElement(\"select\");this.options.languages.forEach((t=>{let{key:n,label:r}=t;const i=e.ownerDocument.createElement(\"option\");i.textContent=r,i.setAttribute(\"value\",n),e.appendChild(i)})),e.addEventListener(\"change\",(()=>{t.format(G.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)})),null==t.uiNode&&(t.attachUI(e),t.children.head&&(e.value=G.formats(t.children.head.domNode)))}))}initTimer(){let t=null;this.quill.on(p.Ay.events.SCROLL_OPTIMIZE,(()=>{t&&clearTimeout(t),t=setTimeout((()=>{this.highlight(),t=null}),this.options.interval)}))}highlight(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.quill.selection.composing)return;this.quill.update(p.Ay.sources.USER);const n=this.quill.getSelection();(null==t?this.quill.scroll.descendants(X):[t]).forEach((t=>{t.highlight(this.highlightBlot,e)})),this.quill.update(p.Ay.sources.SILENT),null!=n&&this.quill.setSelection(n,p.Ay.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"plain\";if(e=this.languages[e]?e:\"plain\",\"plain\"===e)return(0,V.X)(t).split(\"\\n\").reduce(((t,n,r)=>(0!==r&&t.insert(\"\\n\",{[D.Ay.blotName]:e}),t.insert(n))),new(z()));const n=this.quill.root.ownerDocument.createElement(\"div\");return n.classList.add(D.Ay.className),n.innerHTML=((t,e,n)=>{if(\"string\"==typeof t.versionString){const r=t.versionString.split(\".\")[0];if(parseInt(r,10)>=11)return t.highlight(n,{language:e}).value}return t.highlight(e,n).value})(this.options.hljs,e,t),(0,K.hV)(this.quill.scroll,n,[(t,e)=>{const n=W.value(t);return n?e.compose((new(z())).retain(e.length(),{[Z.blotName]:n})):e}],[(t,n)=>t.data.split(\"\\n\").reduce(((t,n,r)=>(0!==r&&t.insert(\"\\n\",{[D.Ay.blotName]:e}),t.insert(n))),n)],new WeakMap)}}Q.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:\"plain\",label:\"Plain\"},{key:\"bash\",label:\"Bash\"},{key:\"cpp\",label:\"C++\"},{key:\"cs\",label:\"C#\"},{key:\"css\",label:\"CSS\"},{key:\"diff\",label:\"Diff\"},{key:\"xml\",label:\"HTML/XML\"},{key:\"java\",label:\"Java\"},{key:\"javascript\",label:\"JavaScript\"},{key:\"markdown\",label:\"Markdown\"},{key:\"php\",label:\"PHP\"},{key:\"python\",label:\"Python\"},{key:\"ruby\",label:\"Ruby\"},{key:\"sql\",label:\"SQL\"}]};class J extends a.Ay{static blotName=\"table\";static tagName=\"TD\";static create(t){const e=super.create();return t?e.setAttribute(\"data-row\",t):e.setAttribute(\"data-row\",nt()),e}static formats(t){if(t.hasAttribute(\"data-row\"))return t.getAttribute(\"data-row\")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===J.blotName&&e?this.domNode.setAttribute(\"data-row\",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}class Y extends f.A{static blotName=\"table-row\";static tagName=\"TR\";checkMerge(){if(super.checkMerge()&&null!=this.next.children.head){const t=this.children.head.formats(),e=this.children.tail.formats(),n=this.next.children.head.formats(),r=this.next.children.tail.formats();return t.table===e.table&&t.table===n.table&&t.table===r.table}return!1}optimize(t){super.optimize(t),this.children.forEach((t=>{if(null==t.next)return;const e=t.formats(),n=t.next.formats();if(e.table!==n.table){const e=this.splitAfter(t);e&&e.optimize(),this.prev&&this.prev.optimize()}}))}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}class tt extends f.A{static blotName=\"table-body\";static tagName=\"TBODY\"}class et extends f.A{static blotName=\"table-container\";static tagName=\"TABLE\";balanceCells(){const t=this.descendants(Y),e=t.reduce(((t,e)=>Math.max(e.children.length,t)),0);t.forEach((t=>{new Array(e-t.children.length).fill(0).forEach((()=>{let e;null!=t.children.head&&(e=J.formats(t.children.head.domNode));const n=this.scroll.create(J.blotName,e);t.appendChild(n),n.optimize()}))}))}cells(t){return this.rows().map((e=>e.children.at(t)))}deleteColumn(t){const[e]=this.descendant(tt);null!=e&&null!=e.children.head&&e.children.forEach((e=>{const n=e.children.at(t);null!=n&&n.remove()}))}insertColumn(t){const[e]=this.descendant(tt);null!=e&&null!=e.children.head&&e.children.forEach((e=>{const n=e.children.at(t),r=J.formats(e.children.head.domNode),i=this.scroll.create(J.blotName,r);e.insertBefore(i,n)}))}insertRow(t){const[e]=this.descendant(tt);if(null==e||null==e.children.head)return;const n=nt(),r=this.scroll.create(Y.blotName);e.children.head.children.forEach((()=>{const t=this.scroll.create(J.blotName,n);r.appendChild(t)}));const i=e.children.at(t);e.insertBefore(r,i)}rows(){const t=this.children.head;return null==t?[]:t.children.map((t=>t))}}function nt(){return`row-${Math.random().toString(36).slice(2,6)}`}et.allowedChildren=[tt],tt.requiredContainer=et,tt.allowedChildren=[Y],Y.requiredContainer=tt,Y.allowedChildren=[J],J.requiredContainer=Y;class rt extends F.A{static register(){p.Ay.register(J),p.Ay.register(Y),p.Ay.register(tt),p.Ay.register(et)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(et).forEach((t=>{t.balanceCells()}))}deleteColumn(){const[t,,e]=this.getTable();null!=e&&(t.deleteColumn(e.cellOffset()),this.quill.update(p.Ay.sources.USER))}deleteRow(){const[,t]=this.getTable();null!=t&&(t.remove(),this.quill.update(p.Ay.sources.USER))}deleteTable(){const[t]=this.getTable();if(null==t)return;const e=t.offset();t.remove(),this.quill.update(p.Ay.sources.USER),this.quill.setSelection(e,p.Ay.sources.SILENT)}getTable(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(null==t)return[null,null,null,-1];const[e,n]=this.quill.getLine(t.index);if(null==e||e.statics.blotName!==J.blotName)return[null,null,null,-1];const r=e.parent;return[r.parent.parent,r,e,n]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[n,r,i]=this.getTable(e);if(null==i)return;const s=i.cellOffset();n.insertColumn(s+t),this.quill.update(p.Ay.sources.USER);let o=r.rowOffset();0===t&&(o+=1),this.quill.setSelection(e.index+o,e.length,p.Ay.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[n,r,i]=this.getTable(e);if(null==i)return;const s=r.rowOffset();n.insertRow(s+t),this.quill.update(p.Ay.sources.USER),t>0?this.quill.setSelection(e,p.Ay.sources.SILENT):this.quill.setSelection(e.index+r.children.length,e.length,p.Ay.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const n=this.quill.getSelection();if(null==n)return;const r=new Array(t).fill(0).reduce((t=>{const n=new Array(e).fill(\"\\n\").join(\"\");return t.insert(n,{table:nt()})}),(new(z())).retain(n.index));this.quill.updateContents(r,p.Ay.sources.USER),this.quill.setSelection(n.index,p.Ay.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(p.Ay.events.SCROLL_OPTIMIZE,(t=>{t.some((t=>!![\"TD\",\"TR\",\"TBODY\",\"TABLE\"].includes(t.target.tagName)&&(this.quill.once(p.Ay.events.TEXT_CHANGE,((t,e,n)=>{n===p.Ay.sources.USER&&this.balanceTables()})),!0)))}))}}var it=rt;const st=(0,n(6078).A)(\"quill:toolbar\");class ot extends F.A{constructor(t,e){if(super(t,e),Array.isArray(this.options.container)){const e=document.createElement(\"div\");e.setAttribute(\"role\",\"toolbar\"),function(t,e){Array.isArray(e[0])||(e=[e]),e.forEach((e=>{const n=document.createElement(\"span\");n.classList.add(\"ql-formats\"),e.forEach((t=>{if(\"string\"==typeof t)lt(n,t);else{const e=Object.keys(t)[0],r=t[e];Array.isArray(r)?function(t,e,n){const r=document.createElement(\"select\");r.classList.add(`ql-${e}`),n.forEach((t=>{const e=document.createElement(\"option\");!1!==t?e.setAttribute(\"value\",String(t)):e.setAttribute(\"selected\",\"selected\"),r.appendChild(e)})),t.appendChild(r)}(n,e,r):lt(n,e,r)}})),t.appendChild(n)}))}(e,this.options.container),t.container?.parentNode?.insertBefore(e,t.container),this.container=e}else\"string\"==typeof this.options.container?this.container=document.querySelector(this.options.container):this.container=this.options.container;this.container instanceof HTMLElement?(this.container.classList.add(\"ql-toolbar\"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach((t=>{const e=this.options.handlers?.[t];e&&this.addHandler(t,e)})),Array.from(this.container.querySelectorAll(\"button, select\")).forEach((t=>{this.attach(t)})),this.quill.on(p.Ay.events.EDITOR_CHANGE,(()=>{const[t]=this.quill.selection.getRange();this.update(t)}))):st.error(\"Container required for toolbar\",this.options)}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find((t=>0===t.indexOf(\"ql-\")));if(!e)return;if(e=e.slice(3),\"BUTTON\"===t.tagName&&t.setAttribute(\"type\",\"button\"),null==this.handlers[e]&&null==this.quill.scroll.query(e))return void st.warn(\"ignoring attaching to nonexistent format\",e,t);const n=\"SELECT\"===t.tagName?\"change\":\"click\";t.addEventListener(n,(n=>{let r;if(\"SELECT\"===t.tagName){if(t.selectedIndex<0)return;const e=t.options[t.selectedIndex];r=!e.hasAttribute(\"selected\")&&(e.value||!1)}else r=!t.classList.contains(\"ql-active\")&&(t.value||!t.hasAttribute(\"value\")),n.preventDefault();this.quill.focus();const[i]=this.quill.selection.getRange();if(null!=this.handlers[e])this.handlers[e].call(this,r);else if(this.quill.scroll.query(e).prototype instanceof s.EmbedBlot){if(r=prompt(`Enter ${e}`),!r)return;this.quill.updateContents((new(z())).retain(i.index).delete(i.length).insert({[e]:r}),p.Ay.sources.USER)}else this.quill.format(e,r,p.Ay.sources.USER);this.update(i)})),this.controls.push([e,t])}update(t){const e=null==t?{}:this.quill.getFormat(t);this.controls.forEach((n=>{const[r,i]=n;if(\"SELECT\"===i.tagName){let n=null;if(null==t)n=null;else if(null==e[r])n=i.querySelector(\"option[selected]\");else if(!Array.isArray(e[r])){let t=e[r];\"string\"==typeof t&&(t=t.replace(/\"/g,'\\\\\"')),n=i.querySelector(`option[value=\"${t}\"]`)}null==n?(i.value=\"\",i.selectedIndex=-1):n.selected=!0}else if(null==t)i.classList.remove(\"ql-active\"),i.setAttribute(\"aria-pressed\",\"false\");else if(i.hasAttribute(\"value\")){const t=e[r],n=t===i.getAttribute(\"value\")||null!=t&&t.toString()===i.getAttribute(\"value\")||null==t&&!i.getAttribute(\"value\");i.classList.toggle(\"ql-active\",n),i.setAttribute(\"aria-pressed\",n.toString())}else{const t=null!=e[r];i.classList.toggle(\"ql-active\",t),i.setAttribute(\"aria-pressed\",t.toString())}}))}}function lt(t,e,n){const r=document.createElement(\"button\");r.setAttribute(\"type\",\"button\"),r.classList.add(`ql-${e}`),r.setAttribute(\"aria-pressed\",\"false\"),null!=n?(r.value=n,r.setAttribute(\"aria-label\",`${e}: ${n}`)):r.setAttribute(\"aria-label\",e),t.appendChild(r)}ot.DEFAULTS={},ot.DEFAULTS={container:null,handlers:{clean(){const t=this.quill.getSelection();if(null!=t)if(0===t.length){const t=this.quill.getFormat();Object.keys(t).forEach((t=>{null!=this.quill.scroll.query(t,s.Scope.INLINE)&&this.quill.format(t,!1,p.Ay.sources.USER)}))}else this.quill.removeFormat(t.index,t.length,p.Ay.sources.USER)},direction(t){const{align:e}=this.quill.getFormat();\"rtl\"===t&&null==e?this.quill.format(\"align\",\"right\",p.Ay.sources.USER):t||\"right\"!==e||this.quill.format(\"align\",!1,p.Ay.sources.USER),this.quill.format(\"direction\",t,p.Ay.sources.USER)},indent(t){const e=this.quill.getSelection(),n=this.quill.getFormat(e),r=parseInt(n.indent||0,10);if(\"+1\"===t||\"-1\"===t){let e=\"+1\"===t?1:-1;\"rtl\"===n.direction&&(e*=-1),this.quill.format(\"indent\",r+e,p.Ay.sources.USER)}},link(t){!0===t&&(t=prompt(\"Enter link URL:\")),this.quill.format(\"link\",t,p.Ay.sources.USER)},list(t){const e=this.quill.getSelection(),n=this.quill.getFormat(e);\"check\"===t?\"checked\"===n.list||\"unchecked\"===n.list?this.quill.format(\"list\",!1,p.Ay.sources.USER):this.quill.format(\"list\",\"unchecked\",p.Ay.sources.USER):this.quill.format(\"list\",t,p.Ay.sources.USER)}}};const at='<svg viewbox=\"0 0 18 18\"><polyline class=\"ql-even ql-stroke\" points=\"5 7 3 9 5 11\"/><polyline class=\"ql-even ql-stroke\" points=\"13 7 15 9 13 11\"/><line class=\"ql-stroke\" x1=\"10\" x2=\"8\" y1=\"5\" y2=\"13\"/></svg>';var ct={align:{\"\":'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"13\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"9\" y1=\"4\" y2=\"4\"/></svg>',center:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"14\" x2=\"4\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"12\" x2=\"6\" y1=\"4\" y2=\"4\"/></svg>',right:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"5\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"9\" y1=\"4\" y2=\"4\"/></svg>',justify:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"4\" y2=\"4\"/></svg>'},background:'<svg viewbox=\"0 0 18 18\"><g class=\"ql-fill ql-color-label\"><polygon points=\"6 6.868 6 6 5 6 5 7 5.942 7 6 6.868\"/><rect height=\"1\" width=\"1\" x=\"4\" y=\"4\"/><polygon points=\"6.817 5 6 5 6 6 6.38 6 6.817 5\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"6\"/><rect height=\"1\" width=\"1\" x=\"3\" y=\"5\"/><rect height=\"1\" width=\"1\" x=\"4\" y=\"7\"/><polygon points=\"4 11.439 4 11 3 11 3 12 3.755 12 4 11.439\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"12\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"9\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"15\"/><polygon points=\"4.63 10 4 10 4 11 4.192 11 4.63 10\"/><rect height=\"1\" width=\"1\" x=\"3\" y=\"8\"/><path d=\"M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z\"/><path d=\"M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z\"/><path d=\"M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z\"/><rect height=\"1\" width=\"1\" x=\"12\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"11\" y=\"3\"/><path d=\"M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"3\"/><rect height=\"1\" width=\"1\" x=\"6\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"3\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"5\" y=\"3\"/><rect height=\"1\" width=\"1\" x=\"9\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"14\"/><polygon points=\"13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174\"/><rect height=\"1\" width=\"1\" x=\"13\" y=\"7\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"5\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"6\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"8\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"9\"/><path d=\"M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"3\"/><polygon points=\"12 6.868 12 6 11.62 6 12 6.868\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"12\" y=\"5\"/><rect height=\"1\" width=\"1\" x=\"13\" y=\"4\"/><polygon points=\"12.933 9 13 9 13 8 12.495 8 12.933 9\"/><rect height=\"1\" width=\"1\" x=\"9\" y=\"14\"/><rect height=\"1\" width=\"1\" x=\"8\" y=\"15\"/><path d=\"M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z\"/><rect height=\"1\" width=\"1\" x=\"5\" y=\"15\"/><path d=\"M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z\"/><rect height=\"1\" width=\"1\" x=\"11\" y=\"15\"/><path d=\"M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"15\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"11\"/></g><polyline class=\"ql-stroke\" points=\"5.5 13 9 5 12.5 13\"/><line class=\"ql-stroke\" x1=\"11.63\" x2=\"6.38\" y1=\"11\" y2=\"11\"/></svg>',blockquote:'<svg viewbox=\"0 0 18 18\"><rect class=\"ql-fill ql-stroke\" height=\"3\" width=\"3\" x=\"4\" y=\"5\"/><rect class=\"ql-fill ql-stroke\" height=\"3\" width=\"3\" x=\"11\" y=\"5\"/><path class=\"ql-even ql-fill ql-stroke\" d=\"M7,8c0,4.031-3,5-3,5\"/><path class=\"ql-even ql-fill ql-stroke\" d=\"M14,8c0,4.031-3,5-3,5\"/></svg>',bold:'<svg viewbox=\"0 0 18 18\"><path class=\"ql-stroke\" d=\"M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z\"/><path class=\"ql-stroke\" d=\"M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z\"/></svg>',clean:'<svg class=\"\" viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"5\" x2=\"13\" y1=\"3\" y2=\"3\"/><line class=\"ql-stroke\" x1=\"6\" x2=\"9.35\" y1=\"12\" y2=\"3\"/><line class=\"ql-stroke\" x1=\"11\" x2=\"15\" y1=\"11\" y2=\"15\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"11\" y1=\"11\" y2=\"15\"/><rect class=\"ql-fill\" height=\"1\" rx=\"0.5\" ry=\"0.5\" width=\"7\" x=\"2\" y=\"14\"/></svg>',code:at,\"code-block\":at,color:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-color-label ql-stroke ql-transparent\" x1=\"3\" x2=\"15\" y1=\"15\" y2=\"15\"/><polyline class=\"ql-stroke\" points=\"5.5 11 9 3 12.5 11\"/><line class=\"ql-stroke\" x1=\"11.63\" x2=\"6.38\" y1=\"9\" y2=\"9\"/></svg>',direction:{\"\":'<svg viewbox=\"0 0 18 18\"><polygon class=\"ql-stroke ql-fill\" points=\"3 11 5 9 3 7 3 11\"/><line class=\"ql-stroke ql-fill\" x1=\"15\" x2=\"11\" y1=\"4\" y2=\"4\"/><path class=\"ql-fill\" d=\"M11,3a3,3,0,0,0,0,6h1V3H11Z\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"11\" y=\"4\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"13\" y=\"4\"/></svg>',rtl:'<svg viewbox=\"0 0 18 18\"><polygon class=\"ql-stroke ql-fill\" points=\"15 12 13 10 15 8 15 12\"/><line class=\"ql-stroke ql-fill\" x1=\"9\" x2=\"5\" y1=\"4\" y2=\"4\"/><path class=\"ql-fill\" d=\"M5,3A3,3,0,0,0,5,9H6V3H5Z\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"5\" y=\"4\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"7\" y=\"4\"/></svg>'},formula:'<svg viewbox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z\"/><rect class=\"ql-fill\" height=\"1.6\" rx=\"0.8\" ry=\"0.8\" width=\"5\" x=\"5.15\" y=\"6.2\"/><path class=\"ql-fill\" d=\"M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z\"/></svg>',header:{1:'<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z\"/></svg>',2:'<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z\"/></svg>',3:'<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z\"/></svg>',4:'<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z\"/></svg>',5:'<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z\"/></svg>',6:'<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z\"/></svg>'},italic:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"7\" x2=\"13\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"5\" x2=\"11\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"8\" x2=\"10\" y1=\"14\" y2=\"4\"/></svg>',image:'<svg viewbox=\"0 0 18 18\"><rect class=\"ql-stroke\" height=\"10\" width=\"12\" x=\"3\" y=\"4\"/><circle class=\"ql-fill\" cx=\"6\" cy=\"7\" r=\"1\"/><polyline class=\"ql-even ql-fill\" points=\"5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12\"/></svg>',indent:{\"+1\":'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"/><polyline class=\"ql-fill ql-stroke\" points=\"3 7 3 11 5 9 3 7\"/></svg>',\"-1\":'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"/><polyline class=\"ql-stroke\" points=\"5 7 5 11 3 9 5 7\"/></svg>'},link:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"7\" x2=\"11\" y1=\"7\" y2=\"11\"/><path class=\"ql-even ql-stroke\" d=\"M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z\"/><path class=\"ql-even ql-stroke\" d=\"M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z\"/></svg>',list:{bullet:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"6\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"6\" x2=\"15\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"6\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"3\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"3\" y1=\"14\" y2=\"14\"/></svg>',check:'<svg class=\"\" viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"4\" y2=\"4\"/><polyline class=\"ql-stroke\" points=\"3 4 4 5 6 3\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"14\" y2=\"14\"/><polyline class=\"ql-stroke\" points=\"3 14 4 15 6 13\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"/><polyline class=\"ql-stroke\" points=\"3 9 4 10 6 8\"/></svg>',ordered:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"7\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"7\" x2=\"15\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"7\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke ql-thin\" x1=\"2.5\" x2=\"4.5\" y1=\"5.5\" y2=\"5.5\"/><path class=\"ql-fill\" d=\"M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z\"/><path class=\"ql-stroke ql-thin\" d=\"M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156\"/><path class=\"ql-stroke ql-thin\" d=\"M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109\"/></svg>'},script:{sub:'<svg viewbox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z\"/><path class=\"ql-fill\" d=\"M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z\"/></svg>',super:'<svg viewbox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z\"/><path class=\"ql-fill\" d=\"M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z\"/></svg>'},strike:'<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke ql-thin\" x1=\"15.5\" x2=\"2.5\" y1=\"8.5\" y2=\"9.5\"/><path class=\"ql-fill\" d=\"M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z\"/><path class=\"ql-fill\" d=\"M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z\"/></svg>',table:'<svg viewbox=\"0 0 18 18\"><rect class=\"ql-stroke\" height=\"12\" width=\"12\" x=\"3\" y=\"3\"/><rect class=\"ql-fill\" height=\"2\" width=\"3\" x=\"5\" y=\"5\"/><rect class=\"ql-fill\" height=\"2\" width=\"4\" x=\"9\" y=\"5\"/><g class=\"ql-fill ql-transparent\"><rect height=\"2\" width=\"3\" x=\"5\" y=\"8\"/><rect height=\"2\" width=\"4\" x=\"9\" y=\"8\"/><rect height=\"2\" width=\"3\" x=\"5\" y=\"11\"/><rect height=\"2\" width=\"4\" x=\"9\" y=\"11\"/></g></svg>',underline:'<svg viewbox=\"0 0 18 18\"><path class=\"ql-stroke\" d=\"M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3\"/><rect class=\"ql-fill\" height=\"1\" rx=\"0.5\" ry=\"0.5\" width=\"12\" x=\"3\" y=\"15\"/></svg>',video:'<svg viewbox=\"0 0 18 18\"><rect class=\"ql-stroke\" height=\"12\" width=\"12\" x=\"3\" y=\"3\"/><rect class=\"ql-fill\" height=\"12\" width=\"1\" x=\"5\" y=\"3\"/><rect class=\"ql-fill\" height=\"12\" width=\"1\" x=\"12\" y=\"3\"/><rect class=\"ql-fill\" height=\"2\" width=\"8\" x=\"5\" y=\"8\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"5\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"7\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"10\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"12\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"5\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"7\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"10\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"12\"/></svg>'};let ut=0;function ht(t,e){t.setAttribute(e,`${!(\"true\"===t.getAttribute(e))}`)}var dt=class{constructor(t){this.select=t,this.container=document.createElement(\"span\"),this.buildPicker(),this.select.style.display=\"none\",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener(\"mousedown\",(()=>{this.togglePicker()})),this.label.addEventListener(\"keydown\",(t=>{switch(t.key){case\"Enter\":this.togglePicker();break;case\"Escape\":this.escape(),t.preventDefault()}})),this.select.addEventListener(\"change\",this.update.bind(this))}togglePicker(){this.container.classList.toggle(\"ql-expanded\"),ht(this.label,\"aria-expanded\"),ht(this.options,\"aria-hidden\")}buildItem(t){const e=document.createElement(\"span\");e.tabIndex=\"0\",e.setAttribute(\"role\",\"button\"),e.classList.add(\"ql-picker-item\");const n=t.getAttribute(\"value\");return n&&e.setAttribute(\"data-value\",n),t.textContent&&e.setAttribute(\"data-label\",t.textContent),e.addEventListener(\"click\",(()=>{this.selectItem(e,!0)})),e.addEventListener(\"keydown\",(t=>{switch(t.key){case\"Enter\":this.selectItem(e,!0),t.preventDefault();break;case\"Escape\":this.escape(),t.preventDefault()}})),e}buildLabel(){const t=document.createElement(\"span\");return t.classList.add(\"ql-picker-label\"),t.innerHTML='<svg viewbox=\"0 0 18 18\"><polygon class=\"ql-stroke\" points=\"7 11 9 13 11 11 7 11\"/><polygon class=\"ql-stroke\" points=\"7 7 9 5 11 7 7 7\"/></svg>',t.tabIndex=\"0\",t.setAttribute(\"role\",\"button\"),t.setAttribute(\"aria-expanded\",\"false\"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement(\"span\");t.classList.add(\"ql-picker-options\"),t.setAttribute(\"aria-hidden\",\"true\"),t.tabIndex=\"-1\",t.id=`ql-picker-options-${ut}`,ut+=1,this.label.setAttribute(\"aria-controls\",t.id),this.options=t,Array.from(this.select.options).forEach((e=>{const n=this.buildItem(e);t.appendChild(n),!0===e.selected&&this.selectItem(n)})),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach((t=>{this.container.setAttribute(t.name,t.value)})),this.container.classList.add(\"ql-picker\"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout((()=>this.label.focus()),1)}close(){this.container.classList.remove(\"ql-expanded\"),this.label.setAttribute(\"aria-expanded\",\"false\"),this.options.setAttribute(\"aria-hidden\",\"true\")}selectItem(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.container.querySelector(\".ql-selected\");t!==n&&(null!=n&&n.classList.remove(\"ql-selected\"),null!=t&&(t.classList.add(\"ql-selected\"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute(\"data-value\")?this.label.setAttribute(\"data-value\",t.getAttribute(\"data-value\")):this.label.removeAttribute(\"data-value\"),t.hasAttribute(\"data-label\")?this.label.setAttribute(\"data-label\",t.getAttribute(\"data-label\")):this.label.removeAttribute(\"data-label\"),e&&(this.select.dispatchEvent(new Event(\"change\")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const e=this.container.querySelector(\".ql-picker-options\").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);const e=null!=t&&t!==this.select.querySelector(\"option[selected]\");this.label.classList.toggle(\"ql-active\",e)}},ft=class extends dt{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add(\"ql-color-picker\"),Array.from(this.container.querySelectorAll(\".ql-picker-item\")).slice(0,7).forEach((t=>{t.classList.add(\"ql-primary\")}))}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute(\"value\")||\"\",e}selectItem(t,e){super.selectItem(t,e);const n=this.label.querySelector(\".ql-color-label\"),r=t&&t.getAttribute(\"data-value\")||\"\";n&&(\"line\"===n.tagName?n.style.stroke=r:n.style.fill=r)}},pt=class extends dt{constructor(t,e){super(t),this.container.classList.add(\"ql-icon-picker\"),Array.from(this.container.querySelectorAll(\".ql-picker-item\")).forEach((t=>{t.innerHTML=e[t.getAttribute(\"data-value\")||\"\"]})),this.defaultItem=this.container.querySelector(\".ql-selected\"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const n=t||this.defaultItem;if(null!=n){if(this.label.innerHTML===n.innerHTML)return;this.label.innerHTML=n.innerHTML}}},gt=class{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer(\"ql-tooltip\"),this.root.innerHTML=this.constructor.TEMPLATE,(t=>{const{overflowY:e}=getComputedStyle(t,null);return\"visible\"!==e&&\"clip\"!==e})(this.quill.root)&&this.quill.root.addEventListener(\"scroll\",(()=>{this.root.style.marginTop=-1*this.quill.root.scrollTop+\"px\"})),this.hide()}hide(){this.root.classList.add(\"ql-hidden\")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,n=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${n}px`,this.root.classList.remove(\"ql-flip\");const r=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let s=0;if(i.right>r.right&&(s=r.right-i.right,this.root.style.left=`${e+s}px`),i.left<r.left&&(s=r.left-i.left,this.root.style.left=`${e+s}px`),i.bottom>r.bottom){const e=i.bottom-i.top,r=t.bottom-t.top+e;this.root.style.top=n-r+\"px\",this.root.classList.add(\"ql-flip\")}return s}show(){this.root.classList.remove(\"ql-editing\"),this.root.classList.remove(\"ql-hidden\")}},mt=n(8347),bt=n(5374),yt=n(9609);const vt=[!1,\"center\",\"right\",\"justify\"],At=[\"#000000\",\"#e60000\",\"#ff9900\",\"#ffff00\",\"#008a00\",\"#0066cc\",\"#9933ff\",\"#ffffff\",\"#facccc\",\"#ffebcc\",\"#ffffcc\",\"#cce8cc\",\"#cce0f5\",\"#ebd6ff\",\"#bbbbbb\",\"#f06666\",\"#ffc266\",\"#ffff66\",\"#66b966\",\"#66a3e0\",\"#c285ff\",\"#888888\",\"#a10000\",\"#b26b00\",\"#b2b200\",\"#006100\",\"#0047b2\",\"#6b24b2\",\"#444444\",\"#5c0000\",\"#663d00\",\"#666600\",\"#003700\",\"#002966\",\"#3d1466\"],xt=[!1,\"serif\",\"monospace\"],Nt=[\"1\",\"2\",\"3\",!1],Et=[\"small\",!1,\"large\",\"huge\"];class wt extends yt.A{constructor(t,e){super(t,e);const n=e=>{document.body.contains(t.root)?(null==this.tooltip||this.tooltip.root.contains(e.target)||document.activeElement===this.tooltip.textbox||this.quill.hasFocus()||this.tooltip.hide(),null!=this.pickers&&this.pickers.forEach((t=>{t.container.contains(e.target)||t.close()}))):document.body.removeEventListener(\"click\",n)};t.emitter.listenDOM(\"click\",document.body,n)}addModule(t){const e=super.addModule(t);return\"toolbar\"===t&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach((t=>{(t.getAttribute(\"class\")||\"\").split(/\\s+/).forEach((n=>{if(n.startsWith(\"ql-\")&&(n=n.slice(3),null!=e[n]))if(\"direction\"===n)t.innerHTML=e[n][\"\"]+e[n].rtl;else if(\"string\"==typeof e[n])t.innerHTML=e[n];else{const r=t.value||\"\";null!=r&&e[n][r]&&(t.innerHTML=e[n][r])}}))}))}buildPickers(t,e){this.pickers=Array.from(t).map((t=>{if(t.classList.contains(\"ql-align\")&&(null==t.querySelector(\"option\")&&kt(t,vt),\"object\"==typeof e.align))return new pt(t,e.align);if(t.classList.contains(\"ql-background\")||t.classList.contains(\"ql-color\")){const n=t.classList.contains(\"ql-background\")?\"background\":\"color\";return null==t.querySelector(\"option\")&&kt(t,At,\"background\"===n?\"#ffffff\":\"#000000\"),new ft(t,e[n])}return null==t.querySelector(\"option\")&&(t.classList.contains(\"ql-font\")?kt(t,xt):t.classList.contains(\"ql-header\")?kt(t,Nt):t.classList.contains(\"ql-size\")&&kt(t,Et)),new dt(t)})),this.quill.on(bt.A.events.EDITOR_CHANGE,(()=>{this.pickers.forEach((t=>{t.update()}))}))}}wt.DEFAULTS=(0,mt.A)({},yt.A.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit(\"formula\")},image(){let t=this.container.querySelector(\"input.ql-image[type=file]\");null==t&&(t=document.createElement(\"input\"),t.setAttribute(\"type\",\"file\"),t.setAttribute(\"accept\",this.quill.uploader.options.mimetypes.join(\", \")),t.classList.add(\"ql-image\"),t.addEventListener(\"change\",(()=>{const e=this.quill.getSelection(!0);this.quill.uploader.upload(e,t.files),t.value=\"\"})),this.container.appendChild(t)),t.click()},video(){this.quill.theme.tooltip.edit(\"video\")}}}}});class qt extends gt{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type=\"text\"]'),this.listen()}listen(){this.textbox.addEventListener(\"keydown\",(t=>{\"Enter\"===t.key?(this.save(),t.preventDefault()):\"Escape\"===t.key&&(this.cancel(),t.preventDefault())}))}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:\"link\",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.root.classList.remove(\"ql-hidden\"),this.root.classList.add(\"ql-editing\"),null==this.textbox)return;null!=e?this.textbox.value=e:t!==this.root.getAttribute(\"data-mode\")&&(this.textbox.value=\"\");const n=this.quill.getBounds(this.quill.selection.savedRange);null!=n&&this.position(n),this.textbox.select(),this.textbox.setAttribute(\"placeholder\",this.textbox.getAttribute(`data-${t}`)||\"\"),this.root.setAttribute(\"data-mode\",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute(\"data-mode\")){case\"link\":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,\"link\",t,bt.A.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format(\"link\",t,bt.A.sources.USER)),this.quill.root.scrollTop=e;break}case\"video\":t=function(t){let e=t.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/)||t.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);return e?`${e[1]||\"https\"}://www.youtube.com/embed/${e[2]}?showinfo=0`:(e=t.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/))?`${e[1]||\"https\"}://player.vimeo.com/video/${e[2]}/`:t}(t);case\"formula\":{if(!t)break;const e=this.quill.getSelection(!0);if(null!=e){const n=e.index+e.length;this.quill.insertEmbed(n,this.root.getAttribute(\"data-mode\"),t,bt.A.sources.USER),\"formula\"===this.root.getAttribute(\"data-mode\")&&this.quill.insertText(n+1,\" \",bt.A.sources.USER),this.quill.setSelection(n+2,bt.A.sources.USER)}break}}this.textbox.value=\"\",this.hide()}}function kt(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach((e=>{const r=document.createElement(\"option\");e===n?r.setAttribute(\"selected\",\"selected\"):r.setAttribute(\"value\",String(e)),t.appendChild(r)}))}var _t=n(8298);const Lt=[[\"bold\",\"italic\",\"link\"],[{header:1},{header:2},\"blockquote\"]];class St extends qt{static TEMPLATE=['<span class=\"ql-tooltip-arrow\"></span>','<div class=\"ql-tooltip-editor\">','<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">','<a class=\"ql-close\"></a>',\"</div>\"].join(\"\");constructor(t,e){super(t,e),this.quill.on(bt.A.events.EDITOR_CHANGE,((t,e,n,r)=>{if(t===bt.A.events.SELECTION_CHANGE)if(null!=e&&e.length>0&&r===bt.A.sources.USER){this.show(),this.root.style.left=\"0px\",this.root.style.width=\"\",this.root.style.width=`${this.root.offsetWidth}px`;const t=this.quill.getLines(e.index,e.length);if(1===t.length){const t=this.quill.getBounds(e);null!=t&&this.position(t)}else{const n=t[t.length-1],r=this.quill.getIndex(n),i=Math.min(n.length()-1,e.index+e.length-r),s=this.quill.getBounds(new _t.Q(r,i));null!=s&&this.position(s)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()}))}listen(){super.listen(),this.root.querySelector(\".ql-close\").addEventListener(\"click\",(()=>{this.root.classList.remove(\"ql-editing\")})),this.quill.on(bt.A.events.SCROLL_OPTIMIZE,(()=>{setTimeout((()=>{if(this.root.classList.contains(\"ql-hidden\"))return;const t=this.quill.getSelection();if(null!=t){const e=this.quill.getBounds(t);null!=e&&this.position(e)}}),1)}))}cancel(){this.show()}position(t){const e=super.position(t),n=this.root.querySelector(\".ql-tooltip-arrow\");return n.style.marginLeft=\"\",0!==e&&(n.style.marginLeft=-1*e-n.offsetWidth/2+\"px\"),e}}class Ot extends wt{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=Lt),super(t,e),this.quill.container.classList.add(\"ql-bubble\")}extendToolbar(t){this.tooltip=new St(this.quill,this.options.bounds),null!=t.container&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll(\"button\"),ct),this.buildPickers(t.container.querySelectorAll(\"select\"),ct))}}Ot.DEFAULTS=(0,mt.A)({},wt.DEFAULTS,{modules:{toolbar:{handlers:{link(t){t?this.quill.theme.tooltip.edit():this.quill.format(\"link\",!1,p.Ay.sources.USER)}}}}});const Tt=[[{header:[\"1\",\"2\",\"3\",!1]}],[\"bold\",\"italic\",\"underline\",\"link\"],[{list:\"ordered\"},{list:\"bullet\"}],[\"clean\"]];class jt extends qt{static TEMPLATE=['<a class=\"ql-preview\" rel=\"noopener noreferrer\" target=\"_blank\" href=\"about:blank\"></a>','<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">','<a class=\"ql-action\"></a>','<a class=\"ql-remove\"></a>'].join(\"\");preview=this.root.querySelector(\"a.ql-preview\");listen(){super.listen(),this.root.querySelector(\"a.ql-action\").addEventListener(\"click\",(t=>{this.root.classList.contains(\"ql-editing\")?this.save():this.edit(\"link\",this.preview.textContent),t.preventDefault()})),this.root.querySelector(\"a.ql-remove\").addEventListener(\"click\",(t=>{if(null!=this.linkRange){const t=this.linkRange;this.restoreFocus(),this.quill.formatText(t,\"link\",!1,bt.A.sources.USER),delete this.linkRange}t.preventDefault(),this.hide()})),this.quill.on(bt.A.events.SELECTION_CHANGE,((t,e,n)=>{if(null!=t){if(0===t.length&&n===bt.A.sources.USER){const[e,n]=this.quill.scroll.descendant(w,t.index);if(null!=e){this.linkRange=new _t.Q(t.index-n,e.length());const r=w.formats(e.domNode);this.preview.textContent=r,this.preview.setAttribute(\"href\",r),this.show();const i=this.quill.getBounds(this.linkRange);return void(null!=i&&this.position(i))}}else delete this.linkRange;this.hide()}}))}show(){super.show(),this.root.removeAttribute(\"data-mode\")}}class Ct extends wt{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=Tt),super(t,e),this.quill.container.classList.add(\"ql-snow\")}extendToolbar(t){null!=t.container&&(t.container.classList.add(\"ql-snow\"),this.buildButtons(t.container.querySelectorAll(\"button\"),ct),this.buildPickers(t.container.querySelectorAll(\"select\"),ct),this.tooltip=new jt(this.quill,this.options.bounds),t.container.querySelector(\".ql-link\")&&this.quill.keyboard.addBinding({key:\"k\",shortKey:!0},((e,n)=>{t.handlers.link.call(t,!n.format.link)})))}}Ct.DEFAULTS=(0,mt.A)({},wt.DEFAULTS,{modules:{toolbar:{handlers:{link(t){if(t){const t=this.quill.getSelection();if(null==t||0===t.length)return;let e=this.quill.getText(t);/^\\S+@\\S+\\.\\S+$/.test(e)&&0!==e.indexOf(\"mailto:\")&&(e=`mailto:${e}`);const{tooltip:n}=this.quill.theme;n.edit(\"link\",e)}else this.quill.format(\"link\",!1,p.Ay.sources.USER)}}}}});var Rt=Ct;t.default.register({\"attributors/attribute/direction\":i.Mc,\"attributors/class/align\":e.qh,\"attributors/class/background\":b.l,\"attributors/class/color\":y.g3,\"attributors/class/direction\":i.sY,\"attributors/class/font\":v.q,\"attributors/class/size\":A.U,\"attributors/style/align\":e.Hu,\"attributors/style/background\":b.s,\"attributors/style/color\":y.JM,\"attributors/style/direction\":i.VL,\"attributors/style/font\":v.z,\"attributors/style/size\":A.r},!0),t.default.register({\"formats/align\":e.qh,\"formats/direction\":i.sY,\"formats/indent\":l,\"formats/background\":b.s,\"formats/color\":y.JM,\"formats/font\":v.q,\"formats/size\":A.U,\"formats/blockquote\":u,\"formats/code-block\":D.Ay,\"formats/header\":d,\"formats/list\":m,\"formats/bold\":E,\"formats/code\":D.Cy,\"formats/italic\":class extends E{static blotName=\"italic\";static tagName=[\"EM\",\"I\"]},\"formats/link\":w,\"formats/script\":_,\"formats/strike\":class extends E{static blotName=\"strike\";static tagName=[\"S\",\"STRIKE\"]},\"formats/underline\":S,\"formats/formula\":j,\"formats/image\":I,\"formats/video\":U,\"modules/syntax\":Q,\"modules/table\":it,\"modules/toolbar\":ot,\"themes/bubble\":Ot,\"themes/snow\":Rt,\"ui/icons\":ct,\"ui/picker\":dt,\"ui/icon-picker\":pt,\"ui/color-picker\":ft,\"ui/tooltip\":gt},!0);var It=t.default}(),r.default}()}));\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,YAAU,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,CAAC,IAAE,YAAU,OAAO,UAAQ,QAAQ,QAAM,EAAE,IAAE,EAAE,QAAM,EAAE;AAAA,IAAC,EAAE,MAAM,WAAU;AAAC,aAAO,WAAU;AAAC,YAAI,IAAE,EAAC,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,EAAE,CAAC,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,UAAS;AAAA,YAAC,QAAM,CAAC;AAAA,YAAE,QAAO;AAAC,qBAAO,QAAM,KAAK,MAAM,UAAQ,KAAK,MAAM,QAAM,EAAE,IAAI,IAAG,KAAK,MAAM;AAAA,YAAK;AAAA,YAAC,SAASH,IAAEC,IAAE;AAAC,oBAAM,SAASD,IAAEC,EAAC,GAAE,KAAK,QAAM,CAAC;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEE,IAAE;AAAC,cAAAH,MAAG,MAAI,KAAK,OAAO,MAAMC,IAAEC,GAAE,MAAM,KAAK,IAAEH,KAAEC,OAAI,KAAK,OAAO,KAAG,KAAK,OAAOC,IAAEE,EAAC,IAAE,MAAM,SAASJ,IAAE,KAAK,IAAIC,IAAE,KAAK,OAAO,IAAED,KAAE,CAAC,GAAEE,IAAEE,EAAC,GAAE,KAAK,QAAM,CAAC;AAAA,YAAE;AAAA,YAAC,SAASJ,IAAEC,IAAEC,IAAE;AAAC,kBAAG,QAAMA,GAAE,QAAO,MAAM,SAASF,IAAEC,IAAEC,EAAC,GAAE,MAAK,KAAK,QAAM,CAAC;AAAG,kBAAG,MAAID,GAAE,OAAO;AAAO,oBAAME,KAAEF,GAAE,MAAM,IAAI,GAAEG,KAAED,GAAE,MAAM;AAAE,cAAAC,GAAE,SAAO,MAAIJ,KAAE,KAAK,OAAO,IAAE,KAAG,QAAM,KAAK,SAAS,OAAK,MAAM,SAAS,KAAK,IAAIA,IAAE,KAAK,OAAO,IAAE,CAAC,GAAEI,EAAC,IAAE,KAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO,GAAEA,EAAC,GAAE,KAAK,QAAM,CAAC;AAAG,kBAAIC,KAAE;AAAK,cAAAF,GAAE,OAAQ,CAACH,IAAEC,QAAKI,KAAEA,GAAE,MAAML,IAAE,IAAE,GAAEK,GAAE,SAAS,GAAEJ,EAAC,GAAEA,GAAE,SAASD,KAAEI,GAAE,MAAM;AAAA,YAAC;AAAA,YAAC,aAAaJ,IAAEC,IAAE;AAAC,oBAAK,EAAC,MAAKC,GAAC,IAAE,KAAK;AAAS,oBAAM,aAAaF,IAAEC,EAAC,GAAEC,cAAa,EAAE,KAAGA,GAAE,OAAO,GAAE,KAAK,QAAM,CAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO,QAAM,KAAK,MAAM,WAAS,KAAK,MAAM,SAAO,MAAM,OAAO,IAAE,IAAG,KAAK,MAAM;AAAA,YAAM;AAAA,YAAC,aAAaF,IAAEC,IAAE;AAAC,oBAAM,aAAaD,IAAEC,EAAC,GAAE,KAAK,QAAM,CAAC;AAAA,YAAC;AAAA,YAAC,SAASD,IAAE;AAAC,oBAAM,SAASA,EAAC,GAAE,KAAK,QAAM,CAAC;AAAA,YAAC;AAAA,YAAC,KAAKA,IAAE;AAAC,qBAAO,MAAM,KAAKA,IAAE,IAAE;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAE;AAAC,oBAAM,YAAYA,EAAC,GAAE,KAAK,QAAM,CAAC;AAAA,YAAC;AAAA,YAAC,MAAMA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,kBAAGA,OAAI,MAAID,MAAGA,MAAG,KAAK,OAAO,IAAE,IAAG;AAAC,sBAAMC,KAAE,KAAK,MAAM;AAAE,uBAAO,MAAID,MAAG,KAAK,OAAO,aAAaC,IAAE,IAAI,GAAE,SAAO,KAAK,OAAO,aAAaA,IAAE,KAAK,IAAI,GAAEA;AAAA,cAAE;AAAC,oBAAMC,KAAE,MAAM,MAAMF,IAAEC,EAAC;AAAE,qBAAO,KAAK,QAAM,CAAC,GAAEC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,WAAS,SAAQ,EAAE,UAAQ,KAAI,EAAE,eAAa,EAAE,GAAE,EAAE,kBAAgB,CAAC,EAAE,GAAE,EAAE,GAAEC,GAAE,WAAU,EAAE,CAAC;AAAA,UAAE,MAAM,UAAUA,GAAE,UAAS;AAAA,YAAC,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,aAAW,IAAIA,GAAE,gBAAgB,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,qBAAO,KAAI,EAAE,KAAI,OAAO,KAAK,MAAM,GAAE,kCAAI,KAAK,QAAQ,IAAK,KAAK,WAAW,OAAO,EAAE;AAAA,YAAC;AAAA,YAAC,OAAOH,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,OAAO,MAAMF,IAAEG,GAAE,MAAM,eAAe;AAAE,sBAAMD,MAAG,KAAK,WAAW,UAAUA,IAAED,EAAC;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAK,OAAOD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAASH,IAAEC,IAAEC,IAAE;AAAC,kBAAG,QAAMA,GAAE,QAAO,KAAK,MAAM,SAASF,IAAEC,IAAEC,EAAC;AAAE,oBAAMC,KAAEF,GAAE,MAAM,IAAI,GAAEG,KAAED,GAAE,IAAI,GAAEE,KAAEF,GAAE,IAAK,CAAAH,OAAG;AAAC,sBAAMC,KAAE,KAAK,OAAO,OAAO,EAAE,QAAQ;AAAE,uBAAOA,GAAE,SAAS,GAAED,EAAC,GAAEC;AAAA,cAAC,CAAE,GAAEK,KAAE,KAAK,MAAMN,EAAC;AAAE,cAAAK,GAAE,QAAS,CAAAL,OAAG;AAAC,qBAAK,OAAO,aAAaA,IAAEM,EAAC;AAAA,cAAC,CAAE,GAAEF,MAAG,KAAK,OAAO,aAAa,KAAK,OAAO,OAAO,QAAOA,EAAC,GAAEE,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEN,IAAE;AAAC,gBAAIC,KAAE,EAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,MAAI,UAAU,CAAC;AAAE,mBAAOD,GAAE,YAAYG,GAAE,QAAQ,EAAE,OAAQ,CAACH,IAAEE,OAAI,MAAIA,GAAE,OAAO,IAAEF,KAAEA,GAAE,OAAOE,GAAE,MAAM,GAAE,EAAEA,IAAE,CAAC,GAAED,EAAC,CAAC,GAAG,KAAI,EAAE,IAAE,EAAE,OAAO,MAAK,EAAED,EAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,gBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAEC,KAAE,EAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,MAAI,UAAU,CAAC;AAAE,mBAAO,QAAMF,KAAEC,MAAG,aAAYD,MAAG,cAAY,OAAOA,GAAE,YAAUC,KAAE,kCAAIA,KAAKD,GAAE,QAAQ,IAAGE,MAAG,OAAOD,GAAE,YAAY,IAAG,QAAMD,GAAE,UAAQ,aAAWA,GAAE,OAAO,QAAQ,YAAUA,GAAE,OAAO,QAAQ,UAAQA,GAAE,QAAQ,QAAMC,KAAE,EAAED,GAAE,QAAOC,IAAEC,EAAC;AAAA,UAAE;AAAC,YAAE,QAAMC,GAAE,MAAM;AAAA,QAAU,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,UAAS;AAAA,YAAC,OAAO,QAAO;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,eAAC,KAAK,QAAM,KAAK,SAAO,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,qBAAM;AAAA,YAAE;AAAA,UAAC;AAAC,YAAE,WAAS,SAAQ,EAAE,UAAQ,MAAKF,GAAE,IAAE;AAAA,QAAC,GAAE,KAAI,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,cAAa;AAAA,UAAC;AAAC,UAAAF,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,UAAS;AAAA,YAAC,OAAO,WAAS;AAAA,YAAS,OAAO,YAAU;AAAA,YAAY,OAAO,UAAQ;AAAA,YAAO,OAAO,WAAS;AAAA,YAAS,OAAO,QAAO;AAAA,YAAC;AAAA,YAAC,YAAYH,IAAEC,IAAEC,IAAE;AAAC,oBAAMF,IAAEC,EAAC,GAAE,KAAK,YAAUC,IAAE,KAAK,WAAS,SAAS,eAAe,EAAE,QAAQ,GAAE,KAAK,QAAQ,YAAY,KAAK,QAAQ,GAAE,KAAK,cAAY;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,sBAAM,KAAK,UAAQ,KAAK,OAAO,YAAY,IAAI;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,kBAAG,MAAI,KAAK,YAAY,QAAO,KAAK,MAAM,OAAOD,IAAEC,EAAC;AAAE,kBAAIC,KAAE,MAAKE,KAAE;AAAE,qBAAK,QAAMF,MAAGA,GAAE,QAAQ,UAAQC,GAAE,MAAM,aAAY,CAAAC,MAAGF,GAAE,OAAOA,GAAE,MAAM,GAAEA,KAAEA,GAAE;AAAO,sBAAMA,OAAI,KAAK,cAAY,EAAE,SAAS,QAAOA,GAAE,SAAS,GAAEA,GAAE,SAASE,IAAE,EAAE,SAAS,QAAOJ,IAAEC,EAAC,GAAE,KAAK,cAAY;AAAA,YAAE;AAAA,YAAC,MAAMD,IAAEC,IAAE;AAAC,qBAAOD,OAAI,KAAK,WAAS,IAAE,MAAM,MAAMA,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO,KAAK;AAAA,YAAW;AAAA,YAAC,WAAU;AAAC,qBAAM,CAAC,KAAK,UAAS,KAAK,SAAS,KAAK,MAAM;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,SAAO;AAAA,YAAI;AAAA,YAAC,UAAS;AAAC,kBAAG,KAAK,UAAU,aAAW,QAAM,KAAK,OAAO,QAAO;AAAK,oBAAMD,KAAE,KAAK,UAAU,eAAe;AAAE,qBAAK,QAAM,KAAK,QAAQ,aAAW,KAAK,QAAQ,cAAY,KAAK,WAAU,MAAK,QAAQ,WAAW,aAAa,KAAK,QAAQ,WAAU,KAAK,OAAO;AAAE,oBAAMC,KAAE,KAAK,gBAAgB,EAAE,IAAE,KAAK,OAAK,MAAKC,KAAED,KAAEA,GAAE,OAAO,IAAE,GAAEE,KAAE,KAAK,gBAAgB,EAAE,IAAE,KAAK,OAAK,MAAK,IAAEA,KAAEA,GAAE,OAAK,IAAG,EAAC,UAAS,EAAC,IAAE,MAAK,IAAE,EAAE,KAAK,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE;AAAE,kBAAI;AAAE,kBAAG,EAAE,OAAK,EAAE,UAASF,GAAE,KAAEA,KAAG,KAAGE,QAAKF,GAAE,SAASA,GAAE,OAAO,GAAE,IAAE,CAAC,GAAEE,MAAGA,GAAE,OAAO;AAAA,uBAAWA,GAAE,KAAEA,IAAEA,GAAE,SAAS,GAAE,CAAC;AAAA,mBAAM;AAAC,sBAAMH,KAAE,SAAS,eAAe,CAAC;AAAE,oBAAE,KAAK,OAAO,OAAOA,EAAC,GAAE,KAAK,OAAO,aAAa,GAAE,IAAI;AAAA,cAAC;AAAC,kBAAG,KAAK,OAAO,GAAEA,IAAE;AAAC,sBAAMI,KAAE,CAACJ,IAAEI,OAAIH,MAAGD,OAAIC,GAAE,UAAQG,KAAEJ,OAAI,IAAEE,KAAEE,KAAE,IAAED,MAAGH,OAAIG,GAAE,UAAQD,KAAE,EAAE,SAAOE,KAAE,MAAKC,KAAED,GAAEJ,GAAE,MAAM,MAAKA,GAAE,MAAM,MAAM,GAAEM,KAAEF,GAAEJ,GAAE,IAAI,MAAKA,GAAE,IAAI,MAAM;AAAE,oBAAG,SAAOK,MAAG,SAAOC,GAAE,QAAM,EAAC,WAAU,EAAE,SAAQ,aAAYD,IAAE,SAAQ,EAAE,SAAQ,WAAUC,GAAC;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAI;AAAA,YAAC,OAAON,IAAEC,IAAE;AAAC,kBAAGD,GAAE,KAAM,CAAAA,OAAG,oBAAkBA,GAAE,QAAMA,GAAE,WAAS,KAAK,QAAS,GAAE;AAAC,sBAAMA,KAAE,KAAK,QAAQ;AAAE,gBAAAA,OAAIC,GAAE,QAAMD;AAAA,cAAE;AAAA,YAAC;AAAA,YAAC,SAASA,IAAE;AAAC,oBAAM,SAASA,EAAC;AAAE,kBAAG,EAAC,QAAOC,GAAC,IAAE;AAAK,qBAAKA,MAAG;AAAC,oBAAG,QAAMA,GAAE,QAAQ,SAAQ;AAAC,uBAAK,cAAY,EAAE,SAAS,QAAOA,GAAE,QAAQ,KAAK,OAAOA,EAAC,GAAE,KAAK,OAAO,CAAC,EAAE,OAAO,GAAE,KAAK,cAAY;AAAE;AAAA,gBAAK;AAAC,gBAAAA,KAAEA,GAAE;AAAA,cAAM;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,qBAAM;AAAA,YAAE;AAAA,UAAC;AAAC,UAAAA,GAAE,IAAE;AAAA,QAAC,GAAE,KAAI,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,IAAE;AAAA,UAAS,MAAM,UAAUC,GAAE,UAAS;AAAA,YAAC,YAAYH,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,cAAY,SAAS,cAAc,MAAM,GAAE,KAAK,YAAY,aAAa,mBAAkB,OAAO,GAAE,MAAM,KAAK,KAAK,QAAQ,UAAU,EAAE,QAAS,CAAAD,OAAG;AAAC,qBAAK,YAAY,YAAYA,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,YAAU,SAAS,eAAe,CAAC,GAAE,KAAK,aAAW,SAAS,eAAe,CAAC,GAAE,KAAK,QAAQ,YAAY,KAAK,SAAS,GAAE,KAAK,QAAQ,YAAY,KAAK,WAAW,GAAE,KAAK,QAAQ,YAAY,KAAK,UAAU;AAAA,YAAC;AAAA,YAAC,MAAMA,IAAEC,IAAE;AAAC,qBAAOD,OAAI,KAAK,YAAU,IAAEA,OAAI,KAAK,aAAW,IAAE,MAAM,MAAMA,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,QAAQD,IAAE;AAAC,kBAAIC,IAAEC,KAAE;AAAK,oBAAMC,KAAEH,GAAE,KAAK,MAAM,CAAC,EAAE,KAAK,EAAE;AAAE,kBAAGA,OAAI,KAAK,UAAU,KAAG,KAAK,gBAAgB,EAAE,GAAE;AAAC,sBAAMA,KAAE,KAAK,KAAK,OAAO;AAAE,qBAAK,KAAK,SAASA,IAAEG,EAAC,GAAED,KAAE,EAAC,WAAU,KAAK,KAAK,SAAQ,aAAYF,KAAEG,GAAE,OAAM;AAAA,cAAC,MAAM,CAAAF,KAAE,SAAS,eAAeE,EAAC,GAAE,KAAK,OAAO,aAAa,KAAK,OAAO,OAAOF,EAAC,GAAE,IAAI,GAAEC,KAAE,EAAC,WAAUD,IAAE,aAAYE,GAAE,OAAM;AAAA,kBAAO,CAAAH,OAAI,KAAK,eAAa,KAAK,gBAAgB,EAAE,KAAG,KAAK,KAAK,SAAS,GAAEG,EAAC,GAAED,KAAE,EAAC,WAAU,KAAK,KAAK,SAAQ,aAAYC,GAAE,OAAM,MAAIF,KAAE,SAAS,eAAeE,EAAC,GAAE,KAAK,OAAO,aAAa,KAAK,OAAO,OAAOF,EAAC,GAAE,KAAK,IAAI,GAAEC,KAAE,EAAC,WAAUD,IAAE,aAAYE,GAAE,OAAM;AAAI,qBAAOH,GAAE,OAAK,GAAEE;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,cAAAD,GAAE,QAAS,CAAAA,OAAG;AAAC,oBAAG,oBAAkBA,GAAE,SAAOA,GAAE,WAAS,KAAK,aAAWA,GAAE,WAAS,KAAK,aAAY;AAAC,wBAAME,KAAE,KAAK,QAAQF,GAAE,MAAM;AAAE,kBAAAE,OAAID,GAAE,QAAMC;AAAA,gBAAE;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAC,UAAAD,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,WAAU;AAAA,YAAC,OAAO,kBAAgB,CAAC,GAAE,EAAE,GAAEA,GAAE,WAAU,EAAE,CAAC;AAAA,YAAE,OAAO,QAAM,CAAC,UAAS,UAAS,QAAO,aAAY,UAAS,UAAS,QAAO,UAAS,MAAM;AAAA,YAAE,OAAO,QAAQH,IAAEC,IAAE;AAAC,oBAAMC,KAAE,EAAE,MAAM,QAAQF,EAAC,GAAEG,KAAE,EAAE,MAAM,QAAQF,EAAC;AAAE,qBAAOC,MAAG,KAAGC,MAAG,IAAED,KAAEC,KAAEH,OAAIC,KAAE,IAAED,KAAEC,KAAE,KAAG;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEE,IAAE;AAAC,kBAAG,EAAE,QAAQ,KAAK,QAAQ,UAASF,EAAC,IAAE,KAAG,KAAK,OAAO,MAAMA,IAAEC,GAAE,MAAM,IAAI,GAAE;AAAC,sBAAMA,KAAE,KAAK,QAAQH,IAAEC,EAAC;AAAE,gBAAAG,MAAGD,GAAE,KAAKD,IAAEE,EAAC;AAAA,cAAC,MAAM,OAAM,SAASJ,IAAEC,IAAEC,IAAEE,EAAC;AAAA,YAAC;AAAA,YAAC,SAASJ,IAAE;AAAC,kBAAG,MAAM,SAASA,EAAC,GAAE,KAAK,kBAAkB,KAAG,EAAE,QAAQ,KAAK,QAAQ,UAAS,KAAK,OAAO,QAAQ,QAAQ,IAAE,GAAE;AAAC,sBAAMA,KAAE,KAAK,OAAO,QAAQ,KAAK,OAAO,GAAE,KAAK,OAAO,CAAC;AAAE,qBAAK,aAAaA,EAAC,GAAEA,GAAE,KAAK,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAC,UAAAC,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,SAAQ;AAAA,UAAC;AAAC,gBAAM,IAAE,EAAC,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,KAAI,UAAS,KAAI,QAAO;AAAE,mBAAS,EAAEH,IAAE;AAAC,mBAAOA,GAAE,QAAQ,YAAY,CAAAA,OAAG,EAAEA,EAAC,CAAE;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,SAAQ,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,EAAE,CAAC,GAAE,IAAEA,GAAE,IAAI;AAAE,mBAAS,EAAEF,IAAE;AAAC,mBAAOA,cAAa,EAAE,MAAIA,cAAa,EAAE;AAAA,UAAE;AAAC,mBAAS,EAAEA,IAAE;AAAC,mBAAM,cAAY,OAAOA,GAAE;AAAA,UAAa;AAAA,UAAC,MAAM,UAAU,EAAE,WAAU;AAAA,YAAC,OAAO,WAAS;AAAA,YAAS,OAAO,YAAU;AAAA,YAAY,OAAO,UAAQ;AAAA,YAAM,OAAO,eAAa,EAAE;AAAA,YAAG,OAAO,kBAAgB,CAAC,EAAE,IAAG,EAAE,IAAG,EAAE,CAAC;AAAA,YAAE,YAAYA,IAAEC,IAAEC,IAAE;AAAC,kBAAG,EAAC,SAAQC,GAAC,IAAED;AAAE,oBAAMF,IAAEC,EAAC,GAAE,KAAK,UAAQE,IAAE,KAAK,QAAM,OAAG,KAAK,SAAS,GAAE,KAAK,OAAO,GAAE,KAAK,QAAQ,iBAAiB,aAAa,CAAAH,OAAG,KAAK,gBAAgBA,EAAC,CAAE;AAAA,YAAC;AAAA,YAAC,aAAY;AAAC,oBAAM,QAAQ,KAAK,KAAK,MAAI,KAAK,QAAM,CAAC;AAAA,YAAE;AAAA,YAAC,WAAU;AAAC,kBAAG,CAAC,KAAK,MAAM;AAAO,oBAAMA,KAAE,KAAK;AAAM,mBAAK,QAAM,OAAG,KAAK,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAE;AAAC,mBAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,mBAAkBA,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAE;AAAC,mBAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,qBAAoBA,EAAC;AAAA,YAAC;AAAA,YAAC,gBAAgBA,IAAEC,IAAE;AAAC,mBAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,qBAAoBD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAE;AAAC,oBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,KAAKH,EAAC,GAAE,CAACM,EAAC,IAAE,KAAK,KAAKN,KAAEC,EAAC;AAAE,kBAAG,MAAM,SAASD,IAAEC,EAAC,GAAE,QAAMK,MAAGJ,OAAII,MAAGH,KAAE,GAAE;AAAC,oBAAGD,cAAa,EAAE,MAAII,cAAa,EAAE,GAAG,QAAO,KAAK,KAAK,SAAS;AAAE,sBAAMN,KAAEM,GAAE,SAAS,gBAAgB,EAAE,IAAE,OAAKA,GAAE,SAAS;AAAK,gBAAAJ,GAAE,aAAaI,IAAEN,EAAC,GAAEE,GAAE,OAAO;AAAA,cAAC;AAAC,mBAAK,SAAS;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,kBAAIF,KAAE,EAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,MAAI,UAAU,CAAC;AAAE,mBAAK,QAAQ,aAAa,mBAAkBA,KAAE,SAAO,OAAO;AAAA,YAAC;AAAA,YAAC,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,oBAAM,SAASH,IAAEC,IAAEC,IAAEC,EAAC,GAAE,KAAK,SAAS;AAAA,YAAC;AAAA,YAAC,SAASH,IAAEC,IAAEC,IAAE;AAAC,kBAAGF,MAAG,KAAK,OAAO,EAAE,KAAG,QAAME,MAAG,QAAM,KAAK,OAAO,MAAMD,IAAE,EAAE,MAAM,KAAK,GAAE;AAAC,sBAAMD,KAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,aAAa,QAAQ;AAAE,qBAAK,YAAYA,EAAC,GAAE,QAAME,MAAGD,GAAE,SAAS,IAAI,IAAED,GAAE,SAAS,GAAEC,GAAE,MAAM,GAAE,EAAE,GAAEC,EAAC,IAAEF,GAAE,SAAS,GAAEC,IAAEC,EAAC;AAAA,cAAC,OAAK;AAAC,sBAAMF,KAAE,KAAK,OAAO,OAAOC,IAAEC,EAAC;AAAE,qBAAK,YAAYF,EAAC;AAAA,cAAC;AAAA,kBAAM,OAAM,SAASA,IAAEC,IAAEC,EAAC;AAAE,mBAAK,SAAS;AAAA,YAAC;AAAA,YAAC,aAAaF,IAAEC,IAAE;AAAC,kBAAGD,GAAE,QAAQ,UAAQ,EAAE,MAAM,aAAY;AAAC,sBAAME,KAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,aAAa,QAAQ;AAAE,gBAAAA,GAAE,YAAYF,EAAC,GAAE,MAAM,aAAaE,IAAED,EAAC;AAAA,cAAC,MAAM,OAAM,aAAaD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,eAAeD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,oBAAoBD,GAAE,OAAQ,KAAI,EAAE,KAAI,OAAO,IAAI,CAAC,CAAC,GAAEE,KAAED,GAAE,IAAI;AAAE,kBAAG,QAAMC,GAAE;AAAO,mBAAK,WAAW;AAAE,oBAAME,KAAEH,GAAE,MAAM;AAAE,kBAAGG,IAAE;AAAC,sBAAMJ,KAAE,YAAUI,GAAE,SAAO,MAAIA,GAAE,MAAM,OAAO,KAAG,CAAC,KAAK,WAAW,EAAE,IAAGL,EAAC,EAAE,CAAC,KAAGA,KAAE,KAAK,OAAO,IAAGE,KAAE,YAAUG,GAAE,OAAKA,GAAE,QAAO,KAAI,EAAE,KAAI,OAAO,EAAC,CAACA,GAAE,GAAG,GAAEA,GAAE,MAAK,CAAC;AAAE,kBAAE,MAAKL,IAAEE,EAAC;AAAE,sBAAMC,KAAE,YAAUE,GAAE,OAAK,IAAE,GAAEC,KAAEN,KAAEE,GAAE,OAAO,IAAEC;AAAE,gBAAAF,MAAG,KAAK,SAASK,KAAE,GAAE,IAAI;AAAE,sBAAMC,MAAG,GAAE,EAAE,IAAI,KAAK,KAAKP,EAAC,EAAE,CAAC,CAAC,GAAEQ,KAAE,EAAE,aAAa,KAAKD,IAAEF,GAAE,UAAU,KAAG,CAAC;AAAE,uBAAO,KAAKG,EAAC,EAAE,QAAS,CAAAR,OAAG;AAAC,uBAAK,SAASM,KAAE,GAAE,GAAEN,IAAEQ,GAAER,EAAC,CAAC;AAAA,gBAAC,CAAE,GAAEA,KAAEM;AAAA,cAAC;AAAC,kBAAG,CAACA,IAAEC,EAAC,IAAE,KAAK,SAAS,KAAKP,EAAC;AAAE,cAAAE,GAAE,WAASI,OAAIA,KAAEA,GAAE,MAAMC,EAAC,GAAEA,KAAE,IAAGL,GAAE,QAAS,CAAAF,OAAG;AAAC,oBAAG,YAAUA,GAAE,KAAK,GAAE,KAAK,YAAYA,GAAE,YAAWM,MAAG,MAAM,GAAE,GAAEN,GAAE,KAAK;AAAA,qBAAM;AAAC,wBAAMC,KAAE,KAAK,OAAOD,GAAE,KAAIA,GAAE,KAAK;AAAE,uBAAK,aAAaC,IAAEK,MAAG,MAAM,GAAE,OAAO,KAAKN,GAAE,UAAU,EAAE,QAAS,CAAAE,OAAG;AAAC,oBAAAD,GAAE,OAAOC,IAAEF,GAAE,WAAWE,EAAC,CAAC;AAAA,kBAAC,CAAE;AAAA,gBAAC;AAAA,cAAC,CAAE,IAAG,YAAUC,GAAE,QAAMA,GAAE,MAAM,OAAO,KAAG,EAAE,MAAKG,KAAEA,GAAE,OAAOA,GAAE,MAAM,IAAEC,KAAE,KAAK,OAAO,GAAEJ,GAAE,KAAK,GAAE,KAAK,SAAS,GAAE,KAAK,SAAS;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,qBAAM,WAAS,KAAK,QAAQ,aAAa,iBAAiB;AAAA,YAAC;AAAA,YAAC,KAAKH,IAAE;AAAC,oBAAMC,KAAE,KAAK,KAAKD,EAAC,EAAE,IAAI;AAAE,kBAAG,CAACC,GAAE,QAAM,CAAC,MAAK,EAAE;AAAE,oBAAK,CAACC,IAAEC,EAAC,IAAEF;AAAE,qBAAOC,cAAa,EAAE,WAAS,CAACA,IAAEC,EAAC,IAAE,CAAC,MAAK,EAAE;AAAA,YAAC;AAAA,YAAC,KAAKH,IAAE;AAAC,qBAAOA,OAAI,KAAK,OAAO,IAAE,KAAK,KAAKA,KAAE,CAAC,IAAE,KAAK,WAAW,GAAEA,EAAC;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,kBAAIA,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,OAAO;AAAU,oBAAMC,KAAE,CAACF,IAAEC,IAAEE,OAAI;AAAC,oBAAIC,KAAE,CAAC,GAAEC,KAAEF;AAAE,uBAAOH,GAAE,SAAS,UAAUC,IAAEE,IAAG,CAACH,IAAEC,IAAEE,OAAI;AAAC,oBAAEH,EAAC,IAAEI,GAAE,KAAKJ,EAAC,IAAEA,cAAa,EAAE,kBAAgBI,KAAEA,GAAE,OAAOF,GAAEF,IAAEC,IAAEI,EAAC,CAAC,IAAGA,MAAGF;AAAA,gBAAC,CAAE,GAAEC;AAAA,cAAC;AAAE,qBAAOF,GAAE,MAAKF,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,kBAAID,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,mBAAK,UAAQ,MAAM,SAASD,IAAEC,EAAC,GAAED,GAAE,SAAO,KAAG,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,iBAAgBA,IAAEC,EAAC;AAAA,YAAE;AAAA,YAAC,KAAKD,IAAE;AAAC,qBAAO,MAAM,KAAKA,EAAC,EAAE,MAAM,CAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAE;AAAC,kBAAG,KAAK,MAAM,QAAO,MAAK,MAAM,QAAQA,EAAC,MAAI,KAAK,QAAM,KAAK,MAAM,OAAOA,EAAC;AAAI,kBAAIC,KAAE,EAAE,EAAE,QAAQ;AAAK,0BAAU,OAAOD,OAAIC,KAAED,KAAG,MAAM,QAAQA,EAAC,MAAIA,KAAE,KAAK,SAAS,YAAY,KAAIA,KAAEA,GAAE,OAAQ,CAAAA,OAAG;AAAC,oBAAG,EAAC,QAAOC,GAAC,IAAED;AAAE,sBAAME,KAAE,KAAK,KAAKD,IAAE,IAAE;AAAE,uBAAOC,MAAG,CAAC,EAAEA,EAAC;AAAA,cAAC,CAAE,GAAG,SAAO,KAAG,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,sBAAqBD,IAAED,EAAC,GAAE,MAAM,OAAOA,GAAE,OAAO,CAAC,CAAC,CAAC,GAAEA,GAAE,SAAO,KAAG,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,eAAcC,IAAED,EAAC;AAAA,YAAC;AAAA,YAAC,cAAcA,IAAEC,IAAEC,IAAE;AAAC,oBAAK,CAACC,EAAC,IAAE,KAAK,WAAY,CAAAH,OAAGA,cAAa,EAAE,IAAIA,EAAC;AAAE,cAAAG,MAAGA,GAAE,QAAQ,aAAWF,MAAG,EAAEE,EAAC,KAAGA,GAAE,cAAcD,EAAC;AAAA,YAAC;AAAA,YAAC,gBAAgBF,IAAE;AAAC,cAAAA,GAAE,eAAe;AAAA,YAAC;AAAA,YAAC,oBAAoBA,IAAE;AAAC,oBAAMC,KAAE,CAAC;AAAE,kBAAIC,KAAE,KAAI,EAAE;AAAG,qBAAOF,GAAE,QAAS,CAAAA,OAAG;AAAC,sBAAMG,KAAEH,IAAG;AAAO,oBAAGG,GAAE,KAAG,YAAU,OAAOA,IAAE;AAAC,wBAAMC,KAAED,GAAE,MAAM,IAAI;AAAE,kBAAAC,GAAE,MAAM,GAAE,EAAE,EAAE,QAAS,CAAAD,OAAG;AAAC,oBAAAD,GAAE,OAAOC,IAAEH,GAAE,UAAU,GAAEC,GAAE,KAAK,EAAC,MAAK,SAAQ,OAAMC,IAAE,YAAWF,GAAE,cAAY,CAAC,EAAC,CAAC,GAAEE,KAAE,KAAI,EAAE;AAAA,kBAAE,CAAE;AAAE,wBAAMG,KAAED,GAAEA,GAAE,SAAO,CAAC;AAAE,kBAAAC,MAAGH,GAAE,OAAOG,IAAEL,GAAE,UAAU;AAAA,gBAAC,OAAK;AAAC,wBAAMI,KAAE,OAAO,KAAKD,EAAC,EAAE,CAAC;AAAE,sBAAG,CAACC,GAAE;AAAO,uBAAK,MAAMA,IAAE,EAAE,MAAM,MAAM,IAAEF,GAAE,KAAKF,EAAC,KAAGE,GAAE,OAAO,KAAGD,GAAE,KAAK,EAAC,MAAK,SAAQ,OAAMC,IAAE,YAAW,CAAC,EAAC,CAAC,GAAEA,KAAE,KAAI,EAAE,MAAGD,GAAE,KAAK,EAAC,MAAK,cAAa,KAAIG,IAAE,OAAMD,GAAEC,EAAC,GAAE,YAAWJ,GAAE,cAAY,CAAC,EAAC,CAAC;AAAA,gBAAE;AAAA,cAAC,CAAE,GAAEE,GAAE,OAAO,KAAGD,GAAE,KAAK,EAAC,MAAK,SAAQ,OAAMC,IAAE,YAAW,CAAC,EAAC,CAAC,GAAED;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,kBAAIC;AAAE,oBAAMC,KAAE,CAAC;AAAE,qBAAO,QAAQH,EAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,oBAAG,CAACC,IAAEG,EAAC,IAAEJ;AAAE,wBAAM,KAAK,MAAMC,IAAE,EAAE,MAAM,QAAM,EAAE,MAAM,IAAI,IAAEC,KAAED,KAAEE,GAAEF,EAAC,IAAEG;AAAA,cAAC,CAAE;AAAE,oBAAMA,KAAE,KAAK,OAAOF,MAAG,KAAK,QAAQ,aAAa,UAASA,KAAEF,GAAEE,EAAC,IAAE,MAAM;AAAE,mBAAK,aAAaE,IAAEH,MAAG,MAAM;AAAE,oBAAMI,KAAED,GAAE,OAAO;AAAE,qBAAO,OAAO,QAAQD,EAAC,EAAE,QAAS,CAAAH,OAAG;AAAC,oBAAG,CAACC,IAAEC,EAAC,IAAEF;AAAE,gBAAAI,GAAE,SAAS,GAAEC,IAAEJ,IAAEC,EAAC;AAAA,cAAC,CAAE,GAAEE;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEJ,IAAEC,IAAEC,IAAE;AAAC,YAAAA,GAAE,OAAQ,CAACD,IAAEC,OAAI;AAAC,oBAAMC,KAAE,EAAE,GAAG,OAAOD,EAAC;AAAE,kBAAIG,KAAEH,GAAE,cAAY,CAAC;AAAE,kBAAG,QAAMA,GAAE;AAAO,oBAAG,YAAU,OAAOA,GAAE,QAAO;AAAC,wBAAMC,KAAED,GAAE;AAAO,kBAAAF,GAAE,SAASC,IAAEE,EAAC;AAAE,wBAAK,CAACG,EAAC,IAAEN,GAAE,WAAW,EAAE,UAASC,EAAC,GAAEM,MAAG,GAAE,EAAE,IAAID,EAAC;AAAE,kBAAAD,KAAE,EAAE,aAAa,KAAKE,IAAEF,EAAC,KAAG,CAAC;AAAA,gBAAC,WAAS,YAAU,OAAOH,GAAE,QAAO;AAAC,wBAAMC,KAAE,OAAO,KAAKD,GAAE,MAAM,EAAE,CAAC;AAAE,sBAAG,QAAMC,GAAE,QAAOF;AAAE,sBAAGD,GAAE,SAASC,IAAEE,IAAED,GAAE,OAAOC,EAAC,CAAC,GAAE,QAAMH,GAAE,OAAO,MAAMG,IAAE,EAAE,MAAM,MAAM,GAAE;AAAC,0BAAK,CAACD,EAAC,IAAEF,GAAE,WAAW,EAAE,UAASC,EAAC,GAAEE,MAAG,GAAE,EAAE,IAAID,EAAC;AAAE,oBAAAG,KAAE,EAAE,aAAa,KAAKF,IAAEE,EAAC,KAAG,CAAC;AAAA,kBAAC;AAAA,gBAAC;AAAA;AAAC,qBAAO,OAAO,KAAKA,EAAC,EAAE,QAAS,CAAAH,OAAG;AAAC,gBAAAF,GAAE,SAASC,IAAEE,IAAED,IAAEG,GAAEH,EAAC,CAAC;AAAA,cAAC,CAAE,GAAED,KAAEE;AAAA,YAAC,GAAGF,EAAC;AAAA,UAAC;AAAC,cAAI,IAAE,GAAE,IAAEC,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS,EAAC,OAAM,KAAI,UAAS,KAAI,UAAS,MAAE;AAAA,YAAE,eAAa;AAAA,YAAE,eAAa;AAAA,YAAG,QAAM,EAAC,MAAK,CAAC,GAAE,MAAK,CAAC,EAAC;AAAA,YAAE,eAAa;AAAA,YAAK,YAAYF,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,MAAM,GAAGE,GAAE,GAAG,OAAO,eAAe,CAACH,IAAEC,IAAEC,IAAEE,OAAI;AAAC,gBAAAJ,OAAIG,GAAE,GAAG,OAAO,mBAAiBF,MAAGG,OAAID,GAAE,GAAG,QAAQ,WAAS,KAAK,eAAaF,MAAGD,OAAIG,GAAE,GAAG,OAAO,gBAAc,KAAK,iBAAe,KAAK,QAAQ,YAAUC,OAAID,GAAE,GAAG,QAAQ,OAAK,KAAK,UAAUF,EAAC,IAAE,KAAK,OAAOA,IAAEC,EAAC,IAAG,KAAK,eAAa,EAAE,KAAK,cAAaD,EAAC;AAAA,cAAE,CAAE,GAAE,KAAK,MAAM,SAAS,WAAW,EAAC,KAAI,KAAI,UAAS,KAAE,GAAE,KAAK,KAAK,KAAK,IAAI,CAAC,GAAE,KAAK,MAAM,SAAS,WAAW,EAAC,KAAI,CAAC,KAAI,GAAG,GAAE,UAAS,MAAG,UAAS,KAAE,GAAE,KAAK,KAAK,KAAK,IAAI,CAAC,GAAE,OAAO,KAAK,UAAU,QAAQ,KAAG,KAAK,MAAM,SAAS,WAAW,EAAC,KAAI,KAAI,UAAS,KAAE,GAAE,KAAK,KAAK,KAAK,IAAI,CAAC,GAAE,KAAK,MAAM,KAAK,iBAAiB,eAAe,CAAAD,OAAG;AAAC,kCAAgBA,GAAE,aAAW,KAAK,KAAK,GAAEA,GAAE,eAAe,KAAG,kBAAgBA,GAAE,cAAY,KAAK,KAAK,GAAEA,GAAE,eAAe;AAAA,cAAE,CAAE;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,kBAAG,MAAI,KAAK,MAAMD,EAAC,EAAE,OAAO;AAAO,oBAAME,KAAE,KAAK,MAAMF,EAAC,EAAE,IAAI;AAAE,kBAAG,CAACE,GAAE;AAAO,oBAAME,KAAE,KAAK,MAAM,YAAY,GAAEC,KAAEH,GAAE,MAAM,OAAOE,EAAC;AAAE,mBAAK,MAAMH,EAAC,EAAE,KAAK,EAAC,OAAMI,IAAE,OAAM,EAAEH,GAAE,OAAMG,EAAC,EAAC,CAAC,GAAE,KAAK,eAAa,GAAE,KAAK,eAAa,MAAG,KAAK,MAAM,eAAeH,GAAE,OAAMC,GAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,eAAa,OAAG,KAAK,iBAAiBD,EAAC;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,mBAAK,QAAM,EAAC,MAAK,CAAC,GAAE,MAAK,CAAC,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,eAAa;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,kBAAG,MAAID,GAAE,IAAI,OAAO;AAAO,mBAAK,MAAM,OAAK,CAAC;AAAE,kBAAIE,KAAEF,GAAE,OAAOC,EAAC,GAAEE,KAAE,KAAK;AAAa,oBAAMC,KAAE,KAAK,IAAI;AAAE,kBAAG,KAAK,eAAa,KAAK,QAAQ,QAAMA,MAAG,KAAK,MAAM,KAAK,SAAO,GAAE;AAAC,sBAAMJ,KAAE,KAAK,MAAM,KAAK,IAAI;AAAE,gBAAAA,OAAIE,KAAEA,GAAE,QAAQF,GAAE,KAAK,GAAEG,KAAEH,GAAE;AAAA,cAAM,MAAM,MAAK,eAAaI;AAAE,oBAAIF,GAAE,OAAO,MAAI,KAAK,MAAM,KAAK,KAAK,EAAC,OAAMA,IAAE,OAAMC,GAAC,CAAC,GAAE,KAAK,MAAM,KAAK,SAAO,KAAK,QAAQ,YAAU,KAAK,MAAM,KAAK,MAAM;AAAA,YAAE;AAAA,YAAC,OAAM;AAAC,mBAAK,OAAO,QAAO,MAAM;AAAA,YAAC;AAAA,YAAC,UAAUH,IAAE;AAAC,gBAAE,KAAK,MAAM,MAAKA,EAAC,GAAE,EAAE,KAAK,MAAM,MAAKA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,mBAAK,OAAO,QAAO,MAAM;AAAA,YAAC;AAAA,YAAC,iBAAiBA,IAAE;AAAC,kBAAGA,GAAE,MAAM,MAAK,MAAM,aAAaA,GAAE,OAAMG,GAAE,GAAG,QAAQ,IAAI;AAAA,mBAAM;AAAC,sBAAMF,KAAE,SAASD,IAAEC,IAAE;AAAC,wBAAMC,KAAED,GAAE,OAAQ,CAACD,IAAEC,OAAID,MAAGC,GAAE,UAAQ,IAAI,CAAC;AAAE,sBAAIE,KAAEF,GAAE,OAAO,IAAEC;AAAE,yBAAO,SAASF,IAAEC,IAAE;AAAC,0BAAMC,KAAED,GAAE,IAAIA,GAAE,IAAI,SAAO,CAAC;AAAE,2BAAO,QAAMC,OAAI,QAAMA,GAAE,SAAO,YAAU,OAAOA,GAAE,UAAQA,GAAE,OAAO,SAAS,IAAI,IAAE,QAAMA,GAAE,cAAY,OAAO,KAAKA,GAAE,UAAU,EAAE,KAAM,CAAAD,OAAG,QAAMD,GAAE,MAAMC,IAAE,EAAE,MAAM,KAAK,CAAE;AAAA,kBAAE,EAAED,IAAEC,EAAC,MAAIE,MAAG,IAAGA;AAAA,gBAAC,EAAE,KAAK,MAAM,QAAOH,GAAE,KAAK;AAAE,qBAAK,MAAM,aAAaC,IAAEE,GAAE,GAAG,QAAQ,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAEC,IAAE;AAAC,gBAAIC,KAAED;AAAE,qBAAQA,KAAED,GAAE,SAAO,GAAEC,MAAG,GAAEA,MAAG,GAAE;AAAC,oBAAME,KAAEH,GAAEC,EAAC;AAAE,cAAAD,GAAEC,EAAC,IAAE,EAAC,OAAMC,GAAE,UAAUC,GAAE,OAAM,IAAE,GAAE,OAAMA,GAAE,SAAO,EAAEA,GAAE,OAAMD,EAAC,EAAC,GAAEA,KAAEC,GAAE,MAAM,UAAUD,EAAC,GAAE,MAAIF,GAAEC,EAAC,EAAE,MAAM,OAAO,KAAGD,GAAE,OAAOC,IAAE,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,gBAAG,CAACD,GAAE,QAAOA;AAAE,kBAAME,KAAED,GAAE,kBAAkBD,GAAE,KAAK;AAAE,mBAAM,EAAC,OAAME,IAAE,QAAOD,GAAE,kBAAkBD,GAAE,QAAMA,GAAE,MAAM,IAAEE,GAAC;AAAA,UAAC;AAAC,cAAI,IAAEA,GAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,YAAYF,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAED,GAAE,KAAK,iBAAiB,QAAQ,CAAAC,OAAG;AAAC,gBAAAA,GAAE,eAAe;AAAE,oBAAIC,KAAE;AAAK,oBAAG,SAAS,oBAAoB,CAAAA,KAAE,SAAS,oBAAoBD,GAAE,SAAQA,GAAE,OAAO;AAAA,yBAAU,SAAS,wBAAuB;AAAC,wBAAMD,KAAE,SAAS,uBAAuBC,GAAE,SAAQA,GAAE,OAAO;AAAE,kBAAAC,KAAE,SAAS,YAAY,GAAEA,GAAE,SAASF,GAAE,YAAWA,GAAE,MAAM,GAAEE,GAAE,OAAOF,GAAE,YAAWA,GAAE,MAAM;AAAA,gBAAC;AAAC,sBAAMG,KAAED,MAAGF,GAAE,UAAU,gBAAgBE,EAAC;AAAE,oBAAGC,IAAE;AAAC,wBAAMD,KAAEF,GAAE,UAAU,kBAAkBG,EAAC;AAAE,kBAAAF,GAAE,cAAc,SAAO,KAAK,OAAOC,IAAED,GAAE,aAAa,KAAK;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,CAAC;AAAE,oBAAM,KAAKD,EAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,gBAAAA,MAAG,KAAK,QAAQ,WAAW,SAASA,GAAE,IAAI,KAAGE,GAAE,KAAKF,EAAC;AAAA,cAAC,CAAE,GAAEE,GAAE,SAAO,KAAG,KAAK,QAAQ,QAAQ,KAAK,MAAKF,IAAEE,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,WAAS,EAAC,WAAU,CAAC,aAAY,YAAY,GAAE,QAAQF,IAAEC,IAAE;AAAC,gBAAG,CAAC,KAAK,MAAM,OAAO,MAAM,OAAO,EAAE;AAAO,kBAAMC,KAAED,GAAE,IAAK,CAAAD,OAAG,IAAI,QAAS,CAAAC,OAAG;AAAC,oBAAMC,KAAE,IAAI;AAAW,cAAAA,GAAE,SAAO,MAAI;AAAC,gBAAAD,GAAEC,GAAE,MAAM;AAAA,cAAC,GAAEA,GAAE,cAAcF,EAAC;AAAA,YAAC,CAAE,CAAE;AAAE,oBAAQ,IAAIE,EAAC,EAAE,KAAM,CAAAD,OAAG;AAAC,oBAAMC,KAAED,GAAE,OAAQ,CAACD,IAAEC,OAAID,GAAE,OAAO,EAAC,OAAMC,GAAC,CAAC,GAAI,KAAI,EAAE,KAAI,OAAOD,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,CAAC;AAAE,mBAAK,MAAM,eAAeE,IAAE,EAAE,EAAE,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaF,GAAE,QAAMC,GAAE,QAAO,EAAE,EAAE,QAAQ,MAAM;AAAA,YAAC,CAAE;AAAA,UAAC,EAAC;AAAE,cAAI,IAAE;AAAE,gBAAM,IAAE,CAAC,cAAa,uBAAuB;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAED,GAAE,KAAK,iBAAiB,eAAe,CAAAA,OAAG;AAAC,qBAAK,kBAAkBA,EAAC;AAAA,cAAC,CAAE,GAAE,WAAW,KAAK,UAAU,SAAS,KAAGA,GAAE,GAAGG,GAAE,GAAG,OAAO,0BAA0B,MAAI;AAAC,qBAAK,uBAAuB;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,YAAYH,IAAE;AAAC,eAAC,GAAE,EAAE,IAAI,EAAC,OAAMA,IAAE,OAAM,KAAK,MAAK,CAAC;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAG,kBAAG,MAAID,GAAE,OAAO,QAAM;AAAG,kBAAGC,IAAE;AAAC,sBAAMC,KAAE,KAAK,MAAM,UAAUF,GAAE,OAAM,CAAC;AAAE,qBAAK,YAAYA,EAAC,GAAE,KAAK,MAAM,eAAgB,KAAI,EAAE,KAAI,OAAOA,GAAE,KAAK,EAAE,OAAOC,IAAEC,EAAC,GAAEC,GAAE,GAAG,QAAQ,IAAI;AAAA,cAAC,MAAM,MAAK,YAAYH,EAAC;AAAE,qBAAO,KAAK,MAAM,aAAaA,GAAE,QAAMC,GAAE,QAAO,GAAEE,GAAE,GAAG,QAAQ,MAAM,GAAE;AAAA,YAAE;AAAA,YAAC,kBAAkBH,IAAE;AAAC,kBAAG,KAAK,MAAM,YAAY,eAAaA,GAAE,oBAAkB,CAAC,EAAE,SAASA,GAAE,SAAS,EAAE;AAAO,oBAAMC,KAAED,GAAE,kBAAgBA,GAAE,gBAAgB,EAAE,CAAC,IAAE;AAAK,kBAAG,CAACC,MAAG,SAAKA,GAAE,UAAU;AAAO,oBAAMC,KAAE,SAASF,IAAE;AAAC,uBAAM,YAAU,OAAOA,GAAE,OAAKA,GAAE,OAAKA,GAAE,cAAc,MAAM,SAAS,YAAY,IAAEA,GAAE,aAAa,QAAQ,YAAY,IAAE;AAAA,cAAI,EAAEA,EAAC;AAAE,kBAAG,QAAME,GAAE;AAAO,oBAAMC,KAAE,KAAK,MAAM,UAAU,gBAAgBF,EAAC,GAAEG,KAAED,KAAE,KAAK,MAAM,UAAU,kBAAkBA,EAAC,IAAE;AAAK,cAAAC,MAAG,KAAK,YAAYA,IAAEF,EAAC,KAAGF,GAAE,eAAe;AAAA,YAAC;AAAA,YAAC,yBAAwB;AAAC,oBAAMA,KAAE,KAAK,MAAM,aAAa;AAAE,cAAAA,MAAG,KAAK,YAAYA,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,cAAI,IAAE;AAAE,gBAAM,IAAE,OAAO,KAAK,UAAU,QAAQ;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,cAAY;AAAA,YAAG,0BAAwB;AAAA,YAAE,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,gBAAgB,GAAE,KAAK,0BAA0B;AAAA,YAAC;AAAA,YAAC,kBAAiB;AAAC,mBAAK,MAAM,SAAS,WAAW,EAAC,KAAI,CAAC,aAAY,YAAY,GAAE,QAAO,GAAE,UAAS,MAAK,QAAQD,IAAEC,IAAE;AAAC,oBAAG,EAAC,MAAKC,IAAE,OAAME,GAAC,IAAEH;AAAE,oBAAG,EAAEC,cAAa,EAAE,cAAYA,GAAE,QAAQ,QAAM;AAAG,sBAAMG,KAAE,UAAQ,iBAAiBH,GAAE,OAAO,EAAE;AAAU,uBAAM,CAAC,EAAEG,MAAG,iBAAeD,GAAE,OAAK,CAACC,MAAG,gBAAcD,GAAE,SAAO,KAAK,MAAM,aAAaJ,GAAE,QAAM,GAAEA,GAAE,UAAQI,GAAE,WAAS,IAAE,IAAGD,GAAE,GAAG,QAAQ,IAAI,GAAE;AAAA,cAAG,EAAC,CAAC;AAAA,YAAC;AAAA,YAAC,4BAA2B;AAAC,mBAAK,MAAM,KAAK,iBAAiB,WAAW,CAAAH,OAAG;AAAC,iBAACA,GAAE,qBAAmB,CAAAA,OAAG,gBAAcA,GAAE,OAAK,iBAAeA,GAAE,OAAK,cAAYA,GAAE,OAAK,gBAAcA,GAAE,OAAK,WAASA,GAAE,OAAK,EAAE,CAAC,KAAG,QAAMA,GAAE,OAAK,SAAKA,GAAE,UAAUA,EAAC,KAAG,KAAK,iCAAiC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,mCAAkC;AAAC,mBAAK,0BAAwB,KAAK,IAAI,IAAE,KAAI,KAAK,gBAAc,KAAK,cAAY,MAAG,SAAS,iBAAiB,mBAAmB,MAAI;AAAC,qBAAK,cAAY,OAAG,KAAK,IAAI,KAAG,KAAK,2BAAyB,KAAK,sBAAsB;AAAA,cAAC,GAAG,EAAC,MAAK,KAAE,CAAC;AAAA,YAAE;AAAA,YAAC,wBAAuB;AAAC,oBAAMA,KAAE,SAAS,aAAa;AAAE,kBAAG,CAACA,GAAE;AAAO,oBAAMC,KAAED,GAAE,WAAW,CAAC;AAAE,kBAAG,SAAKC,GAAE,aAAW,MAAIA,GAAE,YAAY;AAAO,oBAAMC,KAAE,KAAK,MAAM,OAAO,KAAKD,GAAE,cAAc;AAAE,kBAAG,EAAEC,cAAa,EAAE,cAAYA,GAAE,QAAQ;AAAO,oBAAMC,KAAE,SAAS,YAAY;AAAE,cAAAA,GAAE,cAAcD,GAAE,MAAM,GAAEC,GAAE,YAAYD,GAAE,MAAM,GAAEF,GAAE,gBAAgB,GAAEA,GAAE,SAASG,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,cAAI,IAAE;AAAE,UAAAA,GAAE,GAAG,SAAS,EAAC,eAAc,EAAE,IAAG,qBAAoB,EAAE,IAAG,eAAc,EAAE,GAAE,mBAAkB,EAAE,GAAE,gBAAe,EAAE,GAAE,eAAc,EAAE,GAAE,gBAAe,EAAE,GAAE,gBAAe,GAAE,cAAa,EAAE,GAAE,qBAAoB,EAAE,IAAG,mBAAkB,GAAE,oBAAmB,EAAE,IAAG,oBAAmB,GAAE,iBAAgB,GAAE,kBAAiB,EAAC,CAAC;AAAE,cAAI,IAAEA,GAAE;AAAA,QAAE,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,KAAG,GAAEA,GAAE,IAAI,EAAE,GAAG,cAAc;AAAE,WAAC,mBAAkB,aAAY,WAAU,OAAO,EAAE,QAAS,CAAAF,OAAG;AAAC,qBAAS,iBAAiBA,IAAG,WAAU;AAAC,uBAAQA,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,oBAAM,KAAK,SAAS,iBAAiB,eAAe,CAAC,EAAE,QAAS,CAAAF,OAAG;AAAC,sBAAME,KAAE,EAAE,EAAE,IAAIF,EAAC;AAAE,gBAAAE,MAAGA,GAAE,WAASA,GAAE,QAAQ,UAAU,GAAGD,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,CAAE;AAAA,UAAC,CAAE;AAAE,cAAI,IAAE,cAAcE,GAAC;AAAA,YAAC,OAAO,SAAO,EAAC,eAAc,iBAAgB,sBAAqB,wBAAuB,mBAAkB,qBAAoB,qBAAoB,uBAAsB,iBAAgB,mBAAkB,eAAc,iBAAgB,qBAAoB,uBAAsB,kBAAiB,oBAAmB,aAAY,eAAc,0BAAyB,4BAA2B,mBAAkB,qBAAoB,wBAAuB,0BAAyB,iBAAgB,kBAAiB;AAAA,YAAE,OAAO,UAAQ,EAAC,KAAI,OAAM,QAAO,UAAS,MAAK,OAAM;AAAA,YAAE,cAAa;AAAC,oBAAM,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,GAAG,SAAQ,EAAE,KAAK;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,uBAAQH,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAEC,EAAC,IAAE,UAAUA,EAAC;AAAE,qBAAO,EAAE,IAAI,KAAK,GAAE,GAAGD,EAAC,GAAE,MAAM,KAAK,GAAGA,EAAC;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAE;AAAC,uBAAQC,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,KAAE,IAAEA,KAAE,IAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,eAAC,KAAK,aAAaH,GAAE,IAAI,KAAG,CAAC,GAAG,QAAS,CAAAC,OAAG;AAAC,oBAAG,EAAC,MAAKE,IAAE,SAAQC,GAAC,IAAEH;AAAE,iBAACD,GAAE,WAASG,MAAGA,GAAE,SAASH,GAAE,MAAM,MAAII,GAAEJ,IAAE,GAAGE,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,UAAUF,IAAEC,IAAEC,IAAE;AAAC,mBAAK,aAAaF,EAAC,MAAI,KAAK,aAAaA,EAAC,IAAE,CAAC,IAAG,KAAK,aAAaA,EAAC,EAAE,KAAK,EAAC,MAAKC,IAAE,SAAQC,GAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,oBAAI;AAAA,QAAO,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,gBAAMC,KAAE,CAAC,SAAQ,QAAO,OAAM,MAAM;AAAE,cAAIC,KAAE;AAAO,mBAAS,EAAEH,IAAE;AAAC,gBAAGG,MAAGD,GAAE,QAAQF,EAAC,KAAGE,GAAE,QAAQC,EAAC,GAAE;AAAC,uBAAQF,KAAE,UAAU,QAAOG,KAAE,IAAI,MAAMH,KAAE,IAAEA,KAAE,IAAE,CAAC,GAAEI,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,sBAAQL,EAAC,EAAE,GAAGI,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEJ,IAAE;AAAC,mBAAOE,GAAE,OAAQ,CAACD,IAAEC,QAAKD,GAAEC,EAAC,IAAE,EAAE,KAAK,SAAQA,IAAEF,EAAC,GAAEC,KAAI,CAAC,CAAC;AAAA,UAAC;AAAC,YAAE,QAAM,CAAAD,OAAG;AAAC,YAAAG,KAAEH;AAAA,UAAC,GAAE,EAAE,QAAM,EAAE,OAAMC,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,MAAK;AAAA,YAAC,OAAO,WAAS,CAAC;AAAA,YAAE,YAAYD,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,mBAAK,QAAMD,IAAE,KAAK,UAAQC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,EAAE,CAAC,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,IAAE;AAAW,mBAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,gBAAG,MAAIF,GAAE,QAAO;AAAC,oBAAK,CAACA,EAAC,IAAE,EAAEE,GAAE,IAAI,CAAC;AAAE,qBAAOD,MAAG,IAAE,UAAUD,EAAC,MAAI,UAAUA,EAAC,IAAI,EAAE,CAAC,GAAEC,KAAE,GAAEC,EAAC,CAAC;AAAA,YAAE;AAAC,kBAAK,CAAC,EAAC,OAAMC,IAAE,QAAOC,IAAE,QAAOC,IAAE,QAAOC,IAAE,MAAKC,GAAC,GAAE,GAAGC,EAAC,IAAER,IAAE,CAACS,IAAEC,EAAC,IAAE,EAAEH,EAAC;AAAE,gBAAGD,KAAEL,GAAE,QAAOC,GAAE,KAAKK,EAAC,GAAED,OAAIL,KAAE,IAAE,IAAIQ,EAAC,OAAOC,EAAC,IAAI,EAAEP,IAAEC,IAAEC,EAAC,CAAC,GAAG,EAAEG,IAAEF,IAAEJ,EAAC,CAAC,KAAG,IAAIO,EAAC,QAAQ,EAAET,IAAEC,KAAE,GAAEC,EAAC,CAAC;AAAG,kBAAMS,KAAET,GAAEA,GAAE,SAAO,CAAC;AAAE,gBAAGI,OAAIL,MAAGM,OAAII,GAAE,QAAM,WAAWD,EAAC,IAAI,EAAEP,IAAEC,IAAEC,EAAC,CAAC,GAAG,EAAEG,IAAEF,IAAEJ,EAAC,CAAC;AAAG,kBAAK,CAACU,EAAC,IAAE,EAAEV,GAAE,IAAI,CAAC;AAAE,mBAAM,UAAUU,EAAC,IAAI,EAAEZ,IAAEC,KAAE,GAAEC,EAAC,CAAC;AAAA,UAAE;AAAC,mBAAS,EAAEF,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,gBAAG,UAASH,MAAG,cAAY,OAAOA,GAAE,KAAK,QAAOA,GAAE,KAAKC,IAAEC,EAAC;AAAE,gBAAGF,cAAa,EAAE,EAAE,SAAO,GAAE,EAAE,GAAGA,GAAE,MAAM,EAAE,MAAMC,IAAEA,KAAEC,EAAC,CAAC,EAAE,WAAW,KAAI,QAAQ;AAAE,gBAAGF,cAAa,EAAE,YAAW;AAAC,kBAAG,qBAAmBA,GAAE,QAAQ,UAAS;AAAC,sBAAMG,KAAE,CAAC;AAAE,uBAAOH,GAAE,SAAS,UAAUC,IAAEC,IAAG,CAACF,IAAEC,IAAEC,OAAI;AAAC,wBAAME,KAAE,aAAYJ,MAAG,cAAY,OAAOA,GAAE,UAAQA,GAAE,QAAQ,IAAE,CAAC;AAAE,kBAAAG,GAAE,KAAK,EAAC,OAAMH,IAAE,QAAOC,IAAE,QAAOC,IAAE,QAAOE,GAAE,UAAQ,GAAE,MAAKA,GAAE,KAAI,CAAC;AAAA,gBAAC,CAAE,GAAE,EAAED,IAAE,IAAG,CAAC,CAAC;AAAA,cAAC;AAAC,oBAAMC,KAAE,CAAC;AAAE,kBAAGJ,GAAE,SAAS,UAAUC,IAAEC,IAAG,CAACF,IAAEC,IAAEC,OAAI;AAAC,gBAAAE,GAAE,KAAK,EAAEJ,IAAEC,IAAEC,EAAC,CAAC;AAAA,cAAC,CAAE,GAAEC,MAAG,WAASH,GAAE,QAAQ,SAAS,QAAOI,GAAE,KAAK,EAAE;AAAE,oBAAK,EAAC,WAAUC,IAAE,WAAUC,GAAC,IAAEN,GAAE,SAAQ,CAACO,IAAEC,EAAC,IAAEH,GAAE,MAAM,IAAIC,EAAC,GAAG;AAAE,qBAAM,aAAWC,KAAE,0CAA0CH,GAAE,KAAK,EAAE,CAAC,IAAII,EAAC,KAAG,GAAGD,EAAC,IAAIH,GAAE,KAAK,EAAE,CAAC,IAAII,EAAC;AAAA,YAAE;AAAC,mBAAOR,GAAE,mBAAmB,UAAQA,GAAE,QAAQ,YAAU;AAAA,UAAE;AAAC,mBAAS,EAAEA,IAAEC,IAAE;AAAC,mBAAO,OAAO,KAAKA,EAAC,EAAE,OAAQ,CAACC,IAAEC,OAAI;AAAC,kBAAG,QAAMH,GAAEG,EAAC,EAAE,QAAOD;AAAE,oBAAME,KAAEH,GAAEE,EAAC;AAAE,qBAAOC,OAAIJ,GAAEG,EAAC,IAAED,GAAEC,EAAC,IAAEC,KAAE,MAAM,QAAQA,EAAC,IAAEA,GAAE,QAAQJ,GAAEG,EAAC,CAAC,IAAE,IAAED,GAAEC,EAAC,IAAEC,GAAE,OAAO,CAACJ,GAAEG,EAAC,CAAC,CAAC,IAAED,GAAEC,EAAC,IAAEC,KAAEF,GAAEC,EAAC,IAAE,CAACC,IAAEJ,GAAEG,EAAC,CAAC,GAAED;AAAA,YAAC,GAAG,CAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAE;AAAC,kBAAMC,KAAE,cAAYD,KAAE,OAAK;AAAK,oBAAOA,IAAE;AAAA,cAAC,KAAI;AAAU,uBAAM,CAACC,IAAE,sBAAsB;AAAA,cAAE,KAAI;AAAY,uBAAM,CAACA,IAAE,wBAAwB;AAAA,cAAE;AAAQ,uBAAM,CAACA,IAAE,EAAE;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAE;AAAC,mBAAOA,GAAE,OAAQ,CAACA,IAAEC,OAAI;AAAC,kBAAG,YAAU,OAAOA,GAAE,QAAO;AAAC,sBAAMC,KAAED,GAAE,OAAO,QAAQ,SAAQ,IAAI,EAAE,QAAQ,OAAM,IAAI;AAAE,uBAAOD,GAAE,OAAOE,IAAED,GAAE,UAAU;AAAA,cAAC;AAAC,qBAAOD,GAAE,KAAKC,EAAC;AAAA,YAAC,GAAG,KAAI,EAAE,IAAE;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,gBAAG,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAEH;AAAE,mBAAO,IAAI,EAAE,EAAEE,KAAED,IAAEE,EAAC;AAAA,UAAC;AAAC,cAAI,IAAE,MAAK;AAAA,YAAC,YAAYH,IAAE;AAAC,mBAAK,SAAOA,IAAE,KAAK,QAAM,KAAK,SAAS;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAE;AAAC,mBAAK,OAAO,OAAO;AAAE,kBAAIC,KAAE,KAAK,OAAO,OAAO;AAAE,mBAAK,OAAO,WAAW;AAAE,oBAAMC,KAAE,EAAEF,EAAC,GAAEO,KAAE,KAAI,EAAE;AAAG,qBAAO,SAASP,IAAE;AAAC,sBAAMC,KAAE,CAAC;AAAE,uBAAOD,GAAE,QAAS,CAAAA,OAAG;AAAC,8BAAU,OAAOA,GAAE,SAAOA,GAAE,OAAO,MAAM,IAAI,EAAE,QAAS,CAACE,IAAEC,OAAI;AAAC,oBAAAA,MAAGF,GAAE,KAAK,EAAC,QAAO,MAAK,YAAWD,GAAE,WAAU,CAAC,GAAEE,MAAGD,GAAE,KAAK,EAAC,QAAOC,IAAE,YAAWF,GAAE,WAAU,CAAC;AAAA,kBAAC,CAAE,IAAEC,GAAE,KAAKD,EAAC;AAAA,gBAAC,CAAE,GAAEC;AAAA,cAAC,EAAEC,GAAE,IAAI,MAAM,CAAC,EAAE,OAAQ,CAACF,IAAEE,OAAI;AAAC,sBAAMI,KAAE,EAAE,GAAG,OAAOJ,EAAC;AAAE,oBAAIM,KAAEN,GAAE,cAAY,CAAC,GAAEQ,KAAE,OAAGC,KAAE;AAAG,oBAAG,QAAMT,GAAE,QAAO;AAAC,sBAAGK,GAAE,OAAOD,EAAC,GAAE,YAAU,OAAOJ,GAAE,QAAO;AAAC,0BAAMI,KAAEJ,GAAE;AAAO,oBAAAS,KAAE,CAACL,GAAE,SAAS,IAAI,MAAIL,MAAGD,MAAG,CAAC,CAAC,KAAK,OAAO,WAAW,EAAE,IAAGA,EAAC,EAAE,CAAC,IAAG,KAAK,OAAO,SAASA,IAAEM,EAAC;AAAE,0BAAK,CAACC,IAAEG,EAAC,IAAE,KAAK,OAAO,KAAKV,EAAC;AAAE,wBAAIY,MAAG,GAAET,GAAE,GAAG,CAAC,IAAG,GAAE,EAAE,IAAII,EAAC,CAAC;AAAE,wBAAGA,cAAa,EAAE,IAAG;AAAC,4BAAK,CAACP,EAAC,IAAEO,GAAE,WAAW,EAAE,UAASG,EAAC;AAAE,sBAAAV,OAAIY,MAAG,GAAET,GAAE,GAAGS,KAAG,GAAE,EAAE,IAAIZ,EAAC,CAAC;AAAA,oBAAE;AAAC,oBAAAQ,KAAE,EAAE,aAAa,KAAKI,IAAEJ,EAAC,KAAG,CAAC;AAAA,kBAAC,WAAS,YAAU,OAAON,GAAE,QAAO;AAAC,0BAAMI,KAAE,OAAO,KAAKJ,GAAE,MAAM,EAAE,CAAC;AAAE,wBAAG,QAAMI,GAAE,QAAON;AAAE,0BAAMO,KAAE,QAAM,KAAK,OAAO,MAAMD,IAAE,EAAE,MAAM,MAAM;AAAE,wBAAGC,GAAE,EAACN,MAAGD,MAAG,KAAK,OAAO,WAAW,EAAE,IAAGA,EAAC,EAAE,CAAC,OAAKW,KAAE;AAAA,6BAAYX,KAAE,GAAE;AAAC,4BAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,OAAO,WAAW,EAAE,UAASF,KAAE,CAAC;AAAE,sBAAAC,cAAa,EAAE,IAAE,SAAOA,GAAE,MAAM,EAAEC,EAAC,MAAIQ,KAAE,QAAIT,cAAa,EAAE,aAAWA,GAAE,QAAQ,UAAQ,EAAE,MAAM,gBAAcS,KAAE;AAAA,oBAAG;AAAC,wBAAG,KAAK,OAAO,SAASV,IAAEM,IAAEJ,GAAE,OAAOI,EAAC,CAAC,GAAEC,IAAE;AAAC,4BAAK,CAACN,EAAC,IAAE,KAAK,OAAO,WAAW,EAAE,UAASD,EAAC;AAAE,0BAAGC,IAAE;AAAC,8BAAMD,MAAG,GAAEG,GAAE,GAAG,CAAC,IAAG,GAAE,EAAE,IAAIF,EAAC,CAAC;AAAE,wBAAAO,KAAE,EAAE,aAAa,KAAKR,IAAEQ,EAAC,KAAG,CAAC;AAAA,sBAAC;AAAA,oBAAC;AAAA,kBAAC;AAAC,kBAAAP,MAAGK;AAAA,gBAAC,WAASC,GAAE,KAAKL,EAAC,GAAE,SAAOA,GAAE,UAAQ,YAAU,OAAOA,GAAE,QAAO;AAAC,wBAAMD,KAAE,OAAO,KAAKC,GAAE,MAAM,EAAE,CAAC;AAAE,sBAAG,QAAMD,GAAE,QAAOD;AAAE,uBAAK,OAAO,cAAcA,IAAEC,IAAEC,GAAE,OAAOD,EAAC,CAAC;AAAA,gBAAC;AAAC,uBAAO,KAAKO,EAAC,EAAE,QAAS,CAAAP,OAAG;AAAC,uBAAK,OAAO,SAASD,IAAEM,IAAEL,IAAEO,GAAEP,EAAC,CAAC;AAAA,gBAAC,CAAE;AAAE,sBAAMY,KAAEH,KAAE,IAAE,GAAEI,KAAEH,KAAE,IAAE;AAAE,uBAAOV,MAAGY,KAAEC,IAAEP,GAAE,OAAOM,EAAC,GAAEN,GAAE,OAAOO,EAAC,GAAEd,KAAEM,KAAEO,KAAEC;AAAA,cAAC,GAAG,CAAC,GAAEP,GAAE,OAAQ,CAACP,IAAEC,OAAI,YAAU,OAAOA,GAAE,UAAQ,KAAK,OAAO,SAASD,IAAEC,GAAE,MAAM,GAAED,MAAGA,KAAE,EAAE,GAAG,OAAOC,EAAC,GAAG,CAAC,GAAE,KAAK,OAAO,SAAS,GAAE,KAAK,OAAO,SAAS,GAAE,KAAK,OAAOC,EAAC;AAAA,YAAC;AAAA,YAAC,WAAWF,IAAEC,IAAE;AAAC,qBAAO,KAAK,OAAO,SAASD,IAAEC,EAAC,GAAE,KAAK,OAAQ,KAAI,EAAE,KAAI,OAAOD,EAAC,EAAE,OAAOC,EAAC,CAAC;AAAA,YAAC;AAAA,YAAC,WAAWD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,mBAAK,OAAO,OAAO,GAAE,OAAO,KAAKA,EAAC,EAAE,QAAS,CAAAC,OAAG;AAAC,qBAAK,OAAO,MAAMH,IAAE,KAAK,IAAIC,IAAE,CAAC,CAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,kBAAAA,GAAE,OAAOG,IAAED,GAAEC,EAAC,CAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,SAAS;AAAE,oBAAMA,KAAG,KAAI,EAAE,KAAI,OAAOH,EAAC,EAAE,OAAOC,KAAG,GAAE,EAAE,GAAGC,EAAC,CAAC;AAAE,qBAAO,KAAK,OAAOC,EAAC;AAAA,YAAC;AAAA,YAAC,WAAWH,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,qBAAO,KAAKA,EAAC,EAAE,QAAS,CAAAC,OAAG;AAAC,qBAAK,OAAO,SAASH,IAAEC,IAAEE,IAAED,GAAEC,EAAC,CAAC;AAAA,cAAC,CAAE;AAAE,oBAAMA,KAAG,KAAI,EAAE,KAAI,OAAOH,EAAC,EAAE,OAAOC,KAAG,GAAE,EAAE,GAAGC,EAAC,CAAC;AAAE,qBAAO,KAAK,OAAOC,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYH,IAAEC,IAAE;AAAC,qBAAO,KAAK,MAAM,MAAMD,IAAEA,KAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,qBAAO,KAAK,OAAO,MAAM,EAAE,OAAQ,CAACD,IAAEC,OAAID,GAAE,OAAOC,GAAE,MAAM,CAAC,GAAG,KAAI,EAAE,IAAE;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,oBAAIF,KAAE,KAAK,OAAO,KAAKD,EAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,sBAAK,CAACC,EAAC,IAAED;AAAE,gBAAAC,cAAa,EAAE,KAAGC,GAAE,KAAKD,EAAC,IAAEA,cAAa,EAAE,YAAUE,GAAE,KAAKF,EAAC;AAAA,cAAC,CAAE,KAAGC,KAAE,KAAK,OAAO,MAAMF,IAAEC,EAAC,GAAEE,KAAE,KAAK,OAAO,YAAY,EAAE,UAASH,IAAEC,EAAC;AAAG,oBAAK,CAACI,IAAEC,EAAC,IAAE,CAACJ,IAAEC,EAAC,EAAE,IAAK,CAAAH,OAAG;AAAC,sBAAMC,KAAED,GAAE,MAAM;AAAE,oBAAG,QAAMC,GAAE,QAAM,CAAC;AAAE,oBAAIC,MAAG,GAAE,EAAE,IAAID,EAAC;AAAE,uBAAK,OAAO,KAAKC,EAAC,EAAE,SAAO,KAAG;AAAC,wBAAMD,KAAED,GAAE,MAAM;AAAE,sBAAG,QAAMC,GAAE,QAAOC;AAAE,kBAAAA,KAAE,GAAG,GAAE,EAAE,IAAID,EAAC,GAAEC,EAAC;AAAA,gBAAC;AAAC,uBAAOA;AAAA,cAAC,CAAE;AAAE,qBAAM,kCAAIG,KAAKC;AAAA,YAAE;AAAA,YAAC,QAAQN,IAAEC,IAAE;AAAC,oBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,OAAO,KAAKH,EAAC;AAAE,kBAAGE,IAAE;AAAC,sBAAME,KAAEF,GAAE,OAAO;AAAE,uBAAOA,GAAE,OAAO,KAAGC,KAAEF,OAAI,MAAIE,MAAGF,OAAIG,MAAG,EAAEF,IAAEC,IAAEF,IAAE,IAAE,IAAE,EAAE,KAAK,QAAOD,IAAEC,IAAE,IAAE;AAAA,cAAC;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,QAAQD,IAAEC,IAAE;AAAC,qBAAO,KAAK,YAAYD,IAAEC,EAAC,EAAE,OAAQ,CAAAD,OAAG,YAAU,OAAOA,GAAE,MAAO,EAAE,IAAK,CAAAA,OAAGA,GAAE,MAAO,EAAE,KAAK,EAAE;AAAA,YAAC;AAAA,YAAC,eAAeA,IAAEC,IAAE;AAAC,oBAAMC,KAAE,EAAED,EAAC,GAAEE,KAAG,KAAI,EAAE,KAAI,OAAOH,EAAC,EAAE,OAAOE,EAAC;AAAE,qBAAO,KAAK,OAAO,eAAeF,IAAEE,EAAC,GAAE,KAAK,OAAOC,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYH,IAAEC,IAAEC,IAAE;AAAC,qBAAO,KAAK,OAAO,SAASF,IAAEC,IAAEC,EAAC,GAAE,KAAK,OAAQ,KAAI,EAAE,KAAI,OAAOF,EAAC,EAAE,OAAO,EAAC,CAACC,EAAC,GAAEC,GAAC,CAAC,CAAC;AAAA,YAAC;AAAA,YAAC,WAAWF,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,qBAAOD,KAAEA,GAAE,QAAQ,SAAQ,IAAI,EAAE,QAAQ,OAAM,IAAI,GAAE,KAAK,OAAO,SAASD,IAAEC,EAAC,GAAE,OAAO,KAAKC,EAAC,EAAE,QAAS,CAAAC,OAAG;AAAC,qBAAK,OAAO,SAASH,IAAEC,GAAE,QAAOE,IAAED,GAAEC,EAAC,CAAC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAQ,KAAI,EAAE,KAAI,OAAOH,EAAC,EAAE,OAAOC,KAAG,GAAE,EAAE,GAAGC,EAAC,CAAC,CAAC;AAAA,YAAC;AAAA,YAAC,UAAS;AAAC,kBAAG,MAAI,KAAK,OAAO,SAAS,OAAO,QAAM;AAAG,kBAAG,KAAK,OAAO,SAAS,SAAO,EAAE,QAAM;AAAG,oBAAMF,KAAE,KAAK,OAAO,SAAS;AAAK,kBAAGA,IAAG,QAAQ,aAAW,EAAE,GAAG,SAAS,QAAM;AAAG,oBAAMC,KAAED;AAAE,qBAAM,EAAEC,GAAE,SAAS,SAAO,MAAIA,GAAE,SAAS,gBAAgB,EAAE;AAAA,YAAC;AAAA,YAAC,aAAaD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,QAAQF,IAAEC,EAAC,GAAE,CAACE,IAAEC,EAAC,IAAE,KAAK,OAAO,KAAKJ,KAAEC,EAAC;AAAE,kBAAII,KAAE,GAAEE,KAAE,KAAI,EAAE;AAAG,sBAAMJ,OAAIE,KAAEF,GAAE,OAAO,IAAEC,IAAEG,KAAEJ,GAAE,MAAM,EAAE,MAAMC,IAAEA,KAAEC,KAAE,CAAC,EAAE,OAAO,IAAI;AAAG,oBAAMG,KAAE,KAAK,YAAYR,IAAEC,KAAEI,EAAC,EAAE,KAAM,KAAI,EAAE,KAAI,OAAOH,EAAC,EAAE,OAAOK,EAAC,CAAC,GAAEE,KAAG,KAAI,EAAE,KAAI,OAAOT,EAAC,EAAE,OAAOQ,EAAC;AAAE,qBAAO,KAAK,WAAWC,EAAC;AAAA,YAAC;AAAA,YAAC,OAAOT,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAO,oBAAMC,KAAE,KAAK;AAAM,kBAAG,MAAIF,GAAE,UAAQ,oBAAkBA,GAAE,CAAC,EAAE,QAAMA,GAAE,CAAC,EAAE,OAAO,KAAK,MAAM,CAAC,KAAG,KAAK,OAAO,KAAKA,GAAE,CAAC,EAAE,MAAM,GAAE;AAAC,sBAAMG,KAAE,KAAK,OAAO,KAAKH,GAAE,CAAC,EAAE,MAAM,GAAEI,MAAG,GAAE,EAAE,IAAID,EAAC,GAAEG,KAAEH,GAAE,OAAO,KAAK,MAAM,GAAEI,KAAEP,GAAE,CAAC,EAAE,SAAS,QAAQ,EAAE,EAAE,UAAS,EAAE,GAAES,KAAG,KAAI,EAAE,KAAI,OAAOF,EAAC,GAAEI,KAAG,KAAI,EAAE,KAAI,OAAOR,GAAE,MAAM,CAAC,GAAES,KAAEX,MAAG,EAAC,UAAS,EAAEA,GAAE,UAAS,CAACK,EAAC,GAAE,UAAS,EAAEL,GAAE,UAAS,CAACK,EAAC,EAAC;AAAE,gBAAAP,KAAG,KAAI,EAAE,KAAI,OAAOO,EAAC,EAAE,OAAOG,GAAE,KAAKE,IAAEC,EAAC,CAAC,EAAE,OAAQ,CAACb,IAAEC,OAAIA,GAAE,SAAOD,GAAE,OAAOC,GAAE,QAAOI,EAAC,IAAEL,GAAE,KAAKC,EAAC,GAAG,KAAI,EAAE,IAAE,GAAE,KAAK,QAAME,GAAE,QAAQH,EAAC;AAAA,cAAC,MAAM,MAAK,QAAM,KAAK,SAAS,GAAEA,OAAI,GAAE,EAAE,GAAGG,GAAE,QAAQH,EAAC,GAAE,KAAK,KAAK,MAAIA,KAAEG,GAAE,KAAK,KAAK,OAAMD,EAAC;AAAG,qBAAOF;AAAA,YAAC;AAAA,UAAC,GAAE,IAAEE,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAE,MAAK;AAAA,YAAC,cAAY;AAAA,YAAG,YAAYF,IAAEC,IAAE;AAAC,mBAAK,SAAOD,IAAE,KAAK,UAAQC,IAAE,KAAK,eAAe;AAAA,YAAC;AAAA,YAAC,iBAAgB;AAAC,mBAAK,OAAO,QAAQ,iBAAiB,oBAAoB,CAAAD,OAAG;AAAC,qBAAK,eAAa,KAAK,uBAAuBA,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,QAAQ,iBAAiB,kBAAkB,CAAAA,OAAG;AAAC,qBAAK,eAAa,eAAgB,MAAI;AAAC,uBAAK,qBAAqBA,EAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,uBAAuBA,IAAE;AAAC,oBAAMC,KAAED,GAAE,kBAAkB,OAAK,KAAK,OAAO,KAAKA,GAAE,QAAO,IAAE,IAAE;AAAK,eAACC,MAAGA,cAAa,EAAE,MAAI,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,0BAAyBD,EAAC,GAAE,KAAK,OAAO,WAAW,GAAE,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,mBAAkBA,EAAC,GAAE,KAAK,cAAY;AAAA,YAAG;AAAA,YAAC,qBAAqBA,IAAE;AAAC,mBAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,wBAAuBA,EAAC,GAAE,KAAK,OAAO,SAAS,GAAE,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,iBAAgBA,EAAC,GAAE,KAAK,cAAY;AAAA,YAAE;AAAA,UAAC,GAAE,IAAEE,GAAE,IAAI;AAAE,gBAAM,IAAE,CAAAF,OAAG;AAAC,kBAAMC,KAAED,GAAE,sBAAsB,GAAEE,KAAE,iBAAgBF,MAAG,KAAK,IAAIC,GAAE,KAAK,IAAED,GAAE,eAAa,GAAEG,KAAE,kBAAiBH,MAAG,KAAK,IAAIC,GAAE,MAAM,IAAED,GAAE,gBAAc;AAAE,mBAAM,EAAC,KAAIC,GAAE,KAAI,OAAMA,GAAE,OAAKD,GAAE,cAAYE,IAAE,QAAOD,GAAE,MAAID,GAAE,eAAaG,IAAE,MAAKF,GAAE,KAAI;AAAA,UAAC,GAAE,IAAE,CAAAD,OAAG;AAAC,kBAAMC,KAAE,SAASD,IAAE,EAAE;AAAE,mBAAO,OAAO,MAAMC,EAAC,IAAE,IAAEA;AAAA,UAAC,GAAE,IAAE,CAACD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,OAAIL,KAAEE,MAAGD,KAAEE,KAAE,IAAEH,KAAEE,KAAE,EAAEA,KAAEF,KAAEI,MAAGH,KAAEE,KAAEF,KAAED,KAAEG,KAAED,KAAEF,KAAEI,KAAEF,KAAED,KAAEE,KAAEE,KAAE;AAAE,gBAAM,IAAE,CAAC,SAAQ,SAAQ,UAAS,UAAS,UAAS,MAAM;AAAE,gBAAM,KAAG,GAAE,EAAE,GAAG,OAAO,GAAE,IAAE,IAAI,EAAE;AAAS,YAAE,WAAW,UAAQ;AAAA,UAAQ,MAAM,EAAC;AAAA,YAAC,OAAO,WAAS,EAAC,QAAO,MAAK,SAAQ,EAAC,WAAU,MAAG,UAAS,MAAG,SAAQ,MAAG,UAAS,KAAE,GAAE,aAAY,IAAG,UAAS,OAAG,UAAS,GAAE,OAAM,UAAS;AAAA,YAAE,OAAO,SAAO,EAAE,EAAE;AAAA,YAAO,OAAO,UAAQ,EAAE,EAAE;AAAA,YAAQ,OAAO,UAAQ;AAAA,YAAQ,OAAO,UAAQ,EAAC,OAAM,EAAE,GAAE,WAAU,GAAE,eAAc,EAAE,GAAE,cAAa,EAAE,EAAC;AAAA,YAAE,OAAO,MAAML,IAAE;AAAC,uBAAKA,OAAIA,KAAE,QAAO,EAAE,EAAE,MAAMA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,KAAKA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,qBAAO,EAAE,EAAE,IAAID,EAAC,KAAG,EAAE,KAAKA,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,OAAOD,IAAE;AAAC,qBAAO,QAAM,KAAK,QAAQA,EAAC,KAAG,EAAE,MAAM,iBAAiBA,EAAC,mCAAmC,GAAE,KAAK,QAAQA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,WAAU;AAAC,kBAAG,YAAU,QAAO,UAAU,UAAQ,IAAE,SAAO,UAAU,CAAC,IAAG;AAAC,sBAAMA,KAAE,UAAU,UAAQ,IAAE,SAAO,UAAU,CAAC,GAAEC,KAAE,CAAC,EAAE,UAAU,UAAQ,IAAE,SAAO,UAAU,CAAC,IAAGC,KAAE,cAAaF,KAAEA,GAAE,WAASA,GAAE;AAAS,4BAAU,OAAOE,KAAE,KAAK,SAAS,WAAWA,EAAC,IAAGF,IAAEC,EAAC,IAAE,OAAO,KAAKD,EAAC,EAAE,QAAS,CAAAE,OAAG;AAAC,uBAAK,SAASA,IAAEF,GAAEE,EAAC,GAAED,EAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,OAAK;AAAC,sBAAMD,KAAE,UAAU,UAAQ,IAAE,SAAO,UAAU,CAAC,GAAEC,KAAE,UAAU,UAAQ,IAAE,SAAO,UAAU,CAAC,GAAEC,KAAE,CAAC,EAAE,UAAU,UAAQ,IAAE,SAAO,UAAU,CAAC;AAAG,wBAAM,KAAK,QAAQF,EAAC,KAAGE,MAAG,EAAE,KAAK,eAAeF,EAAC,SAAQC,EAAC,GAAE,KAAK,QAAQD,EAAC,IAAEC,KAAGD,GAAE,WAAW,QAAQ,KAAGA,GAAE,WAAW,UAAU,MAAIC,MAAG,aAAW,OAAOA,MAAG,eAAaA,GAAE,YAAU,EAAE,SAASA,EAAC,GAAE,cAAY,OAAOA,GAAE,YAAUA,GAAE,SAAS,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,kBAAG,KAAK,UAAQ,SAASD,IAAEC,IAAE;AAAC,sBAAMC,KAAE,EAAEF,EAAC;AAAE,oBAAG,CAACE,GAAE,OAAM,IAAI,MAAM,yBAAyB;AAAE,sBAAMG,KAAE,CAACJ,GAAE,SAAOA,GAAE,UAAQ,EAAE,SAAS,QAAM,EAAE,IAAE,EAAE,OAAO,UAAUA,GAAE,KAAK,EAAE;AAAE,oBAAG,CAACI,GAAE,OAAM,IAAI,MAAM,iBAAiBJ,GAAE,KAAK,wBAAwB;AAAE,sBAAsB,OAAE,UAAlB,WAAQK,GADt1gC,IAC81gC,IAAHC,KAAA,UAAG,IAAH,CAAb,aAA4C,KAAAF,GAAE,UAAlB,WAAQG,GADl3gC,IAC03gC,IAAHC,KAAA,UAAG,IAAH,CAAb;AAA2B,oBAAIC,KAAE,EAAET,GAAE,OAAO;AAAE,wBAAMS,MAAGA,GAAE,WAASA,GAAE,QAAQ,gBAAc,WAASA,KAAE,iCAAIA,KAAJ,EAAM,SAAQ,EAAC,WAAUA,GAAE,QAAO,EAAC;AAAG,sBAAMC,MAAG,GAAER,GAAE,GAAG,CAAC,GAAE,EAAEG,EAAC,GAAE,EAAEE,EAAC,GAAEE,EAAC,GAAEE,KAAE,iDAAIL,KAAK,EAAEE,EAAC,IAAK,EAAER,EAAC;AAAG,oBAAIY,KAAEZ,GAAE;AAAS,uBAAOY,KAAEZ,GAAE,WAAS,EAAE,KAAK,2DAA2D,IAAEY,KAAEZ,GAAE,WAAS,CAACD,IAAEC,IAAEC,OAAI;AAAC,wBAAMC,KAAE,IAAI,EAAE;AAAS,yBAAO,EAAE,QAAS,CAAAH,OAAG;AAAC,0BAAME,KAAED,GAAE,MAAMD,EAAC;AAAE,oBAAAE,MAAGC,GAAE,SAASD,EAAC;AAAA,kBAAC,CAAE,GAAEF,GAAE,QAAS,CAAAA,OAAG;AAAC,wBAAII,KAAEH,GAAE,MAAMD,EAAC;AAAE,oBAAAI,MAAGF,GAAE,MAAM,oBAAoBF,EAAC,kEAAkE;AAAE,wBAAIK,KAAE;AAAE,2BAAKD,KAAG,KAAGD,GAAE,SAASC,EAAC,GAAEA,KAAE,cAAaA,KAAEA,GAAE,qBAAmB,OAAK,MAAKC,MAAG,GAAEA,KAAE,KAAI;AAAC,sBAAAH,GAAE,MAAM,0DAA0DF,EAAC,GAAG;AAAE;AAAA,oBAAK;AAAA,kBAAC,CAAE,GAAEG;AAAA,gBAAC,GAAGF,GAAE,SAAQW,GAAE,UAAS,CAAC,IAAEA,GAAE,UAAS,iCAAIA,KAAJ,EAAM,UAASC,IAAE,WAAUX,IAAE,OAAMG,IAAE,SAAQ,OAAO,QAAQM,EAAC,EAAE,OAAQ,CAACX,IAAEC,OAAI;AAAC,sBAAG,CAACC,IAAEE,EAAC,IAAEH;AAAE,sBAAG,CAACG,GAAE,QAAOJ;AAAE,wBAAMK,KAAE,EAAE,OAAO,WAAWH,EAAC,EAAE;AAAE,yBAAO,QAAMG,MAAG,EAAE,MAAM,eAAeH,EAAC,0CAA0C,GAAEF,MAAG,iCAAIA,KAAJ,EAAM,CAACE,EAAC,IAAG,GAAEC,GAAE,GAAG,CAAC,GAAEE,GAAE,YAAU,CAAC,GAAED,EAAC,EAAC;AAAA,gBAAC,GAAG,CAAC,CAAC,GAAE,QAAO,EAAEQ,GAAE,MAAM,EAAC;AAAA,cAAC,EAAEZ,IAAEC,EAAC,GAAE,KAAK,YAAU,KAAK,QAAQ,WAAU,QAAM,KAAK,UAAU,QAAO,KAAK,EAAE,MAAM,2BAA0BD,EAAC;AAAE,mBAAK,QAAQ,SAAO,EAAE,MAAM,KAAK,QAAQ,KAAK;AAAE,oBAAME,KAAE,KAAK,UAAU,UAAU,KAAK;AAAE,mBAAK,UAAU,UAAU,IAAI,cAAc,GAAE,KAAK,UAAU,YAAU,IAAG,EAAE,EAAE,IAAI,KAAK,WAAU,IAAI,GAAE,KAAK,OAAK,KAAK,aAAa,WAAW,GAAE,KAAK,KAAK,UAAU,IAAI,UAAU,GAAE,KAAK,UAAQ,IAAI,EAAE;AAAE,oBAAMG,KAAE,EAAE,WAAW,UAASE,KAAE,KAAK,QAAQ,SAAS,MAAMF,EAAC;AAAE,kBAAG,CAACE,MAAG,EAAE,cAAaA,IAAG,OAAM,IAAI,MAAM,oCAAoCF,EAAC,QAAQ;AAAE,kBAAG,KAAK,SAAO,IAAIE,GAAE,KAAK,QAAQ,UAAS,KAAK,MAAK,EAAC,SAAQ,KAAK,QAAO,CAAC,GAAE,KAAK,SAAO,IAAI,EAAE,KAAK,MAAM,GAAE,KAAK,YAAU,IAAI,EAAE,EAAE,KAAK,QAAO,KAAK,OAAO,GAAE,KAAK,cAAY,IAAI,EAAE,KAAK,QAAO,KAAK,OAAO,GAAE,KAAK,QAAM,IAAI,KAAK,QAAQ,MAAM,MAAK,KAAK,OAAO,GAAE,KAAK,WAAS,KAAK,MAAM,UAAU,UAAU,GAAE,KAAK,YAAU,KAAK,MAAM,UAAU,WAAW,GAAE,KAAK,UAAQ,KAAK,MAAM,UAAU,SAAS,GAAE,KAAK,WAAS,KAAK,MAAM,UAAU,UAAU,GAAE,KAAK,MAAM,UAAU,OAAO,GAAE,KAAK,MAAM,UAAU,QAAQ,GAAE,KAAK,MAAM,KAAK,GAAE,KAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,eAAe,CAAAP,OAAG;AAAC,gBAAAA,OAAI,EAAE,EAAE,OAAO,eAAa,KAAK,KAAK,UAAU,OAAO,YAAW,KAAK,OAAO,QAAQ,CAAC;AAAA,cAAC,CAAE,GAAE,KAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,eAAe,CAACA,IAAEC,OAAI;AAAC,sBAAMC,KAAE,KAAK,UAAU,WAAU,CAACC,EAAC,IAAE,KAAK,UAAU,SAAS,GAAEC,KAAEF,MAAGC,KAAE,EAAC,UAASD,IAAE,UAASC,GAAC,IAAE;AAAO,kBAAE,KAAK,MAAM,MAAI,KAAK,OAAO,OAAO,MAAKF,IAAEG,EAAC,GAAGJ,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,qBAAqB,CAACA,IAAEC,OAAI;AAAC,sBAAMC,KAAE,KAAK,UAAU,WAAU,CAACC,EAAC,IAAE,KAAK,UAAU,SAAS,GAAEC,KAAEF,MAAGC,KAAE,EAAC,UAASD,IAAE,UAASC,GAAC,IAAE;AAAO,kBAAE,KAAK,MAAM,MAAI;AAAC,wBAAMD,KAAG,KAAI,EAAE,KAAI,OAAOF,GAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAC,CAACA,GAAE,QAAQ,QAAQ,GAAEC,GAAC,CAAC;AAAE,yBAAO,KAAK,OAAO,OAAOC,IAAE,CAAC,GAAEE,EAAC;AAAA,gBAAC,GAAG,EAAE,QAAQ,IAAI;AAAA,cAAC,CAAE,GAAEF,IAAE;AAAC,sBAAMF,KAAE,KAAK,UAAU,QAAQ,EAAC,MAAK,GAAGE,EAAC,eAAc,MAAK,KAAI,CAAC;AAAE,qBAAK,YAAYF,EAAC;AAAA,cAAC;AAAC,mBAAK,QAAQ,MAAM,GAAE,KAAK,QAAQ,eAAa,KAAK,KAAK,aAAa,oBAAmB,KAAK,QAAQ,WAAW,GAAE,KAAK,QAAQ,YAAU,KAAK,QAAQ,GAAE,KAAK,qBAAmB;AAAA,YAAE;AAAA,YAAC,aAAaA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAK,kBAAG,YAAU,OAAOD,IAAE;AAAC,sBAAMC,KAAED;AAAE,iBAACA,KAAE,SAAS,cAAc,KAAK,GAAG,UAAU,IAAIC,EAAC;AAAA,cAAC;AAAC,qBAAO,KAAK,UAAU,aAAaD,IAAEC,EAAC,GAAED;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,mBAAK,UAAU,SAAS,IAAI;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAEC,IAAEC,IAAE;AAAC,qBAAM,CAACF,IAAEC,IAAE,EAACC,EAAC,IAAE,EAAEF,IAAEC,IAAEC,EAAC,GAAE,EAAE,KAAK,MAAM,MAAI,KAAK,OAAO,WAAWF,IAAEC,EAAC,GAAGC,IAAEF,IAAE,KAAGC,EAAC;AAAA,YAAC;AAAA,YAAC,UAAS;AAAC,mBAAK,OAAO,KAAE;AAAA,YAAC;AAAA,YAAC,aAAaD,IAAE;AAAC,mBAAK,qBAAmB;AAAG,oBAAMC,KAAED,GAAE;AAAE,qBAAO,KAAK,qBAAmB,OAAGC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,kBAAID,KAAE,EAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,MAAI,UAAU,CAAC;AAAE,mBAAK,OAAO,OAAOA,EAAC,GAAE,KAAK,UAAU,UAAU,OAAO,eAAc,CAACA,EAAC;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,kBAAIA,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,mBAAK,UAAU,MAAM,GAAEA,GAAE,iBAAe,KAAK,wBAAwB;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAI,qBAAO,EAAE,KAAK,MAAM,MAAI;AAAC,sBAAMA,KAAE,KAAK,aAAa,IAAE;AAAE,oBAAIC,KAAE,KAAI,EAAE;AAAG,oBAAG,QAAMD,GAAE,QAAOC;AAAE,oBAAG,KAAK,OAAO,MAAMH,IAAE,EAAE,MAAM,KAAK,EAAE,CAAAG,KAAE,KAAK,OAAO,WAAWD,GAAE,OAAMA,GAAE,QAAO,EAAC,CAACF,EAAC,GAAEC,GAAC,CAAC;AAAA,qBAAM;AAAC,sBAAG,MAAIC,GAAE,OAAO,QAAO,KAAK,UAAU,OAAOF,IAAEC,EAAC,GAAEE;AAAE,kBAAAA,KAAE,KAAK,OAAO,WAAWD,GAAE,OAAMA,GAAE,QAAO,EAAC,CAACF,EAAC,GAAEC,GAAC,CAAC;AAAA,gBAAC;AAAC,uBAAO,KAAK,aAAaC,IAAE,EAAE,EAAE,QAAQ,MAAM,GAAEC;AAAA,cAAC,GAAGD,EAAC;AAAA,YAAC;AAAA,YAAC,WAAWF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,kBAAIC;AAAE,qBAAM,CAACL,IAAEC,IAAEI,IAAED,EAAC,IAAE,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEC,EAAC,GAAE,EAAE,KAAK,MAAM,MAAI,KAAK,OAAO,WAAWJ,IAAEC,IAAEI,EAAC,GAAGD,IAAEJ,IAAE,CAAC;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,kBAAIC;AAAE,qBAAM,CAACL,IAAEC,IAAEI,IAAED,EAAC,IAAE,EAAEJ,IAAEC,IAAEC,IAAEC,IAAEC,EAAC,GAAE,EAAE,KAAK,MAAM,MAAI,KAAK,OAAO,WAAWJ,IAAEC,IAAEI,EAAC,GAAGD,IAAEJ,IAAE,CAAC;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE;AAAK,kBAAGA,KAAE,YAAU,OAAOF,KAAE,KAAK,UAAU,UAAUA,IAAEC,EAAC,IAAE,KAAK,UAAU,UAAUD,GAAE,OAAMA,GAAE,MAAM,GAAE,CAACE,GAAE,QAAO;AAAK,oBAAMC,KAAE,KAAK,UAAU,sBAAsB;AAAE,qBAAM,EAAC,QAAOD,GAAE,SAAOC,GAAE,KAAI,QAAOD,GAAE,QAAO,MAAKA,GAAE,OAAKC,GAAE,MAAK,OAAMD,GAAE,QAAMC,GAAE,MAAK,KAAID,GAAE,MAAIC,GAAE,KAAI,OAAMD,GAAE,MAAK;AAAA,YAAC;AAAA,YAAC,cAAa;AAAC,kBAAIF,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,KAAK,UAAU,IAAED;AAAE,qBAAM,CAACA,IAAEC,EAAC,IAAE,EAAED,IAAEC,EAAC,GAAE,KAAK,OAAO,YAAYD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,kBAAID,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,KAAK,aAAa,IAAE,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAE,qBAAM,YAAU,OAAOD,KAAE,KAAK,OAAO,UAAUA,IAAEC,EAAC,IAAE,KAAK,OAAO,UAAUD,GAAE,OAAMA,GAAE,MAAM;AAAA,YAAC;AAAA,YAAC,SAASA,IAAE;AAAC,qBAAOA,GAAE,OAAO,KAAK,MAAM;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,qBAAO,KAAK,OAAO,OAAO;AAAA,YAAC;AAAA,YAAC,QAAQA,IAAE;AAAC,qBAAO,KAAK,OAAO,KAAKA,EAAC;AAAA,YAAC;AAAA,YAAC,QAAQA,IAAE;AAAC,qBAAO,KAAK,OAAO,KAAKA,EAAC;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,kBAAIA,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,OAAO;AAAU,qBAAM,YAAU,OAAOD,KAAE,KAAK,OAAO,MAAMA,GAAE,OAAMA,GAAE,MAAM,IAAE,KAAK,OAAO,MAAMA,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAE;AAAC,qBAAO,KAAK,MAAM,QAAQA,EAAC;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,qBAAO,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC,KAAG,KAAK,MAAM,GAAE,KAAK,OAAO,GAAE,KAAK,UAAU,SAAS,EAAE,CAAC;AAAA,YAAC;AAAA,YAAC,kBAAiB;AAAC,kBAAIA,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE;AAAO,qBAAM,YAAU,OAAOD,OAAIC,KAAEA,MAAG,KAAK,UAAU,IAAED,KAAG,CAACA,IAAEC,EAAC,IAAE,EAAED,IAAEC,EAAC,GAAE,KAAK,OAAO,QAAQD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,UAAS;AAAC,kBAAID,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,GAAEC,KAAE,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE;AAAO,qBAAM,YAAU,OAAOD,OAAIC,KAAEA,MAAG,KAAK,UAAU,IAAED,KAAG,CAACA,IAAEC,EAAC,IAAE,EAAED,IAAEC,EAAC,GAAE,KAAK,OAAO,QAAQD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,qBAAO,KAAK,UAAU,SAAS;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,QAAQ;AAAI,qBAAO,EAAE,KAAK,MAAM,MAAI,KAAK,OAAO,YAAYH,IAAEC,IAAEC,EAAC,GAAGC,IAAEH,EAAC;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,kBAAIC;AAAE,qBAAM,CAACL,IAAE,EAACK,IAAED,EAAC,IAAE,EAAEJ,IAAE,GAAEE,IAAEC,IAAEC,EAAC,GAAE,EAAE,KAAK,MAAM,MAAI,KAAK,OAAO,WAAWJ,IAAEC,IAAEI,EAAC,GAAGD,IAAEJ,IAAEC,GAAE,MAAM;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,qBAAO,KAAK,OAAO,UAAU;AAAA,YAAC;AAAA,YAAC,MAAK;AAAC,qBAAO,KAAK,QAAQ,IAAI,GAAG,SAAS;AAAA,YAAC;AAAA,YAAC,KAAI;AAAC,qBAAO,KAAK,QAAQ,GAAG,GAAG,SAAS;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,qBAAO,KAAK,QAAQ,KAAK,GAAG,SAAS;AAAA,YAAC;AAAA,YAAC,aAAaD,IAAEC,IAAEC,IAAE;AAAC,qBAAM,CAACF,IAAEC,IAAE,EAACC,EAAC,IAAE,EAAEF,IAAEC,IAAEC,EAAC,GAAE,EAAE,KAAK,MAAM,MAAI,KAAK,OAAO,aAAaF,IAAEC,EAAC,GAAGC,IAAEF,EAAC;AAAA,YAAC;AAAA,YAAC,mBAAmBA,IAAE;AAAC,eAAC,CAACA,IAAEC,OAAI;AAAC,sBAAMC,KAAEF,GAAE;AAAc,oBAAIG,KAAEF,IAAEG,KAAEJ;AAAE,uBAAKI,MAAG;AAAC,wBAAMJ,KAAEI,OAAIF,GAAE,MAAKD,KAAED,KAAE,EAAC,KAAI,GAAE,OAAM,OAAO,gBAAgB,SAAOE,GAAE,gBAAgB,aAAY,QAAO,OAAO,gBAAgB,UAAQA,GAAE,gBAAgB,cAAa,MAAK,EAAC,IAAE,EAAEE,EAAC,GAAEE,KAAE,iBAAiBF,EAAC,GAAEG,KAAE,EAAEJ,GAAE,MAAKA,GAAE,OAAMF,GAAE,MAAKA,GAAE,OAAM,EAAEK,GAAE,iBAAiB,GAAE,EAAEA,GAAE,kBAAkB,CAAC,GAAEE,KAAE,EAAEL,GAAE,KAAIA,GAAE,QAAOF,GAAE,KAAIA,GAAE,QAAO,EAAEK,GAAE,gBAAgB,GAAE,EAAEA,GAAE,mBAAmB,CAAC;AAAE,sBAAGC,MAAGC,GAAE,KAAGR,GAAE,CAAAE,GAAE,aAAa,SAASK,IAAEC,EAAC;AAAA,uBAAM;AAAC,0BAAK,EAAC,YAAWR,IAAE,WAAUC,GAAC,IAAEG;AAAE,oBAAAI,OAAIJ,GAAE,aAAWI,KAAGD,OAAIH,GAAE,cAAYG;AAAG,0BAAML,KAAEE,GAAE,aAAWJ,IAAEK,KAAED,GAAE,YAAUH;AAAE,oBAAAE,KAAE,EAAC,MAAKA,GAAE,OAAKD,IAAE,KAAIC,GAAE,MAAIE,IAAE,OAAMF,GAAE,QAAMD,IAAE,QAAOC,GAAE,SAAOE,GAAC;AAAA,kBAAC;AAAC,kBAAAD,KAAEJ,MAAG,YAAUM,GAAE,WAAS,QAAMD,KAAED,IAAG,iBAAeC,GAAE,YAAY,EAAE,QAAM;AAAA,gBAAI;AAAC,oBAAIA;AAAA,cAAC,GAAG,KAAK,MAAKL,EAAC;AAAA,YAAC;AAAA,YAAC,iBAAgB;AAAC,sBAAQ,KAAK,wIAAwI,GAAE,KAAK,wBAAwB;AAAA,YAAC;AAAA,YAAC,0BAAyB;AAAC,oBAAMA,KAAE,KAAK,UAAU,WAAUC,KAAED,MAAG,KAAK,UAAU,UAAUA,GAAE,OAAMA,GAAE,MAAM;AAAE,cAAAC,MAAG,KAAK,mBAAmBA,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAI,qBAAO,EAAE,KAAK,MAAM,MAAI;AAAC,gBAAAD,KAAE,KAAI,EAAE,GAAGA,EAAC;AAAE,sBAAMC,KAAE,KAAK,UAAU,GAAEC,KAAE,KAAK,OAAO,WAAW,GAAED,EAAC,GAAEE,KAAE,KAAK,OAAO,eAAe,GAAEH,EAAC,GAAEI,KAAE,KAAK,OAAO,WAAW,KAAK,UAAU,IAAE,GAAE,CAAC;AAAE,uBAAOF,GAAE,QAAQC,EAAC,EAAE,QAAQC,EAAC;AAAA,cAAC,GAAGH,EAAC;AAAA,YAAC;AAAA,YAAC,aAAaD,IAAEC,IAAEC,IAAE;AAAC,sBAAMF,KAAE,KAAK,UAAU,SAAS,MAAKC,MAAG,EAAE,QAAQ,GAAG,KAAG,CAACD,IAAEC,IAAE,EAACC,EAAC,IAAE,EAAEF,IAAEC,IAAEC,EAAC,GAAE,KAAK,UAAU,SAAS,IAAI,EAAE,EAAE,KAAK,IAAI,GAAEF,EAAC,GAAEC,EAAC,GAAEC,EAAC,GAAEA,OAAI,EAAE,EAAE,QAAQ,UAAQ,KAAK,wBAAwB;AAAA,YAAE;AAAA,YAAC,QAAQF,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAI,oBAAMC,KAAG,KAAI,EAAE,KAAI,OAAOF,EAAC;AAAE,qBAAO,KAAK,YAAYE,IAAED,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,kBAAID,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAK,oBAAMC,KAAE,KAAK,OAAO,OAAOD,EAAC;AAAE,qBAAO,KAAK,UAAU,OAAOA,EAAC,GAAEC;AAAA,YAAC;AAAA,YAAC,eAAeD,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAI,qBAAO,EAAE,KAAK,MAAM,OAAKD,KAAE,KAAI,EAAE,GAAGA,EAAC,GAAE,KAAK,OAAO,WAAWA,EAAC,IAAIC,IAAE,IAAE;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAE;AAAC,mBAAM,YAAU,OAAOA,KAAE,SAAS,cAAcA,EAAC,IAAEA;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,mBAAO,OAAO,QAAQA,MAAG,CAAC,CAAC,EAAE,OAAQ,CAACA,IAAEC,OAAI;AAAC,kBAAG,CAACC,IAAEC,EAAC,IAAEF;AAAE,qBAAM,iCAAID,KAAJ,EAAM,CAACE,EAAC,GAAE,SAAKC,KAAE,CAAC,IAAEA,GAAC;AAAA,YAAC,GAAG,CAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAE;AAAC,mBAAO,OAAO,YAAY,OAAO,QAAQA,EAAC,EAAE,OAAQ,CAAAA,OAAG,WAASA,GAAE,CAAC,CAAE,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAG,CAAC,KAAK,UAAU,KAAGF,OAAI,EAAE,EAAE,QAAQ,QAAM,CAAC,KAAK,mBAAmB,QAAO,KAAI,EAAE;AAAG,gBAAIG,KAAE,QAAMF,KAAE,OAAK,KAAK,aAAa;AAAE,kBAAMG,KAAE,KAAK,OAAO,OAAME,KAAEP,GAAE;AAAE,gBAAG,QAAMI,OAAI,SAAKF,OAAIA,KAAEE,GAAE,QAAO,QAAMD,KAAEC,KAAE,EAAEA,IAAEG,IAAEN,EAAC,IAAE,MAAIE,OAAIC,KAAE,EAAEA,IAAEF,IAAEC,IAAEF,EAAC,IAAG,KAAK,aAAaG,IAAE,EAAE,EAAE,QAAQ,MAAM,IAAGG,GAAE,OAAO,IAAE,GAAE;AAAC,oBAAMP,KAAE,CAAC,EAAE,EAAE,OAAO,aAAYO,IAAEF,IAAEJ,EAAC;AAAE,mBAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,eAAc,GAAGD,EAAC,GAAEC,OAAI,EAAE,EAAE,QAAQ,UAAQ,KAAK,QAAQ,KAAK,GAAGD,EAAC;AAAA,YAAC;AAAC,mBAAOO;AAAA,UAAC;AAAC,mBAAS,EAAEP,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE,CAAC;AAAE,mBAAM,YAAU,OAAOL,GAAE,SAAO,YAAU,OAAOA,GAAE,SAAO,YAAU,OAAOC,MAAGG,KAAED,IAAEA,KAAED,IAAEA,KAAED,IAAEA,KAAED,GAAE,QAAOA,KAAEA,GAAE,UAAQC,KAAED,GAAE,QAAOA,KAAEA,GAAE,SAAO,YAAU,OAAOC,OAAIG,KAAED,IAAEA,KAAED,IAAEA,KAAED,IAAEA,KAAE,IAAG,YAAU,OAAOC,MAAGG,KAAEH,IAAEE,KAAED,MAAG,YAAU,OAAOD,OAAI,QAAMC,KAAEE,GAAEH,EAAC,IAAEC,KAAEC,KAAEF,KAAG,CAACF,IAAEC,IAAEI,IAAED,KAAEA,MAAG,EAAE,EAAE,QAAQ,GAAG;AAAA,UAAC;AAAC,mBAAS,EAAEJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAE,YAAU,OAAOF,KAAEA,KAAE;AAAE,gBAAG,QAAMF,GAAE,QAAO;AAAK,gBAAIK,IAAEC;AAAE,mBAAOL,MAAG,cAAY,OAAOA,GAAE,oBAAkB,CAACI,IAAEC,EAAC,IAAE,CAACN,GAAE,OAAMA,GAAE,QAAMA,GAAE,MAAM,EAAE,IAAK,CAAAA,OAAGC,GAAE,kBAAkBD,IAAEG,OAAI,EAAE,EAAE,QAAQ,IAAI,CAAE,IAAE,CAACE,IAAEC,EAAC,IAAE,CAACN,GAAE,OAAMA,GAAE,QAAMA,GAAE,MAAM,EAAE,IAAK,CAAAA,OAAGA,KAAEC,MAAGD,OAAIC,MAAGE,OAAI,EAAE,EAAE,QAAQ,OAAKH,KAAEI,MAAG,IAAEJ,KAAEI,KAAE,KAAK,IAAIH,IAAED,KAAEI,EAAC,CAAE,GAAE,IAAI,EAAE,EAAEC,IAAEC,KAAED,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASL,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,KAAG,GAAEA,GAAE,IAAI,EAAE,GAAG,iBAAiB;AAAA,UAAE,MAAM,EAAC;AAAA,YAAC,YAAYF,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAE,mBAAK,QAAMD,IAAE,KAAK,SAAOC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,gBAAG;AAAC,cAAAA,GAAE;AAAA,YAAU,SAAOD,IAAE;AAAC,qBAAM;AAAA,YAAE;AAAC,mBAAOA,GAAE,SAASC,EAAC;AAAA,UAAC;AAAC,UAAAA,GAAE,IAAE,MAAK;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,mBAAK,UAAQA,IAAE,KAAK,SAAOD,IAAE,KAAK,YAAU,OAAG,KAAK,YAAU,OAAG,KAAK,OAAK,KAAK,OAAO,SAAQ,KAAK,SAAO,KAAK,OAAO,OAAO,UAAS,IAAI,GAAE,KAAK,aAAW,IAAI,EAAE,GAAE,CAAC,GAAE,KAAK,YAAU,KAAK,YAAW,KAAK,aAAW,MAAK,KAAK,kBAAkB,GAAE,KAAK,eAAe,GAAE,KAAK,QAAQ,UAAU,mBAAkB,UAAU,MAAI;AAAC,qBAAK,aAAW,KAAK,aAAW,WAAW,KAAK,OAAO,KAAK,MAAK,EAAE,EAAE,QAAQ,IAAI,GAAE,CAAC;AAAA,cAAC,CAAE,GAAE,KAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,sBAAsB,MAAI;AAAC,oBAAG,CAAC,KAAK,SAAS,EAAE;AAAO,sBAAMA,KAAE,KAAK,eAAe;AAAE,wBAAMA,MAAGA,GAAE,MAAM,SAAO,KAAK,OAAO,YAAU,KAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,eAAe,CAACC,IAAEC,OAAI;AAAC,sBAAG;AAAC,yBAAK,KAAK,SAASF,GAAE,MAAM,IAAI,KAAG,KAAK,KAAK,SAASA,GAAE,IAAI,IAAI,KAAG,KAAK,eAAeA,GAAE,MAAM,MAAKA,GAAE,MAAM,QAAOA,GAAE,IAAI,MAAKA,GAAE,IAAI,MAAM;AAAE,0BAAMG,KAAED,GAAE,KAAM,CAAAF,OAAG,oBAAkBA,GAAE,QAAM,gBAAcA,GAAE,QAAM,iBAAeA,GAAE,QAAMA,GAAE,WAAS,KAAK,IAAK;AAAE,yBAAK,OAAOG,KAAE,EAAE,EAAE,QAAQ,SAAOF,EAAC;AAAA,kBAAC,SAAOD,IAAE;AAAA,kBAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE,GAAE,KAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,iBAAiB,CAACA,IAAEC,OAAI;AAAC,oBAAGA,GAAE,OAAM;AAAC,wBAAK,EAAC,WAAUD,IAAE,aAAYE,IAAE,SAAQC,IAAE,WAAUC,GAAC,IAAEH,GAAE;AAAM,uBAAK,eAAeD,IAAEE,IAAEC,IAAEC,EAAC,GAAE,KAAK,OAAO,EAAE,EAAE,QAAQ,MAAM;AAAA,gBAAC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,EAAE,EAAE,QAAQ,MAAM;AAAA,YAAC;AAAA,YAAC,oBAAmB;AAAC,mBAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,0BAA0B,MAAI;AAAC,qBAAK,YAAU;AAAA,cAAE,CAAE,GAAE,KAAK,QAAQ,GAAG,EAAE,EAAE,OAAO,iBAAiB,MAAI;AAAC,oBAAG,KAAK,YAAU,OAAG,KAAK,OAAO,QAAO;AAAC,wBAAMJ,KAAE,KAAK,OAAO,QAAQ;AAAE,sBAAG,CAACA,GAAE;AAAO,6BAAY,MAAI;AAAC,yBAAK,eAAeA,GAAE,WAAUA,GAAE,aAAYA,GAAE,SAAQA,GAAE,SAAS;AAAA,kBAAC,GAAG,CAAC;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,iBAAgB;AAAC,mBAAK,QAAQ,UAAU,aAAY,SAAS,MAAM,MAAI;AAAC,qBAAK,YAAU;AAAA,cAAE,CAAE,GAAE,KAAK,QAAQ,UAAU,WAAU,SAAS,MAAM,MAAI;AAAC,qBAAK,YAAU,OAAG,KAAK,OAAO,EAAE,EAAE,QAAQ,IAAI;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,mBAAK,SAAS,MAAI,KAAK,KAAK,MAAM,EAAC,eAAc,KAAE,CAAC,GAAE,KAAK,SAAS,KAAK,UAAU;AAAA,YAAE;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,mBAAK,OAAO,OAAO;AAAE,oBAAMC,KAAE,KAAK,eAAe;AAAE,kBAAG,QAAMA,MAAGA,GAAE,OAAO,aAAW,CAAC,KAAK,OAAO,MAAMF,IAAEG,GAAE,MAAM,KAAK,GAAE;AAAC,oBAAGD,GAAE,MAAM,SAAO,KAAK,OAAO,UAAS;AAAC,wBAAMF,KAAE,KAAK,OAAO,KAAKE,GAAE,MAAM,MAAK,KAAE;AAAE,sBAAG,QAAMF,GAAE;AAAO,sBAAGA,cAAaG,GAAE,UAAS;AAAC,0BAAMF,KAAED,GAAE,MAAME,GAAE,MAAM,MAAM;AAAE,oBAAAF,GAAE,OAAO,aAAa,KAAK,QAAOC,EAAC;AAAA,kBAAC,MAAM,CAAAD,GAAE,aAAa,KAAK,QAAOE,GAAE,MAAM,IAAI;AAAE,uBAAK,OAAO,OAAO;AAAA,gBAAC;AAAC,qBAAK,OAAO,OAAOF,IAAEC,EAAC,GAAE,KAAK,OAAO,SAAS,GAAE,KAAK,eAAe,KAAK,OAAO,UAAS,KAAK,OAAO,SAAS,KAAK,MAAM,GAAE,KAAK,OAAO;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAE,oBAAMC,KAAE,KAAK,OAAO,OAAO;AAAE,kBAAIC;AAAE,cAAAH,KAAE,KAAK,IAAIA,IAAEE,KAAE,CAAC,GAAED,KAAE,KAAK,IAAID,KAAEC,IAAEC,KAAE,CAAC,IAAEF;AAAE,kBAAG,CAACI,IAAEC,EAAC,IAAE,KAAK,OAAO,KAAKL,EAAC;AAAE,kBAAG,QAAMI,GAAE,QAAO;AAAK,kBAAGH,KAAE,KAAGI,OAAID,GAAE,OAAO,GAAE;AAAC,sBAAK,CAACH,EAAC,IAAE,KAAK,OAAO,KAAKD,KAAE,CAAC;AAAE,oBAAGC,IAAE;AAAC,wBAAK,CAACC,EAAC,IAAE,KAAK,OAAO,KAAKF,EAAC,GAAE,CAACG,EAAC,IAAE,KAAK,OAAO,KAAKH,KAAE,CAAC;AAAE,kBAAAE,OAAIC,OAAIC,KAAEH,IAAEI,KAAE;AAAA,gBAAE;AAAA,cAAC;AAAC,eAACF,IAAEE,EAAC,IAAED,GAAE,SAASC,IAAE,IAAE;AAAE,oBAAMC,KAAE,SAAS,YAAY;AAAE,kBAAGL,KAAE,EAAE,QAAOK,GAAE,SAASH,IAAEE,EAAC,GAAE,CAACD,IAAEC,EAAC,IAAE,KAAK,OAAO,KAAKL,KAAEC,EAAC,GAAE,QAAMG,KAAE,QAAM,CAACD,IAAEE,EAAC,IAAED,GAAE,SAASC,IAAE,IAAE,GAAEC,GAAE,OAAOH,IAAEE,EAAC,GAAEC,GAAE,sBAAsB;AAAG,kBAAIC,IAAEC,KAAE;AAAO,kBAAGL,cAAa,MAAK;AAAC,oBAAG,CAACA,GAAE,KAAK,OAAO,QAAO;AAAK,gBAAAE,KAAEF,GAAE,KAAK,UAAQG,GAAE,SAASH,IAAEE,EAAC,GAAEC,GAAE,OAAOH,IAAEE,KAAE,CAAC,MAAIC,GAAE,SAASH,IAAEE,KAAE,CAAC,GAAEC,GAAE,OAAOH,IAAEE,EAAC,GAAEG,KAAE,UAASD,KAAED,GAAE,sBAAsB;AAAA,cAAC,OAAK;AAAC,oBAAG,EAAEF,GAAE,mBAAmB,SAAS,QAAO;AAAK,gBAAAG,KAAEH,GAAE,QAAQ,sBAAsB,GAAEC,KAAE,MAAIG,KAAE;AAAA,cAAQ;AAAC,qBAAM,EAAC,QAAOD,GAAE,MAAIA,GAAE,QAAO,QAAOA,GAAE,QAAO,MAAKA,GAAEC,EAAC,GAAE,OAAMD,GAAEC,EAAC,GAAE,KAAID,GAAE,KAAI,OAAM,EAAC;AAAA,YAAC;AAAA,YAAC,iBAAgB;AAAC,oBAAMP,KAAE,SAAS,aAAa;AAAE,kBAAG,QAAMA,MAAGA,GAAE,cAAY,EAAE,QAAO;AAAK,oBAAMC,KAAED,GAAE,WAAW,CAAC;AAAE,kBAAG,QAAMC,GAAE,QAAO;AAAK,oBAAMC,KAAE,KAAK,gBAAgBD,EAAC;AAAE,qBAAO,EAAE,KAAK,kBAAiBC,EAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,oBAAMF,KAAE,KAAK,OAAO;AAAQ,kBAAG,iBAAgBA,MAAG,CAACA,GAAE,YAAY,QAAM,CAAC,MAAK,IAAI;AAAE,oBAAMC,KAAE,KAAK,eAAe;AAAE,qBAAO,QAAMA,KAAE,CAAC,MAAK,IAAI,IAAE,CAAC,KAAK,kBAAkBA,EAAC,GAAEA,EAAC;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,qBAAO,SAAS,kBAAgB,KAAK,QAAM,QAAM,SAAS,iBAAe,EAAE,KAAK,MAAK,SAAS,aAAa;AAAA,YAAC;AAAA,YAAC,kBAAkBD,IAAE;AAAC,oBAAMC,KAAE,CAAC,CAACD,GAAE,MAAM,MAAKA,GAAE,MAAM,MAAM,CAAC;AAAE,cAAAA,GAAE,OAAO,aAAWC,GAAE,KAAK,CAACD,GAAE,IAAI,MAAKA,GAAE,IAAI,MAAM,CAAC;AAAE,oBAAME,KAAED,GAAE,IAAK,CAAAD,OAAG;AAAC,sBAAK,CAACC,IAAEC,EAAC,IAAEF,IAAEI,KAAE,KAAK,OAAO,KAAKH,IAAE,IAAE,GAAEI,KAAED,GAAE,OAAO,KAAK,MAAM;AAAE,uBAAO,MAAIF,KAAEG,KAAED,cAAaD,GAAE,WAASE,KAAED,GAAE,MAAMH,IAAEC,EAAC,IAAEG,KAAED,GAAE,OAAO;AAAA,cAAC,CAAE,GAAEA,KAAE,KAAK,IAAI,KAAK,IAAI,GAAGF,EAAC,GAAE,KAAK,OAAO,OAAO,IAAE,CAAC,GAAEG,KAAE,KAAK,IAAID,IAAE,GAAGF,EAAC;AAAE,qBAAO,IAAI,EAAEG,IAAED,KAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,gBAAgBL,IAAE;AAAC,kBAAG,CAAC,EAAE,KAAK,MAAKA,GAAE,cAAc,KAAG,CAACA,GAAE,aAAW,CAAC,EAAE,KAAK,MAAKA,GAAE,YAAY,EAAE,QAAO;AAAK,oBAAMC,KAAE,EAAC,OAAM,EAAC,MAAKD,GAAE,gBAAe,QAAOA,GAAE,YAAW,GAAE,KAAI,EAAC,MAAKA,GAAE,cAAa,QAAOA,GAAE,UAAS,GAAE,QAAOA,GAAC;AAAE,qBAAM,CAACC,GAAE,OAAMA,GAAE,GAAG,EAAE,QAAS,CAAAD,OAAG;AAAC,oBAAG,EAAC,MAAKC,IAAE,QAAOC,GAAC,IAAEF;AAAE,uBAAK,EAAEC,cAAa,SAAOA,GAAE,WAAW,SAAO,IAAG,KAAGA,GAAE,WAAW,SAAOC,GAAE,CAAAD,KAAEA,GAAE,WAAWC,EAAC,GAAEA,KAAE;AAAA,qBAAM;AAAC,sBAAGD,GAAE,WAAW,WAASC,GAAE;AAAM,kBAAAD,KAAEA,GAAE,WAAUC,KAAED,cAAa,OAAKA,GAAE,KAAK,SAAOA,GAAE,WAAW,SAAO,IAAEA,GAAE,WAAW,SAAOA,GAAE,WAAW,SAAO;AAAA,gBAAC;AAAC,gBAAAD,GAAE,OAAKC,IAAED,GAAE,SAAOE;AAAA,cAAC,CAAE,GAAED;AAAA,YAAC;AAAA,YAAC,cAAcD,IAAE;AAAC,oBAAMC,KAAE,KAAK,OAAO,OAAO,GAAEC,KAAE,CAACF,IAAEE,OAAI;AAAC,gBAAAF,KAAE,KAAK,IAAIC,KAAE,GAAED,EAAC;AAAE,sBAAK,CAACG,IAAEC,EAAC,IAAE,KAAK,OAAO,KAAKJ,EAAC;AAAE,uBAAOG,KAAEA,GAAE,SAASC,IAAEF,EAAC,IAAE,CAAC,MAAK,EAAE;AAAA,cAAC;AAAE,qBAAM,CAAC,GAAGA,GAAEF,GAAE,OAAM,KAAE,GAAE,GAAGE,GAAEF,GAAE,QAAMA,GAAE,QAAO,IAAE,CAAC;AAAA,YAAC;AAAA,YAAC,eAAeA,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAEF,IAAEG,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAEF,IAAEG,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,kBAAG,EAAE,KAAK,kBAAiBJ,IAAEC,IAAEC,IAAEC,EAAC,GAAE,QAAMH,OAAI,QAAM,KAAK,KAAK,cAAY,QAAMA,GAAE,cAAY,QAAME,GAAE,YAAY;AAAO,oBAAMG,KAAE,SAAS,aAAa;AAAE,kBAAG,QAAMA,GAAE,KAAG,QAAML,IAAE;AAAC,qBAAK,SAAS,KAAG,KAAK,KAAK,MAAM,EAAC,eAAc,KAAE,CAAC;AAAE,sBAAK,EAAC,QAAOM,GAAC,IAAE,KAAK,eAAe,KAAG,CAAC;AAAE,oBAAG,QAAMA,MAAGF,MAAGJ,OAAIM,GAAE,kBAAgBL,OAAIK,GAAE,eAAaJ,OAAII,GAAE,gBAAcH,OAAIG,GAAE,WAAU;AAAC,kBAAAN,cAAa,WAAS,SAAOA,GAAE,YAAUC,KAAE,MAAM,KAAKD,GAAE,WAAW,UAAU,EAAE,QAAQA,EAAC,GAAEA,KAAEA,GAAE,aAAYE,cAAa,WAAS,SAAOA,GAAE,YAAUC,KAAE,MAAM,KAAKD,GAAE,WAAW,UAAU,EAAE,QAAQA,EAAC,GAAEA,KAAEA,GAAE;AAAY,wBAAME,KAAE,SAAS,YAAY;AAAE,kBAAAA,GAAE,SAASJ,IAAEC,EAAC,GAAEG,GAAE,OAAOF,IAAEC,EAAC,GAAEE,GAAE,gBAAgB,GAAEA,GAAE,SAASD,EAAC;AAAA,gBAAC;AAAA,cAAC,MAAM,CAAAC,GAAE,gBAAgB,GAAE,KAAK,KAAK,KAAK;AAAA,YAAC;AAAA,YAAC,SAASL,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAI,kBAAG,YAAU,OAAOD,OAAIC,KAAED,IAAEA,KAAE,QAAI,EAAE,KAAK,YAAWD,EAAC,GAAE,QAAMA,IAAE;AAAC,sBAAME,KAAE,KAAK,cAAcF,EAAC;AAAE,qBAAK,eAAe,GAAGE,IAAED,EAAC;AAAA,cAAC,MAAM,MAAK,eAAe,IAAI;AAAE,mBAAK,OAAOC,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,kBAAIF,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,EAAE,QAAQ;AAAK,oBAAMC,KAAE,KAAK,WAAU,CAACC,IAAEC,EAAC,IAAE,KAAK,SAAS;AAAE,kBAAG,KAAK,YAAUD,IAAE,KAAK,aAAWC,IAAE,QAAM,KAAK,cAAY,KAAK,aAAW,KAAK,YAAW,EAAE,GAAE,EAAE,GAAGF,IAAE,KAAK,SAAS,GAAE;AAAC,oBAAG,CAAC,KAAK,aAAW,QAAME,MAAGA,GAAE,OAAO,aAAWA,GAAE,MAAM,SAAO,KAAK,OAAO,UAAS;AAAC,wBAAMH,KAAE,KAAK,OAAO,QAAQ;AAAE,kBAAAA,MAAG,KAAK,eAAeA,GAAE,WAAUA,GAAE,aAAYA,GAAE,SAAQA,GAAE,SAAS;AAAA,gBAAC;AAAC,sBAAME,KAAE,CAAC,EAAE,EAAE,OAAO,mBAAkB,GAAE,EAAE,GAAG,KAAK,SAAS,IAAG,GAAE,EAAE,GAAGD,EAAC,GAAED,EAAC;AAAE,qBAAK,QAAQ,KAAK,EAAE,EAAE,OAAO,eAAc,GAAGE,EAAC,GAAEF,OAAI,EAAE,EAAE,QAAQ,UAAQ,KAAK,QAAQ,KAAK,GAAGE,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAE;AAAC;AAAA,UAAa,MAAMC,GAAC;AAAA,YAAC,OAAO,WAAS,EAAC,SAAQ,CAAC,EAAC;AAAA,YAAE,OAAO,SAAO,EAAC,SAAQA,GAAC;AAAA,YAAE,UAAQ,CAAC;AAAA,YAAE,YAAYF,IAAEC,IAAE;AAAC,mBAAK,QAAMD,IAAE,KAAK,UAAQC;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,qBAAO,KAAK,KAAK,QAAQ,OAAO,EAAE,QAAS,CAAAD,OAAG;AAAC,wBAAM,KAAK,QAAQA,EAAC,KAAG,KAAK,UAAUA,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAM,YAAY,OAAO,WAAWD,EAAC,EAAE;AAAE,qBAAO,KAAK,QAAQA,EAAC,IAAE,IAAIC,GAAE,KAAK,OAAM,KAAK,QAAQ,QAAQD,EAAC,KAAG,CAAC,CAAC,GAAE,KAAK,QAAQA,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,UAAAC,GAAE,IAAEC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI;AAAE,gBAAM,IAAE,EAAC,OAAMC,GAAE,MAAM,OAAM,WAAU,CAAC,SAAQ,UAAS,SAAS,EAAC,GAAE,IAAE,IAAIA,GAAE,WAAW,SAAQ,SAAQ,CAAC,GAAE,IAAE,IAAIA,GAAE,gBAAgB,SAAQ,YAAW,CAAC,GAAE,IAAE,IAAIA,GAAE,gBAAgB,SAAQ,cAAa,CAAC;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,IAAE,IAAIC,GAAE,gBAAgB,cAAa,SAAQ,EAAC,OAAMA,GAAE,MAAM,OAAM,CAAC,GAAE,IAAE,IAAI,EAAE,GAAG,cAAa,oBAAmB,EAAC,OAAMA,GAAE,MAAM,OAAM,CAAC;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,OAAOF,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAOD,EAAC;AAAE,qBAAOC,GAAE,aAAa,cAAa,OAAO,GAAEA;AAAA,YAAC;AAAA,YAAC,KAAKD,IAAEC,IAAE;AAAC,qBAAO,KAAK,SAAS,IAAK,CAAAD,OAAGA,GAAE,OAAO,KAAG,IAAE,KAAGA,GAAE,QAAQ,SAAU,EAAE,KAAK,IAAI,EAAE,MAAMA,IAAEA,KAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,KAAKD,IAAEC,IAAE;AAAC,qBAAM;AAAA,GAAW,GAAE,EAAE,GAAG,KAAK,KAAKD,IAAEC,EAAC,CAAC,CAAC;AAAA;AAAA,YAAU;AAAA,UAAC;AAAA,UAAC,MAAM,UAAUE,GAAE,GAAE;AAAA,YAAC,OAAO,MAAI;AAAA,YAAK,OAAO,WAAU;AAAC,gBAAE,GAAG,SAAS,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,MAAM,UAAU,EAAE,EAAC;AAAA,UAAC;AAAC,YAAE,WAAS,QAAO,EAAE,UAAQ,QAAO,EAAE,WAAS,cAAa,EAAE,YAAU,iBAAgB,EAAE,UAAQ,OAAM,EAAE,WAAS,wBAAuB,EAAE,YAAU,2BAA0B,EAAE,UAAQ,OAAM,EAAE,kBAAgB,CAAC,CAAC,GAAE,EAAE,kBAAgB,CAAC,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC,GAAE,EAAE,oBAAkB;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI;AAAA,UAAE,MAAM,UAAUC,GAAE,gBAAe;AAAA,YAAC,MAAMH,IAAE;AAAC,kBAAIC,KAAE,MAAM,MAAMD,EAAC;AAAE,qBAAOC,GAAE,WAAW,MAAM,KAAGA,KAAEA,GAAE,QAAQ,WAAU,EAAE,EAAE,QAAQ,WAAU,EAAE,GAAE,IAAIA,GAAE,MAAM,GAAG,EAAE,IAAK,CAAAD,OAAG,KAAK,SAASA,IAAE,EAAE,EAAE,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE,CAAE,EAAE,KAAK,EAAE,CAAC,MAAIC;AAAA,YAAC;AAAA,UAAC;AAAC,gBAAM,IAAE,IAAIE,GAAE,gBAAgB,SAAQ,YAAW,EAAC,OAAMA,GAAE,MAAM,OAAM,CAAC,GAAE,IAAE,IAAI,EAAE,SAAQ,SAAQ,EAAC,OAAMA,GAAE,MAAM,OAAM,CAAC;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI;AAAE,gBAAM,IAAE,EAAC,OAAMC,GAAE,MAAM,OAAM,WAAU,CAAC,KAAK,EAAC,GAAE,IAAE,IAAIA,GAAE,WAAW,aAAY,OAAM,CAAC,GAAE,IAAE,IAAIA,GAAE,gBAAgB,aAAY,gBAAe,CAAC,GAAE,IAAE,IAAIA,GAAE,gBAAgB,aAAY,aAAY,CAAC;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI;AAAE,gBAAM,IAAE,EAAC,OAAMC,GAAE,MAAM,QAAO,WAAU,CAAC,SAAQ,WAAW,EAAC,GAAE,IAAE,IAAIA,GAAE,gBAAgB,QAAO,WAAU,CAAC;AAAA,UAAE,MAAM,UAAUA,GAAE,gBAAe;AAAA,YAAC,MAAMH,IAAE;AAAC,qBAAO,MAAM,MAAMA,EAAC,EAAE,QAAQ,SAAQ,EAAE;AAAA,YAAC;AAAA,UAAC;AAAC,gBAAM,IAAE,IAAI,EAAE,QAAO,eAAc,CAAC;AAAA,QAAC,GAAE,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI;AAAE,gBAAM,IAAE,IAAIC,GAAE,gBAAgB,QAAO,WAAU,EAAC,OAAMA,GAAE,MAAM,QAAO,WAAU,CAAC,SAAQ,SAAQ,MAAM,EAAC,CAAC,GAAE,IAAE,IAAIA,GAAE,gBAAgB,QAAO,aAAY,EAAC,OAAMA,GAAE,MAAM,QAAO,WAAU,CAAC,QAAO,QAAO,MAAM,EAAC,CAAC;AAAA,QAAC,GAAE,KAAI,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,EAAE,CAAC,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,IAAE,yBAAwB,IAAE,CAAC,KAAI,MAAK,IAAI,GAAE,IAAE,CAAAF,OAAGA,MAAG,EAAE,SAASA,GAAE,OAAO,GAAE,IAAE,2BAA0B,IAAE,6BAA4B,IAAE,iCAAgC,IAAE,CAAC,SAASA,IAAE;AAAC,wDAA0CA,GAAE,gBAAgB,aAAa,SAAS,MAAI,CAAAA,OAAG;AAAC,oBAAMC,KAAE,MAAM,KAAKD,GAAE,iBAAiB,mBAAmB,CAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,cAAAF,GAAE,QAAS,CAAAD,OAAG;AAAC,iBAACA,GAAE,aAAa,OAAO,KAAG,IAAI,MAAM,CAAC,IAAEE,GAAE,KAAKF,EAAC,IAAEG,GAAE,KAAKH,EAAC;AAAA,cAAC,CAAE,GAAEE,GAAE,QAAS,CAAAF,OAAGA,GAAE,YAAY,YAAYA,EAAC,CAAE;AAAE,oBAAMI,KAAEJ,GAAE,gBAAgB,WAAUK,KAAEF,GAAE,IAAK,CAAAH,QAAI,CAACA,IAAEC,OAAI;AAAC,sBAAMC,KAAEF,GAAE,aAAa,OAAO,GAAEG,KAAED,IAAG,MAAM,CAAC;AAAE,oBAAG,CAACC,GAAE,QAAO;AAAK,sBAAMC,KAAE,OAAOD,GAAE,CAAC,CAAC,GAAEE,KAAEH,IAAG,MAAM,CAAC,GAAEI,KAAED,KAAE,OAAOA,GAAE,CAAC,CAAC,IAAE,GAAEE,KAAE,IAAI,OAAO,UAAUH,EAAC,SAASE,EAAC,uDAAsD,GAAG,GAAEE,KAAEP,GAAE,MAAMM,EAAC;AAAE,uBAAM,EAAC,IAAGH,IAAE,QAAOE,IAAE,MAAKE,MAAG,aAAWA,GAAE,CAAC,IAAE,WAAS,WAAU,SAAQR,GAAC;AAAA,cAAC,GAAGA,IAAEI,EAAC,CAAE,EAAE,OAAQ,CAAAJ,OAAGA,EAAE;AAAE,qBAAKK,GAAE,UAAQ;AAAC,sBAAML,KAAE,CAAC;AAAE,oBAAIC,KAAEI,GAAE,MAAM;AAAE,uBAAKJ,KAAG,CAAAD,GAAE,KAAKC,EAAC,GAAEA,KAAEI,GAAE,UAAQA,GAAE,CAAC,GAAG,YAAUJ,GAAE,QAAQ,sBAAoBI,GAAE,CAAC,EAAE,OAAKJ,GAAE,KAAGI,GAAE,MAAM,IAAE;AAAK,sBAAMH,KAAE,SAAS,cAAc,IAAI;AAAE,gBAAAF,GAAE,QAAS,CAAAA,OAAG;AAAC,wBAAMC,KAAE,SAAS,cAAc,IAAI;AAAE,kBAAAA,GAAE,aAAa,aAAYD,GAAE,IAAI,GAAEA,GAAE,SAAO,KAAGC,GAAE,aAAa,SAAQ,gBAAcD,GAAE,SAAO,EAAE,GAAEC,GAAE,YAAUD,GAAE,QAAQ,WAAUE,GAAE,YAAYD,EAAC;AAAA,gBAAC,CAAE;AAAE,sBAAME,KAAEH,GAAE,CAAC,GAAG,SAAQ,EAAC,YAAWI,GAAC,IAAED,MAAG,CAAC;AAAE,gBAAAA,MAAGC,IAAG,aAAaF,IAAEC,EAAC,GAAEH,GAAE,MAAM,CAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,sBAAG,EAAC,SAAQC,GAAC,IAAED;AAAE,kBAAAI,IAAG,YAAYH,EAAC;AAAA,gBAAC,CAAE;AAAA,cAAC;AAAA,YAAC,GAAGD,EAAC;AAAA,UAAC,GAAE,SAASA,IAAE;AAAC,YAAAA,GAAE,cAAc,6BAA6B,OAAK,CAAAA,OAAG;AAAC,oBAAM,KAAKA,GAAE,iBAAiB,yBAAyB,CAAC,EAAE,OAAQ,CAAAA,OAAGA,GAAE,aAAa,OAAO,GAAG,MAAM,CAAC,CAAE,EAAE,QAAS,CAAAC,OAAG;AAAC,sBAAMC,KAAEF,GAAE,uBAAuB;AAAE,gBAAAE,GAAE,OAAO,GAAGD,GAAE,UAAU,GAAEA,GAAE,YAAY,aAAaC,IAAED,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,GAAGD,EAAC,IAAG,CAAAA,OAAG;AAAC,oBAAM,KAAKA,GAAE,iBAAiB,IAAI,CAAC,EAAE,OAAQ,CAAAA,OAAG,EAAEA,GAAE,sBAAsB,KAAG,EAAEA,GAAE,kBAAkB,CAAE,EAAE,QAAS,CAAAA,OAAG;AAAC,gBAAAA,GAAE,YAAY,YAAYA,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC,GAAGA,EAAC;AAAA,UAAE,CAAC;AAAE,gBAAM,KAAG,GAAE,EAAE,GAAG,iBAAiB,GAAE,IAAE,CAAC,CAAC,KAAK,WAAU,SAASA,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAEH,GAAE;AAAK,gBAAG,UAAQA,GAAE,eAAe,QAAQ,QAAOC,GAAE,OAAOE,GAAE,KAAK,CAAC;AAAE,gBAAG,CAAC,EAAEH,EAAC,GAAE;AAAC,kBAAG,MAAIG,GAAE,KAAK,EAAE,UAAQA,GAAE,SAAS,IAAI,KAAG,CAAC,SAASH,IAAEC,IAAE;AAAC,uBAAOD,GAAE,0BAAwBA,GAAE,sBAAoB,CAAC,EAAEA,GAAE,wBAAuBC,EAAC,KAAG,CAAC,EAAED,GAAE,oBAAmBC,EAAC;AAAA,cAAC,EAAED,IAAEE,EAAC,EAAE,QAAOD;AAAE,cAAAE,KAAEA,GAAE,QAAQ,gBAAe,GAAG,GAAEA,KAAEA,GAAE,QAAQ,UAAS,GAAG,IAAG,QAAMH,GAAE,mBAAiB,QAAMA,GAAE,iBAAe,EAAEA,GAAE,eAAcE,EAAC,KAAGF,GAAE,2BAA2B,WAAS,EAAEA,GAAE,iBAAgBE,EAAC,OAAKC,KAAEA,GAAE,QAAQ,MAAK,EAAE,KAAI,QAAMH,GAAE,eAAa,QAAMA,GAAE,iBAAe,EAAEA,GAAE,eAAcE,EAAC,KAAGF,GAAE,uBAAuB,WAAS,EAAEA,GAAE,aAAYE,EAAC,OAAKC,KAAEA,GAAE,QAAQ,MAAK,EAAE,IAAGA,KAAEA,GAAE,WAAW,KAAI,GAAG;AAAA,YAAC;AAAC,mBAAOF,GAAE,OAAOE,EAAC;AAAA,UAAC,CAAC,GAAE,CAAC,KAAK,WAAU,CAAC,GAAE,CAAC,MAAK,SAASH,IAAEC,IAAE;AAAC,mBAAO,EAAEA,IAAE,IAAI,KAAGA,GAAE,OAAO,IAAI,GAAEA;AAAA,UAAC,CAAC,GAAE,CAAC,KAAK,cAAa,CAAC,GAAE,CAAC,KAAK,cAAa,SAASD,IAAEC,IAAEC,IAAE;AAAC,kBAAME,KAAEF,GAAE,MAAMF,EAAC;AAAE,gBAAG,QAAMI,GAAE,QAAOH;AAAE,gBAAGG,GAAE,qBAAqBD,GAAE,WAAU;AAAC,oBAAMF,KAAE,CAAC,GAAEE,KAAEC,GAAE,MAAMJ,EAAC;AAAE,kBAAG,QAAMG,GAAE,QAAOF,GAAEG,GAAE,QAAQ,IAAED,IAAG,KAAI,EAAE,KAAI,OAAOF,IAAEG,GAAE,QAAQJ,IAAEE,EAAC,CAAC;AAAA,YAAC,WAASE,GAAE,qBAAqBD,GAAE,aAAW,CAAC,EAAEF,IAAE,IAAI,KAAGA,GAAE,OAAO,IAAI,GAAE,cAAaG,MAAG,aAAYA,MAAG,cAAY,OAAOA,GAAE,QAAQ,QAAO,EAAEH,IAAEG,GAAE,UAASA,GAAE,QAAQJ,IAAEE,EAAC,GAAEA,EAAC;AAAE,mBAAOD;AAAA,UAAC,CAAC,GAAE,CAAC,KAAK,cAAa,SAASD,IAAEC,IAAEC,IAAE;AAAC,kBAAME,KAAED,GAAE,WAAW,KAAKH,EAAC,GAAEK,KAAEF,GAAE,gBAAgB,KAAKH,EAAC,GAAEM,KAAEH,GAAE,gBAAgB,KAAKH,EAAC,GAAEO,KAAE,CAAC;AAAE,mBAAOH,GAAE,OAAOC,EAAC,EAAE,OAAOC,EAAC,EAAE,QAAS,CAAAL,OAAG;AAAC,kBAAIG,KAAEF,GAAE,MAAMD,IAAEE,GAAE,MAAM,SAAS;AAAE,sBAAMC,OAAIG,GAAEH,GAAE,QAAQ,IAAEA,GAAE,MAAMJ,EAAC,GAAEO,GAAEH,GAAE,QAAQ,OAAKA,KAAE,EAAEH,EAAC,GAAE,QAAMG,MAAGA,GAAE,aAAWH,MAAGG,GAAE,YAAUH,OAAIM,GAAEH,GAAE,QAAQ,IAAEA,GAAE,MAAMJ,EAAC,KAAG,SAAQI,KAAE,EAAEH,EAAC,GAAE,QAAMG,MAAGA,GAAE,aAAWH,MAAGG,GAAE,YAAUH,OAAIG,KAAE,EAAEH,EAAC,GAAEM,GAAEH,GAAE,QAAQ,IAAEA,GAAE,MAAMJ,EAAC,KAAG;AAAA,YAAQ,CAAE,GAAE,OAAO,QAAQO,EAAC,EAAE,OAAQ,CAACP,IAAEC,OAAI;AAAC,kBAAG,CAACE,IAAEC,EAAC,IAAEH;AAAE,qBAAO,EAAED,IAAEG,IAAEC,IAAEF,EAAC;AAAA,YAAC,GAAGD,EAAC;AAAA,UAAC,CAAC,GAAE,CAAC,KAAK,cAAa,SAASD,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAE,CAAC,GAAEC,KAAEJ,GAAE,SAAO,CAAC;AAAE,mBAAM,aAAWI,GAAE,cAAYD,GAAE,SAAO,OAAI,gBAAcC,GAAE,mBAAiBD,GAAE,YAAU,OAAI,mBAAiBC,GAAE,mBAAiBD,GAAE,SAAO,QAAKC,GAAE,YAAY,WAAW,MAAM,KAAG,SAASA,GAAE,YAAW,EAAE,KAAG,SAAOD,GAAE,OAAK,OAAIF,KAAE,OAAO,QAAQE,EAAC,EAAE,OAAQ,CAACH,IAAEC,OAAI;AAAC,kBAAG,CAACE,IAAEC,EAAC,IAAEH;AAAE,qBAAO,EAAED,IAAEG,IAAEC,IAAEF,EAAC;AAAA,YAAC,GAAGD,EAAC,GAAE,WAAWG,GAAE,cAAY,CAAC,IAAE,IAAG,KAAI,EAAE,KAAI,OAAO,GAAI,EAAE,OAAOH,EAAC,IAAEA;AAAA,UAAC,CAAC,GAAE,CAAC,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAED,GAAE,MAAMF,EAAC;AAAE,gBAAG,QAAMG,MAAG,WAASA,GAAE,YAAU,CAAC,EAAEF,IAAE,IAAI,EAAE,QAAOA;AAAE,gBAAIG,KAAE,IAAGE,KAAEN,GAAE;AAAW,mBAAK,QAAMM,KAAG,EAAC,MAAK,IAAI,EAAE,SAASA,GAAE,OAAO,MAAIF,MAAG,IAAGE,KAAEA,GAAE;AAAW,mBAAOF,MAAG,IAAEH,KAAEA,GAAE,OAAQ,CAACD,IAAEC,OAAIA,GAAE,SAAOA,GAAE,cAAY,YAAU,OAAOA,GAAE,WAAW,SAAOD,GAAE,KAAKC,EAAC,IAAED,GAAE,OAAOC,GAAE,QAAO,iBAAC,QAAOG,MAAKH,GAAE,cAAY,CAAC,EAAE,IAAED,IAAG,KAAI,EAAE,IAAE;AAAA,UAAC,CAAC,GAAE,CAAC,UAAS,SAASA,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAEH;AAAE,gBAAII,KAAE,SAAOD,GAAE,UAAQ,YAAU;AAAS,kBAAME,KAAEF,GAAE,aAAa,cAAc;AAAE,mBAAOE,OAAID,KAAE,WAASC,KAAE,YAAU,cAAa,EAAEJ,IAAE,QAAOG,IAAEF,EAAC;AAAA,UAAC,CAAC,GAAE,CAAC,OAAM,SAASF,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAED,GAAE,MAAM,YAAY;AAAE,mBAAO,EAAED,IAAE,cAAa,CAACE,MAAG,EAAE,aAAYA,OAAI,cAAY,OAAOA,GAAE,WAASA,GAAE,QAAQH,IAAEE,EAAC,GAAEA,EAAC;AAAA,UAAC,CAAC,GAAE,CAAC,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAE,YAAUH,GAAE,eAAe,UAAQA,GAAE,gBAAcA,GAAE,eAAe;AAAc,mBAAO,QAAMG,KAAE,EAAEF,IAAE,SAAQ,MAAM,KAAKE,GAAE,iBAAiB,IAAI,CAAC,EAAE,QAAQH,EAAC,IAAE,GAAEE,EAAC,IAAED;AAAA,UAAC,CAAC,GAAE,CAAC,KAAI,EAAE,MAAM,CAAC,GAAE,CAAC,KAAI,EAAE,QAAQ,CAAC,GAAE,CAAC,UAAS,EAAE,QAAQ,CAAC,GAAE,CAAC,SAAQ,WAAU;AAAC,mBAAO,KAAI,EAAE;AAAA,UAAE,CAAC,CAAC,GAAE,IAAE,CAAC,EAAE,IAAG,EAAE,EAAE,EAAE,OAAQ,CAACD,IAAEC,QAAKD,GAAEC,GAAE,OAAO,IAAEA,IAAED,KAAI,CAAC,CAAC,GAAE,IAAE,CAAC,EAAE,IAAG,EAAE,GAAE,EAAE,IAAG,EAAE,IAAG,EAAE,GAAE,EAAE,CAAC,EAAE,OAAQ,CAACA,IAAEC,QAAKD,GAAEC,GAAE,OAAO,IAAEA,IAAED,KAAI,CAAC,CAAC;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS,EAAC,UAAS,CAAC,EAAC;AAAA,YAAE,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,MAAM,KAAK,iBAAiB,QAAQ,CAAAD,OAAG,KAAK,cAAcA,IAAE,KAAE,CAAE,GAAE,KAAK,MAAM,KAAK,iBAAiB,OAAO,CAAAA,OAAG,KAAK,cAAcA,IAAE,IAAE,CAAE,GAAE,KAAK,MAAM,KAAK,iBAAiB,SAAQ,KAAK,eAAe,KAAK,IAAI,CAAC,GAAE,KAAK,WAAS,CAAC,GAAE,EAAE,OAAO,KAAK,QAAQ,YAAU,CAAC,CAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,oBAAG,CAACC,IAAEC,EAAC,IAAEF;AAAE,qBAAK,WAAWC,IAAEC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,WAAWF,IAAEC,IAAE;AAAC,mBAAK,SAAS,KAAK,CAACD,IAAEC,EAAC,CAAC;AAAA,YAAC;AAAA,YAAC,QAAQD,IAAE;AAAC,kBAAG,EAAC,MAAKC,IAAE,MAAKC,GAAC,IAAEF,IAAEG,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,kBAAGA,GAAE,EAAE,GAAG,QAAQ,EAAE,QAAO,KAAI,EAAE,KAAI,OAAOD,MAAG,IAAG,EAAC,CAAC,EAAE,GAAG,QAAQ,GAAEC,GAAE,EAAE,GAAG,QAAQ,EAAC,CAAC;AAAE,kBAAG,CAACF,GAAE,QAAO,KAAI,EAAE,KAAI,OAAOC,MAAG,IAAGC,EAAC;AAAE,oBAAMC,KAAE,KAAK,YAAYH,EAAC;AAAE,qBAAO,EAAEG,IAAE,IAAI,MAAI,QAAMA,GAAE,IAAIA,GAAE,IAAI,SAAO,CAAC,EAAE,cAAYD,GAAE,SAAOC,GAAE,QAAS,KAAI,EAAE,KAAI,OAAOA,GAAE,OAAO,IAAE,CAAC,EAAE,OAAO,CAAC,CAAC,IAAEA;AAAA,YAAC;AAAA,YAAC,cAAcJ,IAAE;AAAC,eAAC,CAAAA,OAAG;AAAC,gBAAAA,GAAE,mBAAiB,EAAE,QAAS,CAAAC,OAAG;AAAC,kBAAAA,GAAED,EAAC;AAAA,gBAAC,CAAE;AAAA,cAAC,GAAGA,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAE;AAAC,oBAAMC,KAAG,IAAI,YAAW,gBAAgBD,IAAE,WAAW;AAAE,mBAAK,cAAcC,EAAC;AAAE,oBAAMC,KAAED,GAAE,MAAKE,KAAE,oBAAI,WAAQ,CAACC,IAAEC,EAAC,IAAE,KAAK,gBAAgBH,IAAEC,EAAC;AAAE,qBAAO,EAAE,KAAK,MAAM,QAAOD,IAAEE,IAAEC,IAAEF,EAAC;AAAA,YAAC;AAAA,YAAC,qBAAqBH,IAAEC,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,EAAE,GAAG,QAAQ;AAAI,kBAAG,YAAU,OAAOF,IAAE;AAAC,sBAAME,KAAE,KAAK,QAAQ,EAAC,MAAKF,IAAE,MAAK,GAAE,CAAC;AAAE,qBAAK,MAAM,YAAYE,IAAED,EAAC,GAAE,KAAK,MAAM,aAAa,GAAE,EAAE,GAAG,QAAQ,MAAM;AAAA,cAAC,OAAK;AAAC,sBAAME,KAAE,KAAK,QAAQ,EAAC,MAAKF,IAAE,MAAK,GAAE,CAAC;AAAE,qBAAK,MAAM,eAAgB,KAAI,EAAE,KAAI,OAAOD,EAAC,EAAE,OAAOG,EAAC,GAAED,EAAC,GAAE,KAAK,MAAM,aAAaF,KAAEG,GAAE,OAAO,GAAE,EAAE,GAAG,QAAQ,MAAM;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,cAAcH,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,kBAAGD,GAAE,iBAAiB;AAAO,cAAAA,GAAE,eAAe;AAAE,oBAAK,CAACE,EAAC,IAAE,KAAK,MAAM,UAAU,SAAS;AAAE,kBAAG,QAAMA,GAAE;AAAO,oBAAK,EAAC,MAAKC,IAAE,MAAKC,GAAC,IAAE,KAAK,OAAOF,IAAED,EAAC;AAAE,cAAAD,GAAE,eAAe,QAAQ,cAAaI,EAAC,GAAEJ,GAAE,eAAe,QAAQ,aAAYG,EAAC,GAAEF,OAAI,GAAE,EAAE,IAAI,EAAC,OAAMC,IAAE,OAAM,KAAK,MAAK,CAAC;AAAA,YAAC;AAAA,YAAC,iBAAiBF,IAAE;AAAC,qBAAOA,GAAE,MAAM,OAAO,EAAE,OAAQ,CAAAA,OAAG,QAAMA,GAAE,CAAC,CAAE,EAAE,KAAK,IAAI;AAAA,YAAC;AAAA,YAAC,eAAeA,IAAE;AAAC,kBAAGA,GAAE,oBAAkB,CAAC,KAAK,MAAM,UAAU,EAAE;AAAO,cAAAA,GAAE,eAAe;AAAE,oBAAMC,KAAE,KAAK,MAAM,aAAa,IAAE;AAAE,kBAAG,QAAMA,GAAE;AAAO,oBAAMC,KAAEF,GAAE,eAAe,QAAQ,WAAW;AAAE,kBAAIG,KAAEH,GAAE,eAAe,QAAQ,YAAY;AAAE,kBAAG,CAACE,MAAG,CAACC,IAAE;AAAC,sBAAMF,KAAED,GAAE,eAAe,QAAQ,eAAe;AAAE,gBAAAC,OAAIE,KAAE,KAAK,iBAAiBF,EAAC;AAAA,cAAE;AAAC,oBAAMG,KAAE,MAAM,KAAKJ,GAAE,eAAe,SAAO,CAAC,CAAC;AAAE,kBAAG,CAACE,MAAGE,GAAE,SAAO,EAAE,MAAK,MAAM,SAAS,OAAOH,IAAEG,EAAC;AAAA,mBAAM;AAAC,oBAAGF,MAAGE,GAAE,SAAO,GAAE;AAAC,wBAAMJ,KAAG,IAAI,YAAW,gBAAgBE,IAAE,WAAW;AAAE,sBAAG,MAAIF,GAAE,KAAK,qBAAmB,UAAQA,GAAE,KAAK,mBAAmB,QAAQ,QAAO,KAAK,KAAK,MAAM,SAAS,OAAOC,IAAEG,EAAC;AAAA,gBAAC;AAAC,qBAAK,QAAQH,IAAE,EAAC,MAAKC,IAAE,MAAKC,GAAC,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,OAAOH,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAM,QAAQD,EAAC;AAAE,qBAAM,EAAC,MAAK,KAAK,MAAM,gBAAgBA,EAAC,GAAE,MAAKC,GAAC;AAAA,YAAC;AAAA,YAAC,QAAQD,IAAEC,IAAE;AAAC,kBAAG,EAAC,MAAKC,IAAE,MAAKC,GAAC,IAAEF;AAAE,oBAAMG,KAAE,KAAK,MAAM,UAAUJ,GAAE,KAAK,GAAEM,KAAE,KAAK,QAAQ,EAAC,MAAKJ,IAAE,MAAKC,GAAC,GAAEC,EAAC;AAAE,gBAAE,IAAI,WAAUE,IAAE,EAAC,MAAKJ,IAAE,MAAKC,GAAC,CAAC;AAAE,oBAAMI,KAAG,KAAI,EAAE,KAAI,OAAOP,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,EAAE,OAAOM,EAAC;AAAE,mBAAK,MAAM,eAAeC,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaA,GAAE,OAAO,IAAEP,GAAE,QAAO,EAAE,GAAG,QAAQ,MAAM,GAAE,KAAK,MAAM,wBAAwB;AAAA,YAAC;AAAA,YAAC,gBAAgBA,IAAEC,IAAE;AAAC,oBAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,qBAAO,KAAK,SAAS,QAAS,CAAAC,OAAG;AAAC,sBAAK,CAACC,IAAEC,EAAC,IAAEF;AAAE,wBAAOC,IAAE;AAAA,kBAAC,KAAK,KAAK;AAAU,oBAAAF,GAAE,KAAKG,EAAC;AAAE;AAAA,kBAAM,KAAK,KAAK;AAAa,oBAAAJ,GAAE,KAAKI,EAAC;AAAE;AAAA,kBAAM;AAAQ,0BAAM,KAAKN,GAAE,iBAAiBK,EAAC,CAAC,EAAE,QAAS,CAAAL,OAAG;AAAC,0BAAGC,GAAE,IAAID,EAAC,GAAE;AAAC,8BAAME,KAAED,GAAE,IAAID,EAAC;AAAE,wBAAAE,IAAG,KAAKI,EAAC;AAAA,sBAAC,MAAM,CAAAL,GAAE,IAAID,IAAE,CAACM,EAAC,CAAC;AAAA,oBAAC,CAAE;AAAA,gBAAC;AAAA,cAAC,CAAE,GAAE,CAACJ,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAOA,GAAE,MAAMF,EAAC,IAAED,GAAE,OAAQ,CAACA,IAAEG,OAAI;AAAC,kBAAG,CAACA,GAAE,OAAO,QAAOH;AAAE,kBAAGG,GAAE,cAAYA,GAAE,WAAWF,EAAC,EAAE,QAAOD,GAAE,KAAKG,EAAC;AAAE,oBAAMC,KAAEF,KAAE,EAAC,CAACD,EAAC,GAAEC,GAAC,IAAE,CAAC;AAAE,qBAAOF,GAAE,OAAOG,GAAE,QAAO,kCAAIC,KAAKD,GAAE,WAAW;AAAA,YAAC,GAAG,KAAI,EAAE,IAAE,IAAEH;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEC,IAAE;AAAC,gBAAIC,KAAE;AAAG,qBAAQC,KAAEH,GAAE,IAAI,SAAO,GAAEG,MAAG,KAAGD,GAAE,SAAOD,GAAE,QAAO,EAAEE,IAAE;AAAC,oBAAMF,KAAED,GAAE,IAAIG,EAAC;AAAE,kBAAG,YAAU,OAAOF,GAAE,OAAO;AAAM,cAAAC,KAAED,GAAE,SAAOC;AAAA,YAAC;AAAC,mBAAOA,GAAE,MAAM,KAAGD,GAAE,MAAM,MAAIA;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,gBAAG,EAAED,cAAa,SAAS,QAAM;AAAG,kBAAME,KAAED,GAAE,MAAMD,EAAC;AAAE,mBAAM,EAAEE,MAAGA,GAAE,qBAAqBC,GAAE,cAAY,CAAC,WAAU,WAAU,cAAa,UAAS,MAAK,OAAM,MAAK,MAAK,YAAW,cAAa,UAAS,UAAS,QAAO,MAAK,MAAK,MAAK,MAAK,MAAK,MAAK,UAAS,UAAS,MAAK,QAAO,OAAM,MAAK,UAAS,KAAI,OAAM,WAAU,SAAQ,MAAK,MAAK,MAAK,OAAO,EAAE,SAASH,GAAE,QAAQ,YAAY,CAAC;AAAA,UAAC;AAAC,gBAAM,IAAE,oBAAI;AAAQ,mBAAS,EAAEA,IAAE;AAAC,mBAAO,QAAMA,OAAI,EAAE,IAAIA,EAAC,MAAI,UAAQA,GAAE,UAAQ,EAAE,IAAIA,IAAE,IAAE,IAAE,EAAE,IAAIA,IAAE,EAAEA,GAAE,UAAU,CAAC,IAAG,EAAE,IAAIA,EAAC;AAAA,UAAE;AAAC,mBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAOH,GAAE,aAAWA,GAAE,YAAUE,GAAE,OAAQ,CAACD,IAAEC,OAAIA,GAAEF,IAAEC,IAAEF,EAAC,GAAG,KAAI,EAAE,IAAE,IAAEC,GAAE,aAAWA,GAAE,eAAa,MAAM,KAAKA,GAAE,cAAY,CAAC,CAAC,EAAE,OAAQ,CAACI,IAAEC,OAAI;AAAC,kBAAIC,KAAE,EAAEP,IAAEM,IAAEJ,IAAEC,IAAEC,EAAC;AAAE,qBAAOE,GAAE,aAAWL,GAAE,iBAAeM,KAAEL,GAAE,OAAQ,CAACD,IAAEC,OAAIA,GAAEI,IAAEL,IAAED,EAAC,GAAGO,EAAC,GAAEA,MAAGH,GAAE,IAAIE,EAAC,KAAG,CAAC,GAAG,OAAQ,CAACL,IAAEC,OAAIA,GAAEI,IAAEL,IAAED,EAAC,GAAGO,EAAC,IAAGF,GAAE,OAAOE,EAAC;AAAA,YAAC,GAAG,KAAI,EAAE,IAAE,IAAE,KAAI,EAAE;AAAA,UAAE;AAAC,mBAAS,EAAEP,IAAE;AAAC,mBAAM,CAACC,IAAEC,IAAEC,OAAI,EAAED,IAAEF,IAAE,MAAGG,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAEC,IAAEC,IAAE;AAAC,gBAAG,CAAC,EAAED,IAAE,IAAI,GAAE;AAAC,kBAAG,EAAED,IAAEE,EAAC,MAAIF,GAAE,WAAW,SAAO,KAAGA,cAAa,sBAAsB,QAAOC,GAAE,OAAO,IAAI;AAAE,kBAAGA,GAAE,OAAO,IAAE,KAAGD,GAAE,aAAY;AAAC,oBAAIG,KAAEH,GAAE;AAAY,uBAAK,QAAMG,MAAG;AAAC,sBAAG,EAAEA,IAAED,EAAC,EAAE,QAAOD,GAAE,OAAO,IAAI;AAAE,wBAAMD,KAAEE,GAAE,MAAMC,EAAC;AAAE,sBAAGH,MAAGA,GAAE,qBAAqB,EAAE,GAAG,QAAOC,GAAE,OAAO,IAAI;AAAE,kBAAAE,KAAEA,GAAE;AAAA,gBAAU;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,IAAG,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,EAAE,CAAC,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,gBAAM,KAAG,GAAE,EAAE,GAAG,gBAAgB,GAAE,IAAE,OAAO,KAAK,UAAU,QAAQ,IAAE,YAAU;AAAA,UAAU,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,MAAMF,IAAEC,IAAE;AAAC,qBAAM,CAAC,CAAC,UAAS,WAAU,WAAU,UAAU,EAAE,KAAM,CAAAC,OAAG,CAAC,CAACD,GAAEC,EAAC,MAAIF,GAAEE,EAAC,KAAG,SAAOD,GAAEC,EAAC,CAAE,MAAID,GAAE,QAAMD,GAAE,OAAKC,GAAE,QAAMD,GAAE;AAAA,YAAM;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,WAAS,CAAC,GAAE,OAAO,KAAK,KAAK,QAAQ,QAAQ,EAAE,QAAS,CAAAD,OAAG;AAAC,qBAAK,QAAQ,SAASA,EAAC,KAAG,KAAK,WAAW,KAAK,QAAQ,SAASA,EAAC,CAAC;AAAA,cAAC,CAAE,GAAE,KAAK,WAAW,EAAC,KAAI,SAAQ,UAAS,KAAI,GAAE,KAAK,WAAW,GAAE,KAAK,WAAW,EAAC,KAAI,SAAQ,SAAQ,MAAK,SAAQ,MAAK,QAAO,KAAI,GAAG,MAAI;AAAA,cAAC,CAAE,GAAE,WAAW,KAAK,UAAU,SAAS,KAAG,KAAK,WAAW,EAAC,KAAI,YAAW,GAAE,EAAC,WAAU,KAAE,GAAE,KAAK,eAAe,GAAE,KAAK,WAAW,EAAC,KAAI,SAAQ,GAAE,EAAC,WAAU,KAAE,GAAE,KAAK,YAAY,MAAI,KAAK,WAAW,EAAC,KAAI,YAAW,GAAE,EAAC,WAAU,MAAG,QAAO,OAAM,GAAE,KAAK,eAAe,GAAE,KAAK,WAAW,EAAC,KAAI,SAAQ,GAAE,EAAC,WAAU,MAAG,QAAO,OAAM,GAAE,KAAK,YAAY,IAAG,KAAK,WAAW,EAAC,KAAI,YAAW,GAAE,EAAC,WAAU,MAAE,GAAE,KAAK,iBAAiB,GAAE,KAAK,WAAW,EAAC,KAAI,SAAQ,GAAE,EAAC,WAAU,MAAE,GAAE,KAAK,iBAAiB,GAAE,KAAK,WAAW,EAAC,KAAI,aAAY,QAAO,MAAK,SAAQ,MAAK,SAAQ,MAAK,UAAS,KAAI,GAAE,EAAC,WAAU,MAAG,QAAO,EAAC,GAAE,KAAK,eAAe,GAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC,GAAEC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,CAAC;AAAE,oBAAMC,KAAE,SAASH,IAAE;AAAC,oBAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,GAAE,CAAAA,KAAE,EAAC,KAAIA,GAAC;AAAA,qBAAM;AAAC,sBAAG,YAAU,OAAOA,GAAE,QAAO;AAAK,kBAAAA,MAAG,GAAE,EAAE,GAAGA,EAAC;AAAA,gBAAC;AAAC,uBAAOA,GAAE,aAAWA,GAAE,CAAC,IAAEA,GAAE,UAAS,OAAOA,GAAE,WAAUA;AAAA,cAAC,EAAEA,EAAC;AAAE,sBAAMG,MAAG,cAAY,OAAOF,OAAIA,KAAE,EAAC,SAAQA,GAAC,IAAG,cAAY,OAAOC,OAAIA,KAAE,EAAC,SAAQA,GAAC,KAAI,MAAM,QAAQC,GAAE,GAAG,IAAEA,GAAE,MAAI,CAACA,GAAE,GAAG,GAAG,QAAS,CAAAH,OAAG;AAAC,sBAAMI,KAAE,+DAAID,KAAJ,EAAM,KAAIH,OAAKC,KAAKC;AAAG,qBAAK,SAASE,GAAE,GAAG,IAAE,KAAK,SAASA,GAAE,GAAG,KAAG,CAAC,GAAE,KAAK,SAASA,GAAE,GAAG,EAAE,KAAKA,EAAC;AAAA,cAAC,CAAE,KAAG,EAAE,KAAK,6CAA4CD,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,MAAM,KAAK,iBAAiB,WAAW,CAAAH,OAAG;AAAC,oBAAGA,GAAE,oBAAkBA,GAAE,YAAY;AAAO,oBAAG,QAAMA,GAAE,YAAU,YAAUA,GAAE,OAAK,gBAAcA,GAAE,KAAK;AAAO,sBAAMC,MAAG,KAAK,SAASD,GAAE,GAAG,KAAG,CAAC,GAAG,OAAO,KAAK,SAASA,GAAE,KAAK,KAAG,CAAC,CAAC,EAAE,OAAQ,CAAAC,OAAG,EAAE,MAAMD,IAAEC,EAAC,CAAE;AAAE,oBAAG,MAAIA,GAAE,OAAO;AAAO,sBAAMC,KAAE,EAAE,GAAG,KAAKF,GAAE,QAAO,IAAE;AAAE,oBAAGE,MAAGA,GAAE,WAAS,KAAK,MAAM,OAAO;AAAO,sBAAME,KAAE,KAAK,MAAM,aAAa;AAAE,oBAAG,QAAMA,MAAG,CAAC,KAAK,MAAM,SAAS,EAAE;AAAO,sBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQF,GAAE,KAAK,GAAE,CAACK,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQN,GAAE,KAAK,GAAE,CAACO,IAAEC,EAAC,IAAE,MAAIR,GAAE,SAAO,CAACK,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQN,GAAE,QAAMA,GAAE,MAAM,GAAEU,KAAEL,cAAa,EAAE,WAASA,GAAE,MAAM,EAAE,MAAM,GAAEC,EAAC,IAAE,IAAGK,KAAEJ,cAAa,EAAE,WAASA,GAAE,MAAM,EAAE,MAAMC,EAAC,IAAE,IAAGI,KAAE,EAAC,WAAU,MAAIZ,GAAE,QAAO,OAAM,MAAIA,GAAE,UAAQC,GAAE,OAAO,KAAG,GAAE,QAAO,KAAK,MAAM,UAAUD,EAAC,GAAE,MAAKC,IAAE,QAAOC,IAAE,QAAOQ,IAAE,QAAOC,IAAE,OAAMf,GAAC;AAAE,gBAAAC,GAAE,KAAM,CAAAD,OAAG;AAAC,sBAAG,QAAMA,GAAE,aAAWA,GAAE,cAAYgB,GAAE,UAAU,QAAM;AAAG,sBAAG,QAAMhB,GAAE,SAAOA,GAAE,UAAQgB,GAAE,MAAM,QAAM;AAAG,sBAAG,QAAMhB,GAAE,UAAQA,GAAE,WAASgB,GAAE,OAAO,QAAM;AAAG,sBAAG,MAAM,QAAQhB,GAAE,MAAM,GAAE;AAAC,wBAAGA,GAAE,OAAO,MAAO,CAAAA,OAAG,QAAMgB,GAAE,OAAOhB,EAAC,CAAE,EAAE,QAAM;AAAA,kBAAE,WAAS,YAAU,OAAOA,GAAE,UAAQ,CAAC,OAAO,KAAKA,GAAE,MAAM,EAAE,MAAO,CAAAC,OAAG,SAAKD,GAAE,OAAOC,EAAC,IAAE,QAAMe,GAAE,OAAOf,EAAC,IAAE,UAAKD,GAAE,OAAOC,EAAC,IAAE,QAAMe,GAAE,OAAOf,EAAC,KAAG,GAAEE,GAAE,GAAGH,GAAE,OAAOC,EAAC,GAAEe,GAAE,OAAOf,EAAC,CAAC,CAAE,EAAE,QAAM;AAAG,yBAAM,EAAE,QAAMD,GAAE,UAAQ,CAACA,GAAE,OAAO,KAAKgB,GAAE,MAAM,KAAG,QAAMhB,GAAE,UAAQ,CAACA,GAAE,OAAO,KAAKgB,GAAE,MAAM,KAAG,SAAKhB,GAAE,QAAQ,KAAK,MAAKI,IAAEY,IAAEhB,EAAC;AAAA,gBAAE,CAAE,KAAGA,GAAE,eAAe;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,gBAAgBA,IAAEC,IAAE;AAAC,oBAAMC,KAAE,kCAAkC,KAAKD,GAAE,MAAM,IAAE,IAAE;AAAE,kBAAG,MAAID,GAAE,SAAO,KAAK,MAAM,UAAU,KAAG,EAAE;AAAO,kBAAIG,KAAE,CAAC;AAAE,oBAAK,CAACC,EAAC,IAAE,KAAK,MAAM,QAAQJ,GAAE,KAAK;AAAE,kBAAIO,KAAG,KAAI,EAAE,KAAI,OAAOP,GAAE,QAAME,EAAC,EAAE,OAAOA,EAAC;AAAE,kBAAG,MAAID,GAAE,QAAO;AAAC,sBAAK,CAACA,EAAC,IAAE,KAAK,MAAM,QAAQD,GAAE,QAAM,CAAC;AAAE,oBAAGC,MAAG,EAAE,YAAUA,GAAE,QAAQ,YAAUA,GAAE,OAAO,KAAG,IAAG;AAAC,wBAAMA,KAAEG,GAAE,QAAQ,GAAEF,KAAE,KAAK,MAAM,UAAUF,GAAE,QAAM,GAAE,CAAC;AAAE,sBAAGG,KAAE,EAAE,aAAa,KAAKF,IAAEC,EAAC,KAAG,CAAC,GAAE,OAAO,KAAKC,EAAC,EAAE,SAAO,GAAE;AAAC,0BAAMF,KAAG,KAAI,EAAE,KAAI,OAAOD,GAAE,QAAMI,GAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAED,EAAC;AAAE,oBAAAI,KAAEA,GAAE,QAAQN,EAAC;AAAA,kBAAC;AAAA,gBAAC;AAAA,cAAC;AAAC,mBAAK,MAAM,eAAeM,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,MAAM;AAAA,YAAC;AAAA,YAAC,aAAaP,IAAEC,IAAE;AAAC,oBAAMC,KAAE,kCAAkC,KAAKD,GAAE,MAAM,IAAE,IAAE;AAAE,kBAAGD,GAAE,SAAO,KAAK,MAAM,UAAU,IAAEE,GAAE;AAAO,kBAAIC,KAAE,CAAC;AAAE,oBAAK,CAACC,EAAC,IAAE,KAAK,MAAM,QAAQJ,GAAE,KAAK;AAAE,kBAAIO,KAAG,KAAI,EAAE,KAAI,OAAOP,GAAE,KAAK,EAAE,OAAOE,EAAC;AAAE,kBAAGD,GAAE,UAAQG,GAAE,OAAO,IAAE,GAAE;AAAC,sBAAK,CAACH,EAAC,IAAE,KAAK,MAAM,QAAQD,GAAE,QAAM,CAAC;AAAE,oBAAGC,IAAE;AAAC,wBAAMC,KAAEE,GAAE,QAAQ,GAAEE,KAAE,KAAK,MAAM,UAAUN,GAAE,OAAM,CAAC;AAAE,kBAAAG,KAAE,EAAE,aAAa,KAAKD,IAAEI,EAAC,KAAG,CAAC,GAAE,OAAO,KAAKH,EAAC,EAAE,SAAO,MAAII,KAAEA,GAAE,OAAON,GAAE,OAAO,IAAE,CAAC,EAAE,OAAO,GAAEE,EAAC;AAAA,gBAAE;AAAA,cAAC;AAAC,mBAAK,MAAM,eAAeI,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,MAAM;AAAA,YAAC;AAAA,YAAC,kBAAkBP,IAAE;AAAC,gBAAE,EAAC,OAAMA,IAAE,OAAM,KAAK,MAAK,CAAC,GAAE,KAAK,MAAM,MAAM;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMC,KAAE,OAAO,KAAKD,GAAE,MAAM,EAAE,OAAQ,CAACD,IAAEE,QAAK,KAAK,MAAM,OAAO,MAAMA,IAAE,EAAE,MAAM,KAAK,KAAG,CAAC,MAAM,QAAQD,GAAE,OAAOC,EAAC,CAAC,MAAIF,GAAEE,EAAC,IAAED,GAAE,OAAOC,EAAC,IAAGF,KAAI,CAAC,CAAC,GAAEG,KAAG,KAAI,EAAE,KAAI,OAAOH,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,EAAE,OAAO,MAAKE,EAAC;AAAE,mBAAK,MAAM,eAAeC,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaH,GAAE,QAAM,GAAE,EAAE,GAAG,QAAQ,MAAM,GAAE,KAAK,MAAM,MAAM;AAAA,YAAC;AAAA,UAAC;AAAC,gBAAM,IAAE,EAAC,UAAS,EAAC,MAAK,EAAE,MAAM,GAAE,QAAO,EAAE,QAAQ,GAAE,WAAU,EAAE,WAAW,GAAE,QAAO,EAAC,KAAI,OAAM,QAAO,CAAC,cAAa,UAAS,MAAM,GAAE,QAAQA,IAAEC,IAAE;AAAC,mBAAM,EAAE,CAACA,GAAE,aAAW,MAAIA,GAAE,YAAU,KAAK,MAAM,OAAO,UAAS,MAAK,EAAE,GAAG,QAAQ,IAAI,GAAE;AAAA,UAAG,EAAC,GAAE,SAAQ,EAAC,KAAI,OAAM,UAAS,MAAG,QAAO,CAAC,cAAa,UAAS,MAAM,GAAE,QAAQD,IAAEC,IAAE;AAAC,mBAAM,EAAE,CAACA,GAAE,aAAW,MAAIA,GAAE,YAAU,KAAK,MAAM,OAAO,UAAS,MAAK,EAAE,GAAG,QAAQ,IAAI,GAAE;AAAA,UAAG,EAAC,GAAE,qBAAoB,EAAC,KAAI,aAAY,WAAU,MAAG,UAAS,MAAK,SAAQ,MAAK,SAAQ,MAAK,QAAO,MAAK,QAAO,CAAC,UAAS,MAAM,GAAE,QAAO,GAAE,QAAQD,IAAEC,IAAE;AAAC,oBAAMA,GAAE,OAAO,SAAO,KAAK,MAAM,OAAO,UAAS,MAAK,EAAE,GAAG,QAAQ,IAAI,IAAE,QAAMA,GAAE,OAAO,QAAM,KAAK,MAAM,OAAO,QAAO,OAAG,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,GAAE,qBAAoB,EAAE,IAAE,GAAE,sBAAqB,EAAE,KAAE,GAAE,cAAa,EAAC,KAAI,OAAM,UAAS,MAAG,WAAU,MAAG,QAAO,OAAM,QAAQD,IAAE;AAAC,iBAAK,MAAM,WAAWA,GAAE,QAAM,GAAE,GAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,GAAE,KAAI,EAAC,KAAI,OAAM,QAAQA,IAAEC,IAAE;AAAC,gBAAGA,GAAE,OAAO,MAAM,QAAM;AAAG,iBAAK,MAAM,QAAQ,OAAO;AAAE,kBAAMC,KAAG,KAAI,EAAE,KAAI,OAAOF,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,EAAE,OAAO,GAAI;AAAE,mBAAO,KAAK,MAAM,eAAeE,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,QAAQ,OAAO,GAAE,KAAK,MAAM,aAAaF,GAAE,QAAM,GAAE,EAAE,GAAG,QAAQ,MAAM,GAAE;AAAA,UAAE,EAAC,GAAE,0BAAyB,EAAC,KAAI,SAAQ,WAAU,MAAG,QAAO,CAAC,YAAY,GAAE,OAAM,MAAG,UAAS;AAAC,iBAAK,MAAM,OAAO,cAAa,OAAG,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,GAAE,oBAAmB,EAAC,KAAI,SAAQ,WAAU,MAAG,QAAO,CAAC,MAAM,GAAE,OAAM,MAAG,QAAQA,IAAEC,IAAE;AAAC,kBAAMC,KAAE,EAAC,MAAK,MAAE;AAAE,YAAAD,GAAE,OAAO,WAASC,GAAE,SAAO,QAAI,KAAK,MAAM,WAAWF,GAAE,OAAMA,GAAE,QAAOE,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,GAAE,mBAAkB,EAAC,KAAI,SAAQ,WAAU,MAAG,QAAO,EAAC,MAAK,UAAS,GAAE,QAAQF,IAAE;AAAC,kBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQF,GAAE,KAAK,GAAEG,KAAE,iCAAIF,GAAE,QAAQ,IAAd,EAAgB,MAAK,UAAS,IAAEG,KAAG,KAAI,EAAE,KAAI,OAAOJ,GAAE,KAAK,EAAE,OAAO,MAAKG,EAAC,EAAE,OAAOF,GAAE,OAAO,IAAEC,KAAE,CAAC,EAAE,OAAO,GAAE,EAAC,MAAK,YAAW,CAAC;AAAE,iBAAK,MAAM,eAAeE,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaJ,GAAE,QAAM,GAAE,EAAE,GAAG,QAAQ,MAAM,GAAE,KAAK,MAAM,wBAAwB;AAAA,UAAC,EAAC,GAAE,gBAAe,EAAC,KAAI,SAAQ,WAAU,MAAG,QAAO,CAAC,QAAQ,GAAE,QAAO,MAAK,QAAQA,IAAEC,IAAE;AAAC,kBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQH,GAAE,KAAK,GAAEI,KAAG,KAAI,EAAE,KAAI,OAAOJ,GAAE,KAAK,EAAE,OAAO,MAAKC,GAAE,MAAM,EAAE,OAAOC,GAAE,OAAO,IAAEC,KAAE,CAAC,EAAE,OAAO,GAAE,EAAC,QAAO,KAAI,CAAC;AAAE,iBAAK,MAAM,eAAeC,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaJ,GAAE,QAAM,GAAE,EAAE,GAAG,QAAQ,MAAM,GAAE,KAAK,MAAM,wBAAwB;AAAA,UAAC,EAAC,GAAE,mBAAkB,EAAC,KAAI,aAAY,QAAO,CAAC,OAAO,GAAE,WAAU,MAAG,QAAO,GAAE,UAAS;AAAA,UAAC,EAAC,GAAE,gBAAe,EAAC,KAAI,UAAS,QAAO,CAAC,OAAO,GAAE,WAAU,MAAG,QAAO,MAAK,UAAS;AAAA,UAAC,EAAC,GAAE,eAAc,EAAC,KAAI,SAAQ,UAAS,MAAK,QAAO,CAAC,OAAO,GAAE,QAAQA,IAAE;AAAC,kBAAMC,KAAE,KAAK,MAAM,UAAU,OAAO;AAAE,gBAAGA,IAAE;AAAC,oBAAK,CAACC,IAAEC,IAAEC,IAAEC,EAAC,IAAEJ,GAAE,SAASD,EAAC,GAAEO,KAAE,SAASP,IAAEC,IAAEC,IAAEC,IAAE;AAAC,uBAAO,QAAMF,GAAE,QAAM,QAAMA,GAAE,OAAK,QAAMC,GAAE,QAAM,QAAMA,GAAE,OAAK,MAAIC,KAAE,KAAG,IAAE,QAAMD,GAAE,OAAK,KAAG,IAAE,QAAMD,GAAE,OAAK,KAAG,QAAMA,GAAE,OAAK,IAAE;AAAA,cAAI,EAAE,GAAEE,IAAEC,IAAEC,EAAC;AAAE,kBAAG,QAAME,GAAE;AAAO,kBAAIE,KAAEP,GAAE,OAAO;AAAE,kBAAGK,KAAE,GAAE;AAAC,sBAAMN,KAAG,KAAI,EAAE,KAAI,OAAOQ,EAAC,EAAE,OAAO,IAAI;AAAE,qBAAK,MAAM,eAAeR,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaD,GAAE,QAAM,GAAEA,GAAE,QAAO,EAAE,GAAG,QAAQ,MAAM;AAAA,cAAC,WAASO,KAAE,GAAE;AAAC,gBAAAE,MAAGP,GAAE,OAAO;AAAE,sBAAMF,KAAG,KAAI,EAAE,KAAI,OAAOS,EAAC,EAAE,OAAO,IAAI;AAAE,qBAAK,MAAM,eAAeT,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaS,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC,EAAC,GAAE,aAAY,EAAC,KAAI,OAAM,UAAS,MAAK,QAAO,CAAC,OAAO,GAAE,QAAQT,IAAEC,IAAE;AAAC,kBAAK,EAAC,OAAMC,IAAE,MAAKC,GAAC,IAAEF,IAAEG,KAAED,GAAE,OAAO,KAAK,MAAM,MAAM;AAAE,YAAAD,GAAE,WAAS,KAAK,MAAM,aAAaE,KAAE,GAAE,EAAE,GAAG,QAAQ,IAAI,IAAE,KAAK,MAAM,aAAaA,KAAED,GAAE,OAAO,GAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,GAAE,iBAAgB,EAAC,KAAI,KAAI,UAAS,MAAK,WAAU,MAAG,QAAO,EAAC,cAAa,OAAG,YAAW,OAAG,OAAM,MAAE,GAAE,QAAO,mCAAkC,QAAQH,IAAEC,IAAE;AAAC,gBAAG,QAAM,KAAK,MAAM,OAAO,MAAM,MAAM,EAAE,QAAM;AAAG,kBAAK,EAAC,QAAOC,GAAC,IAAED,GAAE,QAAO,CAACE,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQJ,GAAE,KAAK;AAAE,gBAAGI,KAAEF,GAAE,QAAM;AAAG,gBAAIG;AAAE,oBAAOJ,GAAE,OAAO,KAAK,GAAE;AAAA,cAAC,KAAI;AAAA,cAAK,KAAI;AAAM,gBAAAI,KAAE;AAAY;AAAA,cAAM,KAAI;AAAM,gBAAAA,KAAE;AAAU;AAAA,cAAM,KAAI;AAAA,cAAI,KAAI;AAAI,gBAAAA,KAAE;AAAS;AAAA,cAAM;AAAQ,gBAAAA,KAAE;AAAA,YAAS;AAAC,iBAAK,MAAM,WAAWL,GAAE,OAAM,KAAI,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,QAAQ,OAAO;AAAE,kBAAMO,KAAG,KAAI,EAAE,KAAI,OAAOP,GAAE,QAAMI,EAAC,EAAE,OAAOF,KAAE,CAAC,EAAE,OAAOC,GAAE,OAAO,IAAE,IAAEC,EAAC,EAAE,OAAO,GAAE,EAAC,MAAKC,GAAC,CAAC;AAAE,mBAAO,KAAK,MAAM,eAAeE,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,QAAQ,OAAO,GAAE,KAAK,MAAM,aAAaP,GAAE,QAAME,IAAE,EAAE,GAAG,QAAQ,MAAM,GAAE;AAAA,UAAE,EAAC,GAAE,aAAY,EAAC,KAAI,SAAQ,WAAU,MAAG,QAAO,CAAC,YAAY,GAAE,QAAO,MAAK,QAAO,SAAQ,QAAQF,IAAE;AAAC,kBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQF,GAAE,KAAK;AAAE,gBAAIG,KAAE,GAAEC,KAAEH;AAAE,mBAAK,QAAMG,MAAGA,GAAE,OAAO,KAAG,KAAGA,GAAE,QAAQ,EAAE,YAAY,IAAG,KAAGA,KAAEA,GAAE,MAAKD,MAAG,GAAEA,MAAG,GAAE;AAAC,oBAAMA,KAAG,KAAI,EAAE,KAAI,OAAOH,GAAE,QAAMC,GAAE,OAAO,IAAEC,KAAE,CAAC,EAAE,OAAO,GAAE,EAAC,cAAa,KAAI,CAAC,EAAE,OAAO,CAAC;AAAE,qBAAO,KAAK,MAAM,eAAeC,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaH,GAAE,QAAM,GAAE,EAAE,GAAG,QAAQ,MAAM,GAAE;AAAA,YAAE;AAAC,mBAAM;AAAA,UAAE,EAAC,GAAE,cAAa,EAAE,aAAY,KAAE,GAAE,oBAAmB,EAAE,aAAY,IAAE,GAAE,eAAc,EAAE,cAAa,KAAE,GAAE,qBAAoB,EAAE,cAAa,IAAE,GAAE,cAAa,EAAE,KAAE,GAAE,YAAW,EAAE,IAAE,EAAC,EAAC;AAAE,mBAAS,EAAEA,IAAE;AAAC,mBAAM,EAAC,KAAI,OAAM,UAAS,CAACA,IAAE,QAAO,EAAC,cAAa,KAAE,GAAE,QAAQC,IAAEC,IAAE;AAAC,kBAAG,EAAC,OAAMC,GAAC,IAAED;AAAE,oBAAME,KAAE,KAAK,MAAM,OAAO,MAAM,YAAY,GAAE,EAAC,KAAIC,GAAC,IAAED;AAAE,kBAAG,MAAIH,GAAE,UAAQ,CAACE,GAAE,SAAS,QAAO,KAAK,MAAM,WAAWF,GAAE,OAAMI,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,KAAK,MAAM,aAAaJ,GAAE,QAAMI,GAAE,QAAO,EAAE,GAAG,QAAQ,MAAM;AAAE,oBAAMC,KAAE,MAAIL,GAAE,SAAO,KAAK,MAAM,SAASA,GAAE,OAAM,CAAC,IAAE,KAAK,MAAM,SAASA,EAAC;AAAE,kBAAG,EAAC,OAAMM,IAAE,QAAOE,GAAC,IAAER;AAAE,cAAAK,GAAE,QAAS,CAACL,IAAEC,OAAI;AAAC,gBAAAF,MAAGC,GAAE,SAAS,GAAEI,EAAC,GAAE,MAAIH,KAAEK,MAAGF,GAAE,SAAOI,MAAGJ,GAAE,UAAQJ,GAAE,QAAQ,YAAY,WAAWI,EAAC,MAAIJ,GAAE,SAAS,GAAEI,GAAE,MAAM,GAAE,MAAIH,KAAEK,MAAGF,GAAE,SAAOI,MAAGJ,GAAE;AAAA,cAAO,CAAE,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaE,IAAEE,IAAE,EAAE,GAAG,QAAQ,MAAM;AAAA,YAAC,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAET,IAAEC,IAAE;AAAC,mBAAM,EAAC,KAAID,IAAE,UAASC,IAAE,QAAO,MAAK,CAAC,gBAAcD,KAAE,WAAS,QAAQ,GAAE,MAAK,QAAQE,IAAE;AAAC,kBAAG,EAAC,OAAMC,GAAC,IAAED;AAAE,+BAAeF,OAAIG,MAAGD,GAAE,SAAO;AAAG,oBAAK,CAACE,EAAC,IAAE,KAAK,MAAM,QAAQD,EAAC;AAAE,qBAAM,EAAEC,cAAa,EAAE,cAAY,gBAAcJ,KAAEC,KAAE,KAAK,MAAM,aAAaC,GAAE,QAAM,GAAEA,GAAE,SAAO,GAAE,EAAE,GAAG,QAAQ,IAAI,IAAE,KAAK,MAAM,aAAaA,GAAE,QAAM,GAAE,EAAE,GAAG,QAAQ,IAAI,IAAED,KAAE,KAAK,MAAM,aAAaC,GAAE,OAAMA,GAAE,SAAO,GAAE,EAAE,GAAG,QAAQ,IAAI,IAAE,KAAK,MAAM,aAAaA,GAAE,QAAMA,GAAE,SAAO,GAAE,EAAE,GAAG,QAAQ,IAAI,GAAE;AAAA,YAAG,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAE;AAAC,mBAAM,EAAC,KAAIA,GAAE,CAAC,GAAE,UAAS,MAAG,QAAQC,IAAEC,IAAE;AAAC,mBAAK,MAAM,OAAOF,IAAE,CAACE,GAAE,OAAOF,EAAC,GAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,YAAC,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,mBAAM,EAAC,KAAIA,KAAE,YAAU,aAAY,WAAU,MAAG,QAAO,CAAC,OAAO,GAAE,QAAQC,IAAEC,IAAE;AAAC,oBAAMC,KAAEH,KAAE,SAAO,QAAOI,KAAEF,GAAE,MAAKG,KAAED,GAAE,OAAOD,EAAC;AAAE,kBAAG,QAAME,IAAE;AAAC,oBAAG,gBAAcA,GAAE,QAAQ,UAAS;AAAC,sBAAIL,KAAEK,GAAE,SAAS,MAAKJ,KAAEG;AAAE,yBAAK,QAAMH,GAAE,OAAM,CAAAA,KAAEA,GAAE,MAAKD,KAAEA,GAAE;AAAK,wBAAMG,KAAEH,GAAE,OAAO,KAAK,MAAM,MAAM,IAAE,KAAK,IAAIE,GAAE,QAAOF,GAAE,OAAO,IAAE,CAAC;AAAE,uBAAK,MAAM,aAAaG,IAAE,GAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,gBAAC;AAAA,cAAC,OAAK;AAAC,sBAAMF,KAAEG,GAAE,MAAM,EAAED,EAAC;AAAE,wBAAMF,OAAID,KAAE,KAAK,MAAM,aAAaC,GAAE,OAAO,KAAK,MAAM,MAAM,IAAEA,GAAE,OAAO,IAAE,GAAE,GAAE,EAAE,GAAG,QAAQ,IAAI,IAAE,KAAK,MAAM,aAAaA,GAAE,OAAO,KAAK,MAAM,MAAM,GAAE,GAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,cAAE;AAAC,qBAAM;AAAA,YAAE,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAE;AAAC,gBAAG,EAAC,OAAMC,IAAE,OAAMC,GAAC,IAAEF;AAAE,kBAAMG,KAAEF,GAAE,SAASC,EAAC;AAAE,gBAAIE,KAAE,CAAC;AAAE,gBAAGD,GAAE,SAAO,GAAE;AAAC,oBAAMH,KAAEG,GAAE,CAAC,EAAE,QAAQ,GAAEF,KAAEE,GAAEA,GAAE,SAAO,CAAC,EAAE,QAAQ;AAAE,cAAAC,KAAE,EAAE,aAAa,KAAKH,IAAED,EAAC,KAAG,CAAC;AAAA,YAAC;AAAC,YAAAC,GAAE,WAAWC,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,OAAO,KAAKE,EAAC,EAAE,SAAO,KAAGH,GAAE,WAAWC,GAAE,OAAM,GAAEE,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAEH,GAAE,aAAaC,GAAE,OAAM,EAAE,GAAG,QAAQ,MAAM;AAAA,UAAC;AAAC,YAAE,WAAS;AAAA,QAAC,GAAE,MAAK,SAASF,IAAE;AAAC;AAAa,cAAIC,KAAE,OAAO,UAAU,gBAAeC,KAAE;AAAI,mBAASC,KAAG;AAAA,UAAC;AAAC,mBAAS,EAAEH,IAAEC,IAAEC,IAAE;AAAC,iBAAK,KAAGF,IAAE,KAAK,UAAQC,IAAE,KAAK,OAAKC,MAAG;AAAA,UAAE;AAAC,mBAAS,EAAEF,IAAEC,IAAEE,IAAEE,IAAEC,IAAE;AAAC,gBAAG,cAAY,OAAOH,GAAE,OAAM,IAAI,UAAU,iCAAiC;AAAE,gBAAII,KAAE,IAAI,EAAEJ,IAAEE,MAAGL,IAAEM,EAAC,GAAE,IAAEJ,KAAEA,KAAED,KAAEA;AAAE,mBAAOD,GAAE,QAAQ,CAAC,IAAEA,GAAE,QAAQ,CAAC,EAAE,KAAGA,GAAE,QAAQ,CAAC,IAAE,CAACA,GAAE,QAAQ,CAAC,GAAEO,EAAC,IAAEP,GAAE,QAAQ,CAAC,EAAE,KAAKO,EAAC,KAAGP,GAAE,QAAQ,CAAC,IAAEO,IAAEP,GAAE,iBAAgBA;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEC,IAAE;AAAC,iBAAG,EAAED,GAAE,eAAaA,GAAE,UAAQ,IAAIG,OAAE,OAAOH,GAAE,QAAQC,EAAC;AAAA,UAAC;AAAC,mBAAS,IAAG;AAAC,iBAAK,UAAQ,IAAIE,MAAE,KAAK,eAAa;AAAA,UAAC;AAAC,iBAAO,WAASA,GAAE,YAAU,uBAAO,OAAO,IAAI,GAAG,IAAIA,KAAG,cAAYD,KAAE,SAAK,EAAE,UAAU,aAAW,WAAU;AAAC,gBAAIF,IAAEG,IAAEC,KAAE,CAAC;AAAE,gBAAG,MAAI,KAAK,aAAa,QAAOA;AAAE,iBAAID,MAAKH,KAAE,KAAK,QAAQ,CAAAC,GAAE,KAAKD,IAAEG,EAAC,KAAGC,GAAE,KAAKF,KAAEC,GAAE,MAAM,CAAC,IAAEA,EAAC;AAAE,mBAAO,OAAO,wBAAsBC,GAAE,OAAO,OAAO,sBAAsBJ,EAAC,CAAC,IAAEI;AAAA,UAAC,GAAE,EAAE,UAAU,YAAU,SAASJ,IAAE;AAAC,gBAAIC,KAAEC,KAAEA,KAAEF,KAAEA,IAAEG,KAAE,KAAK,QAAQF,EAAC;AAAE,gBAAG,CAACE,GAAE,QAAM,CAAC;AAAE,gBAAGA,GAAE,GAAG,QAAM,CAACA,GAAE,EAAE;AAAE,qBAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOG,KAAE,IAAI,MAAMD,EAAC,GAAED,KAAEC,IAAED,KAAI,CAAAE,GAAEF,EAAC,IAAED,GAAEC,EAAC,EAAE;AAAG,mBAAOE;AAAA,UAAC,GAAE,EAAE,UAAU,gBAAc,SAASN,IAAE;AAAC,gBAAIC,KAAEC,KAAEA,KAAEF,KAAEA,IAAEG,KAAE,KAAK,QAAQF,EAAC;AAAE,mBAAOE,KAAEA,GAAE,KAAG,IAAEA,GAAE,SAAO;AAAA,UAAC,GAAE,EAAE,UAAU,OAAK,SAASH,IAAEC,IAAEE,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAEL,KAAEA,KAAEF,KAAEA;AAAE,gBAAG,CAAC,KAAK,QAAQO,EAAC,EAAE,QAAM;AAAG,gBAAI,GAAE,GAAE,IAAE,KAAK,QAAQA,EAAC,GAAE,IAAE,UAAU;AAAO,gBAAG,EAAE,IAAG;AAAC,sBAAO,EAAE,QAAM,KAAK,eAAeP,IAAE,EAAE,IAAG,QAAO,IAAE,GAAE,GAAE;AAAA,gBAAC,KAAK;AAAE,yBAAO,EAAE,GAAG,KAAK,EAAE,OAAO,GAAE;AAAA,gBAAG,KAAK;AAAE,yBAAO,EAAE,GAAG,KAAK,EAAE,SAAQC,EAAC,GAAE;AAAA,gBAAG,KAAK;AAAE,yBAAO,EAAE,GAAG,KAAK,EAAE,SAAQA,IAAEE,EAAC,GAAE;AAAA,gBAAG,KAAK;AAAE,yBAAO,EAAE,GAAG,KAAK,EAAE,SAAQF,IAAEE,IAAEC,EAAC,GAAE;AAAA,gBAAG,KAAK;AAAE,yBAAO,EAAE,GAAG,KAAK,EAAE,SAAQH,IAAEE,IAAEC,IAAEC,EAAC,GAAE;AAAA,gBAAG,KAAK;AAAE,yBAAO,EAAE,GAAG,KAAK,EAAE,SAAQJ,IAAEE,IAAEC,IAAEC,IAAEC,EAAC,GAAE;AAAA,cAAE;AAAC,mBAAI,IAAE,GAAE,IAAE,IAAI,MAAM,IAAE,CAAC,GAAE,IAAE,GAAE,IAAI,GAAE,IAAE,CAAC,IAAE,UAAU,CAAC;AAAE,gBAAE,GAAG,MAAM,EAAE,SAAQ,CAAC;AAAA,YAAC,OAAK;AAAC,kBAAI,GAAE,IAAE,EAAE;AAAO,mBAAI,IAAE,GAAE,IAAE,GAAE,IAAI,SAAO,EAAE,CAAC,EAAE,QAAM,KAAK,eAAeN,IAAE,EAAE,CAAC,EAAE,IAAG,QAAO,IAAE,GAAE,GAAE;AAAA,gBAAC,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,OAAO;AAAE;AAAA,gBAAM,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,SAAQC,EAAC;AAAE;AAAA,gBAAM,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,SAAQA,IAAEE,EAAC;AAAE;AAAA,gBAAM,KAAK;AAAE,oBAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,EAAE,SAAQF,IAAEE,IAAEC,EAAC;AAAE;AAAA,gBAAM;AAAQ,sBAAG,CAAC,EAAE,MAAI,IAAE,GAAE,IAAE,IAAI,MAAM,IAAE,CAAC,GAAE,IAAE,GAAE,IAAI,GAAE,IAAE,CAAC,IAAE,UAAU,CAAC;AAAE,oBAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,SAAQ,CAAC;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAM;AAAA,UAAE,GAAE,EAAE,UAAU,KAAG,SAASJ,IAAEC,IAAEC,IAAE;AAAC,mBAAO,EAAE,MAAKF,IAAEC,IAAEC,IAAE,KAAE;AAAA,UAAC,GAAE,EAAE,UAAU,OAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC,mBAAO,EAAE,MAAKF,IAAEC,IAAEC,IAAE,IAAE;AAAA,UAAC,GAAE,EAAE,UAAU,iBAAe,SAASF,IAAEC,IAAEE,IAAEC,IAAE;AAAC,gBAAIC,KAAEH,KAAEA,KAAEF,KAAEA;AAAE,gBAAG,CAAC,KAAK,QAAQK,EAAC,EAAE,QAAO;AAAK,gBAAG,CAACJ,GAAE,QAAO,EAAE,MAAKI,EAAC,GAAE;AAAK,gBAAIE,KAAE,KAAK,QAAQF,EAAC;AAAE,gBAAGE,GAAE,GAAG,CAAAA,GAAE,OAAKN,MAAGG,MAAG,CAACG,GAAE,QAAMJ,MAAGI,GAAE,YAAUJ,MAAG,EAAE,MAAKE,EAAC;AAAA,iBAAM;AAAC,uBAAQ,IAAE,GAAE,IAAE,CAAC,GAAE,IAAEE,GAAE,QAAO,IAAE,GAAE,IAAI,EAACA,GAAE,CAAC,EAAE,OAAKN,MAAGG,MAAG,CAACG,GAAE,CAAC,EAAE,QAAMJ,MAAGI,GAAE,CAAC,EAAE,YAAUJ,OAAI,EAAE,KAAKI,GAAE,CAAC,CAAC;AAAE,gBAAE,SAAO,KAAK,QAAQF,EAAC,IAAE,MAAI,EAAE,SAAO,EAAE,CAAC,IAAE,IAAE,EAAE,MAAKA,EAAC;AAAA,YAAC;AAAC,mBAAO;AAAA,UAAI,GAAE,EAAE,UAAU,qBAAmB,SAASL,IAAE;AAAC,gBAAIC;AAAE,mBAAOD,MAAGC,KAAEC,KAAEA,KAAEF,KAAEA,IAAE,KAAK,QAAQC,EAAC,KAAG,EAAE,MAAKA,EAAC,MAAI,KAAK,UAAQ,IAAIE,MAAE,KAAK,eAAa,IAAG;AAAA,UAAI,GAAE,EAAE,UAAU,MAAI,EAAE,UAAU,gBAAe,EAAE,UAAU,cAAY,EAAE,UAAU,IAAG,EAAE,WAASD,IAAE,EAAE,eAAa,GAAEF,GAAE,UAAQ;AAAA,QAAC,GAAE,MAAK,SAASA,IAAE;AAAC,cAAIC,KAAE,IAAGC,KAAE,GAAEC,KAAE;AAAE,mBAAS,EAAEH,IAAEe,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAGlB,OAAIe,GAAE,QAAOf,KAAE,CAAC,CAACG,IAAEH,EAAC,CAAC,IAAE,CAAC;AAAE,gBAAG,QAAMgB,IAAE;AAAC,kBAAIG,KAAE,SAASnB,IAAEC,IAAEC,IAAE;AAAC,oBAAIC,KAAE,YAAU,OAAOD,KAAE,EAAC,OAAMA,IAAE,QAAO,EAAC,IAAEA,GAAE,UAASE,KAAE,YAAU,OAAOF,KAAE,OAAKA,GAAE,UAASG,KAAEL,GAAE,QAAOM,KAAEL,GAAE;AAAO,oBAAG,MAAIE,GAAE,WAAS,SAAOC,MAAG,MAAIA,GAAE,SAAQ;AAAC,sBAAIG,KAAEJ,GAAE,OAAMK,KAAER,GAAE,MAAM,GAAEO,EAAC,GAAEE,KAAET,GAAE,MAAMO,EAAC,GAAEG,KAAEN,KAAEA,GAAE,QAAM,MAAKO,KAAEJ,KAAED,KAAED;AAAE,uBAAI,SAAOK,MAAGA,OAAIC,OAAI,EAAEA,KAAE,KAAGA,KAAEL,KAAG;AAAC,wBAAIM,KAAEX,GAAE,MAAM,GAAEU,EAAC;AAAE,yBAAII,KAAEd,GAAE,MAAMU,EAAC,OAAKF,IAAE;AAAC,0BAAII,KAAE,KAAK,IAAIN,IAAEI,EAAC;AAAE,2BAAIM,KAAET,GAAE,MAAM,GAAEK,EAAC,QAAMM,KAAEP,GAAE,MAAM,GAAEC,EAAC,GAAG,QAAO,EAAEI,IAAET,GAAE,MAAMK,EAAC,GAAED,GAAE,MAAMC,EAAC,GAAEJ,EAAC;AAAA,oBAAC;AAAA,kBAAC;AAAC,sBAAG,SAAOC,MAAGA,OAAIH,IAAE;AAAC,wBAAIO,KAAEP,IAAEQ,MAAGH,KAAEX,GAAE,MAAM,GAAEa,EAAC,GAAEb,GAAE,MAAMa,EAAC;AAAG,wBAAGF,OAAIJ,IAAE;AAAC,0BAAIQ,KAAE,KAAK,IAAIX,KAAES,IAAER,KAAEQ,EAAC;AAAE,2BAAII,KAAET,GAAE,MAAMA,GAAE,SAAOO,EAAC,QAAMI,KAAEL,GAAE,MAAMA,GAAE,SAAOC,EAAC,GAAG,QAAO,EAAER,IAAEC,GAAE,MAAM,GAAEA,GAAE,SAAOO,EAAC,GAAED,GAAE,MAAM,GAAEA,GAAE,SAAOC,EAAC,GAAEE,EAAC;AAAA,oBAAC;AAAA,kBAAC;AAAA,gBAAC;AAAC,oBAAGf,GAAE,SAAO,KAAGC,MAAG,MAAIA,GAAE,QAAO;AAAC,sBAAIa,KAAEjB,GAAE,MAAM,GAAEG,GAAE,KAAK,GAAEe,KAAElB,GAAE,MAAMG,GAAE,QAAMA,GAAE,MAAM;AAAE,sBAAG,EAAEG,MAAGO,KAAEI,GAAE,WAASD,KAAEE,GAAE,UAAS;AAAC,wBAAIC,KAAElB,GAAE,MAAM,GAAEY,EAAC,GAAEO,KAAEnB,GAAE,MAAMK,KAAEU,EAAC;AAAE,wBAAGC,OAAIE,MAAGD,OAAIE,GAAE,QAAO,EAAEH,IAAEjB,GAAE,MAAMa,IAAER,KAAEW,EAAC,GAAEf,GAAE,MAAMY,IAAEP,KAAEU,EAAC,GAAEE,EAAC;AAAA,kBAAC;AAAA,gBAAC;AAAC,uBAAO;AAAA,cAAI,EAAElB,IAAEe,IAAEC,EAAC;AAAE,kBAAGG,GAAE,QAAOA;AAAA,YAAC;AAAC,gBAAI,IAAE,EAAEnB,IAAEe,EAAC,GAAE,IAAEf,GAAE,UAAU,GAAE,CAAC;AAAE,gBAAE,EAAEA,KAAEA,GAAE,UAAU,CAAC,GAAEe,KAAEA,GAAE,UAAU,CAAC,CAAC;AAAE,gBAAI,IAAEf,GAAE,UAAUA,GAAE,SAAO,CAAC,GAAE,IAAE,SAASA,IAAEO,IAAE;AAAC,kBAAIE;AAAE,kBAAG,CAACT,GAAE,QAAM,CAAC,CAACE,IAAEK,EAAC,CAAC;AAAE,kBAAG,CAACA,GAAE,QAAM,CAAC,CAACN,IAAED,EAAC,CAAC;AAAE,kBAAIU,KAAEV,GAAE,SAAOO,GAAE,SAAOP,KAAEO,IAAEI,KAAEX,GAAE,SAAOO,GAAE,SAAOA,KAAEP,IAAEY,KAAEF,GAAE,QAAQC,EAAC;AAAE,kBAAG,OAAKC,GAAE,QAAOH,KAAE,CAAC,CAACP,IAAEQ,GAAE,UAAU,GAAEE,EAAC,CAAC,GAAE,CAACT,IAAEQ,EAAC,GAAE,CAACT,IAAEQ,GAAE,UAAUE,KAAED,GAAE,MAAM,CAAC,CAAC,GAAEX,GAAE,SAAOO,GAAE,WAASE,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,CAAC,IAAER,KAAGQ;AAAE,kBAAG,MAAIE,GAAE,OAAO,QAAM,CAAC,CAACV,IAAED,EAAC,GAAE,CAACE,IAAEK,EAAC,CAAC;AAAE,kBAAIM,KAAE,SAASb,IAAEC,IAAE;AAAC,oBAAIC,KAAEF,GAAE,SAAOC,GAAE,SAAOD,KAAEC,IAAEE,KAAEH,GAAE,SAAOC,GAAE,SAAOA,KAAED;AAAE,oBAAGE,GAAE,SAAO,KAAG,IAAEC,GAAE,SAAOD,GAAE,OAAO,QAAO;AAAK,yBAASE,GAAEJ,IAAEC,IAAEC,IAAE;AAAC,2BAAQC,IAAEC,IAAEC,IAAEE,IAAEE,KAAET,GAAE,UAAUE,IAAEA,KAAE,KAAK,MAAMF,GAAE,SAAO,CAAC,CAAC,GAAEU,KAAE,IAAGC,KAAE,IAAG,QAAMD,KAAET,GAAE,QAAQQ,IAAEC,KAAE,CAAC,MAAI;AAAC,wBAAIE,KAAE,EAAEZ,GAAE,UAAUE,EAAC,GAAED,GAAE,UAAUS,EAAC,CAAC,GAAEG,KAAE,EAAEb,GAAE,UAAU,GAAEE,EAAC,GAAED,GAAE,UAAU,GAAES,EAAC,CAAC;AAAE,oBAAAC,GAAE,SAAOE,KAAED,OAAID,KAAEV,GAAE,UAAUS,KAAEG,IAAEH,EAAC,IAAET,GAAE,UAAUS,IAAEA,KAAEE,EAAC,GAAET,KAAEH,GAAE,UAAU,GAAEE,KAAEW,EAAC,GAAET,KAAEJ,GAAE,UAAUE,KAAEU,EAAC,GAAEP,KAAEJ,GAAE,UAAU,GAAES,KAAEG,EAAC,GAAEN,KAAEN,GAAE,UAAUS,KAAEE,EAAC;AAAA,kBAAE;AAAC,yBAAO,IAAED,GAAE,UAAQX,GAAE,SAAO,CAACG,IAAEC,IAAEC,IAAEE,IAAEI,EAAC,IAAE;AAAA,gBAAI;AAAC,oBAAIN,IAAEE,IAAEE,IAAEC,IAAEC,IAAEC,KAAER,GAAEF,IAAEC,IAAE,KAAK,KAAKD,GAAE,SAAO,CAAC,CAAC,GAAEW,KAAET,GAAEF,IAAEC,IAAE,KAAK,KAAKD,GAAE,SAAO,CAAC,CAAC;AAAE,uBAAOU,MAAGC,MAAGR,KAAEQ,KAAED,MAAGA,GAAE,CAAC,EAAE,SAAOC,GAAE,CAAC,EAAE,SAAOD,KAAEC,KAAED,IAAEZ,GAAE,SAAOC,GAAE,UAAQM,KAAEF,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,MAAIK,KAAEL,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC,IAAG,CAACE,IAAEE,IAAEC,IAAEC,IAAEN,GAAE,CAAC,CAAC,KAAG;AAAA,cAAI,EAAEL,IAAEO,EAAC;AAAE,kBAAGM,IAAE;AAAC,oBAAIC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,GAAEG,KAAEH,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,GAAEQ,KAAE,EAAEP,IAAEE,EAAC,GAAEG,KAAE,EAAEJ,IAAEE,EAAC;AAAE,uBAAOI,GAAE,OAAO,CAAC,CAAClB,IAAEe,EAAC,CAAC,GAAEC,EAAC;AAAA,cAAC;AAAC,qBAAO,SAASnB,IAAEG,IAAE;AAAC,yBAAQC,KAAEJ,GAAE,QAAOM,KAAEH,GAAE,QAAOI,KAAE,KAAK,MAAMH,KAAEE,MAAG,CAAC,GAAEE,KAAED,IAAEE,KAAE,IAAEF,IAAEG,KAAE,IAAI,MAAMD,EAAC,GAAEE,KAAE,IAAI,MAAMF,EAAC,GAAEG,KAAE,GAAEA,KAAEH,IAAEG,KAAI,CAAAF,GAAEE,EAAC,IAAE,IAAGD,GAAEC,EAAC,IAAE;AAAG,gBAAAF,GAAEF,KAAE,CAAC,IAAE,GAAEG,GAAEH,KAAE,CAAC,IAAE;AAAE,yBAAQK,KAAET,KAAEE,IAAEQ,KAAED,KAAE,KAAG,GAAEE,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEG,KAAE,GAAEA,KAAEd,IAAEc,MAAI;AAAC,2BAAQF,KAAE,CAACE,KAAEN,IAAEI,MAAGE,KAAEL,IAAEG,MAAG,GAAE;AAAC,6BAAQC,KAAEZ,KAAEW,IAAEG,MAAG,IAAEH,OAAI,CAACE,MAAGF,OAAIE,MAAGX,GAAEU,KAAE,CAAC,IAAEV,GAAEU,KAAE,CAAC,IAAEV,GAAEU,KAAE,CAAC,IAAEV,GAAEU,KAAE,CAAC,IAAE,KAAGD,IAAE,IAAEf,MAAGkB,KAAEhB,MAAGN,GAAE,OAAO,CAAC,MAAIG,GAAE,OAAOmB,EAAC,IAAG,MAAIA;AAAI,wBAAGZ,GAAEU,EAAC,IAAE,GAAE,IAAEhB,GAAE,CAAAY,MAAG;AAAA,6BAAUM,KAAEhB,GAAE,CAAAS,MAAG;AAAA,6BAAUD,OAAI,IAAEN,KAAEK,KAAEM,OAAI,KAAG,IAAEV,MAAG,OAAKE,GAAE,CAAC,KAAG,MAAIY,KAAEnB,KAAEO,GAAE,CAAC,GAAG,QAAO,EAAEX,IAAEG,IAAE,GAAEmB,EAAC;AAAA,kBAAC;AAAC,2BAAQE,KAAE,CAACH,KAAEJ,IAAEO,MAAGH,KAAEH,IAAEM,MAAG,GAAE;AAAC,6BAAQD,IAAE,IAAEf,KAAEgB,IAAE,KAAGD,KAAEC,OAAI,CAACH,MAAGG,OAAIH,MAAGV,GAAE,IAAE,CAAC,IAAEA,GAAE,IAAE,CAAC,IAAEA,GAAE,IAAE,CAAC,IAAEA,GAAE,IAAE,CAAC,IAAE,KAAGa,IAAED,KAAEnB,MAAG,IAAEE,MAAGN,GAAE,OAAOI,KAAEmB,KAAE,CAAC,MAAIpB,GAAE,OAAOG,KAAE,IAAE,CAAC,IAAG,CAAAiB,MAAI;AAAI,wBAAGZ,GAAE,CAAC,IAAEY,IAAEA,KAAEnB,GAAE,CAAAc,MAAG;AAAA,6BAAU,IAAEZ,GAAE,CAAAW,MAAG;AAAA,6BAAU,CAACH,IAAE;AAAC,0BAAI;AAAE,2BAAIM,KAAEZ,KAAEK,KAAEW,OAAI,KAAGJ,KAAEX,MAAG,OAAKC,GAAEU,EAAC;AAAE,4BAAGE,KAAEd,MAAG,IAAEE,GAAEU,EAAC,KAAGA,IAAE,MAAIG,KAAEnB,KAAEmB,IAAG,QAAO,EAAEvB,IAAEG,IAAE,GAAEmB,EAAC;AAAA;AAAA,oBAAC;AAAA,kBAAC;AAAA,gBAAC;AAAC,uBAAM,CAAC,CAACrB,IAAED,EAAC,GAAE,CAACE,IAAEC,EAAC,CAAC;AAAA,cAAC,EAAEH,IAAEO,EAAC;AAAA,YAAC,EAAEP,KAAEA,GAAE,UAAU,GAAEA,GAAE,SAAO,CAAC,GAAEe,KAAEA,GAAE,UAAU,GAAEA,GAAE,SAAO,CAAC,CAAC;AAAE,mBAAO,KAAG,EAAE,QAAQ,CAACZ,IAAE,CAAC,CAAC,GAAE,KAAG,EAAE,KAAK,CAACA,IAAE,CAAC,CAAC,GAAE,EAAE,GAAEe,EAAC,GAAED,MAAG,SAASjB,IAAE;AAAC,uBAAQI,KAAE,OAAGC,KAAE,CAAC,GAAEC,KAAE,GAAES,KAAE,MAAKC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEG,KAAE,GAAEF,KAAE,GAAEH,KAAEhB,GAAE,SAAQ,CAAAA,GAAEgB,EAAC,EAAE,CAAC,KAAGb,MAAGE,GAAEC,IAAG,IAAEU,IAAEC,KAAEI,IAAEH,KAAEC,IAAEE,KAAE,GAAEF,KAAE,GAAEJ,KAAEf,GAAEgB,EAAC,EAAE,CAAC,MAAIhB,GAAEgB,EAAC,EAAE,CAAC,KAAGd,KAAEmB,MAAGrB,GAAEgB,EAAC,EAAE,CAAC,EAAE,SAAOG,MAAGnB,GAAEgB,EAAC,EAAE,CAAC,EAAE,QAAOD,MAAGA,GAAE,UAAQ,KAAK,IAAIE,IAAEC,EAAC,KAAGH,GAAE,UAAQ,KAAK,IAAIM,IAAEF,EAAC,MAAInB,GAAE,OAAOK,GAAEC,KAAE,CAAC,GAAE,GAAE,CAACL,IAAEc,EAAC,CAAC,GAAEf,GAAEK,GAAEC,KAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAEJ,IAAEI,MAAIU,KAAE,EAAEV,KAAE,IAAED,GAAEC,KAAE,CAAC,IAAE,IAAGW,KAAE,GAAEC,KAAE,GAAEG,KAAE,GAAEF,KAAE,GAAEJ,KAAE,MAAKX,KAAE,QAAKY;AAAI,mBAAIZ,MAAG,EAAEJ,EAAC,GAAE,SAASA,IAAE;AAAC,yBAASC,GAAED,IAAEC,IAAE;AAAC,sBAAG,CAACD,MAAG,CAACC,GAAE,QAAO;AAAE,sBAAIC,KAAEF,GAAE,OAAOA,GAAE,SAAO,CAAC,GAAEG,KAAEF,GAAE,OAAO,CAAC,GAAEG,KAAEF,GAAE,MAAM,CAAC,GAAEG,KAAEF,GAAE,MAAM,CAAC,GAAEG,KAAEF,MAAGF,GAAE,MAAM,CAAC,GAAEK,KAAEF,MAAGF,GAAE,MAAM,CAAC,GAAEK,KAAEF,MAAGJ,GAAE,MAAM,CAAC,GAAEY,KAAEP,MAAGJ,GAAE,MAAM,CAAC,GAAEY,KAAEP,MAAGR,GAAE,MAAM,CAAC,GAAEgB,KAAEF,MAAGb,GAAE,MAAM,CAAC;AAAE,yBAAOc,MAAGC,KAAE,IAAER,MAAGM,KAAE,IAAEV,MAAG,CAACE,MAAGC,KAAE,IAAED,MAAGC,KAAE,IAAEH,MAAGC,KAAE,IAAE;AAAA,gBAAC;AAAC,yBAAQH,KAAE,GAAEA,KAAEF,GAAE,SAAO,KAAG;AAAC,sBAAGA,GAAEE,KAAE,CAAC,EAAE,CAAC,KAAGC,MAAGH,GAAEE,KAAE,CAAC,EAAE,CAAC,KAAGC,IAAE;AAAC,wBAAIC,KAAEJ,GAAEE,KAAE,CAAC,EAAE,CAAC,GAAEG,KAAEL,GAAEE,EAAC,EAAE,CAAC,GAAEI,KAAEN,GAAEE,KAAE,CAAC,EAAE,CAAC,GAAEK,KAAE,EAAEH,IAAEC,EAAC;AAAE,wBAAGE,IAAE;AAAC,0BAAIO,KAAET,GAAE,UAAUA,GAAE,SAAOE,EAAC;AAAE,sBAAAH,KAAEA,GAAE,UAAU,GAAEA,GAAE,SAAOG,EAAC,GAAEF,KAAES,KAAET,GAAE,UAAU,GAAEA,GAAE,SAAOE,EAAC,GAAED,KAAEQ,KAAER;AAAA,oBAAC;AAAC,6BAAQS,KAAEX,IAAEY,KAAEX,IAAEY,KAAEX,IAAEY,KAAEjB,GAAEG,IAAEC,EAAC,IAAEJ,GAAEI,IAAEC,EAAC,GAAED,GAAE,OAAO,CAAC,MAAIC,GAAE,OAAO,CAAC,KAAG;AAAC,sBAAAF,MAAGC,GAAE,OAAO,CAAC,GAAEA,KAAEA,GAAE,UAAU,CAAC,IAAEC,GAAE,OAAO,CAAC,GAAEA,KAAEA,GAAE,UAAU,CAAC;AAAE,0BAAIe,KAAEpB,GAAEG,IAAEC,EAAC,IAAEJ,GAAEI,IAAEC,EAAC;AAAE,sBAAAe,MAAGH,OAAIA,KAAEG,IAAEN,KAAEX,IAAEY,KAAEX,IAAEY,KAAEX;AAAA,oBAAE;AAAC,oBAAAN,GAAEE,KAAE,CAAC,EAAE,CAAC,KAAGa,OAAIA,KAAEf,GAAEE,KAAE,CAAC,EAAE,CAAC,IAAEa,MAAGf,GAAE,OAAOE,KAAE,GAAE,CAAC,GAAEA,OAAKF,GAAEE,EAAC,EAAE,CAAC,IAAEc,IAAEC,KAAEjB,GAAEE,KAAE,CAAC,EAAE,CAAC,IAAEe,MAAGjB,GAAE,OAAOE,KAAE,GAAE,CAAC,GAAEA;AAAA,kBAAK;AAAC,kBAAAA;AAAA,gBAAG;AAAA,cAAC,EAAEF,EAAC,GAAEgB,KAAE,GAAEA,KAAEhB,GAAE,UAAQ;AAAC,oBAAGA,GAAEgB,KAAE,CAAC,EAAE,CAAC,KAAGf,MAAGD,GAAEgB,EAAC,EAAE,CAAC,KAAGd,IAAE;AAAC,sBAAIkB,KAAEpB,GAAEgB,KAAE,CAAC,EAAE,CAAC,GAAEM,KAAEtB,GAAEgB,EAAC,EAAE,CAAC,GAAEQ,KAAE,EAAEJ,IAAEE,EAAC,GAAEC,KAAE,EAAED,IAAEF,EAAC;AAAE,kBAAAI,MAAGD,MAAGC,MAAGJ,GAAE,SAAO,KAAGI,MAAGF,GAAE,SAAO,OAAKtB,GAAE,OAAOgB,IAAE,GAAE,CAACb,IAAEmB,GAAE,UAAU,GAAEE,EAAC,CAAC,CAAC,GAAExB,GAAEgB,KAAE,CAAC,EAAE,CAAC,IAAEI,GAAE,UAAU,GAAEA,GAAE,SAAOI,EAAC,GAAExB,GAAEgB,KAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,UAAUE,EAAC,GAAER,SAAMO,MAAGH,GAAE,SAAO,KAAGG,MAAGD,GAAE,SAAO,OAAKtB,GAAE,OAAOgB,IAAE,GAAE,CAACb,IAAEiB,GAAE,UAAU,GAAEG,EAAC,CAAC,CAAC,GAAEvB,GAAEgB,KAAE,CAAC,EAAE,CAAC,IAAEd,IAAEF,GAAEgB,KAAE,CAAC,EAAE,CAAC,IAAEM,GAAE,UAAU,GAAEA,GAAE,SAAOC,EAAC,GAAEvB,GAAEgB,KAAE,CAAC,EAAE,CAAC,IAAEf,IAAED,GAAEgB,KAAE,CAAC,EAAE,CAAC,IAAEI,GAAE,UAAUG,EAAC,GAAEP,OAAKA;AAAA,gBAAG;AAAC,gBAAAA;AAAA,cAAG;AAAA,YAAC,EAAE,CAAC,GAAE;AAAA,UAAC;AAAC,mBAAS,EAAEhB,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIE,KAAEL,GAAE,UAAU,GAAEE,EAAC,GAAEI,KAAEL,GAAE,UAAU,GAAEE,EAAC,GAAEI,KAAEP,GAAE,UAAUE,EAAC,GAAEM,KAAEP,GAAE,UAAUE,EAAC,GAAEM,KAAE,EAAEJ,IAAEC,EAAC,GAAEI,KAAE,EAAEH,IAAEC,EAAC;AAAE,mBAAOC,GAAE,OAAOC,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEV,IAAEC,IAAE;AAAC,gBAAG,CAACD,MAAG,CAACC,MAAGD,GAAE,OAAO,CAAC,MAAIC,GAAE,OAAO,CAAC,EAAE,QAAO;AAAE,qBAAQC,KAAE,GAAEC,KAAE,KAAK,IAAIH,GAAE,QAAOC,GAAE,MAAM,GAAEG,KAAED,IAAEE,KAAE,GAAEH,KAAEE,KAAG,CAAAJ,GAAE,UAAUK,IAAED,EAAC,KAAGH,GAAE,UAAUI,IAAED,EAAC,IAAEC,KAAEH,KAAEE,KAAED,KAAEC,IAAEA,KAAE,KAAK,OAAOD,KAAED,MAAG,IAAEA,EAAC;AAAE,mBAAO,EAAEF,GAAE,WAAWI,KAAE,CAAC,CAAC,KAAGA,MAAIA;AAAA,UAAC;AAAC,mBAAS,EAAEJ,IAAEC,IAAE;AAAC,gBAAIC,KAAEF,GAAE,QAAOG,KAAEF,GAAE;AAAO,gBAAG,KAAGC,MAAG,KAAGC,GAAE,QAAO;AAAE,YAAAD,KAAEC,KAAEH,KAAEA,GAAE,UAAUE,KAAEC,EAAC,IAAED,KAAEC,OAAIF,KAAEA,GAAE,UAAU,GAAEC,EAAC;AAAG,gBAAIE,KAAE,KAAK,IAAIF,IAAEC,EAAC;AAAE,gBAAGH,MAAGC,GAAE,QAAOG;AAAE,qBAAQC,KAAE,GAAEC,KAAE,OAAI;AAAC,kBAAIC,KAAEP,GAAE,UAAUI,KAAEE,EAAC,GAAEE,KAAEP,GAAE,QAAQM,EAAC;AAAE,kBAAG,MAAIC,GAAE,QAAOH;AAAE,cAAAC,MAAGE,IAAE,KAAGA,MAAGR,GAAE,UAAUI,KAAEE,EAAC,KAAGL,GAAE,UAAU,GAAEK,EAAC,MAAID,KAAEC,IAAEA;AAAA,YAAI;AAAA,UAAC;AAAC,mBAAS,EAAEN,IAAEC,IAAE;AAAC,gBAAG,CAACD,MAAG,CAACC,MAAGD,GAAE,MAAM,EAAE,MAAIC,GAAE,MAAM,EAAE,EAAE,QAAO;AAAE,qBAAQC,KAAE,GAAEC,KAAE,KAAK,IAAIH,GAAE,QAAOC,GAAE,MAAM,GAAEG,KAAED,IAAEE,KAAE,GAAEH,KAAEE,KAAG,CAAAJ,GAAE,UAAUA,GAAE,SAAOI,IAAEJ,GAAE,SAAOK,EAAC,KAAGJ,GAAE,UAAUA,GAAE,SAAOG,IAAEH,GAAE,SAAOI,EAAC,IAAEA,KAAEH,KAAEE,KAAED,KAAEC,IAAEA,KAAE,KAAK,OAAOD,KAAED,MAAG,IAAEA,EAAC;AAAE,mBAAO,EAAEF,GAAE,WAAWA,GAAE,SAAOI,EAAC,CAAC,KAAGA,MAAIA;AAAA,UAAC;AAAC,cAAI,IAAE,gBAAe,IAAE,MAAK,IAAE,UAAS,IAAE,YAAW,IAAE;AAAc,mBAAS,EAAEJ,IAAEI,IAAE;AAAC,YAAAJ,GAAE,KAAK,CAACG,IAAE,EAAE,CAAC;AAAE,qBAAQE,IAAEE,KAAE,GAAEE,KAAE,GAAEC,KAAE,GAAEC,KAAE,IAAGC,KAAE,IAAGL,KAAEP,GAAE,SAAQ,KAAGO,KAAEP,GAAE,SAAO,KAAG,CAACA,GAAEO,EAAC,EAAE,CAAC,EAAE,CAAAP,GAAE,OAAOO,IAAE,CAAC;AAAA,gBAAO,SAAOP,GAAEO,EAAC,EAAE,CAAC,GAAE;AAAA,cAAC,KAAKL;AAAE,gBAAAQ,MAAIE,MAAGZ,GAAEO,EAAC,EAAE,CAAC,GAAEA;AAAI;AAAA,cAAM,KAAKN;AAAE,gBAAAQ,MAAIE,MAAGX,GAAEO,EAAC,EAAE,CAAC,GAAEA;AAAI;AAAA,cAAM,KAAKJ;AAAE,oBAAIU,KAAEN,KAAEG,KAAED,KAAE;AAAE,oBAAGL,IAAE;AAAC,sBAAGS,MAAG,KAAG,EAAEb,GAAEa,EAAC,EAAE,CAAC,CAAC,GAAE;AAAC,wBAAIE,KAAEf,GAAEa,EAAC,EAAE,CAAC,EAAE,MAAM,EAAE;AAAE,wBAAGb,GAAEa,EAAC,EAAE,CAAC,IAAEb,GAAEa,EAAC,EAAE,CAAC,EAAE,MAAM,GAAE,EAAE,GAAEF,KAAEI,KAAEJ,IAAEC,KAAEG,KAAEH,IAAE,CAACZ,GAAEa,EAAC,EAAE,CAAC,GAAE;AAAC,sBAAAb,GAAE,OAAOa,IAAE,CAAC,GAAEN;AAAI,0BAAIS,KAAEH,KAAE;AAAE,sBAAAb,GAAEgB,EAAC,KAAGhB,GAAEgB,EAAC,EAAE,CAAC,MAAId,OAAIQ,MAAIE,KAAEZ,GAAEgB,EAAC,EAAE,CAAC,IAAEJ,IAAEI,OAAKhB,GAAEgB,EAAC,KAAGhB,GAAEgB,EAAC,EAAE,CAAC,MAAIf,OAAIQ,MAAIE,KAAEX,GAAEgB,EAAC,EAAE,CAAC,IAAEL,IAAEK,OAAKH,KAAEG;AAAA,oBAAC;AAAA,kBAAC;AAAC,oBAAEhB,GAAEO,EAAC,EAAE,CAAC,CAAC,MAAIQ,KAAEf,GAAEO,EAAC,EAAE,CAAC,EAAE,OAAO,CAAC,GAAEP,GAAEO,EAAC,EAAE,CAAC,IAAEP,GAAEO,EAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAAEI,MAAGI,IAAEH,MAAGG;AAAA,gBAAE;AAAC,oBAAGR,KAAEP,GAAE,SAAO,KAAG,CAACA,GAAEO,EAAC,EAAE,CAAC,GAAE;AAAC,kBAAAP,GAAE,OAAOO,IAAE,CAAC;AAAE;AAAA,gBAAK;AAAC,oBAAGI,GAAE,SAAO,KAAGC,GAAE,SAAO,GAAE;AAAC,kBAAAD,GAAE,SAAO,KAAGC,GAAE,SAAO,MAAI,OAAKP,KAAE,EAAEO,IAAED,EAAC,OAAKE,MAAG,IAAEb,GAAEa,EAAC,EAAE,CAAC,KAAGD,GAAE,UAAU,GAAEP,EAAC,KAAGL,GAAE,OAAO,GAAE,GAAE,CAACG,IAAES,GAAE,UAAU,GAAEP,EAAC,CAAC,CAAC,GAAEE,OAAKK,KAAEA,GAAE,UAAUP,EAAC,GAAEM,KAAEA,GAAE,UAAUN,EAAC,IAAG,OAAKA,KAAE,EAAEO,IAAED,EAAC,OAAKX,GAAEO,EAAC,EAAE,CAAC,IAAEK,GAAE,UAAUA,GAAE,SAAOP,EAAC,IAAEL,GAAEO,EAAC,EAAE,CAAC,GAAEK,KAAEA,GAAE,UAAU,GAAEA,GAAE,SAAOP,EAAC,GAAEM,KAAEA,GAAE,UAAU,GAAEA,GAAE,SAAON,EAAC;AAAI,sBAAIgB,KAAEX,KAAED;AAAE,wBAAIE,GAAE,UAAQ,MAAIC,GAAE,UAAQZ,GAAE,OAAOO,KAAEc,IAAEA,EAAC,GAAEd,MAAGc,MAAG,MAAIV,GAAE,UAAQX,GAAE,OAAOO,KAAEc,IAAEA,IAAE,CAACnB,IAAEU,EAAC,CAAC,GAAEL,KAAEA,KAAEc,KAAE,KAAG,MAAIT,GAAE,UAAQZ,GAAE,OAAOO,KAAEc,IAAEA,IAAE,CAACpB,IAAEU,EAAC,CAAC,GAAEJ,KAAEA,KAAEc,KAAE,MAAIrB,GAAE,OAAOO,KAAEc,IAAEA,IAAE,CAACpB,IAAEU,EAAC,GAAE,CAACT,IAAEU,EAAC,CAAC,GAAEL,KAAEA,KAAEc,KAAE;AAAA,gBAAE;AAAC,sBAAId,MAAGP,GAAEO,KAAE,CAAC,EAAE,CAAC,MAAIJ,MAAGH,GAAEO,KAAE,CAAC,EAAE,CAAC,KAAGP,GAAEO,EAAC,EAAE,CAAC,GAAEP,GAAE,OAAOO,IAAE,CAAC,KAAGA,MAAIG,KAAE,GAAED,KAAE,GAAEE,KAAE,IAAGC,KAAE;AAAA,YAAE;AAAC,mBAAKZ,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,KAAGA,GAAE,IAAI;AAAE,gBAAImB,KAAE;AAAG,iBAAIZ,KAAE,GAAEA,KAAEP,GAAE,SAAO,IAAG,CAAAA,GAAEO,KAAE,CAAC,EAAE,CAAC,MAAIJ,MAAGH,GAAEO,KAAE,CAAC,EAAE,CAAC,MAAIJ,OAAIH,GAAEO,EAAC,EAAE,CAAC,EAAE,UAAUP,GAAEO,EAAC,EAAE,CAAC,EAAE,SAAOP,GAAEO,KAAE,CAAC,EAAE,CAAC,EAAE,MAAM,MAAIP,GAAEO,KAAE,CAAC,EAAE,CAAC,KAAGP,GAAEO,EAAC,EAAE,CAAC,IAAEP,GAAEO,KAAE,CAAC,EAAE,CAAC,IAAEP,GAAEO,EAAC,EAAE,CAAC,EAAE,UAAU,GAAEP,GAAEO,EAAC,EAAE,CAAC,EAAE,SAAOP,GAAEO,KAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAEP,GAAEO,KAAE,CAAC,EAAE,CAAC,IAAEP,GAAEO,KAAE,CAAC,EAAE,CAAC,IAAEP,GAAEO,KAAE,CAAC,EAAE,CAAC,GAAEP,GAAE,OAAOO,KAAE,GAAE,CAAC,GAAEY,KAAE,QAAInB,GAAEO,EAAC,EAAE,CAAC,EAAE,UAAU,GAAEP,GAAEO,KAAE,CAAC,EAAE,CAAC,EAAE,MAAM,KAAGP,GAAEO,KAAE,CAAC,EAAE,CAAC,MAAIP,GAAEO,KAAE,CAAC,EAAE,CAAC,KAAGP,GAAEO,KAAE,CAAC,EAAE,CAAC,GAAEP,GAAEO,EAAC,EAAE,CAAC,IAAEP,GAAEO,EAAC,EAAE,CAAC,EAAE,UAAUP,GAAEO,KAAE,CAAC,EAAE,CAAC,EAAE,MAAM,IAAEP,GAAEO,KAAE,CAAC,EAAE,CAAC,GAAEP,GAAE,OAAOO,KAAE,GAAE,CAAC,GAAEY,KAAE,QAAKZ;AAAI,YAAAY,MAAG,EAAEnB,IAAEI,EAAC;AAAA,UAAC;AAAC,mBAAS,EAAEJ,IAAE;AAAC,mBAAOA,MAAG,SAAOA,MAAG;AAAA,UAAK;AAAC,mBAAS,EAAEA,IAAE;AAAC,mBAAOA,MAAG,SAAOA,MAAG;AAAA,UAAK;AAAC,mBAAS,EAAEA,IAAE;AAAC,mBAAO,EAAEA,GAAE,WAAW,CAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAE;AAAC,mBAAO,EAAEA,GAAE,WAAWA,GAAE,SAAO,CAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEI,IAAEC,IAAEC,IAAE;AAAC,mBAAO,EAAEN,EAAC,KAAG,EAAEM,EAAC,IAAE,OAAK,SAASN,IAAE;AAAC,uBAAQC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAF,GAAEE,EAAC,EAAE,CAAC,EAAE,SAAO,KAAGD,GAAE,KAAKD,GAAEE,EAAC,CAAC;AAAE,qBAAOD;AAAA,YAAC,EAAE,CAAC,CAACE,IAAEH,EAAC,GAAE,CAACC,IAAEG,EAAC,GAAE,CAACF,IAAEG,EAAC,GAAE,CAACF,IAAEG,EAAC,CAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,EAAEN,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAO,EAAEH,IAAEC,IAAEC,IAAEC,IAAE,IAAE;AAAA,UAAC;AAAC,YAAE,SAAOD,IAAE,EAAE,SAAOD,IAAE,EAAE,QAAME,IAAEH,GAAE,UAAQ;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAAF,KAAEE,GAAE,IAAIF,EAAC;AAAE,cAAIG,KAAE,6BAA4B,IAAE,kBAAiB,IAAE,sBAAqB,IAAE,oBAAmB,IAAE,iBAAgB,IAAE,qBAAoB,IAAE,8BAA6B,IAAE,gBAAe,IAAE,mBAAkB,IAAE,mBAAkB,IAAE,oBAAmB,IAAE,mBAAkB,IAAE,gBAAe,IAAE,mBAAkB,IAAE,mBAAkB,IAAE,oBAAmB,IAAE,wBAAuB,IAAE,qBAAoB,IAAE,yBAAwB,IAAE,yBAAwB,IAAE,sBAAqB,IAAE,uBAAsB,IAAE,uBAAsB,IAAE,uBAAsB,IAAE,8BAA6B,IAAE,wBAAuB,IAAE,wBAAuB,IAAE,QAAO,IAAE,+BAA8B,IAAE,oBAAmB,IAAE,CAAC;AAAE,YAAE,CAAC,IAAE,EAAE,gBAAgB,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,MAAG,EAAE,gBAAgB,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE;AAAG,cAAI,IAAE,YAAU,OAAOD,GAAE,KAAGA,GAAE,KAAGA,GAAE,EAAE,WAAS,UAAQA,GAAE,GAAE,IAAE,YAAU,OAAO,QAAM,QAAM,KAAK,WAAS,UAAQ,MAAK,IAAE,KAAG,KAAG,SAAS,aAAa,EAAE,GAAE,IAAED,MAAG,CAACA,GAAE,YAAUA,IAAE,IAAE,KAAGD,MAAG,CAACA,GAAE,YAAUA,IAAE,IAAE,KAAG,EAAE,YAAU;AAAE,mBAAS,EAAEA,IAAEC,IAAE;AAAC,mBAAOD,GAAE,IAAIC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAED;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEC,IAAE;AAAC,mBAAOD,GAAE,IAAIC,EAAC,GAAED;AAAA,UAAC;AAAC,mBAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAEL,KAAEA,GAAE,SAAO;AAAE,iBAAIG,MAAGE,OAAIH,KAAEF,GAAE,EAAEI,EAAC,IAAG,EAAEA,KAAEC,KAAG,CAAAH,KAAED,GAAEC,IAAEF,GAAEI,EAAC,GAAEA,IAAEJ,EAAC;AAAE,mBAAOE;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE;AAAG,gBAAG,QAAMD,MAAG,cAAY,OAAOA,GAAE,SAAS,KAAG;AAAC,cAAAC,KAAE,CAAC,EAAED,KAAE;AAAA,YAAG,SAAOA,IAAE;AAAA,YAAC;AAAC,mBAAOC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,mBAAOA,GAAE,QAAS,SAASA,IAAEG,IAAE;AAAC,cAAAD,GAAE,EAAED,EAAC,IAAE,CAACE,IAAEH,EAAC;AAAA,YAAC,CAAE,GAAEE;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAEC,IAAE;AAAC,mBAAO,SAASC,IAAE;AAAC,qBAAOF,GAAEC,GAAEC,EAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,mBAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,cAAAE,GAAE,EAAED,EAAC,IAAED;AAAA,YAAC,CAAE,GAAEE;AAAA,UAAC;AAAC,cAAI,GAAE,IAAE,MAAM,WAAU,IAAE,SAAS,WAAU,IAAE,OAAO,WAAU,IAAE,EAAE,oBAAoB,GAAE,KAAG,IAAE,SAAS,KAAK,KAAG,EAAE,QAAM,EAAE,KAAK,YAAU,EAAE,KAAG,mBAAiB,IAAE,IAAG,IAAE,EAAE,UAAS,KAAG,EAAE,gBAAe,KAAG,EAAE,UAAS,KAAG,OAAO,MAAI,EAAE,KAAK,EAAE,EAAE,QAAQ,uBAAsB,MAAM,EAAE,QAAQ,0DAAyD,OAAO,IAAE,GAAG,GAAE,KAAG,IAAE,EAAE,SAAO,QAAO,KAAG,EAAE,QAAO,KAAG,EAAE,YAAW,KAAG,EAAE,OAAO,gBAAe,MAAM,GAAE,KAAG,OAAO,QAAO,KAAG,EAAE,sBAAqB,KAAG,EAAE,QAAO,KAAG,OAAO,uBAAsB,KAAG,KAAG,GAAG,WAAS,QAAO,KAAG,EAAE,OAAO,MAAK,MAAM,GAAE,KAAG,GAAG,GAAE,UAAU,GAAE,KAAG,GAAG,GAAE,KAAK,GAAE,KAAG,GAAG,GAAE,SAAS,GAAE,KAAG,GAAG,GAAE,KAAK,GAAE,KAAG,GAAG,GAAE,SAAS,GAAE,KAAG,GAAG,QAAO,QAAQ,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,KAAG,GAAG,YAAU,QAAO,KAAG,KAAG,GAAG,UAAQ;AAAO,mBAAS,GAAGF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAEF,KAAEA,GAAE,SAAO;AAAE,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGH,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAEF,KAAEA,GAAE,SAAO;AAAE,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGH,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAEF,KAAEA,GAAE,SAAO;AAAE,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGH,IAAE;AAAC,iBAAK,WAAS,IAAI,GAAGA,EAAC;AAAA,UAAC;AAAC,mBAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,KAAEH,GAAEC,EAAC;AAAE,eAAG,KAAKD,IAAEC,EAAC,KAAG,GAAGE,IAAED,EAAC,MAAI,WAASA,MAAGD,MAAKD,QAAKA,GAAEC,EAAC,IAAEC;AAAA,UAAE;AAAC,mBAAS,GAAGF,IAAEC,IAAE;AAAC,qBAAQC,KAAEF,GAAE,QAAOE,OAAK,KAAG,GAAGF,GAAEE,EAAC,EAAE,CAAC,GAAED,EAAC,EAAE,QAAOC;AAAE,mBAAM;AAAA,UAAE;AAAC,mBAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAEC,IAAES,IAAEK,IAAE;AAAC,gBAAIO;AAAE,gBAAGtB,OAAIsB,KAAEZ,KAAEV,GAAEH,IAAEI,IAAES,IAAEK,EAAC,IAAEf,GAAEH,EAAC,IAAG,WAASyB,GAAE,QAAOA;AAAE,gBAAG,CAAC,GAAGzB,EAAC,EAAE,QAAOA;AAAE,gBAAI0B,KAAE,GAAG1B,EAAC;AAAE,gBAAG0B,IAAE;AAAC,kBAAGD,KAAE,SAASzB,IAAE;AAAC,oBAAIC,KAAED,GAAE,QAAOE,KAAEF,GAAE,YAAYC,EAAC;AAAE,uBAAOA,MAAG,YAAU,OAAOD,GAAE,CAAC,KAAG,GAAG,KAAKA,IAAE,OAAO,MAAIE,GAAE,QAAMF,GAAE,OAAME,GAAE,QAAMF,GAAE,QAAOE;AAAA,cAAC,EAAEF,EAAC,GAAE,CAACC,GAAE,QAAO,SAASD,IAAEC,IAAE;AAAC,oBAAIC,KAAE,IAAGC,KAAEH,GAAE;AAAO,qBAAIC,OAAIA,KAAE,MAAME,EAAC,IAAG,EAAED,KAAEC,KAAG,CAAAF,GAAEC,EAAC,IAAEF,GAAEE,EAAC;AAAE,uBAAOD;AAAA,cAAC,EAAED,IAAEyB,EAAC;AAAA,YAAC,OAAK;AAAC,kBAAIE,KAAE,GAAG3B,EAAC,GAAE4B,KAAED,MAAG,KAAGA,MAAG;AAAE,kBAAG,GAAG3B,EAAC,EAAE,QAAO,SAASA,IAAEC,IAAE;AAAC,oBAAGA,GAAE,QAAOD,GAAE,MAAM;AAAE,oBAAIE,KAAE,IAAIF,GAAE,YAAYA,GAAE,MAAM;AAAE,uBAAOA,GAAE,KAAKE,EAAC,GAAEA;AAAA,cAAC,EAAEF,IAAEC,EAAC;AAAE,kBAAG0B,MAAG,KAAGA,MAAG,KAAGC,MAAG,CAACf,IAAE;AAAC,oBAAG,EAAEb,EAAC,EAAE,QAAOa,KAAEb,KAAE,CAAC;AAAE,oBAAGyB,KAAE,SAASzB,IAAE;AAAC,yBAAM,cAAY,OAAOA,GAAE,eAAa,GAAGA,EAAC,IAAE,CAAC,IAAE,GAAGC,KAAE,GAAGD,EAAC,CAAC,IAAE,GAAGC,EAAC,IAAE,CAAC;AAAE,sBAAIA;AAAA,gBAAC,EAAE2B,KAAE,CAAC,IAAE5B,EAAC,GAAE,CAACC,GAAE,QAAO,SAASD,IAAEC,IAAE;AAAC,yBAAO,GAAGD,IAAE,GAAGA,EAAC,GAAEC,EAAC;AAAA,gBAAC,EAAED,IAAE,SAASA,IAAEC,IAAE;AAAC,yBAAOD,MAAG,GAAGC,IAAE,GAAGA,EAAC,GAAED,EAAC;AAAA,gBAAC,EAAEyB,IAAEzB,EAAC,CAAC;AAAA,cAAC,OAAK;AAAC,oBAAG,CAAC,EAAE2B,EAAC,EAAE,QAAOd,KAAEb,KAAE,CAAC;AAAE,gBAAAyB,KAAE,SAASzB,IAAEC,IAAEC,IAAEC,IAAE;AAAC,sBAAIC,IAAEC,KAAEL,GAAE;AAAY,0BAAOC,IAAE;AAAA,oBAAC,KAAK;AAAE,6BAAO,GAAGD,EAAC;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAE,6BAAO,IAAIK,GAAE,CAACL,EAAC;AAAA,oBAAE,KAAK;AAAE,6BAAO,SAASA,IAAEC,IAAE;AAAC,4BAAIC,KAAED,KAAE,GAAGD,GAAE,MAAM,IAAEA,GAAE;AAAO,+BAAO,IAAIA,GAAE,YAAYE,IAAEF,GAAE,YAAWA,GAAE,UAAU;AAAA,sBAAC,EAAEA,IAAEG,EAAC;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAE,6BAAO,SAASH,IAAEC,IAAE;AAAC,4BAAIC,KAAED,KAAE,GAAGD,GAAE,MAAM,IAAEA,GAAE;AAAO,+BAAO,IAAIA,GAAE,YAAYE,IAAEF,GAAE,YAAWA,GAAE,MAAM;AAAA,sBAAC,EAAEA,IAAEG,EAAC;AAAA,oBAAE,KAAK;AAAE,6BAAO,SAASH,IAAEC,IAAEC,IAAE;AAAC,+BAAO,EAAED,KAAEC,GAAE,EAAEF,EAAC,GAAE,IAAE,IAAE,EAAEA,EAAC,GAAE,GAAE,IAAIA,GAAE,aAAW;AAAA,sBAAC,EAAEA,IAAEG,IAAED,EAAC;AAAA,oBAAE,KAAK;AAAA,oBAAE,KAAK;AAAE,6BAAO,IAAIG,GAAEL,EAAC;AAAA,oBAAE,KAAK;AAAE,6BAAO,SAASA,IAAE;AAAC,4BAAIC,KAAE,IAAID,GAAE,YAAYA,GAAE,QAAO,EAAE,KAAKA,EAAC,CAAC;AAAE,+BAAOC,GAAE,YAAUD,GAAE,WAAUC;AAAA,sBAAC,EAAED,EAAC;AAAA,oBAAE,KAAK;AAAE,6BAAO,SAASA,IAAEC,IAAEC,IAAE;AAAC,+BAAO,EAAED,KAAEC,GAAE,EAAEF,EAAC,GAAE,IAAE,IAAE,EAAEA,EAAC,GAAE,GAAE,IAAIA,GAAE,aAAW;AAAA,sBAAC,EAAEA,IAAEG,IAAED,EAAC;AAAA,oBAAE,KAAK;AAAE,6BAAOE,KAAEJ,IAAE,KAAG,OAAO,GAAG,KAAKI,EAAC,CAAC,IAAE,CAAC;AAAA,kBAAC;AAAA,gBAAC,EAAEJ,IAAE2B,IAAE,IAAG1B,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,YAAAiB,OAAIA,KAAE,IAAI;AAAI,gBAAIW,KAAEX,GAAE,IAAIlB,EAAC;AAAE,gBAAG6B,GAAE,QAAOA;AAAE,gBAAGX,GAAE,IAAIlB,IAAEyB,EAAC,GAAE,CAACC,GAAE,KAAII,KAAE5B,KAAE,SAASF,IAAE;AAAC,qBAAO,SAASA,IAAEC,IAAEC,IAAE;AAAC,oBAAIC,KAAEF,GAAED,EAAC;AAAE,uBAAO,GAAGA,EAAC,IAAEG,KAAE,SAASH,IAAEC,IAAE;AAAC,2BAAQC,KAAE,IAAGC,KAAEF,GAAE,QAAOG,KAAEJ,GAAE,QAAO,EAAEE,KAAEC,KAAG,CAAAH,GAAEI,KAAEF,EAAC,IAAED,GAAEC,EAAC;AAAE,yBAAOF;AAAA,gBAAC,EAAEG,IAAED,GAAEF,EAAC,CAAC;AAAA,cAAC,EAAEA,IAAE,IAAG,EAAE;AAAA,YAAC,EAAEA,EAAC,IAAE,GAAGA,EAAC;AAAE,mBAAO,SAASA,IAAEC,IAAE;AAAC,uBAAQC,KAAE,IAAGC,KAAEH,KAAEA,GAAE,SAAO,GAAE,EAAEE,KAAEC,MAAG,UAAKF,GAAED,GAAEE,EAAC,GAAEA,EAAC,IAAG;AAAA,YAAC,EAAE4B,MAAG9B,IAAG,SAASI,IAAEC,IAAE;AAAC,cAAAyB,OAAI1B,KAAEJ,GAAEK,KAAED,EAAC,IAAG,GAAGqB,IAAEpB,IAAE,GAAGD,IAAEH,IAAEC,IAAEC,IAAEE,IAAEL,IAAEkB,EAAC,CAAC;AAAA,YAAC,CAAE,GAAEO;AAAA,UAAC;AAAC,mBAAS,GAAGzB,IAAE;AAAC,gBAAIC,KAAE,IAAID,GAAE,YAAYA,GAAE,UAAU;AAAE,mBAAO,IAAI,GAAGC,EAAC,EAAE,IAAI,IAAI,GAAGD,EAAC,CAAC,GAAEC;AAAA,UAAC;AAAC,mBAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAAD,OAAIA,KAAE,CAAC;AAAG,qBAAQE,KAAE,IAAGC,KAAEJ,GAAE,QAAO,EAAEG,KAAEC,MAAG;AAAC,kBAAIC,KAAEL,GAAEG,EAAC,GAAEG,KAAEJ,KAAEA,GAAED,GAAEI,EAAC,GAAEN,GAAEM,EAAC,GAAEA,IAAEJ,IAAEF,EAAC,IAAE;AAAO,iBAAGE,IAAEI,IAAE,WAASC,KAAEP,GAAEM,EAAC,IAAEC,EAAC;AAAA,YAAC;AAAC,mBAAOL;AAAA,UAAC;AAAC,mBAAS,GAAGF,IAAEC,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,KAAEJ,GAAE;AAAS,oBAAO,aAAWG,KAAE,QAAOD,KAAED,QAAK,YAAUE,MAAG,YAAUA,MAAG,aAAWA,KAAE,gBAAcD,KAAE,SAAOA,MAAGE,GAAE,YAAU,OAAOH,KAAE,WAAS,MAAM,IAAEG,GAAE;AAAA,UAAG;AAAC,mBAAS,GAAGJ,IAAEC,IAAE;AAAC,gBAAIC,KAAE,SAASF,IAAEC,IAAE;AAAC,qBAAO,QAAMD,KAAE,SAAOA,GAAEC,EAAC;AAAA,YAAC,EAAED,IAAEC,EAAC;AAAE,mBAAO,SAASD,IAAE;AAAC,qBAAM,EAAE,CAAC,GAAGA,EAAC,MAAIC,KAAED,IAAE,KAAG,KAAKC,SAAM,GAAGD,EAAC,KAAG,EAAEA,EAAC,IAAE,KAAG,GAAG,KAAK,GAAGA,EAAC,CAAC;AAAE,kBAAIC;AAAA,YAAC,EAAEC,EAAC,IAAEA,KAAE;AAAA,UAAM;AAAC,aAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,KAAG,GAAG,IAAI,IAAE,CAAC;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASF,IAAE;AAAC,mBAAO,KAAK,IAAIA,EAAC,KAAG,OAAO,KAAK,SAASA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,gBAAG,IAAG;AAAC,kBAAIC,KAAED,GAAED,EAAC;AAAE,qBAAOE,OAAIC,KAAE,SAAOD;AAAA,YAAC;AAAC,mBAAO,GAAG,KAAKD,IAAED,EAAC,IAAEC,GAAED,EAAC,IAAE;AAAA,UAAM,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,mBAAO,KAAG,WAASA,GAAED,EAAC,IAAE,GAAG,KAAKC,IAAED,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,mBAAO,KAAK,SAASD,EAAC,IAAE,MAAI,WAASC,KAAEE,KAAEF,IAAE;AAAA,UAAI,GAAE,GAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,CAAC;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASD,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAED,EAAC;AAAE,mBAAM,EAAEE,KAAE,MAAIA,MAAGD,GAAE,SAAO,IAAEA,GAAE,IAAI,IAAE,GAAG,KAAKA,IAAEC,IAAE,CAAC,GAAE;AAAA,UAAG,GAAE,GAAG,UAAU,MAAI,SAASF,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAED,EAAC;AAAE,mBAAOE,KAAE,IAAE,SAAOD,GAAEC,EAAC,EAAE,CAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASF,IAAE;AAAC,mBAAO,GAAG,KAAK,UAASA,EAAC,IAAE;AAAA,UAAE,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAEF,EAAC;AAAE,mBAAOG,KAAE,IAAED,GAAE,KAAK,CAACF,IAAEC,EAAC,CAAC,IAAEC,GAAEC,EAAC,EAAE,CAAC,IAAEF,IAAE;AAAA,UAAI,GAAE,GAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,EAAC,MAAK,IAAI,MAAG,KAAI,KAAI,MAAI,OAAI,QAAO,IAAI,KAAE;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASD,IAAE;AAAC,mBAAO,GAAG,MAAKA,EAAC,EAAE,OAAOA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,GAAG,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,GAAG,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,mBAAO,GAAG,MAAKD,EAAC,EAAE,IAAIA,IAAEC,EAAC,GAAE;AAAA,UAAI,GAAE,GAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,IAAI;AAAA,UAAE,GAAE,GAAG,UAAU,SAAO,SAASD,IAAE;AAAC,mBAAO,KAAK,SAAS,OAAOA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,gBAAGA,cAAa,IAAG;AAAC,kBAAIC,KAAED,GAAE;AAAS,kBAAG,CAAC,MAAIC,GAAE,SAAO,IAAI,QAAOA,GAAE,KAAK,CAACH,IAAEC,EAAC,CAAC,GAAE;AAAK,cAAAC,KAAE,KAAK,WAAS,IAAI,GAAGC,EAAC;AAAA,YAAC;AAAC,mBAAOD,GAAE,IAAIF,IAAEC,EAAC,GAAE;AAAA,UAAI;AAAE,cAAI,KAAG,KAAG,EAAE,IAAG,MAAM,IAAE,WAAU;AAAC,mBAAM,CAAC;AAAA,UAAC,GAAE,KAAG,SAASD,IAAE;AAAC,mBAAO,GAAG,KAAKA,EAAC;AAAA,UAAC;AAAE,mBAAS,GAAGA,IAAEC,IAAE;AAAC,mBAAM,CAAC,EAAEA,KAAE,QAAMA,KAAE,IAAEA,QAAK,YAAU,OAAOD,MAAG,EAAE,KAAKA,EAAC,MAAIA,KAAE,MAAIA,KAAE,KAAG,KAAGA,KAAEC;AAAA,UAAC;AAAC,mBAAS,GAAGD,IAAE;AAAC,gBAAIC,KAAED,MAAGA,GAAE;AAAY,mBAAOA,QAAK,cAAY,OAAOC,MAAGA,GAAE,aAAW;AAAA,UAAE;AAAC,mBAAS,GAAGD,IAAE;AAAC,gBAAG,QAAMA,IAAE;AAAC,kBAAG;AAAC,uBAAO,EAAE,KAAKA,EAAC;AAAA,cAAC,SAAOA,IAAE;AAAA,cAAC;AAAC,kBAAG;AAAC,uBAAOA,KAAE;AAAA,cAAE,SAAOA,IAAE;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAM;AAAA,UAAE;AAAC,mBAAS,GAAGA,IAAEC,IAAE;AAAC,mBAAOD,OAAIC,MAAGD,MAAGA,MAAGC,MAAGA;AAAA,UAAC;AAAC,WAAC,MAAI,GAAG,IAAI,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,KAAG,KAAG,MAAI,GAAG,IAAI,IAAE,KAAG,KAAG,MAAI,GAAG,GAAG,QAAQ,CAAC,KAAG,KAAG,MAAI,GAAG,IAAI,IAAE,KAAG,KAAG,MAAI,GAAG,IAAI,IAAE,KAAG,OAAK,KAAG,SAASD,IAAE;AAAC,gBAAIC,KAAE,GAAG,KAAKD,EAAC,GAAEE,KAAED,MAAG,IAAED,GAAE,cAAY,QAAOG,KAAED,KAAE,GAAGA,EAAC,IAAE;AAAO,gBAAGC,GAAE,SAAOA,IAAE;AAAA,cAAC,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC;AAAG,cAAI,KAAG,MAAM;AAAQ,mBAAS,GAAGD,IAAE;AAAC,mBAAO,QAAMA,MAAG,SAASA,IAAE;AAAC,qBAAM,YAAU,OAAOA,MAAGA,KAAE,MAAIA,KAAE,KAAG,KAAGA,MAAG;AAAA,YAAC,EAAEA,GAAE,MAAM,KAAG,CAAC,GAAGA,EAAC;AAAA,UAAC;AAAC,cAAI,KAAG,MAAI,WAAU;AAAC,mBAAM;AAAA,UAAE;AAAE,mBAAS,GAAGA,IAAE;AAAC,gBAAIC,KAAE,GAAGD,EAAC,IAAE,GAAG,KAAKA,EAAC,IAAE;AAAG,mBAAOC,MAAG,KAAGA,MAAG;AAAA,UAAC;AAAC,mBAAS,GAAGD,IAAE;AAAC,gBAAIC,KAAE,OAAOD;AAAE,mBAAM,CAAC,CAACA,OAAI,YAAUC,MAAG,cAAYA;AAAA,UAAE;AAAC,mBAAS,GAAGD,IAAE;AAAC,mBAAO,GAAGA,EAAC,IAAE,SAASA,IAAEC,IAAE;AAAC,kBAAIC,KAAE,GAAGF,EAAC,KAAG,SAASA,IAAE;AAAC,uBAAO,SAASA,IAAE;AAAC,yBAAO,yBAASA,IAAE;AAAC,2BAAM,CAAC,CAACA,MAAG,YAAU,OAAOA;AAAA,kBAAC,EAAEA,EAAC,KAAG,GAAGA,EAAC;AAAA,gBAAC,EAAEA,EAAC,KAAG,GAAG,KAAKA,IAAE,QAAQ,MAAI,CAAC,GAAG,KAAKA,IAAE,QAAQ,KAAG,GAAG,KAAKA,EAAC,KAAG;AAAA,cAAE,EAAEA,EAAC,IAAE,SAASA,IAAEC,IAAE;AAAC,yBAAQC,KAAE,IAAGC,KAAE,MAAMH,EAAC,GAAE,EAAEE,KAAEF,KAAG,CAAAG,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAE,uBAAOC;AAAA,cAAC,EAAEH,GAAE,QAAO,MAAM,IAAE,CAAC,GAAEG,KAAED,GAAE,QAAOE,KAAE,CAAC,CAACD;AAAE,uBAAQG,MAAKN,GAAE,EAACC,MAAG,CAAC,GAAG,KAAKD,IAAEM,EAAC,KAAGF,OAAI,YAAUE,MAAG,GAAGA,IAAEH,EAAC,MAAID,GAAE,KAAKI,EAAC;AAAE,qBAAOJ;AAAA,YAAC,EAAEF,EAAC,IAAE,SAASA,IAAE;AAAC,kBAAG,CAAC,GAAGA,EAAC,EAAE,QAAO,GAAGA,EAAC;AAAE,kBAAIC,KAAE,CAAC;AAAE,uBAAQC,MAAK,OAAOF,EAAC,EAAE,IAAG,KAAKA,IAAEE,EAAC,KAAG,iBAAeA,MAAGD,GAAE,KAAKC,EAAC;AAAE,qBAAOD;AAAA,YAAC,EAAED,EAAC;AAAA,UAAC;AAAC,UAAAA,GAAE,UAAQ,SAASA,IAAE;AAAC,mBAAO,GAAGA,IAAE,MAAG,IAAE;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAAF,KAAEE,GAAE,IAAIF,EAAC;AAAE,cAAIG,KAAE,6BAA4B,IAAE,GAAE,IAAE,GAAE,IAAE,kBAAiB,IAAE,sBAAqB,IAAE,kBAAiB,IAAE,0BAAyB,IAAE,oBAAmB,IAAE,iBAAgB,IAAE,kBAAiB,IAAE,qBAAoB,IAAE,8BAA6B,IAAE,gBAAe,IAAE,mBAAkB,IAAE,iBAAgB,IAAE,mBAAkB,IAAE,oBAAmB,IAAE,kBAAiB,IAAE,mBAAkB,IAAE,gBAAe,IAAE,mBAAkB,IAAE,sBAAqB,IAAE,oBAAmB,IAAE,wBAAuB,IAAE,qBAAoB,IAAE,+BAA8B,IAAE,oBAAmB,IAAE,CAAC;AAAE,YAAE,uBAAuB,IAAE,EAAE,uBAAuB,IAAE,EAAE,oBAAoB,IAAE,EAAE,qBAAqB,IAAE,EAAE,qBAAqB,IAAE,EAAE,qBAAqB,IAAE,EAAE,4BAA4B,IAAE,EAAE,sBAAsB,IAAE,EAAE,sBAAsB,IAAE,MAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE;AAAG,cAAI,IAAE,YAAU,OAAOD,GAAE,KAAGA,GAAE,KAAGA,GAAE,EAAE,WAAS,UAAQA,GAAE,GAAE,IAAE,YAAU,OAAO,QAAM,QAAM,KAAK,WAAS,UAAQ,MAAK,IAAE,KAAG,KAAG,SAAS,aAAa,EAAE,GAAE,IAAED,MAAG,CAACA,GAAE,YAAUA,IAAE,IAAE,KAAGD,MAAG,CAACA,GAAE,YAAUA,IAAE,IAAE,KAAG,EAAE,YAAU,GAAE,IAAE,KAAG,EAAE,SAAQ,IAAE,WAAU;AAAC,gBAAG;AAAC,qBAAO,KAAG,EAAE,WAAS,EAAE,QAAQ,MAAM;AAAA,YAAC,SAAOA,IAAE;AAAA,YAAC;AAAA,UAAC,EAAE,GAAE,IAAE,KAAG,EAAE;AAAa,mBAAS,EAAEA,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAO,EAAEE,KAAEC,KAAG,KAAGF,GAAED,GAAEE,EAAC,GAAEA,IAAEF,EAAC,EAAE,QAAM;AAAG,mBAAM;AAAA,UAAE;AAAC,mBAAS,EAAEA,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,mBAAOA,GAAE,QAAS,SAASA,IAAEG,IAAE;AAAC,cAAAD,GAAE,EAAED,EAAC,IAAE,CAACE,IAAEH,EAAC;AAAA,YAAC,CAAE,GAAEE;AAAA,UAAC;AAAC,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,mBAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,cAAAE,GAAE,EAAED,EAAC,IAAED;AAAA,YAAC,CAAE,GAAEE;AAAA,UAAC;AAAC,cAAI,GAAE,GAAE,GAAE,IAAE,MAAM,WAAU,IAAE,SAAS,WAAU,IAAE,OAAO,WAAU,IAAE,EAAE,oBAAoB,GAAE,IAAE,EAAE,UAAS,IAAE,EAAE,gBAAe,KAAG,IAAE,SAAS,KAAK,KAAG,EAAE,QAAM,EAAE,KAAK,YAAU,EAAE,KAAG,mBAAiB,IAAE,IAAG,IAAE,EAAE,UAAS,KAAG,OAAO,MAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,uBAAsB,MAAM,EAAE,QAAQ,0DAAyD,OAAO,IAAE,GAAG,GAAE,KAAG,IAAE,EAAE,SAAO,QAAO,KAAG,EAAE,QAAO,KAAG,EAAE,YAAW,KAAG,EAAE,sBAAqB,KAAG,EAAE,QAAO,KAAG,KAAG,GAAG,cAAY,QAAO,KAAG,OAAO,uBAAsB,KAAG,KAAG,GAAG,WAAS,QAAO,MAAI,IAAE,OAAO,MAAK,IAAE,QAAO,SAASF,IAAE;AAAC,mBAAO,EAAE,EAAEA,EAAC,CAAC;AAAA,UAAC,IAAG,KAAG,GAAG,GAAE,UAAU,GAAE,KAAG,GAAG,GAAE,KAAK,GAAE,KAAG,GAAG,GAAE,SAAS,GAAE,KAAG,GAAG,GAAE,KAAK,GAAE,KAAG,GAAG,GAAE,SAAS,GAAE,KAAG,GAAG,QAAO,QAAQ,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,GAAG,EAAE,GAAE,KAAG,KAAG,GAAG,YAAU,QAAO,KAAG,KAAG,GAAG,UAAQ;AAAO,mBAAS,GAAGA,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGH,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGH,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGH,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,WAAS,IAAI,MAAG,EAAEC,KAAEC,KAAG,MAAK,IAAIF,GAAEC,EAAC,CAAC;AAAA,UAAC;AAAC,mBAAS,GAAGD,IAAE;AAAC,gBAAIC,KAAE,KAAK,WAAS,IAAI,GAAGD,EAAC;AAAE,iBAAK,OAAKC,GAAE;AAAA,UAAI;AAAC,mBAAS,GAAGD,IAAEC,IAAE;AAAC,qBAAQC,KAAEF,GAAE,QAAOE,OAAK,KAAG,GAAGF,GAAEE,EAAC,EAAE,CAAC,GAAED,EAAC,EAAE,QAAOC;AAAE,mBAAM;AAAA,UAAE;AAAC,mBAAS,GAAGF,IAAE;AAAC,mBAAO,QAAMA,KAAE,WAASA,KAAE,IAAE,IAAE,MAAI,MAAM,OAAOA,EAAC,IAAE,SAASA,IAAE;AAAC,kBAAIC,KAAE,EAAE,KAAKD,IAAE,EAAE,GAAEE,KAAEF,GAAE,EAAE;AAAE,kBAAG;AAAC,gBAAAA,GAAE,EAAE,IAAE;AAAO,oBAAIG,KAAE;AAAA,cAAE,SAAOH,IAAE;AAAA,cAAC;AAAC,kBAAII,KAAE,EAAE,KAAKJ,EAAC;AAAE,qBAAOG,OAAIF,KAAED,GAAE,EAAE,IAAEE,KAAE,OAAOF,GAAE,EAAE,IAAGI;AAAA,YAAC,EAAEJ,EAAC,IAAE,SAASA,IAAE;AAAC,qBAAO,EAAE,KAAKA,EAAC;AAAA,YAAC,EAAEA,EAAC;AAAA,UAAC;AAAC,mBAAS,GAAGA,IAAE;AAAC,mBAAO,GAAGA,EAAC,KAAG,GAAGA,EAAC,KAAG;AAAA,UAAC;AAAC,mBAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,mBAAON,OAAIC,OAAI,QAAMD,MAAG,QAAMC,MAAG,CAAC,GAAGD,EAAC,KAAG,CAAC,GAAGC,EAAC,IAAED,MAAGA,MAAGC,MAAGA,KAAE,SAASD,IAAEC,IAAEC,IAAEC,IAAEG,IAAEG,IAAE;AAAC,kBAAII,KAAE,GAAGb,EAAC,GAAEc,KAAE,GAAGb,EAAC,GAAEgB,KAAEJ,KAAE,IAAE,GAAGb,EAAC,GAAEqB,KAAEP,KAAE,IAAE,GAAGb,EAAC,GAAEkB,MAAGF,KAAEA,MAAG,IAAE,IAAEA,OAAI,GAAEM,MAAGF,KAAEA,MAAG,IAAE,IAAEA,OAAI,GAAEU,KAAEd,MAAGI;AAAE,kBAAGU,MAAG,GAAG/B,EAAC,GAAE;AAAC,oBAAG,CAAC,GAAGC,EAAC,EAAE,QAAM;AAAG,gBAAAY,KAAE,MAAGM,KAAE;AAAA,cAAE;AAAC,kBAAGY,MAAG,CAACZ,GAAE,QAAOV,OAAIA,KAAE,IAAI,OAAII,MAAG,GAAGb,EAAC,IAAE,GAAGA,IAAEC,IAAEC,IAAEC,IAAEG,IAAEG,EAAC,IAAE,SAAST,IAAEC,IAAEC,IAAEC,IAAEG,IAAEC,IAAEC,IAAE;AAAC,wBAAON,IAAE;AAAA,kBAAC,KAAK;AAAE,wBAAGF,GAAE,cAAYC,GAAE,cAAYD,GAAE,cAAYC,GAAE,WAAW,QAAM;AAAG,oBAAAD,KAAEA,GAAE,QAAOC,KAAEA,GAAE;AAAA,kBAAO,KAAK;AAAE,2BAAM,EAAED,GAAE,cAAYC,GAAE,cAAY,CAACM,GAAE,IAAI,GAAGP,EAAC,GAAE,IAAI,GAAGC,EAAC,CAAC;AAAA,kBAAG,KAAK;AAAA,kBAAE,KAAK;AAAA,kBAAE,KAAK;AAAE,2BAAO,GAAG,CAACD,IAAE,CAACC,EAAC;AAAA,kBAAE,KAAK;AAAE,2BAAOD,GAAE,QAAMC,GAAE,QAAMD,GAAE,WAASC,GAAE;AAAA,kBAAQ,KAAK;AAAA,kBAAE,KAAK;AAAE,2BAAOD,MAAGC,KAAE;AAAA,kBAAG,KAAK;AAAE,wBAAIQ,KAAE;AAAA,kBAAE,KAAK;AAAE,wBAAII,KAAEV,KAAE;AAAE,wBAAGM,OAAIA,KAAE,IAAGT,GAAE,QAAMC,GAAE,QAAM,CAACY,GAAE,QAAM;AAAG,wBAAIC,KAAEN,GAAE,IAAIR,EAAC;AAAE,wBAAGc,GAAE,QAAOA,MAAGb;AAAE,oBAAAE,MAAG,GAAEK,GAAE,IAAIR,IAAEC,EAAC;AAAE,wBAAIgB,KAAE,GAAGR,GAAET,EAAC,GAAES,GAAER,EAAC,GAAEE,IAAEG,IAAEC,IAAEC,EAAC;AAAE,2BAAOA,GAAE,OAAOR,EAAC,GAAEiB;AAAA,kBAAE,KAAI;AAAkB,wBAAG,GAAG,QAAO,GAAG,KAAKjB,EAAC,KAAG,GAAG,KAAKC,EAAC;AAAA,gBAAC;AAAC,uBAAM;AAAA,cAAE,EAAED,IAAEC,IAAEgB,IAAEf,IAAEC,IAAEG,IAAEG,EAAC;AAAE,kBAAG,EAAEP,KAAE,IAAG;AAAC,oBAAI8B,KAAEb,MAAG,EAAE,KAAKnB,IAAE,aAAa,GAAEiC,KAAEV,MAAG,EAAE,KAAKtB,IAAE,aAAa;AAAE,oBAAG+B,MAAGC,IAAE;AAAC,sBAAIC,KAAEF,KAAEhC,GAAE,MAAM,IAAEA,IAAEyB,KAAEQ,KAAEhC,GAAE,MAAM,IAAEA;AAAE,yBAAOQ,OAAIA,KAAE,IAAI,OAAIH,GAAE4B,IAAET,IAAEvB,IAAEC,IAAEM,EAAC;AAAA,gBAAC;AAAA,cAAC;AAAC,qBAAM,CAAC,CAACsB,OAAItB,OAAIA,KAAE,IAAI,OAAI,SAAST,IAAEC,IAAEC,IAAEC,IAAEE,IAAEC,IAAE;AAAC,oBAAIC,KAAEL,KAAE,GAAEM,KAAE,GAAGR,EAAC,GAAES,KAAED,GAAE;AAAO,oBAAGC,MAAG,GAAGR,EAAC,EAAE,UAAQ,CAACM,GAAE,QAAM;AAAG,yBAAQG,KAAED,IAAEC,QAAK;AAAC,sBAAIC,KAAEH,GAAEE,EAAC;AAAE,sBAAG,EAAEH,KAAEI,MAAKV,KAAE,EAAE,KAAKA,IAAEU,EAAC,GAAG,QAAM;AAAA,gBAAE;AAAC,oBAAIC,KAAEN,GAAE,IAAIN,EAAC;AAAE,oBAAGY,MAAGN,GAAE,IAAIL,EAAC,EAAE,QAAOW,MAAGX;AAAE,oBAAIY,KAAE;AAAG,gBAAAP,GAAE,IAAIN,IAAEC,EAAC,GAAEK,GAAE,IAAIL,IAAED,EAAC;AAAE,yBAAQc,KAAEP,IAAE,EAAEG,KAAED,MAAG;AAAC,sBAAIM,KAAEf,GAAEW,KAAEH,GAAEE,EAAC,CAAC,GAAEM,KAAEf,GAAEU,EAAC;AAAE,sBAAGR,GAAE,KAAIc,KAAEV,KAAEJ,GAAEa,IAAED,IAAEJ,IAAEV,IAAED,IAAEM,EAAC,IAAEH,GAAEY,IAAEC,IAAEL,IAAEX,IAAEC,IAAEK,EAAC;AAAE,sBAAG,EAAE,WAASW,KAAEF,OAAIC,MAAGX,GAAEU,IAAEC,IAAEd,IAAEC,IAAEG,EAAC,IAAEW,KAAG;AAAC,oBAAAJ,KAAE;AAAG;AAAA,kBAAK;AAAC,kBAAAC,OAAIA,KAAE,iBAAeH;AAAA,gBAAE;AAAC,oBAAGE,MAAG,CAACC,IAAE;AAAC,sBAAII,KAAElB,GAAE,aAAYqB,KAAEpB,GAAE;AAAY,kBAAAiB,MAAGG,MAAG,EAAE,iBAAgBrB,OAAI,EAAE,iBAAgBC,OAAI,cAAY,OAAOiB,MAAGA,cAAaA,MAAG,cAAY,OAAOG,MAAGA,cAAaA,OAAIR,KAAE;AAAA,gBAAG;AAAC,uBAAOP,GAAE,OAAON,EAAC,GAAEM,GAAE,OAAOL,EAAC,GAAEY;AAAA,cAAC,EAAEb,IAAEC,IAAEC,IAAEC,IAAEG,IAAEG,EAAC;AAAA,YAAE,EAAET,IAAEC,IAAEC,IAAEC,IAAE,IAAGG,EAAC;AAAA,UAAE;AAAC,mBAAS,GAAGN,IAAEC,IAAEC,IAAEC,IAAEG,IAAEC,IAAE;AAAC,gBAAIC,KAAEN,KAAE,GAAEO,KAAET,GAAE,QAAOU,KAAET,GAAE;AAAO,gBAAGQ,MAAGC,MAAG,EAAEF,MAAGE,KAAED,IAAG,QAAM;AAAG,gBAAIE,KAAEJ,GAAE,IAAIP,EAAC;AAAE,gBAAGW,MAAGJ,GAAE,IAAIN,EAAC,EAAE,QAAOU,MAAGV;AAAE,gBAAIW,KAAE,IAAGC,KAAE,MAAGC,KAAEZ,KAAE,IAAE,IAAI,OAAG;AAAO,iBAAIK,GAAE,IAAIP,IAAEC,EAAC,GAAEM,GAAE,IAAIN,IAAED,EAAC,GAAE,EAAEY,KAAEH,MAAG;AAAC,kBAAIM,KAAEf,GAAEY,EAAC,GAAEI,KAAEf,GAAEW,EAAC;AAAE,kBAAGT,GAAE,KAAIc,KAAET,KAAEL,GAAEa,IAAED,IAAEH,IAAEX,IAAED,IAAEO,EAAC,IAAEJ,GAAEY,IAAEC,IAAEJ,IAAEZ,IAAEC,IAAEM,EAAC;AAAE,kBAAG,WAASU,IAAE;AAAC,oBAAGA,GAAE;AAAS,gBAAAJ,KAAE;AAAG;AAAA,cAAK;AAAC,kBAAGC,IAAE;AAAC,oBAAG,CAAC,EAAEb,IAAG,SAASD,IAAEC,IAAE;AAAC,sBAAGG,KAAEH,IAAE,CAACa,GAAE,IAAIV,EAAC,MAAIW,OAAIf,MAAGM,GAAES,IAAEf,IAAEE,IAAEC,IAAEI,EAAC,GAAG,QAAOO,GAAE,KAAKb,EAAC;AAAE,sBAAIG;AAAA,gBAAC,CAAE,GAAE;AAAC,kBAAAS,KAAE;AAAG;AAAA,gBAAK;AAAA,cAAC,WAASE,OAAIC,MAAG,CAACV,GAAES,IAAEC,IAAEd,IAAEC,IAAEI,EAAC,GAAE;AAAC,gBAAAM,KAAE;AAAG;AAAA,cAAK;AAAA,YAAC;AAAC,mBAAON,GAAE,OAAOP,EAAC,GAAEO,GAAE,OAAON,EAAC,GAAEY;AAAA,UAAC;AAAC,mBAAS,GAAGb,IAAE;AAAC,mBAAO,SAASA,IAAEC,IAAEC,IAAE;AAAC,kBAAIC,KAAEF,GAAED,EAAC;AAAE,qBAAO,GAAGA,EAAC,IAAEG,KAAE,SAASH,IAAEC,IAAE;AAAC,yBAAQC,KAAE,IAAGC,KAAEF,GAAE,QAAOG,KAAEJ,GAAE,QAAO,EAAEE,KAAEC,KAAG,CAAAH,GAAEI,KAAEF,EAAC,IAAED,GAAEC,EAAC;AAAE,uBAAOF;AAAA,cAAC,EAAEG,IAAED,GAAEF,EAAC,CAAC;AAAA,YAAC,EAAEA,IAAE,IAAG,EAAE;AAAA,UAAC;AAAC,mBAAS,GAAGA,IAAEC,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,KAAEJ,GAAE;AAAS,oBAAO,aAAWG,KAAE,QAAOD,KAAED,QAAK,YAAUE,MAAG,YAAUA,MAAG,aAAWA,KAAE,gBAAcD,KAAE,SAAOA,MAAGE,GAAE,YAAU,OAAOH,KAAE,WAAS,MAAM,IAAEG,GAAE;AAAA,UAAG;AAAC,mBAAS,GAAGJ,IAAEC,IAAE;AAAC,gBAAIC,KAAE,SAASF,IAAEC,IAAE;AAAC,qBAAO,QAAMD,KAAE,SAAOA,GAAEC,EAAC;AAAA,YAAC,EAAED,IAAEC,EAAC;AAAE,mBAAO,SAASD,IAAE;AAAC,qBAAM,EAAE,CAAC,GAAGA,EAAC,KAAG,SAASA,IAAE;AAAC,uBAAM,CAAC,CAAC,KAAG,KAAKA;AAAA,cAAC,EAAEA,EAAC,OAAK,GAAGA,EAAC,IAAE,KAAG,GAAG,KAAK,GAAGA,EAAC,CAAC;AAAA,YAAC,EAAEE,EAAC,IAAEA,KAAE;AAAA,UAAM;AAAC,aAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,KAAG,GAAG,IAAI,IAAE,CAAC,GAAE,KAAK,OAAK;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASF,IAAE;AAAC,gBAAIC,KAAE,KAAK,IAAID,EAAC,KAAG,OAAO,KAAK,SAASA,EAAC;AAAE,mBAAO,KAAK,QAAMC,KAAE,IAAE,GAAEA;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASD,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,gBAAG,IAAG;AAAC,kBAAIC,KAAED,GAAED,EAAC;AAAE,qBAAOE,OAAIC,KAAE,SAAOD;AAAA,YAAC;AAAC,mBAAO,EAAE,KAAKD,IAAED,EAAC,IAAEC,GAAED,EAAC,IAAE;AAAA,UAAM,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,mBAAO,KAAG,WAASA,GAAED,EAAC,IAAE,EAAE,KAAKC,IAAED,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,mBAAO,KAAK,QAAM,KAAK,IAAIF,EAAC,IAAE,IAAE,GAAEE,GAAEF,EAAC,IAAE,MAAI,WAASC,KAAEE,KAAEF,IAAE;AAAA,UAAI,GAAE,GAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,CAAC,GAAE,KAAK,OAAK;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASD,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAED,EAAC;AAAE,mBAAM,EAAEE,KAAE,MAAIA,MAAGD,GAAE,SAAO,IAAEA,GAAE,IAAI,IAAE,GAAG,KAAKA,IAAEC,IAAE,CAAC,GAAE,EAAE,KAAK,MAAK;AAAA,UAAG,GAAE,GAAG,UAAU,MAAI,SAASF,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAED,EAAC;AAAE,mBAAOE,KAAE,IAAE,SAAOD,GAAEC,EAAC,EAAE,CAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASF,IAAE;AAAC,mBAAO,GAAG,KAAK,UAASA,EAAC,IAAE;AAAA,UAAE,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,GAAGD,IAAEF,EAAC;AAAE,mBAAOG,KAAE,KAAG,EAAE,KAAK,MAAKD,GAAE,KAAK,CAACF,IAAEC,EAAC,CAAC,KAAGC,GAAEC,EAAC,EAAE,CAAC,IAAEF,IAAE;AAAA,UAAI,GAAE,GAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,OAAK,GAAE,KAAK,WAAS,EAAC,MAAK,IAAI,MAAG,KAAI,KAAI,MAAI,OAAI,QAAO,IAAI,KAAE;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASD,IAAE;AAAC,gBAAIC,KAAE,GAAG,MAAKD,EAAC,EAAE,OAAOA,EAAC;AAAE,mBAAO,KAAK,QAAMC,KAAE,IAAE,GAAEA;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASD,IAAE;AAAC,mBAAO,GAAG,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,GAAG,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,GAAG,MAAKF,EAAC,GAAEG,KAAED,GAAE;AAAK,mBAAOA,GAAE,IAAIF,IAAEC,EAAC,GAAE,KAAK,QAAMC,GAAE,QAAMC,KAAE,IAAE,GAAE;AAAA,UAAI,GAAE,GAAG,UAAU,MAAI,GAAG,UAAU,OAAK,SAASH,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,IAAEG,EAAC,GAAE;AAAA,UAAI,GAAE,GAAG,UAAU,MAAI,SAASH,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,IAAI,MAAG,KAAK,OAAK;AAAA,UAAC,GAAE,GAAG,UAAU,SAAO,SAASA,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAED,GAAE,OAAOD,EAAC;AAAE,mBAAO,KAAK,OAAKC,GAAE,MAAKC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASF,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,GAAG,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,gBAAGA,cAAa,IAAG;AAAC,kBAAIC,KAAED,GAAE;AAAS,kBAAG,CAAC,MAAIC,GAAE,SAAO,IAAI,QAAOA,GAAE,KAAK,CAACH,IAAEC,EAAC,CAAC,GAAE,KAAK,OAAK,EAAEC,GAAE,MAAK;AAAK,cAAAA,KAAE,KAAK,WAAS,IAAI,GAAGC,EAAC;AAAA,YAAC;AAAC,mBAAOD,GAAE,IAAIF,IAAEC,EAAC,GAAE,KAAK,OAAKC,GAAE,MAAK;AAAA,UAAI;AAAE,cAAI,KAAG,KAAG,SAASF,IAAE;AAAC,mBAAO,QAAMA,KAAE,CAAC,KAAGA,KAAE,OAAOA,EAAC,GAAE,SAASC,IAAEC,IAAE;AAAC,uBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAOI,KAAE,GAAEC,KAAE,CAAC,GAAE,EAAEH,KAAEC,MAAG;AAAC,oBAAIG,KAAEN,GAAEE,EAAC;AAAE,gBAAAK,KAAED,IAAE,GAAG,KAAKP,IAAEQ,EAAC,MAAIF,GAAED,IAAG,IAAEE;AAAA,cAAE;AAAC,kBAAIC;AAAE,qBAAOF;AAAA,YAAC,EAAE,GAAGN,EAAC,CAAC;AAAA,UAAE,IAAE,WAAU;AAAC,mBAAM,CAAC;AAAA,UAAC,GAAE,KAAG;AAAG,mBAAS,GAAGA,IAAEC,IAAE;AAAC,mBAAM,CAAC,EAAEA,KAAE,QAAMA,KAAE,IAAEA,QAAK,YAAU,OAAOD,MAAG,EAAE,KAAKA,EAAC,MAAIA,KAAE,MAAIA,KAAE,KAAG,KAAGA,KAAEC;AAAA,UAAC;AAAC,mBAAS,GAAGD,IAAE;AAAC,gBAAG,QAAMA,IAAE;AAAC,kBAAG;AAAC,uBAAO,EAAE,KAAKA,EAAC;AAAA,cAAC,SAAOA,IAAE;AAAA,cAAC;AAAC,kBAAG;AAAC,uBAAOA,KAAE;AAAA,cAAE,SAAOA,IAAE;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAM;AAAA,UAAE;AAAC,mBAAS,GAAGA,IAAEC,IAAE;AAAC,mBAAOD,OAAIC,MAAGD,MAAGA,MAAGC,MAAGA;AAAA,UAAC;AAAC,WAAC,MAAI,GAAG,IAAI,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,KAAG,KAAG,MAAI,GAAG,IAAI,IAAE,KAAG,KAAG,MAAI,GAAG,GAAG,QAAQ,CAAC,KAAG,KAAG,MAAI,GAAG,IAAI,IAAE,KAAG,KAAG,MAAI,GAAG,IAAI,IAAE,KAAG,OAAK,KAAG,SAASD,IAAE;AAAC,gBAAIC,KAAE,GAAGD,EAAC,GAAEE,KAAED,MAAG,IAAED,GAAE,cAAY,QAAOG,KAAED,KAAE,GAAGA,EAAC,IAAE;AAAG,gBAAGC,GAAE,SAAOA,IAAE;AAAA,cAAC,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,cAAE,KAAK;AAAG,uBAAO;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC;AAAG,cAAI,KAAG,GAAG,2BAAU;AAAC,mBAAO;AAAA,UAAS,EAAE,CAAC,IAAE,KAAG,SAASD,IAAE;AAAC,mBAAO,GAAGA,EAAC,KAAG,EAAE,KAAKA,IAAE,QAAQ,KAAG,CAAC,GAAG,KAAKA,IAAE,QAAQ;AAAA,UAAC,GAAE,KAAG,MAAM,SAAQ,KAAG,MAAI,WAAU;AAAC,mBAAM;AAAA,UAAE;AAAE,mBAAS,GAAGA,IAAE;AAAC,gBAAG,CAAC,GAAGA,EAAC,EAAE,QAAM;AAAG,gBAAIC,KAAE,GAAGD,EAAC;AAAE,mBAAOC,MAAG,KAAGA,MAAG,KAAGA,MAAG,KAAGA,MAAG;AAAA,UAAC;AAAC,mBAAS,GAAGD,IAAE;AAAC,mBAAM,YAAU,OAAOA,MAAGA,KAAE,MAAIA,KAAE,KAAG,KAAGA,MAAG;AAAA,UAAC;AAAC,mBAAS,GAAGA,IAAE;AAAC,gBAAIC,KAAE,OAAOD;AAAE,mBAAO,QAAMA,OAAI,YAAUC,MAAG,cAAYA;AAAA,UAAE;AAAC,mBAAS,GAAGD,IAAE;AAAC,mBAAO,QAAMA,MAAG,YAAU,OAAOA;AAAA,UAAC;AAAC,cAAI,KAAG,IAAE,yBAASA,IAAE;AAAC,mBAAO,SAASC,IAAE;AAAC,qBAAOD,GAAEC,EAAC;AAAA,YAAC;AAAA,UAAC,EAAE,CAAC,IAAE,SAASD,IAAE;AAAC,mBAAO,GAAGA,EAAC,KAAG,GAAGA,GAAE,MAAM,KAAG,CAAC,CAAC,EAAE,GAAGA,EAAC,CAAC;AAAA,UAAC;AAAE,mBAAS,GAAGA,IAAE;AAAC,mBAAO,SAAOC,KAAED,OAAI,GAAGC,GAAE,MAAM,KAAG,CAAC,GAAGA,EAAC,IAAE,SAASD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,GAAGF,EAAC,GAAEG,KAAE,CAACD,MAAG,GAAGF,EAAC,GAAEI,KAAE,CAACF,MAAG,CAACC,MAAG,GAAGH,EAAC,GAAEK,KAAE,CAACH,MAAG,CAACC,MAAG,CAACC,MAAG,GAAGJ,EAAC,GAAEM,KAAEJ,MAAGC,MAAGC,MAAGC,IAAEE,KAAED,KAAE,SAASN,IAAEC,IAAE;AAAC,yBAAQC,KAAE,IAAGC,KAAE,MAAMH,EAAC,GAAE,EAAEE,KAAEF,KAAG,CAAAG,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAE,uBAAOC;AAAA,cAAC,EAAEH,GAAE,QAAO,MAAM,IAAE,CAAC,GAAEQ,KAAED,GAAE;AAAO,uBAAQE,MAAKT,GAAE,EAACC,MAAG,CAAC,EAAE,KAAKD,IAAES,EAAC,KAAGH,OAAI,YAAUG,MAAGL,OAAI,YAAUK,MAAG,YAAUA,OAAIJ,OAAI,YAAUI,MAAG,gBAAcA,MAAG,gBAAcA,OAAI,GAAGA,IAAED,EAAC,MAAID,GAAE,KAAKE,EAAC;AAAE,qBAAOF;AAAA,YAAC,EAAEP,EAAC,IAAE,SAASA,IAAE;AAAC,kBAAGE,MAAGD,KAAED,OAAIC,GAAE,aAAYA,QAAK,cAAY,OAAOC,MAAGA,GAAE,aAAW,GAAG,QAAO,GAAGF,EAAC;AAAE,kBAAIC,IAAEC,IAAEC,KAAE,CAAC;AAAE,uBAAQC,MAAK,OAAOJ,EAAC,EAAE,GAAE,KAAKA,IAAEI,EAAC,KAAG,iBAAeA,MAAGD,GAAE,KAAKC,EAAC;AAAE,qBAAOD;AAAA,YAAC,EAAEH,EAAC;AAAE,gBAAIC;AAAA,UAAC;AAAC,UAAAD,GAAE,UAAQ,SAASA,IAAEC,IAAE;AAAC,mBAAO,GAAGD,IAAEC,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,iBAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAE,gBAAME,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,cAAI;AAAE,WAAC,SAASF,IAAE;AAAC,YAAAA,GAAE,UAAQ,SAASA,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,OAAG;AAAC,0BAAU,OAAOF,OAAIA,KAAE,CAAC,IAAG,YAAU,OAAOC,OAAIA,KAAE,CAAC;AAAG,kBAAIG,KAAED,GAAEF,EAAC;AAAE,cAAAC,OAAIE,KAAE,OAAO,KAAKA,EAAC,EAAE,OAAQ,CAACJ,IAAEC,QAAK,QAAMG,GAAEH,EAAC,MAAID,GAAEC,EAAC,IAAEG,GAAEH,EAAC,IAAGD,KAAI,CAAC,CAAC;AAAG,yBAAUE,MAAKF,GAAE,YAASA,GAAEE,EAAC,KAAG,WAASD,GAAEC,EAAC,MAAIE,GAAEF,EAAC,IAAEF,GAAEE,EAAC;AAAG,qBAAO,OAAO,KAAKE,EAAC,EAAE,SAAO,IAAEA,KAAE;AAAA,YAAM,GAAEJ,GAAE,OAAK,SAASA,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAE;AAAC,0BAAU,OAAOD,OAAIA,KAAE,CAAC,IAAG,YAAU,OAAOC,OAAIA,KAAE,CAAC;AAAG,oBAAMC,KAAE,OAAO,KAAKF,EAAC,EAAE,OAAO,OAAO,KAAKC,EAAC,CAAC,EAAE,OAAQ,CAACC,IAAEC,QAAK,EAAEH,GAAEG,EAAC,GAAEF,GAAEE,EAAC,CAAC,MAAID,GAAEC,EAAC,IAAE,WAASF,GAAEE,EAAC,IAAE,OAAKF,GAAEE,EAAC,IAAGD,KAAI,CAAC,CAAC;AAAE,qBAAO,OAAO,KAAKA,EAAC,EAAE,SAAO,IAAEA,KAAE;AAAA,YAAM,GAAEF,GAAE,SAAO,SAASA,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAE;AAAC,cAAAD,KAAEA,MAAG,CAAC;AAAE,oBAAME,KAAE,OAAO,KAAKD,EAAC,EAAE,OAAQ,CAACC,IAAEC,QAAKF,GAAEE,EAAC,MAAIH,GAAEG,EAAC,KAAG,WAASH,GAAEG,EAAC,MAAID,GAAEC,EAAC,IAAEF,GAAEE,EAAC,IAAGD,KAAI,CAAC,CAAC;AAAE,qBAAO,OAAO,KAAKF,EAAC,EAAE,OAAQ,CAACE,IAAEC,QAAKH,GAAEG,EAAC,MAAIF,GAAEE,EAAC,KAAG,WAASF,GAAEE,EAAC,MAAID,GAAEC,EAAC,IAAE,OAAMD,KAAIA,EAAC;AAAA,YAAC,GAAEF,GAAE,YAAU,SAASA,IAAEC,IAAEC,KAAE,OAAG;AAAC,kBAAG,YAAU,OAAOF,GAAE,QAAOC;AAAE,kBAAG,YAAU,OAAOA,GAAE;AAAO,kBAAG,CAACC,GAAE,QAAOD;AAAE,oBAAME,KAAE,OAAO,KAAKF,EAAC,EAAE,OAAQ,CAACC,IAAEC,QAAK,WAASH,GAAEG,EAAC,MAAID,GAAEC,EAAC,IAAEF,GAAEE,EAAC,IAAGD,KAAI,CAAC,CAAC;AAAE,qBAAO,OAAO,KAAKC,EAAC,EAAE,SAAO,IAAEA,KAAE;AAAA,YAAM;AAAA,UAAC,EAAE,MAAI,IAAE,CAAC,EAAE,GAAEF,GAAE,UAAQ;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,iBAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,eAAaA,GAAE,aAAWA,GAAE,KAAG;AAAO,gBAAME,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,UAAAD,GAAE,eAAa,EAAE;AAAQ,gBAAM,IAAEC,GAAE,IAAI;AAAE,UAAAD,GAAE,KAAG,EAAE;AAAQ,gBAAM,IAAEC,GAAE,IAAI;AAAE,UAAAD,GAAE,aAAW,EAAE;AAAQ,gBAAM,IAAE,OAAO,aAAa,CAAC,GAAE,IAAE,CAACD,IAAEC,OAAI;AAAC,gBAAG,YAAU,OAAOD,MAAG,SAAOA,GAAE,OAAM,IAAI,MAAM,qBAAmB,OAAOA,EAAC;AAAE,gBAAG,YAAU,OAAOC,MAAG,SAAOA,GAAE,OAAM,IAAI,MAAM,qBAAmB,OAAOA,EAAC;AAAE,kBAAMC,KAAE,OAAO,KAAKF,EAAC,EAAE,CAAC;AAAE,gBAAG,CAACE,MAAGA,OAAI,OAAO,KAAKD,EAAC,EAAE,CAAC,EAAE,OAAM,IAAI,MAAM,4BAA4BC,EAAC,OAAO,OAAO,KAAKD,EAAC,EAAE,CAAC,CAAC,EAAE;AAAE,mBAAM,CAACC,IAAEF,GAAEE,EAAC,GAAED,GAAEC,EAAC,CAAC;AAAA,UAAC;AAAA,UAAE,MAAM,EAAC;AAAA,YAAC,YAAYF,IAAE;AAAC,oBAAM,QAAQA,EAAC,IAAE,KAAK,MAAIA,KAAE,QAAMA,MAAG,MAAM,QAAQA,GAAE,GAAG,IAAE,KAAK,MAAIA,GAAE,MAAI,KAAK,MAAI,CAAC;AAAA,YAAC;AAAA,YAAC,OAAO,cAAcA,IAAEC,IAAE;AAAC,mBAAK,SAASD,EAAC,IAAEC;AAAA,YAAC;AAAA,YAAC,OAAO,gBAAgBD,IAAE;AAAC,qBAAO,KAAK,SAASA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,WAAWA,IAAE;AAAC,oBAAMC,KAAE,KAAK,SAASD,EAAC;AAAE,kBAAG,CAACC,GAAE,OAAM,IAAI,MAAM,+BAA+BD,EAAC,GAAG;AAAE,qBAAOC;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,CAAC;AAAE,qBAAM,YAAU,OAAOF,MAAG,MAAIA,GAAE,SAAO,QAAME,GAAE,SAAOF,IAAE,QAAMC,MAAG,YAAU,OAAOA,MAAG,OAAO,KAAKA,EAAC,EAAE,SAAO,MAAIC,GAAE,aAAWD,KAAG,KAAK,KAAKC,EAAC;AAAA,YAAE;AAAA,YAAC,OAAOF,IAAE;AAAC,qBAAOA,MAAG,IAAE,OAAK,KAAK,KAAK,EAAC,QAAOA,GAAC,CAAC;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,kBAAG,YAAU,OAAOD,MAAGA,MAAG,EAAE,QAAO;AAAK,oBAAME,KAAE,EAAC,QAAOF,GAAC;AAAE,qBAAO,QAAMC,MAAG,YAAU,OAAOA,MAAG,OAAO,KAAKA,EAAC,EAAE,SAAO,MAAIC,GAAE,aAAWD,KAAG,KAAK,KAAKC,EAAC;AAAA,YAAC;AAAA,YAAC,KAAKF,IAAE;AAAC,kBAAIC,KAAE,KAAK,IAAI,QAAOC,KAAE,KAAK,IAAID,KAAE,CAAC;AAAE,kBAAGD,KAAE,EAAEA,EAAC,GAAE,YAAU,OAAOE,IAAE;AAAC,oBAAG,YAAU,OAAOF,GAAE,UAAQ,YAAU,OAAOE,GAAE,OAAO,QAAO,KAAK,IAAID,KAAE,CAAC,IAAE,EAAC,QAAOC,GAAE,SAAOF,GAAE,OAAM,GAAE;AAAK,oBAAG,YAAU,OAAOE,GAAE,UAAQ,QAAMF,GAAE,WAASC,MAAG,GAAEC,KAAE,KAAK,IAAID,KAAE,CAAC,GAAE,YAAU,OAAOC,IAAG,QAAO,KAAK,IAAI,QAAQF,EAAC,GAAE;AAAK,oBAAG,EAAEA,GAAE,YAAWE,GAAE,UAAU,GAAE;AAAC,sBAAG,YAAU,OAAOF,GAAE,UAAQ,YAAU,OAAOE,GAAE,OAAO,QAAO,KAAK,IAAID,KAAE,CAAC,IAAE,EAAC,QAAOC,GAAE,SAAOF,GAAE,OAAM,GAAE,YAAU,OAAOA,GAAE,eAAa,KAAK,IAAIC,KAAE,CAAC,EAAE,aAAWD,GAAE,aAAY;AAAK,sBAAG,YAAU,OAAOA,GAAE,UAAQ,YAAU,OAAOE,GAAE,OAAO,QAAO,KAAK,IAAID,KAAE,CAAC,IAAE,EAAC,QAAOC,GAAE,SAAOF,GAAE,OAAM,GAAE,YAAU,OAAOA,GAAE,eAAa,KAAK,IAAIC,KAAE,CAAC,EAAE,aAAWD,GAAE,aAAY;AAAA,gBAAI;AAAA,cAAC;AAAC,qBAAOC,OAAI,KAAK,IAAI,SAAO,KAAK,IAAI,KAAKD,EAAC,IAAE,KAAK,IAAI,OAAOC,IAAE,GAAED,EAAC,GAAE;AAAA,YAAI;AAAA,YAAC,OAAM;AAAC,oBAAMA,KAAE,KAAK,IAAI,KAAK,IAAI,SAAO,CAAC;AAAE,qBAAOA,MAAG,YAAU,OAAOA,GAAE,UAAQ,CAACA,GAAE,cAAY,KAAK,IAAI,IAAI,GAAE;AAAA,YAAI;AAAA,YAAC,OAAOA,IAAE;AAAC,qBAAO,KAAK,IAAI,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,QAAQA,IAAE;AAAC,mBAAK,IAAI,QAAQA,EAAC;AAAA,YAAC;AAAA,YAAC,IAAIA,IAAE;AAAC,qBAAO,KAAK,IAAI,IAAIA,EAAC;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAE;AAAC,oBAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,qBAAO,KAAK,QAAS,CAAAC,OAAG;AAAC,iBAACH,GAAEG,EAAC,IAAEF,KAAEC,IAAG,KAAKC,EAAC;AAAA,cAAC,CAAE,GAAE,CAACF,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,qBAAO,KAAK,IAAI,OAAOD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,qBAAO,KAAK,OAAQ,CAACD,IAAEC,OAAIA,GAAE,SAAOD,KAAE,EAAE,QAAQ,OAAOC,EAAC,IAAEA,GAAE,SAAOD,KAAEC,GAAE,SAAOD,IAAG,CAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO,KAAK,OAAQ,CAACA,IAAEC,OAAID,KAAE,EAAE,QAAQ,OAAOC,EAAC,GAAG,CAAC;AAAA,YAAC;AAAA,YAAC,MAAMD,KAAE,GAAEC,KAAE,IAAE,GAAE;AAAC,oBAAMC,KAAE,CAAC,GAAEC,KAAE,IAAI,EAAE,QAAQ,KAAK,GAAG;AAAE,kBAAIC,KAAE;AAAE,qBAAKA,KAAEH,MAAGE,GAAE,QAAQ,KAAG;AAAC,oBAAIE;AAAE,gBAAAD,KAAEJ,KAAEK,KAAEF,GAAE,KAAKH,KAAEI,EAAC,KAAGC,KAAEF,GAAE,KAAKF,KAAEG,EAAC,GAAEF,GAAE,KAAKG,EAAC,IAAGD,MAAG,EAAE,QAAQ,OAAOC,EAAC;AAAA,cAAC;AAAC,qBAAO,IAAI,EAAEH,EAAC;AAAA,YAAC;AAAA,YAAC,QAAQF,IAAE;AAAC,oBAAMC,KAAE,IAAI,EAAE,QAAQ,KAAK,GAAG,GAAEC,KAAE,IAAI,EAAE,QAAQF,GAAE,GAAG,GAAEG,KAAE,CAAC,GAAEC,KAAEF,GAAE,KAAK;AAAE,kBAAG,QAAME,MAAG,YAAU,OAAOA,GAAE,UAAQ,QAAMA,GAAE,YAAW;AAAC,oBAAIJ,KAAEI,GAAE;AAAO,uBAAK,aAAWH,GAAE,SAAS,KAAGA,GAAE,WAAW,KAAGD,KAAG,CAAAA,MAAGC,GAAE,WAAW,GAAEE,GAAE,KAAKF,GAAE,KAAK,CAAC;AAAE,gBAAAG,GAAE,SAAOJ,KAAE,KAAGE,GAAE,KAAKE,GAAE,SAAOJ,EAAC;AAAA,cAAC;AAAC,oBAAMO,KAAE,IAAI,EAAEJ,EAAC;AAAE,qBAAKF,GAAE,QAAQ,KAAGC,GAAE,QAAQ,IAAG,KAAG,aAAWA,GAAE,SAAS,EAAE,CAAAK,GAAE,KAAKL,GAAE,KAAK,CAAC;AAAA,uBAAU,aAAWD,GAAE,SAAS,EAAE,CAAAM,GAAE,KAAKN,GAAE,KAAK,CAAC;AAAA,mBAAM;AAAC,sBAAMD,KAAE,KAAK,IAAIC,GAAE,WAAW,GAAEC,GAAE,WAAW,CAAC,GAAEC,KAAEF,GAAE,KAAKD,EAAC,GAAEI,KAAEF,GAAE,KAAKF,EAAC;AAAE,oBAAGI,GAAE,QAAO;AAAC,wBAAMI,KAAE,CAAC;AAAE,sBAAG,YAAU,OAAOL,GAAE,OAAO,CAAAK,GAAE,SAAO,YAAU,OAAOJ,GAAE,SAAOJ,KAAEI,GAAE;AAAA,2BAAe,YAAU,OAAOA,GAAE,OAAO,SAAMD,GAAE,SAAOK,GAAE,SAAOL,GAAE,SAAOK,GAAE,SAAOL,GAAE;AAAA,uBAAW;AAAC,0BAAMH,KAAE,QAAMG,GAAE,SAAO,WAAS,UAAS,CAACF,IAAEC,IAAEG,EAAC,IAAE,EAAEF,GAAEH,EAAC,GAAEI,GAAE,MAAM,GAAEE,KAAE,EAAE,WAAWL,EAAC;AAAE,oBAAAO,GAAER,EAAC,IAAE,EAAC,CAACC,EAAC,GAAEK,GAAE,QAAQJ,IAAEG,IAAE,aAAWL,EAAC,EAAC;AAAA,kBAAC;AAAC,wBAAMS,KAAE,EAAE,QAAQ,QAAQN,GAAE,YAAWC,GAAE,YAAW,YAAU,OAAOD,GAAE,MAAM;AAAE,sBAAGM,OAAID,GAAE,aAAWC,KAAGF,GAAE,KAAKC,EAAC,GAAE,CAACN,GAAE,QAAQ,KAAG,EAAEK,GAAE,IAAIA,GAAE,IAAI,SAAO,CAAC,GAAEC,EAAC,GAAE;AAAC,0BAAMR,KAAE,IAAI,EAAEC,GAAE,KAAK,CAAC;AAAE,2BAAOM,GAAE,OAAOP,EAAC,EAAE,KAAK;AAAA,kBAAC;AAAA,gBAAC,MAAK,aAAU,OAAOI,GAAE,WAAS,YAAU,OAAOD,GAAE,UAAQ,YAAU,OAAOA,GAAE,UAAQ,SAAOA,GAAE,WAASI,GAAE,KAAKH,EAAC;AAAA,cAAC;AAAC,qBAAOG,GAAE,KAAK;AAAA,YAAC;AAAA,YAAC,OAAOP,IAAE;AAAC,oBAAMC,KAAE,IAAI,EAAE,KAAK,IAAI,MAAM,CAAC;AAAE,qBAAOD,GAAE,IAAI,SAAO,MAAIC,GAAE,KAAKD,GAAE,IAAI,CAAC,CAAC,GAAEC,GAAE,MAAIA,GAAE,IAAI,OAAOD,GAAE,IAAI,MAAM,CAAC,CAAC,IAAGC;AAAA,YAAC;AAAA,YAAC,KAAKD,IAAEC,IAAE;AAAC,kBAAG,KAAK,QAAMD,GAAE,IAAI,QAAO,IAAI;AAAE,oBAAME,KAAE,CAAC,MAAKF,EAAC,EAAE,IAAK,CAAAC,OAAGA,GAAE,IAAK,CAAAC,OAAG;AAAC,oBAAG,QAAMA,GAAE,OAAO,QAAM,YAAU,OAAOA,GAAE,SAAOA,GAAE,SAAO;AAAE,sBAAM,IAAI,MAAM,oBAAkBD,OAAID,KAAE,OAAK,UAAQ,eAAe;AAAA,cAAC,CAAE,EAAE,KAAK,EAAE,CAAE,GAAEI,KAAE,IAAI,KAAEG,KAAEJ,GAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAED,IAAE,IAAE,GAAES,KAAE,IAAI,EAAE,QAAQ,KAAK,GAAG,GAAE,IAAE,IAAI,EAAE,QAAQV,GAAE,GAAG;AAAE,qBAAOO,GAAE,QAAS,CAAAP,OAAG;AAAC,oBAAIC,KAAED,GAAE,CAAC,EAAE;AAAO,uBAAKC,KAAE,KAAG;AAAC,sBAAIC,KAAE;AAAE,0BAAOF,GAAE,CAAC,GAAE;AAAA,oBAAC,KAAKG,GAAE;AAAO,sBAAAD,KAAE,KAAK,IAAI,EAAE,WAAW,GAAED,EAAC,GAAEG,GAAE,KAAK,EAAE,KAAKF,EAAC,CAAC;AAAE;AAAA,oBAAM,KAAKC,GAAE;AAAO,sBAAAD,KAAE,KAAK,IAAID,IAAES,GAAE,WAAW,CAAC,GAAEA,GAAE,KAAKR,EAAC,GAAEE,GAAE,OAAOF,EAAC;AAAE;AAAA,oBAAM,KAAKC,GAAE;AAAM,sBAAAD,KAAE,KAAK,IAAIQ,GAAE,WAAW,GAAE,EAAE,WAAW,GAAET,EAAC;AAAE,4BAAMD,KAAEU,GAAE,KAAKR,EAAC,GAAEK,KAAE,EAAE,KAAKL,EAAC;AAAE,wBAAEF,GAAE,QAAOO,GAAE,MAAM,IAAEH,GAAE,OAAOF,IAAE,EAAE,QAAQ,KAAKF,GAAE,YAAWO,GAAE,UAAU,CAAC,IAAEH,GAAE,KAAKG,EAAC,EAAE,OAAOL,EAAC;AAAA,kBAAC;AAAC,kBAAAD,MAAGC;AAAA,gBAAC;AAAA,cAAC,CAAE,GAAEE,GAAE,KAAK;AAAA,YAAC;AAAA,YAAC,SAASJ,IAAEC,KAAE,MAAK;AAAC,oBAAMC,KAAE,IAAI,EAAE,QAAQ,KAAK,GAAG;AAAE,kBAAIC,KAAE,IAAI,KAAEC,KAAE;AAAE,qBAAKF,GAAE,QAAQ,KAAG;AAAC,oBAAG,aAAWA,GAAE,SAAS,EAAE;AAAO,sBAAMG,KAAEH,GAAE,KAAK,GAAEI,KAAE,EAAE,QAAQ,OAAOD,EAAC,IAAEH,GAAE,WAAW,GAAEM,KAAE,YAAU,OAAOH,GAAE,SAAOA,GAAE,OAAO,QAAQJ,IAAEK,EAAC,IAAEA,KAAE;AAAG,oBAAGE,KAAE,EAAE,CAAAL,GAAE,KAAKD,GAAE,KAAK,CAAC;AAAA,yBAAUM,KAAE,EAAE,CAAAL,GAAE,KAAKD,GAAE,KAAKM,EAAC,CAAC;AAAA,qBAAM;AAAC,sBAAG,UAAKR,GAAEG,IAAED,GAAE,KAAK,CAAC,EAAE,cAAY,CAAC,GAAEE,EAAC,EAAE;AAAO,kBAAAA,MAAG,GAAED,KAAE,IAAI;AAAA,gBAAC;AAAA,cAAC;AAAC,cAAAA,GAAE,OAAO,IAAE,KAAGH,GAAEG,IAAE,CAAC,GAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,OAAOJ,IAAE;AAAC,oBAAMC,KAAE,IAAI;AAAE,qBAAO,KAAK,OAAQ,CAACC,IAAEC,OAAI;AAAC,oBAAGA,GAAE,OAAO,CAAAF,GAAE,OAAO,EAAE,QAAQ,OAAOE,EAAC,CAAC;AAAA,qBAAM;AAAC,sBAAG,YAAU,OAAOA,GAAE,UAAQ,QAAMA,GAAE,WAAW,QAAOF,GAAE,OAAOE,GAAE,MAAM,GAAED,KAAEC,GAAE;AAAO,sBAAGA,GAAE,UAAQ,YAAU,OAAOA,GAAE,QAAO;AAAC,0BAAMC,KAAED,GAAE,UAAQA,GAAE;AAAO,2BAAOH,GAAE,MAAME,IAAEA,KAAEE,EAAC,EAAE,QAAS,CAAAJ,OAAG;AAAC,sBAAAG,GAAE,SAAOF,GAAE,KAAKD,EAAC,IAAEG,GAAE,UAAQA,GAAE,cAAYF,GAAE,OAAO,EAAE,QAAQ,OAAOD,EAAC,GAAE,EAAE,QAAQ,OAAOG,GAAE,YAAWH,GAAE,UAAU,CAAC;AAAA,oBAAC,CAAE,GAAEE,KAAEE;AAAA,kBAAC;AAAC,sBAAG,YAAU,OAAOD,GAAE,UAAQ,SAAOA,GAAE,QAAO;AAAC,0BAAMC,KAAEJ,GAAE,MAAME,IAAEA,KAAE,CAAC,GAAEG,KAAE,IAAI,EAAE,QAAQD,GAAE,GAAG,EAAE,KAAK,GAAE,CAACG,IAAEE,IAAE,CAAC,IAAE,EAAEN,GAAE,QAAOE,GAAE,MAAM,GAAE,IAAE,EAAE,WAAWE,EAAC;AAAE,2BAAON,GAAE,OAAO,EAAC,CAACM,EAAC,GAAE,EAAE,OAAOE,IAAE,CAAC,EAAC,GAAE,EAAE,QAAQ,OAAON,GAAE,YAAWE,GAAE,UAAU,CAAC,GAAEH,KAAE;AAAA,kBAAC;AAAA,gBAAC;AAAC,uBAAOA;AAAA,cAAC,GAAG,CAAC,GAAED,GAAE,KAAK;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAEC,KAAE,OAAG;AAAC,kBAAGA,KAAE,CAAC,CAACA,IAAE,YAAU,OAAOD,GAAE,QAAO,KAAK,kBAAkBA,IAAEC,EAAC;AAAE,oBAAMC,KAAEF,IAAEG,KAAE,IAAI,EAAE,QAAQ,KAAK,GAAG,GAAEC,KAAE,IAAI,EAAE,QAAQF,GAAE,GAAG,GAAEG,KAAE,IAAI;AAAE,qBAAKF,GAAE,QAAQ,KAAGC,GAAE,QAAQ,IAAG,KAAG,aAAWD,GAAE,SAAS,KAAG,CAACF,MAAG,aAAWG,GAAE,SAAS,EAAE,KAAG,aAAWA,GAAE,SAAS,EAAE,CAAAC,GAAE,KAAKD,GAAE,KAAK,CAAC;AAAA,mBAAM;AAAC,sBAAMJ,KAAE,KAAK,IAAIG,GAAE,WAAW,GAAEC,GAAE,WAAW,CAAC,GAAEF,KAAEC,GAAE,KAAKH,EAAC,GAAEO,KAAEH,GAAE,KAAKJ,EAAC;AAAE,oBAAGE,GAAE,OAAO;AAAS,oBAAGK,GAAE,OAAO,CAAAF,GAAE,KAAKE,EAAC;AAAA,qBAAM;AAAC,wBAAMJ,KAAED,GAAE,QAAOE,KAAEG,GAAE;AAAO,sBAAIC,KAAE,YAAU,OAAOJ,MAAG,SAAOA,KAAEA,KAAEJ;AAAE,sBAAG,YAAU,OAAOG,MAAG,SAAOA,MAAG,YAAU,OAAOC,MAAG,SAAOA,IAAE;AAAC,0BAAMJ,KAAE,OAAO,KAAKG,EAAC,EAAE,CAAC;AAAE,wBAAGH,OAAI,OAAO,KAAKI,EAAC,EAAE,CAAC,GAAE;AAAC,4BAAMF,KAAE,EAAE,WAAWF,EAAC;AAAE,sBAAAE,OAAIM,KAAE,EAAC,CAACR,EAAC,GAAEE,GAAE,UAAUC,GAAEH,EAAC,GAAEI,GAAEJ,EAAC,GAAEC,EAAC,EAAC;AAAA,oBAAE;AAAA,kBAAC;AAAC,kBAAAI,GAAE,OAAOG,IAAE,EAAE,QAAQ,UAAUN,GAAE,YAAWK,GAAE,YAAWN,EAAC,CAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,kBAAM,CAAAI,GAAE,OAAO,EAAE,QAAQ,OAAOF,GAAE,KAAK,CAAC,CAAC;AAAE,qBAAOE,GAAE,KAAK;AAAA,YAAC;AAAA,YAAC,kBAAkBL,IAAEC,KAAE,OAAG;AAAC,cAAAA,KAAE,CAAC,CAACA;AAAE,oBAAMC,KAAE,IAAI,EAAE,QAAQ,KAAK,GAAG;AAAE,kBAAIC,KAAE;AAAE,qBAAKD,GAAE,QAAQ,KAAGC,MAAGH,MAAG;AAAC,sBAAMI,KAAEF,GAAE,WAAW,GAAEG,KAAEH,GAAE,SAAS;AAAE,gBAAAA,GAAE,KAAK,GAAE,aAAWG,MAAG,aAAWA,OAAIF,KAAEH,MAAG,CAACC,QAAKD,MAAGI,KAAGD,MAAGC,MAAGJ,MAAG,KAAK,IAAII,IAAEJ,KAAEG,EAAC;AAAA,cAAC;AAAC,qBAAOH;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,KAAG,EAAE,SAAQ,EAAE,aAAW,EAAE,SAAQ,EAAE,eAAa,EAAE,SAAQ,EAAE,WAAS,CAAC,GAAEC,GAAE,UAAQ,GAAED,GAAE,UAAQ,GAAEA,GAAE,QAAQ,UAAQ;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC;AAAa,cAAIC;AAAE,iBAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,SAASD,IAAE;AAAC,YAAAA,GAAE,SAAO,SAASA,IAAE;AAAC,qBAAM,YAAU,OAAOA,GAAE,SAAOA,GAAE,SAAO,YAAU,OAAOA,GAAE,SAAOA,GAAE,SAAO,YAAU,OAAOA,GAAE,UAAQ,SAAOA,GAAE,SAAO,IAAE,YAAU,OAAOA,GAAE,SAAOA,GAAE,OAAO,SAAO;AAAA,YAAC;AAAA,UAAC,EAAEE,OAAIA,KAAE,CAAC,EAAE,GAAED,GAAE,UAAQC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,iBAAO,eAAeD,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAE,gBAAME,KAAED,GAAE,IAAI;AAAE,UAAAD,GAAE,UAAQ,MAAK;AAAA,YAAC,YAAYD,IAAE;AAAC,mBAAK,MAAIA,IAAE,KAAK,QAAM,GAAE,KAAK,SAAO;AAAA,YAAC;AAAA,YAAC,UAAS;AAAC,qBAAO,KAAK,WAAW,IAAE,IAAE;AAAA,YAAC;AAAA,YAAC,KAAKA,IAAE;AAAC,cAAAA,OAAIA,KAAE,IAAE;AAAG,oBAAMC,KAAE,KAAK,IAAI,KAAK,KAAK;AAAE,kBAAGA,IAAE;AAAC,sBAAMC,KAAE,KAAK,QAAO,IAAEC,GAAE,QAAQ,OAAOF,EAAC;AAAE,oBAAGD,MAAG,IAAEE,MAAGF,KAAE,IAAEE,IAAE,KAAK,SAAO,GAAE,KAAK,SAAO,KAAG,KAAK,UAAQF,IAAE,YAAU,OAAOC,GAAE,OAAO,QAAM,EAAC,QAAOD,GAAC;AAAE;AAAC,wBAAMG,KAAE,CAAC;AAAE,yBAAOF,GAAE,eAAaE,GAAE,aAAWF,GAAE,aAAY,YAAU,OAAOA,GAAE,SAAOE,GAAE,SAAOH,KAAE,YAAU,OAAOC,GAAE,UAAQ,SAAOA,GAAE,SAAOE,GAAE,SAAOF,GAAE,SAAO,YAAU,OAAOA,GAAE,SAAOE,GAAE,SAAOF,GAAE,OAAO,OAAOC,IAAEF,EAAC,IAAEG,GAAE,SAAOF,GAAE,QAAOE;AAAA,gBAAC;AAAA,cAAC;AAAC,qBAAM,EAAC,QAAO,IAAE,EAAC;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,qBAAO,KAAK,IAAI,KAAK,KAAK;AAAA,YAAC;AAAA,YAAC,aAAY;AAAC,qBAAO,KAAK,IAAI,KAAK,KAAK,IAAEA,GAAE,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,CAAC,IAAE,KAAK,SAAO,IAAE;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,oBAAMH,KAAE,KAAK,IAAI,KAAK,KAAK;AAAE,qBAAOA,KAAE,YAAU,OAAOA,GAAE,SAAO,WAAS,YAAU,OAAOA,GAAE,UAAQ,YAAU,OAAOA,GAAE,UAAQ,SAAOA,GAAE,SAAO,WAAS,WAAS;AAAA,YAAQ;AAAA,YAAC,OAAM;AAAC,kBAAG,KAAK,QAAQ,GAAE;AAAC,oBAAG,MAAI,KAAK,OAAO,QAAO,KAAK,IAAI,MAAM,KAAK,KAAK;AAAE;AAAC,wBAAMA,KAAE,KAAK,QAAOC,KAAE,KAAK,OAAMC,KAAE,KAAK,KAAK,GAAEC,KAAE,KAAK,IAAI,MAAM,KAAK,KAAK;AAAE,yBAAO,KAAK,SAAOH,IAAE,KAAK,QAAMC,IAAE,CAACC,EAAC,EAAE,OAAOC,EAAC;AAAA,gBAAC;AAAA,cAAC;AAAC,qBAAM,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAE,SAASF,IAAEC,IAAE;AAAC,qBAAQC,KAAEF,GAAE,QAAOE,OAAK,MAAI,GAAEC,GAAE,GAAGH,GAAEE,EAAC,EAAE,CAAC,GAAED,EAAC,EAAE,QAAOC;AAAE,mBAAM;AAAA,UAAE,GAAE,IAAE,MAAM,UAAU;AAAO,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,CAAC,GAAE,KAAK,OAAK;AAAA,UAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,EAAED,IAAED,EAAC;AAAE,mBAAM,EAAEE,KAAE,MAAIA,MAAGD,GAAE,SAAO,IAAEA,GAAE,IAAI,IAAE,EAAE,KAAKA,IAAEC,IAAE,CAAC,GAAE,EAAE,KAAK,MAAK;AAAA,UAAG,GAAE,EAAE,UAAU,MAAI,SAASF,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,EAAED,IAAED,EAAC;AAAE,mBAAOE,KAAE,IAAE,SAAOD,GAAEC,EAAC,EAAE,CAAC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASF,IAAE;AAAC,mBAAO,EAAE,KAAK,UAASA,EAAC,IAAE;AAAA,UAAE,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAE,EAAED,IAAEF,EAAC;AAAE,mBAAOG,KAAE,KAAG,EAAE,KAAK,MAAKD,GAAE,KAAK,CAACF,IAAEC,EAAC,CAAC,KAAGC,GAAEC,EAAC,EAAE,CAAC,IAAEF,IAAE;AAAA,UAAI;AAAE,cAAI,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,KAAG,GAAEC,GAAE,GAAG,EAAE,GAAE,KAAK;AAAE,UAAAF,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,MAAG,GAAED,GAAE,IAAI,EAAE,GAAG,QAAO,QAAQ,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAE,OAAO,UAAU;AAAe,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,UAAU,QAAM,WAAU;AAAC,iBAAK,WAASA,KAAEA,GAAE,IAAI,IAAE,CAAC,GAAE,KAAK,OAAK;AAAA,UAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAE;AAAC,gBAAIC,KAAE,KAAK,IAAID,EAAC,KAAG,OAAO,KAAK,SAASA,EAAC;AAAE,mBAAO,KAAK,QAAMC,KAAE,IAAE,GAAEA;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASD,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,gBAAGE,IAAE;AAAC,kBAAID,KAAED,GAAED,EAAC;AAAE,qBAAM,gCAA8BE,KAAE,SAAOA;AAAA,YAAC;AAAC,mBAAO,EAAE,KAAKD,IAAED,EAAC,IAAEC,GAAED,EAAC,IAAE;AAAA,UAAM,GAAE,EAAE,UAAU,MAAI,SAASA,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,mBAAOE,KAAE,WAASF,GAAED,EAAC,IAAE,EAAE,KAAKC,IAAED,EAAC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,mBAAO,KAAK,QAAM,KAAK,IAAIF,EAAC,IAAE,IAAE,GAAEE,GAAEF,EAAC,IAAEG,MAAG,WAASF,KAAE,8BAA4BA,IAAE;AAAA,UAAI;AAAE,cAAI,IAAE,GAAE,IAAEC,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAEC,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,KAAEJ,GAAE;AAAS,oBAAO,aAAWG,KAAE,QAAOD,KAAED,QAAK,YAAUE,MAAG,YAAUA,MAAG,aAAWA,KAAE,gBAAcD,KAAE,SAAOA,MAAGE,GAAE,YAAU,OAAOH,KAAE,WAAS,MAAM,IAAEG,GAAE;AAAA,UAAG;AAAE,mBAAS,EAAEJ,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,MAAM,GAAE,EAAEC,KAAEC,MAAG;AAAC,kBAAIC,KAAEH,GAAEC,EAAC;AAAE,mBAAK,IAAIE,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,UAAU,QAAM,WAAU;AAAC,iBAAK,OAAK,GAAE,KAAK,WAAS,EAAC,MAAK,IAAI,KAAE,KAAI,KAAI,EAAE,KAAG,EAAE,MAAG,QAAO,IAAI,IAAC;AAAA,UAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAE;AAAC,gBAAIC,KAAE,EAAE,MAAKD,EAAC,EAAE,OAAOA,EAAC;AAAE,mBAAO,KAAK,QAAMC,KAAE,IAAE,GAAEA;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASD,IAAE;AAAC,mBAAO,EAAE,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,EAAE,MAAKA,EAAC,EAAE,IAAIA,EAAC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,EAAE,MAAKF,EAAC,GAAEG,KAAED,GAAE;AAAK,mBAAOA,GAAE,IAAIF,IAAEC,EAAC,GAAE,KAAK,QAAMC,GAAE,QAAMC,KAAE,IAAE,GAAE;AAAA,UAAI;AAAE,cAAI,IAAE;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE,KAAK,WAAS,IAAIE,GAAE,EAAEH,EAAC;AAAE,iBAAK,OAAKC,GAAE;AAAA,UAAI;AAAC,YAAE,UAAU,QAAM,WAAU;AAAC,iBAAK,WAAS,IAAIE,GAAE,KAAE,KAAK,OAAK;AAAA,UAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAE;AAAC,gBAAIC,KAAE,KAAK,UAASC,KAAED,GAAE,OAAOD,EAAC;AAAE,mBAAO,KAAK,OAAKC,GAAE,MAAKC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASF,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,gBAAIC,KAAE,KAAK;AAAS,gBAAGA,cAAaC,GAAE,GAAE;AAAC,kBAAIG,KAAEJ,GAAE;AAAS,kBAAG,CAAC,EAAE,KAAGI,GAAE,SAAO,IAAI,QAAOA,GAAE,KAAK,CAACN,IAAEC,EAAC,CAAC,GAAE,KAAK,OAAK,EAAEC,GAAE,MAAK;AAAK,cAAAA,KAAE,KAAK,WAAS,IAAI,EAAE,EAAEI,EAAC;AAAA,YAAC;AAAC,mBAAOJ,GAAE,IAAIF,IAAEC,EAAC,GAAE,KAAK,OAAKC,GAAE,MAAK;AAAA,UAAI;AAAE,cAAI,IAAE;AAAA,QAAC,GAAE,KAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,EAAE,EAAE;AAAO,UAAAD,GAAE,IAAEE;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,EAAE,EAAE;AAAW,UAAAD,GAAE,IAAEE;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAE,SAASF,IAAEC,IAAE;AAAC,gBAAIC,MAAG,GAAE,EAAE,GAAGF,EAAC,GAAES,KAAE,CAACP,OAAI,GAAEC,GAAE,GAAGH,EAAC,GAAE,IAAE,CAACE,MAAG,CAACO,OAAI,GAAE,EAAE,GAAGT,EAAC,GAAE,IAAE,CAACE,MAAG,CAACO,MAAG,CAAC,MAAI,GAAE,EAAE,GAAGT,EAAC,GAAE,IAAEE,MAAGO,MAAG,KAAG,GAAE,IAAE,IAAE,SAAST,IAAEC,IAAE;AAAC,uBAAQC,KAAE,IAAGC,KAAE,MAAMH,EAAC,GAAE,EAAEE,KAAEF,KAAG,CAAAG,GAAED,EAAC,IAAED,GAAEC,EAAC;AAAE,qBAAOC;AAAA,YAAC,EAAEH,GAAE,QAAO,MAAM,IAAE,CAAC,GAAE,IAAE,EAAE;AAAO,qBAAQ,KAAKA,GAAE,EAACC,MAAG,CAAC,EAAE,KAAKD,IAAE,CAAC,KAAG,MAAI,YAAU,KAAG,MAAI,YAAU,KAAG,YAAU,MAAI,MAAI,YAAU,KAAG,gBAAc,KAAG,gBAAc,OAAK,GAAE,EAAE,GAAG,GAAE,CAAC,MAAI,EAAE,KAAK,CAAC;AAAE,mBAAO;AAAA,UAAC;AAAA,QAAC,GAAE,KAAI,SAASA,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAEF,GAAE,QAAO,IAAED,GAAE,QAAO,EAAEE,KAAEC,KAAG,CAAAH,GAAE,IAAEE,EAAC,IAAED,GAAEC,EAAC;AAAE,mBAAOF;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,OAAO,UAAU;AAAe,UAAAD,GAAE,IAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,gBAAI,IAAEF,GAAEC,EAAC;AAAE,cAAE,KAAKD,IAAEC,EAAC,MAAI,GAAE,EAAE,GAAG,GAAEC,EAAC,MAAI,WAASA,MAAGD,MAAKD,QAAK,GAAEG,GAAE,GAAGH,IAAEC,IAAEC,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,2BAAaD,MAAGE,GAAE,KAAG,GAAEA,GAAE,GAAGH,IAAEC,IAAE,EAAC,cAAa,MAAG,YAAW,MAAG,OAAMC,IAAE,UAAS,KAAE,CAAC,IAAEF,GAAEC,EAAC,IAAEC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,gBAAI,IAAED,GAAED,EAAC;AAAE,oBAAO,GAAE,EAAE,GAAGA,EAAC,IAAE,KAAG,GAAEG,GAAE,GAAG,GAAED,GAAEF,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,GAAG,GAAE,IAAE,OAAO,WAAU,IAAE,EAAE,gBAAe,IAAE,EAAE,UAAS,IAAEC,GAAE,IAAEA,GAAE,EAAE,cAAY,QAAO,IAAE,OAAO,UAAU,UAAS,IAAEA,GAAE,IAAEA,GAAE,EAAE,cAAY,QAAO,IAAE,SAASH,IAAE;AAAC,mBAAO,QAAMA,KAAE,WAASA,KAAE,uBAAqB,kBAAgB,KAAG,KAAK,OAAOA,EAAC,IAAE,SAASA,IAAE;AAAC,kBAAIC,KAAE,EAAE,KAAKD,IAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,kBAAG;AAAC,gBAAAA,GAAE,CAAC,IAAE;AAAO,oBAAIG,KAAE;AAAA,cAAE,SAAOH,IAAE;AAAA,cAAC;AAAC,kBAAII,KAAE,EAAE,KAAKJ,EAAC;AAAE,qBAAOG,OAAIF,KAAED,GAAE,CAAC,IAAEE,KAAE,OAAOF,GAAE,CAAC,IAAGI;AAAA,YAAC,EAAEJ,EAAC,IAAE,SAASA,IAAE;AAAC,qBAAO,EAAE,KAAKA,EAAC;AAAA,YAAC,EAAEA,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAE;AAAC,mBAAO,SAASC,IAAE;AAAC,qBAAOD,GAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAE;AAAC,gBAAIC,KAAE,IAAID,GAAE,YAAYA,GAAE,UAAU;AAAE,mBAAO,IAAIG,GAAE,EAAEF,EAAC,EAAE,IAAI,IAAIE,GAAE,EAAEH,EAAC,CAAC,GAAEC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAE,YAAU,OAAO,WAAS,WAAS,CAAC,QAAQ,YAAU,SAAQ,IAAE,KAAG,YAAU,OAAO,UAAQ,UAAQ,CAAC,OAAO,YAAU,QAAO,IAAE,KAAG,EAAE,YAAU,IAAEC,GAAE,EAAE,SAAO,QAAO,IAAE,IAAE,EAAE,cAAY;AAAO,UAAAF,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,gBAAGA,GAAE,QAAOD,GAAE,MAAM;AAAE,gBAAIE,KAAEF,GAAE,QAAOG,KAAE,IAAE,EAAED,EAAC,IAAE,IAAIF,GAAE,YAAYE,EAAC;AAAE,mBAAOF,GAAE,KAAKG,EAAC,GAAEA;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,gBAAIC,KAAED,MAAG,GAAEE,GAAE,GAAGH,GAAE,MAAM,IAAEA,GAAE;AAAO,mBAAO,IAAIA,GAAE,YAAYE,IAAEF,GAAE,YAAWA,GAAE,MAAM;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAEH,GAAE;AAAO,iBAAIC,OAAIA,KAAE,MAAME,EAAC,IAAG,EAAED,KAAEC,KAAG,CAAAF,GAAEC,EAAC,IAAEF,GAAEE,EAAC;AAAE,mBAAOD;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAEC,IAAEC,IAAE,GAAE;AAAC,gBAAI,IAAE,CAACA;AAAE,YAAAA,OAAIA,KAAE,CAAC;AAAG,qBAAQ,IAAE,IAAG,IAAED,GAAE,QAAO,EAAE,IAAE,KAAG;AAAC,kBAAI,IAAEA,GAAE,CAAC,GAAE,IAAE,IAAE,EAAEC,GAAE,CAAC,GAAEF,GAAE,CAAC,GAAE,GAAEE,IAAEF,EAAC,IAAE;AAAO,yBAAS,MAAI,IAAEA,GAAE,CAAC,IAAG,KAAG,GAAE,EAAE,GAAGE,IAAE,GAAE,CAAC,KAAG,GAAEC,GAAE,GAAGD,IAAE,GAAE,CAAC;AAAA,YAAC;AAAC,mBAAOA;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAE,WAAU;AAAC,gBAAG;AAAC,kBAAIF,MAAG,GAAEG,GAAE,GAAG,QAAO,gBAAgB;AAAE,qBAAOH,GAAE,CAAC,GAAE,IAAG,CAAC,CAAC,GAAEA;AAAA,YAAC,SAAOA,IAAE;AAAA,YAAC;AAAA,UAAC,EAAE;AAAE,UAAAC,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAE,YAAU,OAAO,UAAQ,UAAQ,OAAO,WAAS,UAAQ;AAAO,UAAAD,GAAE,IAAEC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAE;AAAC,oBAAO,GAAEG,GAAE,GAAGH,IAAE,EAAE,GAAE,EAAE,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,IAAE,IAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,EAAE,EAAE,oBAAoB,GAAE,KAAGC,KAAE,SAAS,KAAK,KAAG,EAAE,QAAM,EAAE,KAAK,YAAU,EAAE,KAAG,mBAAiBA,KAAE,IAAG,IAAED,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,+BAA8B,IAAE,SAAS,WAAU,IAAE,OAAO,WAAU,IAAE,EAAE,UAAS,IAAE,EAAE,gBAAe,IAAE,OAAO,MAAI,EAAE,KAAK,CAAC,EAAE,QAAQ,uBAAsB,MAAM,EAAE,QAAQ,0DAAyD,OAAO,IAAE,GAAG,GAAE,IAAE,SAASF,IAAE;AAAC,mBAAM,EAAE,EAAE,GAAE,EAAE,GAAGA,EAAC,MAAIC,KAAED,IAAE,KAAG,KAAKC,UAAO,GAAE,EAAE,GAAGD,EAAC,IAAE,IAAE,GAAG,MAAM,GAAE,EAAE,GAAGA,EAAC,CAAC;AAAE,gBAAIC;AAAA,UAAC,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,gBAAIC,KAAE,SAASF,IAAEC,IAAE;AAAC,qBAAO,QAAMD,KAAE,SAAOA,GAAEC,EAAC;AAAA,YAAC,EAAED,IAAEC,EAAC;AAAE,mBAAO,EAAEC,EAAC,IAAEA,KAAE;AAAA,UAAM;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,MAAG,GAAED,GAAE,IAAI,EAAE,GAAG,OAAO,gBAAe,MAAM;AAAE,UAAAD,GAAE,IAAEE;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAE,OAAO,UAAU,sBAAqB,IAAE,OAAO,uBAAsB,IAAE,IAAE,SAASF,IAAE;AAAC,mBAAO,QAAMA,KAAE,CAAC,KAAGA,KAAE,OAAOA,EAAC,GAAE,SAASA,IAAEC,IAAE;AAAC,uBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAOI,KAAE,GAAEC,KAAE,CAAC,GAAE,EAAEH,KAAEC,MAAG;AAAC,oBAAIG,KAAEN,GAAEE,EAAC;AAAE,gBAAAD,GAAEK,IAAEJ,IAAEF,EAAC,MAAIK,GAAED,IAAG,IAAEE;AAAA,cAAE;AAAC,qBAAOD;AAAA,YAAC,EAAE,EAAEL,EAAC,GAAG,SAASC,IAAE;AAAC,qBAAO,EAAE,KAAKD,IAAEC,EAAC;AAAA,YAAC,CAAE;AAAA,UAAE,IAAEE,GAAE;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,KAAG,GAAEC,GAAE,GAAG,EAAE,GAAE,UAAU,GAAE,IAAED,GAAE,IAAI,GAAE,KAAG,GAAEC,GAAE,GAAG,EAAE,GAAE,SAAS,GAAE,KAAG,GAAEA,GAAE,GAAG,EAAE,GAAE,KAAK,GAAE,KAAG,GAAEA,GAAE,GAAG,EAAE,GAAE,SAAS,GAAE,IAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,gBAAe,IAAE,oBAAmB,IAAE,gBAAe,IAAE,oBAAmB,IAAE,qBAAoB,KAAG,GAAE,EAAE,GAAG,CAAC,GAAE,KAAG,GAAE,EAAE,GAAG,EAAE,CAAC,GAAE,KAAG,GAAE,EAAE,GAAG,CAAC,GAAE,KAAG,GAAE,EAAE,GAAG,CAAC,GAAE,KAAG,GAAE,EAAE,GAAG,CAAC,GAAE,IAAE,EAAE;AAAE,WAAC,KAAG,EAAE,IAAI,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,KAAG,KAAG,EAAE,KAAG,EAAE,IAAI,EAAE,GAAC,KAAG,KAAG,KAAG,EAAE,EAAE,QAAQ,CAAC,KAAG,KAAG,KAAG,EAAE,IAAI,GAAC,KAAG,KAAG,KAAG,EAAE,IAAI,GAAC,KAAG,OAAK,IAAE,SAASF,IAAE;AAAC,gBAAIC,MAAG,GAAE,EAAE,GAAGD,EAAC,GAAEE,KAAE,qBAAmBD,KAAED,GAAE,cAAY,QAAOG,KAAED,MAAG,GAAE,EAAE,GAAGA,EAAC,IAAE;AAAG,gBAAGC,GAAE,SAAOA,IAAE;AAAA,cAAC,KAAK;AAAE,uBAAO;AAAA,cAAE,KAAK;AAAE,uBAAO;AAAA,cAAE,KAAK;AAAE,uBAAO;AAAA,cAAE,KAAK;AAAE,uBAAO;AAAA,cAAE,KAAK;AAAE,uBAAO;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC;AAAG,cAAI,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,GAAG,GAAE,IAAE,OAAO,QAAO,IAAE,2BAAU;AAAC,qBAASF,KAAG;AAAA,YAAC;AAAC,mBAAO,SAASC,IAAE;AAAC,kBAAG,EAAE,GAAEE,GAAE,GAAGF,EAAC,EAAE,QAAM,CAAC;AAAE,kBAAG,EAAE,QAAO,EAAEA,EAAC;AAAE,cAAAD,GAAE,YAAUC;AAAE,kBAAIC,KAAE,IAAIF;AAAE,qBAAOA,GAAE,YAAU,QAAOE;AAAA,YAAC;AAAA,UAAC,EAAE,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAE,SAASF,IAAE;AAAC,mBAAM,cAAY,OAAOA,GAAE,gBAAc,GAAE,EAAE,GAAGA,EAAC,IAAE,CAAC,IAAE,GAAG,GAAE,EAAE,GAAGA,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAE;AAAmB,UAAAD,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,gBAAIE,KAAE,OAAOH;AAAE,mBAAM,CAAC,EAAEC,KAAE,QAAMA,KAAE,mBAAiBA,QAAK,YAAUE,MAAG,YAAUA,MAAGD,GAAE,KAAKF,EAAC,MAAIA,KAAE,MAAIA,KAAE,KAAG,KAAGA,KAAEC;AAAA,UAAC;AAAA,QAAC,GAAE,KAAI,SAASD,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAE,OAAO;AAAU,UAAAD,GAAE,IAAE,SAASD,IAAE;AAAC,gBAAIC,KAAED,MAAGA,GAAE;AAAY,mBAAOA,QAAK,cAAY,OAAOC,MAAGA,GAAE,aAAWC;AAAA,UAAE;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAE,YAAU,OAAO,WAAS,WAAS,CAAC,QAAQ,YAAU,SAAQ,IAAE,KAAG,YAAU,OAAO,UAAQ,UAAQ,CAAC,OAAO,YAAU,QAAO,IAAE,KAAG,EAAE,YAAU,KAAGC,GAAE,EAAE,SAAQ,IAAE,WAAU;AAAC,gBAAG;AAAC,qBAAO,KAAG,EAAE,WAAS,EAAE,QAAQ,MAAM,EAAE,SAAO,KAAG,EAAE,WAAS,EAAE,QAAQ,MAAM;AAAA,YAAC,SAAOH,IAAE;AAAA,YAAC;AAAA,UAAC,EAAE;AAAE,UAAAC,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,mBAAO,SAASC,IAAE;AAAC,qBAAOF,GAAEC,GAAEC,EAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAE,YAAU,OAAO,QAAM,QAAM,KAAK,WAAS,UAAQ,MAAK,IAAEC,GAAE,KAAG,KAAG,SAAS,aAAa,EAAE;AAAE,UAAAF,GAAE,IAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAE,SAAS,UAAU;AAAS,UAAAD,GAAE,IAAE,SAASD,IAAE;AAAC,gBAAG,QAAMA,IAAE;AAAC,kBAAG;AAAC,uBAAOE,GAAE,KAAKF,EAAC;AAAA,cAAC,SAAOA,IAAE;AAAA,cAAC;AAAC,kBAAG;AAAC,uBAAOA,KAAE;AAAA,cAAE,SAAOA,IAAE;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAM;AAAA,UAAE;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,OAAO,wBAAsB,SAASF,IAAE;AAAC,qBAAQC,KAAE,CAAC,GAAED,KAAG,EAAC,GAAE,EAAE,GAAGC,KAAG,GAAE,EAAE,GAAGD,EAAC,CAAC,GAAEA,MAAG,GAAE,EAAE,GAAGA,EAAC;AAAE,mBAAOC;AAAA,UAAC,IAAE,EAAE,GAAE,IAAEC,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,IAAE,EAAE,GAAE,CAAC;AAAA,UAAC,GAAE,IAAEE,GAAE,IAAI,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAEA,GAAE,IAAI,GAAE,IAAE,QAAO,IAAEA,GAAE,GAAG,GAAE,IAAE,EAAE,IAAE,EAAE,EAAE,YAAU,QAAO,IAAE,IAAE,EAAE,UAAQ,QAAO,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,IAAEC,IAAEC,IAAEC,KAAEN,GAAE;AAAY,oBAAOC,IAAE;AAAA,cAAC,KAAI;AAAuB,wBAAO,GAAE,EAAE,GAAGD,EAAC;AAAA,cAAE,KAAI;AAAA,cAAmB,KAAI;AAAgB,uBAAO,IAAIM,GAAE,CAACN,EAAC;AAAA,cAAE,KAAI;AAAoB,uBAAO,SAASA,IAAEC,IAAE;AAAC,sBAAIC,KAAED,MAAG,GAAE,EAAE,GAAGD,GAAE,MAAM,IAAEA,GAAE;AAAO,yBAAO,IAAIA,GAAE,YAAYE,IAAEF,GAAE,YAAWA,GAAE,UAAU;AAAA,gBAAC,EAAEA,IAAEE,EAAC;AAAA,cAAE,KAAI;AAAA,cAAwB,KAAI;AAAA,cAAwB,KAAI;AAAA,cAAqB,KAAI;AAAA,cAAsB,KAAI;AAAA,cAAsB,KAAI;AAAA,cAAsB,KAAI;AAAA,cAA6B,KAAI;AAAA,cAAuB,KAAI;AAAuB,wBAAO,GAAE,EAAE,GAAGF,IAAEE,EAAC;AAAA,cAAE,KAAI;AAAA,cAAe,KAAI;AAAe,uBAAO,IAAII;AAAA,cAAE,KAAI;AAAA,cAAkB,KAAI;AAAkB,uBAAO,IAAIA,GAAEN,EAAC;AAAA,cAAE,KAAI;AAAkB,wBAAOK,KAAE,KAAID,KAAEJ,IAAG,YAAYI,GAAE,QAAO,EAAE,KAAKA,EAAC,CAAC,GAAG,YAAUA,GAAE,WAAUC;AAAA,cAAE,KAAI;AAAkB,uBAAOF,KAAEH,IAAE,IAAE,OAAO,EAAE,KAAKG,EAAC,CAAC,IAAE,CAAC;AAAA,YAAC;AAAA,UAAC,GAAE,IAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,EAAE,KAAG,EAAE,EAAE,OAAM,IAAE,KAAG,GAAE,EAAE,GAAG,CAAC,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,KAAG,mBAAiB,GAAE,EAAE,GAAGA,EAAC;AAAA,UAAC,GAAE,IAAEE,GAAE,GAAG,GAAE,IAAE,EAAE,KAAG,EAAE,EAAE,OAAM,IAAE,KAAG,GAAE,EAAE,GAAG,CAAC,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,KAAG,mBAAiB,GAAE,EAAE,GAAGA,EAAC;AAAA,UAAC,GAAE,IAAE,sBAAqB,IAAE,qBAAoB,IAAE,mBAAkB,IAAE,CAAC;AAAE,YAAE,CAAC,IAAE,EAAE,gBAAgB,IAAE,EAAE,sBAAsB,IAAE,EAAE,mBAAmB,IAAE,EAAE,kBAAkB,IAAE,EAAE,eAAe,IAAE,EAAE,uBAAuB,IAAE,EAAE,uBAAuB,IAAE,EAAE,oBAAoB,IAAE,EAAE,qBAAqB,IAAE,EAAE,qBAAqB,IAAE,EAAE,cAAc,IAAE,EAAE,iBAAiB,IAAE,EAAE,CAAC,IAAE,EAAE,iBAAiB,IAAE,EAAE,cAAc,IAAE,EAAE,iBAAiB,IAAE,EAAE,iBAAiB,IAAE,EAAE,qBAAqB,IAAE,EAAE,4BAA4B,IAAE,EAAE,sBAAsB,IAAE,EAAE,sBAAsB,IAAE,MAAG,EAAE,gBAAgB,IAAE,EAAE,CAAC,IAAE,EAAE,kBAAkB,IAAE;AAAG,cAAI,IAAE,SAASA,GAAEC,IAAEC,IAAES,IAAEC,IAAEC,IAAEG,IAAE;AAAC,gBAAIG,IAAEC,KAAE,IAAElB,IAAEoB,KAAE,IAAEpB,IAAEsB,KAAE,IAAEtB;AAAE,gBAAGS,OAAIQ,KAAEN,KAAEF,GAAEV,IAAEW,IAAEC,IAAEG,EAAC,IAAEL,GAAEV,EAAC,IAAG,WAASkB,GAAE,QAAOA;AAAE,gBAAG,EAAE,GAAE,EAAE,GAAGlB,EAAC,EAAE,QAAOA;AAAE,gBAAIsB,MAAG,GAAE,EAAE,GAAGtB,EAAC;AAAE,gBAAGsB,IAAE;AAAC,kBAAGJ,KAAE,SAASnB,IAAE;AAAC,oBAAIC,KAAED,GAAE,QAAOE,KAAE,IAAIF,GAAE,YAAYC,EAAC;AAAE,uBAAOA,MAAG,YAAU,OAAOD,GAAE,CAAC,KAAG,EAAE,KAAKA,IAAE,OAAO,MAAIE,GAAE,QAAMF,GAAE,OAAME,GAAE,QAAMF,GAAE,QAAOE;AAAA,cAAC,EAAED,EAAC,GAAE,CAACmB,GAAE,SAAO,GAAE,EAAE,GAAGnB,IAAEkB,EAAC;AAAA,YAAC,OAAK;AAAC,kBAAIY,MAAG,GAAE,EAAE,GAAG9B,EAAC,GAAEiC,KAAEH,MAAG,KAAG,gCAA8BA;AAAE,mBAAI,GAAE,EAAE,GAAG9B,EAAC,EAAE,SAAO,GAAE,EAAE,GAAGA,IAAEmB,EAAC;AAAE,kBAAGW,MAAG,KAAGA,MAAG,KAAGG,MAAG,CAACrB,IAAE;AAAC,oBAAGM,KAAEG,MAAGY,KAAE,CAAC,KAAG,GAAE,EAAE,GAAGjC,EAAC,GAAE,CAACmB,GAAE,QAAOE,KAAE,SAAStB,IAAEC,IAAE;AAAC,0BAAO,GAAE,EAAE,GAAGD,IAAE,EAAEA,EAAC,GAAEC,EAAC;AAAA,gBAAC,EAAEA,IAAE,SAASD,IAAEC,IAAE;AAAC,yBAAOD,OAAI,GAAE,EAAE,GAAGC,KAAG,GAAE,EAAE,GAAGA,EAAC,GAAED,EAAC;AAAA,gBAAC,EAAEmB,IAAElB,EAAC,CAAC,IAAE,SAASD,IAAEC,IAAE;AAAC,0BAAO,GAAE,EAAE,GAAGD,KAAG,GAAE,EAAE,GAAGA,EAAC,GAAEC,EAAC;AAAA,gBAAC,EAAEA,IAAE,SAASD,IAAEC,IAAE;AAAC,yBAAOD,OAAI,GAAE,EAAE,GAAGC,KAAG,GAAE,EAAE,GAAGA,EAAC,GAAED,EAAC;AAAA,gBAAC,EAAEmB,IAAElB,EAAC,CAAC;AAAA,cAAC,OAAK;AAAC,oBAAG,CAAC,EAAE8B,EAAC,EAAE,QAAOlB,KAAEZ,KAAE,CAAC;AAAE,gBAAAkB,KAAE,EAAElB,IAAE8B,IAAEX,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,YAAAJ,OAAIA,KAAE,IAAIb,GAAE;AAAG,gBAAIsB,KAAET,GAAE,IAAIf,EAAC;AAAE,gBAAGwB,GAAE,QAAOA;AAAE,YAAAT,GAAE,IAAIf,IAAEkB,EAAC,GAAE,EAAElB,EAAC,IAAEA,GAAE,QAAS,SAASE,IAAE;AAAC,cAAAgB,GAAE,IAAInB,GAAEG,IAAED,IAAES,IAAER,IAAEF,IAAEe,EAAC,CAAC;AAAA,YAAC,CAAE,IAAE,EAAEf,EAAC,KAAGA,GAAE,QAAS,SAASE,IAAEC,IAAE;AAAC,cAAAe,GAAE,IAAIf,IAAEJ,GAAEG,IAAED,IAAES,IAAEP,IAAEH,IAAEe,EAAC,CAAC;AAAA,YAAC,CAAE;AAAE,gBAAIU,KAAEF,KAAEF,KAAE,IAAE,EAAE,IAAEA,KAAE,EAAE,IAAE,EAAE,GAAEa,KAAEZ,KAAE,SAAOG,GAAEzB,EAAC;AAAE,mBAAO,SAASD,IAAEC,IAAE;AAAC,uBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAO,EAAEE,KAAEC,MAAG,UAAKF,GAAED,GAAEE,EAAC,GAAEA,IAAEF,EAAC,IAAG;AAAA,YAAC,EAAEmC,MAAGlC,IAAG,SAASE,IAAEE,IAAE;AAAC,cAAA8B,OAAIhC,KAAEF,GAAEI,KAAEF,EAAC,KAAI,GAAE,EAAE,GAAGgB,IAAEd,IAAEL,GAAEG,IAAED,IAAES,IAAEN,IAAEJ,IAAEe,EAAC,CAAC;AAAA,YAAC,CAAE,GAAEG;AAAA,UAAC,GAAE,IAAE,SAASnB,IAAE;AAAC,mBAAO,EAAEA,IAAE,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,mBAAOD,OAAIC,MAAGD,MAAGA,MAAGC,MAAGA;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,KAAG,yBAAuB,GAAEG,GAAE,GAAGH,EAAC;AAAA,UAAC,GAAE,IAAE,OAAO,WAAU,IAAE,EAAE,gBAAe,IAAE,EAAE,sBAAqB,IAAE,EAAE,2BAAU;AAAC,mBAAO;AAAA,UAAS,EAAE,CAAC,IAAE,IAAE,SAASA,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,KAAG,EAAE,KAAKA,IAAE,QAAQ,KAAG,CAAC,EAAE,KAAKA,IAAE,QAAQ;AAAA,UAAC,GAAE,IAAE;AAAA,QAAC,GAAE,KAAI,SAASA,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAE,MAAM;AAAQ,UAAAD,GAAE,IAAEC;AAAA,QAAC,GAAE,MAAK,SAASF,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAE;AAAC,mBAAO,QAAMA,OAAI,GAAE,EAAE,GAAGA,GAAE,MAAM,KAAG,EAAE,GAAEG,GAAE,GAAGH,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAE,YAAU,OAAO,WAAS,WAAS,CAAC,QAAQ,YAAU,SAAQ,IAAE,KAAG,YAAU,OAAO,UAAQ,UAAQ,CAAC,OAAO,YAAU,QAAO,IAAE,KAAG,EAAE,YAAU,IAAEC,GAAE,EAAE,SAAO,QAAO,KAAG,IAAE,EAAE,WAAS,WAAS,WAAU;AAAC,mBAAM;AAAA,UAAE;AAAA,QAAC,GAAE,MAAK,SAASH,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI;AAAE,mBAAS,EAAEF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,QAAMF,KAAE,IAAEA,GAAE;AAAO,iBAAI,KAAK,WAAS,IAAI,EAAE,KAAE,EAAEC,KAAEC,KAAG,MAAK,IAAIF,GAAEC,EAAC,CAAC;AAAA,UAAC;AAAC,YAAE,UAAU,MAAI,EAAE,UAAU,OAAK,SAASD,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,IAAE,2BAA2B,GAAE;AAAA,UAAI,GAAE,EAAE,UAAU,MAAI,SAASA,IAAE;AAAC,mBAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,UAAC;AAAE,cAAI,IAAE,GAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAE,QAAMH,KAAE,IAAEA,GAAE,QAAO,EAAEE,KAAEC,KAAG,KAAGF,GAAED,GAAEE,EAAC,GAAEA,IAAEF,EAAC,EAAE,QAAM;AAAG,mBAAM;AAAA,UAAE,GAAE,IAAE,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIG,KAAE,IAAEN,IAAEO,KAAET,GAAE,QAAOU,KAAET,GAAE;AAAO,gBAAGQ,MAAGC,MAAG,EAAEF,MAAGE,KAAED,IAAG,QAAM;AAAG,gBAAIE,KAAEN,GAAE,IAAIL,EAAC,GAAEY,KAAEP,GAAE,IAAIJ,EAAC;AAAE,gBAAGU,MAAGC,GAAE,QAAOD,MAAGV,MAAGW,MAAGZ;AAAE,gBAAIa,KAAE,IAAGC,KAAE,MAAGC,KAAE,IAAEb,KAAE,IAAI,MAAE;AAAO,iBAAIG,GAAE,IAAIL,IAAEC,EAAC,GAAEI,GAAE,IAAIJ,IAAED,EAAC,GAAE,EAAEa,KAAEJ,MAAG;AAAC,kBAAIO,KAAEhB,GAAEa,EAAC,GAAEI,KAAEhB,GAAEY,EAAC;AAAE,kBAAGV,GAAE,KAAIe,KAAEV,KAAEL,GAAEc,IAAED,IAAEH,IAAEZ,IAAED,IAAEK,EAAC,IAAEF,GAAEa,IAAEC,IAAEJ,IAAEb,IAAEC,IAAEI,EAAC;AAAE,kBAAG,WAASa,IAAE;AAAC,oBAAGA,GAAE;AAAS,gBAAAJ,KAAE;AAAG;AAAA,cAAK;AAAC,kBAAGC,IAAE;AAAC,oBAAG,CAAC,EAAEd,IAAG,SAASD,IAAEC,IAAE;AAAC,sBAAGK,KAAEL,IAAE,CAACc,GAAE,IAAIT,EAAC,MAAIU,OAAIhB,MAAGI,GAAEY,IAAEhB,IAAEE,IAAEC,IAAEE,EAAC,GAAG,QAAOU,GAAE,KAAKd,EAAC;AAAE,sBAAIK;AAAA,gBAAC,CAAE,GAAE;AAAC,kBAAAQ,KAAE;AAAG;AAAA,gBAAK;AAAA,cAAC,WAASE,OAAIC,MAAG,CAACb,GAAEY,IAAEC,IAAEf,IAAEC,IAAEE,EAAC,GAAE;AAAC,gBAAAS,KAAE;AAAG;AAAA,cAAK;AAAA,YAAC;AAAC,mBAAOT,GAAE,OAAOL,EAAC,GAAEK,GAAE,OAAOJ,EAAC,GAAEa;AAAA,UAAC,GAAE,IAAEZ,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,mBAAOA,GAAE,QAAS,SAASA,IAAEG,IAAE;AAAC,cAAAD,GAAE,EAAED,EAAC,IAAE,CAACE,IAAEH,EAAC;AAAA,YAAC,CAAE,GAAEE;AAAA,UAAC,GAAE,IAAE,SAASF,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAE,MAAMF,GAAE,IAAI;AAAE,mBAAOA,GAAE,QAAS,SAASA,IAAE;AAAC,cAAAE,GAAE,EAAED,EAAC,IAAED;AAAA,YAAC,CAAE,GAAEE;AAAA,UAAC,GAAE,IAAE,EAAE,IAAE,EAAE,EAAE,YAAU,QAAO,IAAE,IAAE,EAAE,UAAQ,QAAO,IAAEA,GAAE,IAAI,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,sBAAqB,IAAE,kBAAiB,IAAE,mBAAkB,IAAE,OAAO,UAAU,gBAAe,IAAE,SAASF,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,MAAG,GAAE,EAAE,GAAGP,EAAC,GAAES,MAAG,GAAE,EAAE,GAAGR,EAAC,GAAEa,KAAEP,KAAE,KAAG,GAAE,EAAE,GAAGP,EAAC,GAAEoC,KAAE3B,KAAE,KAAG,GAAE,EAAE,GAAGR,EAAC,GAAEoC,MAAGvB,KAAEA,MAAG,IAAE,IAAEA,OAAI,GAAEkB,MAAGI,KAAEA,MAAG,IAAE,IAAEA,OAAI,GAAEH,KAAEnB,MAAGsB;AAAE,gBAAGH,OAAI,GAAE,EAAE,GAAGjC,EAAC,GAAE;AAAC,kBAAG,EAAE,GAAE,EAAE,GAAGC,EAAC,EAAE,QAAM;AAAG,cAAAM,KAAE,MAAG8B,KAAE;AAAA,YAAE;AAAC,gBAAGJ,MAAG,CAACI,GAAE,QAAO/B,OAAIA,KAAE,IAAIH,GAAE,MAAGI,OAAI,GAAE,EAAE,GAAGP,EAAC,IAAE,EAAEA,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,EAAC,IAAE,SAASN,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,sBAAOJ,IAAE;AAAA,gBAAC,KAAI;AAAoB,sBAAGF,GAAE,cAAYC,GAAE,cAAYD,GAAE,cAAYC,GAAE,WAAW,QAAM;AAAG,kBAAAD,KAAEA,GAAE,QAAOC,KAAEA,GAAE;AAAA,gBAAO,KAAI;AAAuB,yBAAM,EAAED,GAAE,cAAYC,GAAE,cAAY,CAACI,GAAE,IAAI,EAAE,EAAEL,EAAC,GAAE,IAAI,EAAE,EAAEC,EAAC,CAAC;AAAA,gBAAG,KAAI;AAAA,gBAAmB,KAAI;AAAA,gBAAgB,KAAI;AAAkB,0BAAO,GAAE,EAAE,GAAG,CAACD,IAAE,CAACC,EAAC;AAAA,gBAAE,KAAI;AAAiB,yBAAOD,GAAE,QAAMC,GAAE,QAAMD,GAAE,WAASC,GAAE;AAAA,gBAAQ,KAAI;AAAA,gBAAkB,KAAI;AAAkB,yBAAOD,MAAGC,KAAE;AAAA,gBAAG,KAAI;AAAe,sBAAIM,KAAE;AAAA,gBAAE,KAAI;AAAe,sBAAIE,KAAE,IAAEN;AAAE,sBAAGI,OAAIA,KAAE,IAAGP,GAAE,QAAMC,GAAE,QAAM,CAACQ,GAAE,QAAM;AAAG,sBAAIK,KAAER,GAAE,IAAIN,EAAC;AAAE,sBAAGc,GAAE,QAAOA,MAAGb;AAAE,kBAAAE,MAAG,GAAEG,GAAE,IAAIN,IAAEC,EAAC;AAAE,sBAAIe,KAAE,EAAET,GAAEP,EAAC,GAAEO,GAAEN,EAAC,GAAEE,IAAEC,IAAEC,IAAEC,EAAC;AAAE,yBAAOA,GAAE,OAAON,EAAC,GAAEgB;AAAA,gBAAE,KAAI;AAAkB,sBAAG,EAAE,QAAO,EAAE,KAAKhB,EAAC,KAAG,EAAE,KAAKC,EAAC;AAAA,cAAC;AAAC,qBAAM;AAAA,YAAE,EAAED,IAAEC,IAAEa,IAAEZ,IAAEE,IAAEC,IAAEC,EAAC;AAAE,gBAAG,EAAE,IAAEJ,KAAG;AAAC,kBAAI,IAAEmC,MAAG,EAAE,KAAKrC,IAAE,aAAa,GAAE,IAAEgC,MAAG,EAAE,KAAK/B,IAAE,aAAa;AAAE,kBAAG,KAAG,GAAE;AAAC,oBAAI,IAAE,IAAED,GAAE,MAAM,IAAEA,IAAE,IAAE,IAAEC,GAAE,MAAM,IAAEA;AAAE,uBAAOK,OAAIA,KAAE,IAAIH,GAAE,MAAGE,GAAE,GAAE,GAAEH,IAAEE,IAAEE,EAAC;AAAA,cAAC;AAAA,YAAC;AAAC,mBAAM,CAAC,CAAC2B,OAAI3B,OAAIA,KAAE,IAAIH,GAAE,MAAG,SAASH,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,kBAAIC,KAAE,IAAEJ,IAAEK,MAAG,GAAE,EAAE,GAAGP,EAAC,GAAEQ,KAAED,GAAE;AAAO,kBAAGC,OAAI,GAAE,EAAE,GAAGP,EAAC,EAAE,UAAQ,CAACK,GAAE,QAAM;AAAG,uBAAQG,KAAED,IAAEC,QAAK;AAAC,oBAAIC,KAAEH,GAAEE,EAAC;AAAE,oBAAG,EAAEH,KAAEI,MAAKT,KAAE,EAAE,KAAKA,IAAES,EAAC,GAAG,QAAM;AAAA,cAAE;AAAC,kBAAIC,KAAEN,GAAE,IAAIL,EAAC,GAAEY,KAAEP,GAAE,IAAIJ,EAAC;AAAE,kBAAGU,MAAGC,GAAE,QAAOD,MAAGV,MAAGW,MAAGZ;AAAE,kBAAIa,KAAE;AAAG,cAAAR,GAAE,IAAIL,IAAEC,EAAC,GAAEI,GAAE,IAAIJ,IAAED,EAAC;AAAE,uBAAQc,KAAER,IAAE,EAAEG,KAAED,MAAG;AAAC,oBAAIO,KAAEf,GAAEU,KAAEH,GAAEE,EAAC,CAAC,GAAES,KAAEjB,GAAES,EAAC;AAAE,oBAAGP,GAAE,KAAIkB,KAAEf,KAAEH,GAAEe,IAAEH,IAAEL,IAAET,IAAED,IAAEK,EAAC,IAAEF,GAAEY,IAAEG,IAAER,IAAEV,IAAEC,IAAEI,EAAC;AAAE,oBAAG,EAAE,WAASgB,KAAEN,OAAIG,MAAGd,GAAEW,IAAEG,IAAEhB,IAAEC,IAAEE,EAAC,IAAEgB,KAAG;AAAC,kBAAAR,KAAE;AAAG;AAAA,gBAAK;AAAC,gBAAAC,OAAIA,KAAE,iBAAeJ;AAAA,cAAE;AAAC,kBAAGG,MAAG,CAACC,IAAE;AAAC,oBAAIK,KAAEnB,GAAE,aAAYoB,KAAEnB,GAAE;AAAY,gBAAAkB,MAAGC,MAAG,EAAE,iBAAgBpB,OAAI,EAAE,iBAAgBC,OAAI,cAAY,OAAOkB,MAAGA,cAAaA,MAAG,cAAY,OAAOC,MAAGA,cAAaA,OAAIP,KAAE;AAAA,cAAG;AAAC,qBAAOR,GAAE,OAAOL,EAAC,GAAEK,GAAE,OAAOJ,EAAC,GAAEY;AAAA,YAAC,EAAEb,IAAEC,IAAEC,IAAEE,IAAEC,IAAEC,EAAC;AAAA,UAAE,GAAE,IAAEJ,GAAE,IAAI,GAAE,IAAE,SAASF,GAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAOJ,OAAIC,OAAI,QAAMD,MAAG,QAAMC,MAAG,EAAE,GAAE,EAAE,GAAGD,EAAC,KAAG,EAAE,GAAE,EAAE,GAAGC,EAAC,IAAED,MAAGA,MAAGC,MAAGA,KAAE,EAAED,IAAEC,IAAEC,IAAEC,IAAEJ,IAAEK,EAAC;AAAA,UAAE,GAAE,IAAE,SAASL,IAAEC,IAAE;AAAC,mBAAO,EAAED,IAAEC,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,cAAIC,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG;AAAE,UAAAD,GAAE,IAAE,SAASD,IAAE;AAAC,gBAAG,EAAE,GAAE,EAAE,GAAGA,EAAC,EAAE,QAAM;AAAG,gBAAIC,MAAG,GAAEE,GAAE,GAAGH,EAAC;AAAE,mBAAM,uBAAqBC,MAAG,gCAA8BA,MAAG,4BAA0BA,MAAG,oBAAkBA;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAE;AAAC,mBAAM,YAAU,OAAOA,MAAGA,KAAE,MAAIA,KAAE,KAAG,KAAGA,MAAG;AAAA,UAAgB;AAAA,QAAC,GAAE,KAAI,SAASA,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAE;AAAC,gBAAIC,KAAE,OAAOD;AAAE,mBAAO,QAAMA,OAAI,YAAUC,MAAG,cAAYA;AAAA,UAAE;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,SAASD,IAAE;AAAC,mBAAO,QAAMA,MAAG,YAAU,OAAOA;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,CAAC;AAAE,YAAE,uBAAuB,IAAE,EAAE,uBAAuB,IAAE,EAAE,oBAAoB,IAAE,EAAE,qBAAqB,IAAE,EAAE,qBAAqB,IAAE,EAAE,qBAAqB,IAAE,EAAE,4BAA4B,IAAE,EAAE,sBAAsB,IAAE,EAAE,sBAAsB,IAAE,MAAG,EAAE,oBAAoB,IAAE,EAAE,gBAAgB,IAAE,EAAE,sBAAsB,IAAE,EAAE,kBAAkB,IAAE,EAAE,mBAAmB,IAAE,EAAE,eAAe,IAAE,EAAE,gBAAgB,IAAE,EAAE,mBAAmB,IAAE,EAAE,cAAc,IAAE,EAAE,iBAAiB,IAAE,EAAE,iBAAiB,IAAE,EAAE,iBAAiB,IAAE,EAAE,cAAc,IAAE,EAAE,iBAAiB,IAAE,EAAE,kBAAkB,IAAE;AAAG,cAAI,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,EAAE,KAAG,EAAE,EAAE,cAAa,IAAE,KAAG,GAAE,EAAE,GAAG,CAAC,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,MAAI,GAAE,EAAE,GAAGA,GAAE,MAAM,KAAG,CAAC,CAAC,GAAG,GAAEG,GAAE,GAAGH,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,KAAG,GAAEA,GAAE,IAAI,EAAE,GAAG,OAAO,MAAK,MAAM,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,KAAG,GAAEG,GAAE,GAAGH,EAAC,IAAE,SAASA,IAAE;AAAC,kBAAG,EAAE,GAAE,EAAE,GAAGA,EAAC,EAAE,QAAO,EAAEA,EAAC;AAAE,kBAAIC,KAAE,CAAC;AAAE,uBAAQC,MAAK,OAAOF,EAAC,EAAE,GAAE,KAAKA,IAAEE,EAAC,KAAG,iBAAeA,MAAGD,GAAE,KAAKC,EAAC;AAAE,qBAAOD;AAAA,YAAC,EAAED,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,KAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAE,OAAO,UAAU,gBAAe,IAAE,SAASF,IAAE;AAAC,gBAAG,EAAE,GAAE,EAAE,GAAGA,EAAC,EAAE,QAAO,SAASA,IAAE;AAAC,kBAAIC,KAAE,CAAC;AAAE,kBAAG,QAAMD,GAAE,UAAQE,MAAK,OAAOF,EAAC,EAAE,CAAAC,GAAE,KAAKC,EAAC;AAAE,qBAAOD;AAAA,YAAC,EAAED,EAAC;AAAE,gBAAIC,MAAG,GAAE,EAAE,GAAGD,EAAC,GAAEE,KAAE,CAAC;AAAE,qBAAQC,MAAKH,GAAE,EAAC,iBAAeG,MAAG,CAACF,MAAG,EAAE,KAAKD,IAAEG,EAAC,MAAID,GAAE,KAAKC,EAAC;AAAE,mBAAOD;AAAA,UAAC,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGA,EAAC,KAAG,GAAEG,GAAE,GAAGH,IAAE,IAAE,IAAE,EAAEA,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASA,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,IAAE,EAAC,GAAE,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIE,IAAE,GAAE,GAAE,GAAE,IAAED,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,aAAC,WAASA,MAAG,EAAE,GAAE,EAAE,GAAGF,GAAEC,EAAC,GAAEC,EAAC,KAAG,WAASA,MAAG,EAAED,MAAKD,SAAM,GAAE,EAAE,GAAGA,IAAEC,IAAEC,EAAC;AAAA,UAAC,GAAE,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,qBAAQC,KAAE,IAAGC,KAAE,OAAOJ,EAAC,GAAEK,KAAEH,GAAEF,EAAC,GAAEM,KAAED,GAAE,QAAOC,QAAK;AAAC,kBAAIC,KAAEF,GAAE,EAAEF,EAAC;AAAE,kBAAG,UAAKF,GAAEG,GAAEG,EAAC,GAAEA,IAAEH,EAAC,EAAE;AAAA,YAAK;AAAC,mBAAOJ;AAAA,UAAC,GAAE,IAAEE,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,GAAG,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAAS,WAAU,IAAE,OAAO,WAAU,IAAE,EAAE,UAAS,IAAE,EAAE,gBAAe,IAAE,EAAE,KAAK,MAAM,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAEC,IAAE;AAAC,iBAAI,kBAAgBA,MAAG,cAAY,OAAOD,GAAEC,EAAC,MAAI,eAAaA,GAAE,QAAOD,GAAEC,EAAC;AAAA,UAAC,GAAE,IAAEC,GAAE,IAAI,GAAE,IAAEA,GAAE,IAAI,GAAE,IAAE,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAIC,IAAEC,KAAE,EAAER,IAAEE,EAAC,GAAEO,KAAE,EAAER,IAAEC,EAAC,GAAES,KAAEL,GAAE,IAAIG,EAAC;AAAE,gBAAGE,GAAE,GAAEX,IAAEE,IAAES,EAAC;AAAA,iBAAM;AAAC,kBAAIoB,KAAE1B,KAAEA,GAAEG,IAAEC,IAAEP,KAAE,IAAGF,IAAEC,IAAEK,EAAC,IAAE,QAAO8B,KAAE,WAASL;AAAE,kBAAGK,IAAE;AAAC,oBAAIT,MAAG,GAAE,EAAE,GAAGlB,EAAC,GAAEmB,KAAE,CAACD,OAAI,GAAE,EAAE,GAAGlB,EAAC,GAAEoB,KAAE,CAACF,MAAG,CAACC,OAAI,GAAE,EAAE,GAAGnB,EAAC;AAAE,gBAAAsB,KAAEtB,IAAEkB,MAAGC,MAAGC,MAAG,GAAE,EAAE,GAAGrB,EAAC,IAAEuB,KAAEvB,MAAGD,KAAEC,KAAG,GAAE,EAAE,GAAGD,EAAC,MAAI,GAAE,EAAE,GAAGA,EAAC,IAAEwB,MAAG,GAAE,EAAE,GAAGvB,EAAC,IAAEoB,MAAGQ,KAAE,OAAGL,MAAG,GAAE,EAAE,GAAGtB,IAAE,IAAE,KAAGoB,MAAGO,KAAE,OAAGL,MAAG,GAAE,EAAE,GAAGtB,IAAE,IAAE,KAAGsB,KAAE,CAAC,KAAG,SAAS/B,IAAE;AAAC,sBAAG,EAAE,GAAE,EAAE,GAAGA,EAAC,KAAG,sBAAoB,GAAE,EAAE,GAAGA,EAAC,EAAE,QAAM;AAAG,sBAAIC,MAAG,GAAE,EAAE,GAAGD,EAAC;AAAE,sBAAG,SAAOC,GAAE,QAAM;AAAG,sBAAIC,KAAE,EAAE,KAAKD,IAAE,aAAa,KAAGA,GAAE;AAAY,yBAAM,cAAY,OAAOC,MAAGA,cAAaA,MAAG,EAAE,KAAKA,EAAC,KAAG;AAAA,gBAAC,EAAEO,EAAC,MAAI,GAAE,EAAE,GAAGA,EAAC,KAAGsB,KAAEvB,KAAG,GAAE,EAAE,GAAGA,EAAC,IAAEuB,KAAE,SAAS/B,IAAE;AAAC,0BAAO,GAAE,EAAE,GAAGA,KAAG,GAAE,EAAE,GAAGA,EAAC,CAAC;AAAA,gBAAC,EAAEQ,EAAC,KAAG,GAAE,EAAE,GAAGA,EAAC,KAAG,EAAE,GAAE,EAAE,GAAGA,EAAC,MAAIuB,MAAG,GAAE,EAAE,GAAGtB,EAAC,MAAI2B,KAAE;AAAA,cAAE;AAAC,cAAAA,OAAI9B,GAAE,IAAIG,IAAEsB,EAAC,GAAE3B,GAAE2B,IAAEtB,IAAEN,IAAEE,IAAEC,EAAC,GAAEA,GAAE,OAAOG,EAAC,IAAG,EAAET,IAAEE,IAAE6B,EAAC;AAAA,YAAC;AAAA,UAAC,GAAE,IAAE,SAAS/B,GAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAAJ,OAAIC,MAAG,EAAEA,IAAG,SAASI,IAAEE,IAAE;AAAC,kBAAGH,OAAIA,KAAE,IAAI,EAAE,OAAI,GAAE,EAAE,GAAGC,EAAC,EAAE,GAAEL,IAAEC,IAAEM,IAAEL,IAAEH,IAAEI,IAAEC,EAAC;AAAA,mBAAM;AAAC,oBAAII,KAAEL,KAAEA,GAAE,EAAEH,IAAEO,EAAC,GAAEF,IAAEE,KAAE,IAAGP,IAAEC,IAAEG,EAAC,IAAE;AAAO,2BAASI,OAAIA,KAAEH,KAAG,EAAEL,IAAEO,IAAEC,EAAC;AAAA,cAAC;AAAA,YAAC,GAAG,EAAE,CAAC;AAAA,UAAC,GAAE,IAAE,SAAST,IAAE;AAAC,mBAAOA;AAAA,UAAC,GAAE,IAAE,KAAK,KAAI,IAAEE,GAAE,IAAI,GAAE,IAAE,EAAE,IAAE,SAASF,IAAEC,IAAE;AAAC,oBAAO,GAAE,EAAE,GAAGD,IAAE,YAAW,EAAC,cAAa,MAAG,YAAW,OAAG,QAAOE,KAAED,IAAE,WAAU;AAAC,qBAAOC;AAAA,YAAC,IAAG,UAAS,KAAE,CAAC;AAAE,gBAAIA;AAAA,UAAC,IAAE,GAAE,IAAE,KAAK,KAAI,KAAGC,KAAE,GAAE,IAAE,GAAE,IAAE,GAAE,WAAU;AAAC,gBAAIH,KAAE,EAAE,GAAEC,KAAE,MAAID,KAAE;AAAG,gBAAG,IAAEA,IAAEC,KAAE,GAAE;AAAC,kBAAG,EAAE,KAAG,IAAI,QAAO,UAAU,CAAC;AAAA,YAAC,MAAM,KAAE;AAAE,mBAAOE,GAAE,MAAM,QAAO,SAAS;AAAA,UAAC,IAAG,IAAE,SAASH,IAAEC,IAAE;AAAC,mBAAO,EAAE,SAASD,IAAEC,IAAEC,IAAE;AAAC,qBAAOD,KAAE,EAAE,WAASA,KAAED,GAAE,SAAO,IAAEC,IAAE,CAAC,GAAE,WAAU;AAAC,yBAAQE,KAAE,WAAUC,KAAE,IAAGC,KAAE,EAAEF,GAAE,SAAOF,IAAE,CAAC,GAAEK,KAAE,MAAMD,EAAC,GAAE,EAAED,KAAEC,KAAG,CAAAC,GAAEF,EAAC,IAAED,GAAEF,KAAEG,EAAC;AAAE,gBAAAA,KAAE;AAAG,yBAAQG,KAAE,MAAMN,KAAE,CAAC,GAAE,EAAEG,KAAEH,KAAG,CAAAM,GAAEH,EAAC,IAAED,GAAEC,EAAC;AAAE,uBAAOG,GAAEN,EAAC,IAAEC,GAAEI,EAAC,GAAE,SAASN,IAAEC,IAAEC,IAAE;AAAC,0BAAOA,GAAE,QAAO;AAAA,oBAAC,KAAK;AAAE,6BAAOF,GAAE,KAAKC,EAAC;AAAA,oBAAE,KAAK;AAAE,6BAAOD,GAAE,KAAKC,IAAEC,GAAE,CAAC,CAAC;AAAA,oBAAE,KAAK;AAAE,6BAAOF,GAAE,KAAKC,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,oBAAE,KAAK;AAAE,6BAAOF,GAAE,KAAKC,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,kBAAC;AAAC,yBAAOF,GAAE,MAAMC,IAAEC,EAAC;AAAA,gBAAC,EAAEF,IAAE,MAAKO,EAAC;AAAA,cAAC;AAAA,YAAC,EAAEP,IAAEC,IAAE,CAAC,GAAED,KAAE,EAAE;AAAA,UAAC,GAAE,IAAEE,GAAE,IAAI,GAAE,KAAG,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,cAAEF,IAAEC,IAAEC,EAAC;AAAA,UAAC,GAAE,EAAG,SAASF,IAAEC,IAAE;AAAC,gBAAIC,KAAE,IAAGC,KAAEF,GAAE,QAAOG,KAAED,KAAE,IAAEF,GAAEE,KAAE,CAAC,IAAE,QAAOE,KAAEF,KAAE,IAAEF,GAAE,CAAC,IAAE;AAAO,iBAAIG,KAAE,EAAE,SAAO,KAAG,cAAY,OAAOA,MAAGD,MAAIC,MAAG,QAAOC,MAAG,SAASL,IAAEC,IAAEC,IAAE;AAAC,kBAAG,EAAE,GAAE,EAAE,GAAGA,EAAC,EAAE,QAAM;AAAG,kBAAIC,KAAE,OAAOF;AAAE,qBAAM,CAAC,EAAE,YAAUE,MAAG,GAAE,EAAE,GAAGD,EAAC,MAAI,GAAE,EAAE,GAAGD,IAAEC,GAAE,MAAM,IAAE,YAAUC,MAAGF,MAAKC,QAAK,GAAE,EAAE,GAAGA,GAAED,EAAC,GAAED,EAAC;AAAA,YAAC,EAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEI,EAAC,MAAID,KAAED,KAAE,IAAE,SAAOC,IAAED,KAAE,IAAGH,KAAE,OAAOA,EAAC,GAAE,EAAEE,KAAEC,MAAG;AAAC,kBAAII,KAAEN,GAAEC,EAAC;AAAE,cAAAK,MAAG,EAAEP,IAAEO,IAAEL,EAAC;AAAA,YAAC;AAAC,mBAAOF;AAAA,UAAC,CAAE;AAAA,QAAE,GAAE,MAAK,SAASA,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,IAAE,WAAU;AAAC,mBAAM,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,MAAK,SAASD,IAAEC,IAAEC,IAAE;AAAC;AAAa,UAAAA,GAAE,EAAED,EAAC,GAAEC,GAAE,EAAED,IAAE,EAAC,YAAW,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,iBAAgB,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,WAAU,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,iBAAgB,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,eAAc,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,WAAU,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,YAAW,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,UAAS,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,YAAW,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,UAAS,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,OAAM,WAAU;AAAC,mBAAOE;AAAA,UAAC,GAAE,YAAW,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,iBAAgB,WAAU;AAAC,mBAAO;AAAA,UAAC,GAAE,UAAS,WAAU;AAAC,mBAAO;AAAA,UAAC,EAAC,CAAC;AAAE,cAAIA,MAAG,CAAAH,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,QAAM,EAAE,IAAE,SAAQA,GAAEA,GAAE,YAAU,EAAE,IAAE,aAAYA,GAAEA,GAAE,OAAK,EAAE,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,EAAE,IAAE,SAAQA,GAAEA,GAAE,aAAW,EAAE,IAAE,cAAaA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,kBAAgB,CAAC,IAAE,mBAAkBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,MAAI,EAAE,IAAE,OAAMA,KAAIG,MAAG,CAAC,CAAC;AAAA,UAAE,MAAM,EAAC;AAAA,YAAC,YAAYH,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,mBAAK,WAASF,IAAE,KAAK,UAAQC;AAAE,oBAAMG,KAAED,GAAE,OAAKA,GAAE;AAAU,mBAAK,QAAM,QAAMD,GAAE,QAAMA,GAAE,QAAMC,GAAE,QAAMC,KAAED,GAAE,WAAU,QAAMD,GAAE,cAAY,KAAK,YAAUA,GAAE;AAAA,YAAU;AAAA,YAAC,OAAO,KAAKF,IAAE;AAAC,qBAAO,MAAM,KAAKA,GAAE,UAAU,EAAE,IAAK,CAAAA,OAAGA,GAAE,IAAK;AAAA,YAAC;AAAA,YAAC,IAAIA,IAAEC,IAAE;AAAC,qBAAM,CAAC,CAAC,KAAK,OAAOD,IAAEC,EAAC,MAAID,GAAE,aAAa,KAAK,SAAQC,EAAC,GAAE;AAAA,YAAG;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,qBAAO,QAAM,KAAK,cAAY,YAAU,OAAOA,KAAE,KAAK,UAAU,QAAQA,GAAE,QAAQ,SAAQ,EAAE,CAAC,IAAE,KAAG,KAAK,UAAU,QAAQA,EAAC,IAAE;AAAA,YAAG;AAAA,YAAC,OAAOD,IAAE;AAAC,cAAAA,GAAE,gBAAgB,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,MAAMA,IAAE;AAAC,oBAAMC,KAAED,GAAE,aAAa,KAAK,OAAO;AAAE,qBAAO,KAAK,OAAOA,IAAEC,EAAC,KAAGA,KAAEA,KAAE;AAAA,YAAE;AAAA,UAAC;AAAA,UAAC,MAAM,UAAU,MAAK;AAAA,YAAC,YAAYD,IAAE;AAAC,oBAAMA,KAAE,iBAAeA,EAAC,GAAE,KAAK,UAAQA,IAAE,KAAK,OAAK,KAAK,YAAY;AAAA,YAAI;AAAA,UAAC;AAAC,gBAAM,IAAE,MAAMA,GAAC;AAAA,YAAC,cAAa;AAAC,mBAAK,aAAW,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,OAAK,CAAC,GAAE,KAAK,QAAM,CAAC;AAAA,YAAC;AAAA,YAAC,OAAO,KAAKA,IAAEC,KAAE,OAAG;AAAC,kBAAG,QAAMD,GAAE,QAAO;AAAK,kBAAG,KAAK,MAAM,IAAIA,EAAC,EAAE,QAAO,KAAK,MAAM,IAAIA,EAAC,KAAG;AAAK,kBAAGC,IAAE;AAAC,oBAAIC,KAAE;AAAK,oBAAG;AAAC,kBAAAA,KAAEF,GAAE;AAAA,gBAAU,QAAM;AAAC,yBAAO;AAAA,gBAAI;AAAC,uBAAO,KAAK,KAAKE,IAAED,EAAC;AAAA,cAAC;AAAC,qBAAO;AAAA,YAAI;AAAA,YAAC,OAAOA,IAAEC,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAMF,EAAC;AAAE,kBAAG,QAAME,GAAE,OAAM,IAAI,EAAE,oBAAoBF,EAAC,OAAO;AAAE,oBAAMI,KAAEF,IAAEG,KAAEL,cAAa,QAAMA,GAAE,aAAW,KAAK,YAAUA,KAAEI,GAAE,OAAOH,EAAC,GAAEK,KAAE,IAAIF,GAAEL,IAAEM,IAAEJ,EAAC;AAAE,qBAAOH,GAAE,MAAM,IAAIQ,GAAE,SAAQA,EAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,KAAKP,IAAEC,KAAE,OAAG;AAAC,qBAAOF,GAAE,KAAKC,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,MAAMF,IAAEC,KAAEE,GAAE,KAAI;AAAC,kBAAID;AAAE,qBAAM,YAAU,OAAOF,KAAEE,KAAE,KAAK,MAAMF,EAAC,KAAG,KAAK,WAAWA,EAAC,IAAEA,cAAa,QAAMA,GAAE,aAAW,KAAK,YAAUE,KAAE,KAAK,MAAM,OAAK,YAAU,OAAOF,KAAEA,KAAEG,GAAE,QAAMA,GAAE,QAAMD,KAAE,KAAK,MAAM,QAAMF,KAAEG,GAAE,QAAMA,GAAE,WAASD,KAAE,KAAK,MAAM,UAAQF,cAAa,aAAWA,GAAE,aAAa,OAAO,KAAG,IAAI,MAAM,KAAK,EAAE,KAAM,CAAAA,QAAIE,KAAE,KAAK,QAAQF,EAAC,GAAE,CAAC,CAACE,GAAG,GAAEA,KAAEA,MAAG,KAAK,KAAKF,GAAE,OAAO,IAAG,QAAME,KAAE,OAAK,WAAUA,MAAGD,KAAEE,GAAE,QAAMD,GAAE,SAAOD,KAAEE,GAAE,OAAKD,GAAE,QAAMA,KAAE;AAAA,YAAI;AAAA,YAAC,YAAYF,IAAE;AAAC,qBAAOA,GAAE,IAAK,CAAAA,OAAG;AAAC,sBAAMC,KAAE,cAAaD,IAAEE,KAAE,cAAaF;AAAE,oBAAG,CAACC,MAAG,CAACC,GAAE,OAAM,IAAI,EAAE,oBAAoB;AAAE,oBAAGD,MAAG,eAAaD,GAAE,SAAS,OAAM,IAAI,EAAE,gCAAgC;AAAE,sBAAMG,KAAEF,KAAED,GAAE,WAASE,KAAEF,GAAE,WAAS;AAAO,uBAAO,KAAK,MAAMG,EAAC,IAAEH,IAAEE,KAAE,YAAU,OAAOF,GAAE,YAAU,KAAK,WAAWA,GAAE,OAAO,IAAEA,MAAGC,OAAID,GAAE,cAAY,KAAK,QAAQA,GAAE,SAAS,IAAEA,KAAGA,GAAE,YAAU,MAAM,QAAQA,GAAE,OAAO,IAAEA,GAAE,UAAQA,GAAE,QAAQ,IAAK,CAAAA,OAAGA,GAAE,YAAY,CAAE,IAAEA,GAAE,UAAQA,GAAE,QAAQ,YAAY,IAAG,MAAM,QAAQA,GAAE,OAAO,IAAEA,GAAE,UAAQ,CAACA,GAAE,OAAO,GAAG,QAAS,CAAAC,OAAG;AAAC,mBAAC,QAAM,KAAK,KAAKA,EAAC,KAAG,QAAMD,GAAE,eAAa,KAAK,KAAKC,EAAC,IAAED;AAAA,gBAAE,CAAE,KAAIA;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,QAAM,oBAAI;AAAQ,cAAI,IAAE;AAAE,mBAAS,EAAEA,IAAEC,IAAE;AAAC,oBAAOD,GAAE,aAAa,OAAO,KAAG,IAAI,MAAM,KAAK,EAAE,OAAQ,CAAAA,OAAG,MAAIA,GAAE,QAAQ,GAAGC,EAAC,GAAG,CAAE;AAAA,UAAC;AAAC,gBAAM,IAAE,cAAc,EAAC;AAAA,YAAC,OAAO,KAAKD,IAAE;AAAC,sBAAOA,GAAE,aAAa,OAAO,KAAG,IAAI,MAAM,KAAK,EAAE,IAAK,CAAAA,OAAGA,GAAE,MAAM,GAAG,EAAE,MAAM,GAAE,EAAE,EAAE,KAAK,GAAG,CAAE;AAAA,YAAC;AAAA,YAAC,IAAIA,IAAEC,IAAE;AAAC,qBAAM,CAAC,CAAC,KAAK,OAAOD,IAAEC,EAAC,MAAI,KAAK,OAAOD,EAAC,GAAEA,GAAE,UAAU,IAAI,GAAG,KAAK,OAAO,IAAIC,EAAC,EAAE,GAAE;AAAA,YAAG;AAAA,YAAC,OAAOD,IAAE;AAAC,gBAAEA,IAAE,KAAK,OAAO,EAAE,QAAS,CAAAC,OAAG;AAAC,gBAAAD,GAAE,UAAU,OAAOC,EAAC;AAAA,cAAC,CAAE,GAAE,MAAID,GAAE,UAAU,UAAQA,GAAE,gBAAgB,OAAO;AAAA,YAAC;AAAA,YAAC,MAAMA,IAAE;AAAC,oBAAMC,MAAG,EAAED,IAAE,KAAK,OAAO,EAAE,CAAC,KAAG,IAAI,MAAM,KAAK,QAAQ,SAAO,CAAC;AAAE,qBAAO,KAAK,OAAOA,IAAEC,EAAC,IAAEA,KAAE;AAAA,YAAE;AAAA,UAAC;AAAE,mBAAS,EAAED,IAAE;AAAC,kBAAMC,KAAED,GAAE,MAAM,GAAG,GAAEE,KAAED,GAAE,MAAM,CAAC,EAAE,IAAK,CAAAD,OAAGA,GAAE,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC,CAAE,EAAE,KAAK,EAAE;AAAE,mBAAOC,GAAE,CAAC,IAAEC;AAAA,UAAC;AAAC,gBAAM,IAAE,cAAc,EAAC;AAAA,YAAC,OAAO,KAAKF,IAAE;AAAC,sBAAOA,GAAE,aAAa,OAAO,KAAG,IAAI,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAGA,GAAE,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAE;AAAA,YAAC;AAAA,YAAC,IAAIA,IAAEC,IAAE;AAAC,qBAAM,CAAC,CAAC,KAAK,OAAOD,IAAEC,EAAC,MAAID,GAAE,MAAM,EAAE,KAAK,OAAO,CAAC,IAAEC,IAAE;AAAA,YAAG;AAAA,YAAC,OAAOD,IAAE;AAAC,cAAAA,GAAE,MAAM,EAAE,KAAK,OAAO,CAAC,IAAE,IAAGA,GAAE,aAAa,OAAO,KAAGA,GAAE,gBAAgB,OAAO;AAAA,YAAC;AAAA,YAAC,MAAMA,IAAE;AAAC,oBAAMC,KAAED,GAAE,MAAM,EAAE,KAAK,OAAO,CAAC;AAAE,qBAAO,KAAK,OAAOA,IAAEC,EAAC,IAAEA,KAAE;AAAA,YAAE;AAAA,UAAC,GAAE,IAAE,MAAK;AAAA,YAAC,YAAYD,IAAE;AAAC,mBAAK,aAAW,CAAC,GAAE,KAAK,UAAQA,IAAE,KAAK,MAAM;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAEC,IAAE;AAAC,cAAAA,KAAED,GAAE,IAAI,KAAK,SAAQC,EAAC,MAAI,QAAMD,GAAE,MAAM,KAAK,OAAO,IAAE,KAAK,WAAWA,GAAE,QAAQ,IAAEA,KAAE,OAAO,KAAK,WAAWA,GAAE,QAAQ,MAAIA,GAAE,OAAO,KAAK,OAAO,GAAE,OAAO,KAAK,WAAWA,GAAE,QAAQ;AAAA,YAAE;AAAA,YAAC,QAAO;AAAC,mBAAK,aAAW,CAAC;AAAE,oBAAMA,KAAE,EAAE,KAAK,KAAK,OAAO;AAAE,kBAAG,QAAMA,GAAE;AAAO,oBAAMC,KAAE,EAAE,KAAK,KAAK,OAAO,GAAEC,KAAE,EAAE,KAAK,KAAK,OAAO,GAAEG,KAAE,EAAE,KAAK,KAAK,OAAO;AAAE,cAAAJ,GAAE,OAAOC,EAAC,EAAE,OAAOG,EAAC,EAAE,QAAS,CAAAJ,OAAG;AAAC,sBAAMC,KAAEF,GAAE,OAAO,MAAMC,IAAEE,GAAE,SAAS;AAAE,gBAAAD,cAAa,MAAI,KAAK,WAAWA,GAAE,QAAQ,IAAEA;AAAA,cAAE,CAAE;AAAA,YAAC;AAAA,YAAC,KAAKF,IAAE;AAAC,qBAAO,KAAK,KAAK,UAAU,EAAE,QAAS,CAAAC,OAAG;AAAC,sBAAMC,KAAE,KAAK,WAAWD,EAAC,EAAE,MAAM,KAAK,OAAO;AAAE,gBAAAD,GAAE,OAAOC,IAAEC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,KAAKF,IAAE;AAAC,mBAAK,KAAKA,EAAC,GAAE,OAAO,KAAK,KAAK,UAAU,EAAE,QAAS,CAAAA,OAAG;AAAC,qBAAK,WAAWA,EAAC,EAAE,OAAO,KAAK,OAAO;AAAA,cAAC,CAAE,GAAE,KAAK,aAAW,CAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO,OAAO,KAAK,KAAK,UAAU,EAAE,OAAQ,CAACA,IAAEC,QAAKD,GAAEC,EAAC,IAAE,KAAK,WAAWA,EAAC,EAAE,MAAM,KAAK,OAAO,GAAED,KAAI,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC,GAAE,IAAE,MAAK;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,mBAAK,SAAOD,IAAE,KAAK,UAAQC,IAAE,EAAE,MAAM,IAAIA,IAAE,IAAI,GAAE,KAAK,OAAK,MAAK,KAAK,OAAK;AAAA,YAAI;AAAA,YAAC,OAAO,OAAOD,IAAE;AAAC,kBAAG,QAAM,KAAK,QAAQ,OAAM,IAAI,EAAE,iCAAiC;AAAE,kBAAIC,IAAEC;AAAE,qBAAO,MAAM,QAAQ,KAAK,OAAO,KAAG,YAAU,OAAOF,MAAGE,KAAEF,GAAE,YAAY,GAAE,SAASE,IAAE,EAAE,EAAE,SAAS,MAAIA,OAAIA,KAAE,SAASA,IAAE,EAAE,MAAI,YAAU,OAAOF,OAAIE,KAAEF,KAAGC,KAAE,YAAU,OAAOC,KAAE,SAAS,cAAc,KAAK,QAAQA,KAAE,CAAC,CAAC,IAAEA,MAAG,KAAK,QAAQ,QAAQA,EAAC,IAAE,KAAG,SAAS,cAAcA,EAAC,IAAE,SAAS,cAAc,KAAK,QAAQ,CAAC,CAAC,KAAGD,KAAE,SAAS,cAAc,KAAK,OAAO,GAAE,KAAK,aAAWA,GAAE,UAAU,IAAI,KAAK,SAAS,GAAEA;AAAA,YAAC;AAAA,YAAC,IAAI,UAAS;AAAC,qBAAO,KAAK;AAAA,YAAW;AAAA,YAAC,SAAQ;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,oBAAMD,KAAE,KAAK,QAAQ,UAAU,KAAE;AAAE,qBAAO,KAAK,OAAO,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,sBAAM,KAAK,UAAQ,KAAK,OAAO,YAAY,IAAI,GAAE,EAAE,MAAM,OAAO,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,SAASA,IAAEC,IAAE;AAAC,mBAAK,QAAQD,IAAEC,EAAC,EAAE,OAAO;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEE,IAAE;AAAC,oBAAMC,KAAE,KAAK,QAAQL,IAAEC,EAAC;AAAE,kBAAG,QAAM,KAAK,OAAO,MAAMC,IAAEC,GAAE,IAAI,KAAGC,GAAE,CAAAC,GAAE,KAAKH,IAAEE,EAAC;AAAA,uBAAU,QAAM,KAAK,OAAO,MAAMF,IAAEC,GAAE,SAAS,GAAE;AAAC,sBAAMH,KAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,KAAK;AAAE,gBAAAK,GAAE,KAAKL,EAAC,GAAEA,GAAE,OAAOE,IAAEE,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,SAASJ,IAAEC,IAAEC,IAAE;AAAC,oBAAMC,KAAE,QAAMD,KAAE,KAAK,OAAO,OAAO,QAAOD,EAAC,IAAE,KAAK,OAAO,OAAOA,IAAEC,EAAC,GAAEE,KAAE,KAAK,MAAMJ,EAAC;AAAE,mBAAK,OAAO,aAAaG,IAAEC,MAAG,MAAM;AAAA,YAAC;AAAA,YAAC,QAAQJ,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAMF,EAAC;AAAE,kBAAG,QAAME,GAAE,OAAM,IAAI,MAAM,2BAA2B;AAAE,qBAAOA,GAAE,MAAMD,EAAC,GAAEC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO;AAAA,YAAC;AAAA,YAAC,OAAOF,KAAE,KAAK,QAAO;AAAC,qBAAO,QAAM,KAAK,UAAQ,SAAOA,KAAE,IAAE,KAAK,OAAO,SAAS,OAAO,IAAI,IAAE,KAAK,OAAO,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,SAASA,IAAE;AAAC,mBAAK,QAAQ,qBAAmB,EAAE,KAAK,kBAAkB,KAAK,QAAQ,sBAAoB,KAAK,KAAK,KAAK,QAAQ,kBAAkB,QAAQ;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,sBAAM,KAAK,QAAQ,cAAY,KAAK,QAAQ,WAAW,YAAY,KAAK,OAAO,GAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMC,KAAE,YAAU,OAAOF,KAAE,KAAK,OAAO,OAAOA,IAAEC,EAAC,IAAED;AAAE,qBAAO,QAAM,KAAK,WAAS,KAAK,OAAO,aAAaE,IAAE,KAAK,QAAM,MAAM,GAAE,KAAK,OAAO,IAAGA;AAAA,YAAC;AAAA,YAAC,MAAMF,IAAEC,IAAE;AAAC,qBAAO,MAAID,KAAE,OAAK,KAAK;AAAA,YAAI;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAA,YAAC;AAAA,YAAC,KAAKD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,YAAU,OAAOF,KAAE,KAAK,OAAO,OAAOA,IAAEC,EAAC,IAAED;AAAE,kBAAG,QAAM,KAAK,UAAQ,KAAK,OAAO,aAAaE,IAAE,KAAK,QAAM,MAAM,GAAE,cAAY,OAAOA,GAAE,YAAY,OAAM,IAAI,EAAE,eAAeF,EAAC,EAAE;AAAE,qBAAOE,GAAE,YAAY,IAAI,GAAEA;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,WAAS;AAAW,cAAI,IAAE;AAAE,gBAAM,IAAE,cAAc,EAAC;AAAA,YAAC,OAAO,MAAMF,IAAE;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,MAAMA,IAAEC,IAAE;AAAC,qBAAO,KAAK,YAAUD,MAAG,KAAK,QAAQ,wBAAwBA,EAAC,IAAE,KAAK,iCAA+B,KAAK,IAAIC,IAAE,CAAC,IAAE;AAAA,YAAE;AAAA,YAAC,SAASD,IAAEC,IAAE;AAAC,kBAAIC,KAAE,MAAM,KAAK,KAAK,OAAO,QAAQ,UAAU,EAAE,QAAQ,KAAK,OAAO;AAAE,qBAAOF,KAAE,MAAIE,MAAG,IAAG,CAAC,KAAK,OAAO,SAAQA,EAAC;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,qBAAM,EAAC,CAAC,KAAK,QAAQ,QAAQ,GAAE,KAAK,QAAQ,MAAM,KAAK,OAAO,KAAG,KAAE;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,QAAMC,GAAE;AAAY,gBAAM,IAAE;AAAA,UAAE,MAAM,EAAC;AAAA,YAAC,cAAa;AAAC,mBAAK,OAAK,MAAK,KAAK,OAAK,MAAK,KAAK,SAAO;AAAA,YAAC;AAAA,YAAC,UAAUH,IAAE;AAAC,kBAAG,KAAK,aAAaA,GAAE,CAAC,GAAE,IAAI,GAAEA,GAAE,SAAO,GAAE;AAAC,sBAAMC,KAAED,GAAE,MAAM,CAAC;AAAE,qBAAK,OAAO,GAAGC,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,GAAGD,IAAE;AAAC,oBAAMC,KAAE,KAAK,SAAS;AAAE,kBAAIC,KAAED,GAAE;AAAE,qBAAKC,MAAGF,KAAE,IAAG,CAAAA,MAAG,GAAEE,KAAED,GAAE;AAAE,qBAAOC;AAAA,YAAC;AAAA,YAAC,SAASF,IAAE;AAAC,oBAAMC,KAAE,KAAK,SAAS;AAAE,kBAAIC,KAAED,GAAE;AAAE,qBAAKC,MAAG;AAAC,oBAAGA,OAAIF,GAAE,QAAM;AAAG,gBAAAE,KAAED,GAAE;AAAA,cAAC;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,QAAQD,IAAE;AAAC,oBAAMC,KAAE,KAAK,SAAS;AAAE,kBAAIC,KAAED,GAAE,GAAEE,KAAE;AAAE,qBAAKD,MAAG;AAAC,oBAAGA,OAAIF,GAAE,QAAOG;AAAE,gBAAAA,MAAG,GAAED,KAAED,GAAE;AAAA,cAAC;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,aAAaD,IAAEC,IAAE;AAAC,sBAAMD,OAAI,KAAK,OAAOA,EAAC,GAAEA,GAAE,OAAKC,IAAE,QAAMA,MAAGD,GAAE,OAAKC,GAAE,MAAK,QAAMA,GAAE,SAAOA,GAAE,KAAK,OAAKD,KAAGC,GAAE,OAAKD,IAAEC,OAAI,KAAK,SAAO,KAAK,OAAKD,OAAI,QAAM,KAAK,QAAM,KAAK,KAAK,OAAKA,IAAEA,GAAE,OAAK,KAAK,MAAK,KAAK,OAAKA,OAAIA,GAAE,OAAK,MAAK,KAAK,OAAK,KAAK,OAAKA,KAAG,KAAK,UAAQ;AAAA,YAAE;AAAA,YAAC,OAAOA,IAAE;AAAC,kBAAIC,KAAE,GAAEC,KAAE,KAAK;AAAK,qBAAK,QAAMA,MAAG;AAAC,oBAAGA,OAAIF,GAAE,QAAOC;AAAE,gBAAAA,MAAGC,GAAE,OAAO,GAAEA,KAAEA,GAAE;AAAA,cAAI;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,OAAOF,IAAE;AAAC,mBAAK,SAASA,EAAC,MAAI,QAAMA,GAAE,SAAOA,GAAE,KAAK,OAAKA,GAAE,OAAM,QAAMA,GAAE,SAAOA,GAAE,KAAK,OAAKA,GAAE,OAAMA,OAAI,KAAK,SAAO,KAAK,OAAKA,GAAE,OAAMA,OAAI,KAAK,SAAO,KAAK,OAAKA,GAAE,OAAM,KAAK,UAAQ;AAAA,YAAE;AAAA,YAAC,SAASA,KAAE,KAAK,MAAK;AAAC,qBAAM,MAAI;AAAC,sBAAMC,KAAED;AAAE,uBAAO,QAAMA,OAAIA,KAAEA,GAAE,OAAMC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,KAAKD,IAAEC,KAAE,OAAG;AAAC,oBAAMC,KAAE,KAAK,SAAS;AAAE,kBAAIC,KAAED,GAAE;AAAE,qBAAKC,MAAG;AAAC,sBAAMC,KAAED,GAAE,OAAO;AAAE,oBAAGH,KAAEI,MAAGH,MAAGD,OAAII,OAAI,QAAMD,GAAE,QAAM,MAAIA,GAAE,KAAK,OAAO,GAAG,QAAM,CAACA,IAAEH,EAAC;AAAE,gBAAAA,MAAGI,IAAED,KAAED,GAAE;AAAA,cAAC;AAAC,qBAAM,CAAC,MAAK,CAAC;AAAA,YAAC;AAAA,YAAC,QAAQF,IAAE;AAAC,oBAAMC,KAAE,KAAK,SAAS;AAAE,kBAAIC,KAAED,GAAE;AAAE,qBAAKC,KAAG,CAAAF,GAAEE,EAAC,GAAEA,KAAED,GAAE;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAEC,IAAEC,IAAE;AAAC,kBAAGD,MAAG,EAAE;AAAO,oBAAK,CAACE,IAAEC,EAAC,IAAE,KAAK,KAAKJ,EAAC;AAAE,kBAAIK,KAAEL,KAAEI;AAAE,oBAAME,KAAE,KAAK,SAASH,EAAC;AAAE,kBAAII,KAAED,GAAE;AAAE,qBAAKC,MAAGF,KAAEL,KAAEC,MAAG;AAAC,sBAAME,KAAEI,GAAE,OAAO;AAAE,gBAAAP,KAAEK,KAAEH,GAAEK,IAAEP,KAAEK,IAAE,KAAK,IAAIJ,IAAEI,KAAEF,KAAEH,EAAC,CAAC,IAAEE,GAAEK,IAAE,GAAE,KAAK,IAAIJ,IAAEH,KAAEC,KAAEI,EAAC,CAAC,GAAEA,MAAGF,IAAEI,KAAED,GAAE;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,IAAIN,IAAE;AAAC,qBAAO,KAAK,OAAQ,CAACC,IAAEC,QAAKD,GAAE,KAAKD,GAAEE,EAAC,CAAC,GAAED,KAAI,CAAC,CAAC;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,SAAS;AAAE,kBAAIC,KAAED,GAAE;AAAE,qBAAKC,KAAG,CAAAF,KAAED,GAAEC,IAAEE,EAAC,GAAEA,KAAED,GAAE;AAAE,qBAAOD;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,kBAAMC,KAAED,GAAE,KAAKD,EAAC;AAAE,gBAAGE,GAAE,QAAOA;AAAE,gBAAG;AAAC,qBAAOD,GAAE,OAAOD,EAAC;AAAA,YAAC,QAAM;AAAC,oBAAME,KAAED,GAAE,OAAOE,GAAE,MAAM;AAAE,qBAAO,MAAM,KAAKH,GAAE,UAAU,EAAE,QAAS,CAAAA,OAAG;AAAC,gBAAAE,GAAE,QAAQ,YAAYF,EAAC;AAAA,cAAC,CAAE,GAAEA,GAAE,cAAYA,GAAE,WAAW,aAAaE,GAAE,SAAQF,EAAC,GAAEE,GAAE,OAAO,GAAEA;AAAA,YAAC;AAAA,UAAC;AAAC,gBAAM,IAAE,MAAMF,WAAU,EAAC;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,SAAO,MAAK,KAAK,MAAM;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAE;AAAC,mBAAK,aAAaA,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,SAAS,QAAS,CAAAA,OAAG;AAAC,gBAAAA,GAAE,OAAO;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,SAASC,IAAE;AAAC,sBAAM,KAAK,UAAQ,KAAK,OAAO,OAAO,GAAE,KAAK,SAAOA,IAAED,GAAE,WAAS,KAAK,OAAO,UAAU,IAAIA,GAAE,OAAO,GAAE,KAAK,OAAO,aAAa,mBAAkB,OAAO,GAAE,KAAK,QAAQ,aAAa,KAAK,QAAO,KAAK,QAAQ,UAAU;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,mBAAK,WAAS,IAAI,KAAE,MAAM,KAAK,KAAK,QAAQ,UAAU,EAAE,OAAQ,CAAAA,OAAGA,OAAI,KAAK,MAAO,EAAE,QAAQ,EAAE,QAAS,CAAAA,OAAG;AAAC,oBAAG;AAAC,wBAAMC,KAAE,EAAED,IAAE,KAAK,MAAM;AAAE,uBAAK,aAAaC,IAAE,KAAK,SAAS,QAAM,MAAM;AAAA,gBAAC,SAAOD,IAAE;AAAC,sBAAGA,cAAa,EAAE;AAAO,wBAAMA;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,SAASA,IAAEC,IAAE;AAAC,kBAAG,MAAID,MAAGC,OAAI,KAAK,OAAO,EAAE,QAAO,KAAK,OAAO;AAAE,mBAAK,SAAS,UAAUD,IAAEC,IAAG,CAACD,IAAEC,IAAEC,OAAI;AAAC,gBAAAF,GAAE,SAASC,IAAEC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,WAAWD,IAAEC,KAAE,GAAE;AAAC,oBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,SAAS,KAAKF,EAAC;AAAE,qBAAO,QAAMD,GAAE,YAAUA,GAAEE,EAAC,KAAG,QAAMF,GAAE,YAAUE,cAAaF,KAAE,CAACE,IAAEC,EAAC,IAAED,cAAaH,KAAEG,GAAE,WAAWF,IAAEG,EAAC,IAAE,CAAC,MAAK,EAAE;AAAA,YAAC;AAAA,YAAC,YAAYH,IAAEC,KAAE,GAAEC,KAAE,OAAO,WAAU;AAAC,kBAAIC,KAAE,CAAC,GAAEC,KAAEF;AAAE,qBAAO,KAAK,SAAS,UAAUD,IAAEC,IAAG,CAACD,IAAEC,IAAEG,OAAI;AAAC,iBAAC,QAAML,GAAE,YAAUA,GAAEC,EAAC,KAAG,QAAMD,GAAE,YAAUC,cAAaD,OAAIG,GAAE,KAAKF,EAAC,GAAEA,cAAaF,OAAII,KAAEA,GAAE,OAAOF,GAAE,YAAYD,IAAEE,IAAEE,EAAC,CAAC,IAAGA,MAAGC;AAAA,cAAC,CAAE,GAAEF;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,SAAS,QAAS,CAAAJ,OAAG;AAAC,gBAAAA,GAAE,OAAO;AAAA,cAAC,CAAE,GAAE,MAAM,OAAO;AAAA,YAAC;AAAA,YAAC,yBAAwB;AAAC,kBAAIC,KAAE;AAAG,mBAAK,SAAS,QAAS,CAAAC,OAAG;AAAC,gBAAAD,MAAG,KAAK,QAAQ,gBAAgB,KAAM,CAAAD,OAAGE,cAAaF,EAAE,MAAIE,GAAE,QAAQ,UAAQC,GAAE,cAAY,QAAMD,GAAE,QAAM,KAAK,WAAWA,EAAC,GAAE,QAAMA,GAAE,QAAM,KAAK,WAAWA,GAAE,IAAI,GAAEA,GAAE,OAAO,OAAO,GAAED,KAAE,QAAIC,cAAaF,KAAEE,GAAE,OAAO,IAAEA,GAAE,OAAO;AAAA,cAAE,CAAE;AAAA,YAAC;AAAA,YAAC,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAK,SAAS,UAAUH,IAAEC,IAAG,CAACD,IAAEC,IAAEG,OAAI;AAAC,gBAAAJ,GAAE,SAASC,IAAEG,IAAEF,IAAEC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,SAASH,IAAEC,IAAEC,IAAE;AAAC,oBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,SAAS,KAAKJ,EAAC;AAAE,kBAAGG,GAAE,CAAAA,GAAE,SAASC,IAAEH,IAAEC,EAAC;AAAA,mBAAM;AAAC,sBAAMF,KAAE,QAAME,KAAE,KAAK,OAAO,OAAO,QAAOD,EAAC,IAAE,KAAK,OAAO,OAAOA,IAAEC,EAAC;AAAE,qBAAK,YAAYF,EAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,aAAaA,IAAEC,IAAE;AAAC,sBAAMD,GAAE,UAAQA,GAAE,OAAO,SAAS,OAAOA,EAAC;AAAE,kBAAIE,KAAE;AAAK,mBAAK,SAAS,aAAaF,IAAEC,MAAG,IAAI,GAAED,GAAE,SAAO,MAAK,QAAMC,OAAIC,KAAED,GAAE,WAAU,KAAK,QAAQ,eAAaD,GAAE,WAAS,KAAK,QAAQ,gBAAcE,OAAI,KAAK,QAAQ,aAAaF,GAAE,SAAQE,EAAC,GAAEF,GAAE,OAAO;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO,KAAK,SAAS,OAAQ,CAACA,IAAEC,OAAID,KAAEC,GAAE,OAAO,GAAG,CAAC;AAAA,YAAC;AAAA,YAAC,aAAaD,IAAEC,IAAE;AAAC,mBAAK,SAAS,QAAS,CAAAC,OAAG;AAAC,gBAAAF,GAAE,aAAaE,IAAED,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,SAASD,IAAE;AAAC,kBAAG,MAAM,SAASA,EAAC,GAAE,KAAK,uBAAuB,GAAE,QAAM,KAAK,UAAQ,KAAK,WAAS,KAAK,QAAQ,cAAY,KAAK,QAAQ,aAAa,KAAK,QAAO,KAAK,QAAQ,UAAU,GAAE,MAAI,KAAK,SAAS,OAAO,KAAG,QAAM,KAAK,QAAQ,cAAa;AAAC,sBAAMA,KAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,aAAa,QAAQ;AAAE,qBAAK,YAAYA,EAAC;AAAA,cAAC,MAAM,MAAK,OAAO;AAAA,YAAC;AAAA,YAAC,KAAKC,IAAEC,KAAE,OAAG;AAAC,oBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,SAAS,KAAKH,IAAEC,EAAC,GAAEG,KAAE,CAAC,CAAC,MAAKJ,EAAC,CAAC;AAAE,qBAAOE,cAAaH,KAAEK,GAAE,OAAOF,GAAE,KAAKC,IAAEF,EAAC,CAAC,KAAG,QAAMC,MAAGE,GAAE,KAAK,CAACF,IAAEC,EAAC,CAAC,GAAEC;AAAA,YAAE;AAAA,YAAC,YAAYL,IAAE;AAAC,mBAAK,SAAS,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYC,IAAEC,IAAE;AAAC,oBAAMC,KAAE,YAAU,OAAOF,KAAE,KAAK,OAAO,OAAOA,IAAEC,EAAC,IAAED;AAAE,qBAAOE,cAAaH,MAAG,KAAK,aAAaG,EAAC,GAAE,MAAM,YAAYA,EAAC;AAAA,YAAC;AAAA,YAAC,MAAMH,IAAEC,KAAE,OAAG;AAAC,kBAAG,CAACA,IAAE;AAAC,oBAAG,MAAID,GAAE,QAAO;AAAK,oBAAGA,OAAI,KAAK,OAAO,EAAE,QAAO,KAAK;AAAA,cAAI;AAAC,oBAAME,KAAE,KAAK,MAAM;AAAE,qBAAO,KAAK,UAAQ,KAAK,OAAO,aAAaA,IAAE,KAAK,QAAM,MAAM,GAAE,KAAK,SAAS,UAAUF,IAAE,KAAK,OAAO,GAAG,CAACA,IAAEG,IAAEC,OAAI;AAAC,sBAAMC,KAAEL,GAAE,MAAMG,IAAEF,EAAC;AAAE,wBAAMI,MAAGH,GAAE,YAAYG,EAAC;AAAA,cAAC,CAAE,GAAEH;AAAA,YAAC;AAAA,YAAC,WAAWF,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAM;AAAE,qBAAK,QAAMD,GAAE,OAAM,CAAAC,GAAE,YAAYD,GAAE,IAAI;AAAE,qBAAO,KAAK,UAAQ,KAAK,OAAO,aAAaC,IAAE,KAAK,QAAM,MAAM,GAAEA;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,UAAQ,KAAK,aAAa,KAAK,QAAO,KAAK,QAAM,MAAM,GAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,cAAAH,GAAE,QAAS,CAAAA,OAAG;AAAC,gBAAAA,GAAE,WAAS,KAAK,WAAS,gBAAcA,GAAE,SAAOE,GAAE,KAAK,GAAGF,GAAE,UAAU,GAAEG,GAAE,KAAK,GAAGH,GAAE,YAAY;AAAA,cAAE,CAAE,GAAEG,GAAE,QAAS,CAAAH,OAAG;AAAC,oBAAG,QAAMA,GAAE,cAAY,aAAWA,GAAE,WAAS,SAAS,KAAK,wBAAwBA,EAAC,IAAE,KAAK,+BAA+B;AAAO,sBAAMC,KAAE,KAAK,OAAO,KAAKD,EAAC;AAAE,wBAAMC,OAAI,QAAMA,GAAE,QAAQ,cAAYA,GAAE,QAAQ,eAAa,KAAK,YAAUA,GAAE,OAAO;AAAA,cAAC,CAAE,GAAEC,GAAE,OAAQ,CAAAF,OAAGA,GAAE,eAAa,KAAK,WAASA,OAAI,KAAK,MAAO,EAAE,KAAM,CAACA,IAAEC,OAAID,OAAIC,KAAE,IAAED,GAAE,wBAAwBC,EAAC,IAAE,KAAK,8BAA4B,IAAE,EAAG,EAAE,QAAS,CAAAD,OAAG;AAAC,oBAAIC,KAAE;AAAK,wBAAMD,GAAE,gBAAcC,KAAE,KAAK,OAAO,KAAKD,GAAE,WAAW;AAAG,sBAAME,KAAE,EAAEF,IAAE,KAAK,MAAM;AAAE,iBAACE,GAAE,SAAOD,MAAG,QAAMC,GAAE,UAAQ,QAAMA,GAAE,UAAQA,GAAE,OAAO,YAAY,IAAI,GAAE,KAAK,aAAaA,IAAED,MAAG,MAAM;AAAA,cAAE,CAAE,GAAE,KAAK,uBAAuB;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,UAAQ;AAAG,gBAAM,IAAE,GAAE,IAAE,MAAMD,WAAU,EAAC;AAAA,YAAC,OAAO,OAAOA,IAAE;AAAC,qBAAO,MAAM,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQC,IAAEC,IAAE;AAAC,oBAAMC,KAAED,GAAE,MAAMF,GAAE,QAAQ;AAAE,kBAAG,QAAMG,MAAGF,GAAE,YAAUE,GAAE,SAAQ;AAAC,oBAAG,YAAU,OAAO,KAAK,QAAQ,QAAM;AAAG,oBAAG,MAAM,QAAQ,KAAK,OAAO,EAAE,QAAOF,GAAE,QAAQ,YAAY;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,aAAW,IAAI,EAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,kBAAGD,OAAI,KAAK,QAAQ,YAAUC,IAAE;AAAC,sBAAMF,KAAE,KAAK,OAAO,MAAMC,IAAEE,GAAE,MAAM;AAAE,oBAAG,QAAMH,GAAE;AAAO,gBAAAA,cAAa,IAAE,KAAK,WAAW,UAAUA,IAAEE,EAAC,IAAEA,OAAID,OAAI,KAAK,QAAQ,YAAU,KAAK,QAAQ,EAAEA,EAAC,MAAIC,OAAI,KAAK,YAAYD,IAAEC,EAAC;AAAA,cAAC,MAAM,MAAK,SAAS,QAAS,CAAAD,OAAG;AAAC,gBAAAA,cAAaD,OAAIC,KAAEA,GAAE,KAAKD,GAAE,UAAS,IAAE,IAAG,KAAK,WAAW,KAAKC,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,UAAS;AAAC,oBAAMD,KAAE,KAAK,WAAW,OAAO,GAAEC,KAAE,KAAK,QAAQ,QAAQ,KAAK,SAAQ,KAAK,MAAM;AAAE,qBAAO,QAAMA,OAAID,GAAE,KAAK,QAAQ,QAAQ,IAAEC,KAAGD;AAAA,YAAC;AAAA,YAAC,SAASA,IAAEC,IAAEC,IAAEE,IAAE;AAAC,sBAAM,KAAK,QAAQ,EAAEF,EAAC,KAAG,KAAK,OAAO,MAAMA,IAAEC,GAAE,SAAS,IAAE,KAAK,QAAQH,IAAEC,EAAC,EAAE,OAAOC,IAAEE,EAAC,IAAE,MAAM,SAASJ,IAAEC,IAAEC,IAAEE,EAAC;AAAA,YAAC;AAAA,YAAC,SAASH,IAAE;AAAC,oBAAM,SAASA,EAAC;AAAE,oBAAMC,KAAE,KAAK,QAAQ;AAAE,kBAAG,MAAI,OAAO,KAAKA,EAAC,EAAE,OAAO,QAAO,KAAK,OAAO;AAAE,oBAAMC,KAAE,KAAK;AAAK,cAAAA,cAAaH,MAAGG,GAAE,SAAO,QAAM,SAASH,IAAEC,IAAE;AAAC,oBAAG,OAAO,KAAKD,EAAC,EAAE,WAAS,OAAO,KAAKC,EAAC,EAAE,OAAO,QAAM;AAAG,2BAAUC,MAAKF,GAAE,KAAGA,GAAEE,EAAC,MAAID,GAAEC,EAAC,EAAE,QAAM;AAAG,uBAAM;AAAA,cAAE,EAAEA,IAAEC,GAAE,QAAQ,CAAC,MAAIA,GAAE,aAAa,IAAI,GAAEA,GAAE,OAAO;AAAA,YAAE;AAAA,YAAC,YAAYH,IAAEC,IAAE;AAAC,oBAAMC,KAAE,MAAM,YAAYF,IAAEC,EAAC;AAAE,qBAAO,KAAK,WAAW,KAAKC,EAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,oBAAM,OAAOD,IAAEC,EAAC,GAAED,GAAE,KAAM,CAAAA,OAAGA,GAAE,WAAS,KAAK,WAAS,iBAAeA,GAAE,IAAK,KAAG,KAAK,WAAW,MAAM;AAAA,YAAC;AAAA,YAAC,KAAKC,IAAEC,IAAE;AAAC,oBAAMC,KAAE,MAAM,KAAKF,IAAEC,EAAC;AAAE,qBAAOC,cAAaH,MAAG,KAAK,WAAW,KAAKG,EAAC,GAAEA;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,kBAAgB,CAAC,GAAE,CAAC,GAAE,EAAE,WAAS,UAAS,EAAE,QAAMA,GAAE,aAAY,EAAE,UAAQ;AAAO,gBAAM,IAAE,GAAE,IAAE,MAAMH,WAAU,EAAC;AAAA,YAAC,OAAO,OAAOA,IAAE;AAAC,qBAAO,MAAM,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQC,IAAEC,IAAE;AAAC,oBAAMC,KAAED,GAAE,MAAMF,GAAE,QAAQ;AAAE,kBAAG,QAAMG,MAAGF,GAAE,YAAUE,GAAE,SAAQ;AAAC,oBAAG,YAAU,OAAO,KAAK,QAAQ,QAAM;AAAG,oBAAG,MAAM,QAAQ,KAAK,OAAO,EAAE,QAAOF,GAAE,QAAQ,YAAY;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,aAAW,IAAI,EAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,oBAAMG,KAAE,KAAK,OAAO,MAAMJ,IAAEE,GAAE,KAAK;AAAE,sBAAME,OAAIA,cAAa,IAAE,KAAK,WAAW,UAAUA,IAAEH,EAAC,IAAED,OAAI,KAAK,QAAQ,YAAUC,KAAEA,OAAID,OAAI,KAAK,QAAQ,YAAU,KAAK,QAAQ,EAAEA,EAAC,MAAIC,OAAI,KAAK,YAAYD,IAAEC,EAAC,IAAE,KAAK,YAAYF,GAAE,QAAQ;AAAA,YAAE;AAAA,YAAC,UAAS;AAAC,oBAAMA,KAAE,KAAK,WAAW,OAAO,GAAEC,KAAE,KAAK,QAAQ,QAAQ,KAAK,SAAQ,KAAK,MAAM;AAAE,qBAAO,QAAMA,OAAID,GAAE,KAAK,QAAQ,QAAQ,IAAEC,KAAGD;AAAA,YAAC;AAAA,YAAC,SAASA,IAAEC,IAAEC,IAAEE,IAAE;AAAC,sBAAM,KAAK,OAAO,MAAMF,IAAEC,GAAE,KAAK,IAAE,KAAK,OAAOD,IAAEE,EAAC,IAAE,MAAM,SAASJ,IAAEC,IAAEC,IAAEE,EAAC;AAAA,YAAC;AAAA,YAAC,SAASJ,IAAEC,IAAEC,IAAE;AAAC,kBAAG,QAAMA,MAAG,QAAM,KAAK,OAAO,MAAMD,IAAEE,GAAE,MAAM,EAAE,OAAM,SAASH,IAAEC,IAAEC,EAAC;AAAA,mBAAM;AAAC,sBAAMC,KAAE,KAAK,MAAMH,EAAC;AAAE,oBAAG,QAAMG,GAAE,OAAM,IAAI,MAAM,4CAA4C;AAAE;AAAC,wBAAMH,KAAE,KAAK,OAAO,OAAOC,IAAEC,EAAC;AAAE,kBAAAC,GAAE,OAAO,aAAaH,IAAEG,EAAC;AAAA,gBAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,YAAYH,IAAEC,IAAE;AAAC,oBAAMC,KAAE,MAAM,YAAYF,IAAEC,EAAC;AAAE,qBAAO,KAAK,WAAW,KAAKC,EAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,oBAAM,OAAOD,IAAEC,EAAC,GAAED,GAAE,KAAM,CAAAA,OAAGA,GAAE,WAAS,KAAK,WAAS,iBAAeA,GAAE,IAAK,KAAG,KAAK,WAAW,MAAM;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,WAAS,SAAQ,EAAE,QAAMG,GAAE,YAAW,EAAE,UAAQ,KAAI,EAAE,kBAAgB,CAAC,GAAE,GAAE,CAAC;AAAE,gBAAM,IAAE,GAAE,IAAE,cAAc,EAAC;AAAA,YAAC,aAAY;AAAC,qBAAO,SAAO,KAAK,QAAM,KAAK,KAAK,QAAQ,aAAW,KAAK,QAAQ;AAAA,YAAQ;AAAA,YAAC,SAASH,IAAEC,IAAE;AAAC,oBAAM,SAASD,IAAEC,EAAC,GAAE,KAAK,uBAAuB;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,oBAAM,SAASH,IAAEC,IAAEC,IAAEC,EAAC,GAAE,KAAK,uBAAuB;AAAA,YAAC;AAAA,YAAC,SAASH,IAAEC,IAAEC,IAAE;AAAC,oBAAM,SAASF,IAAEC,IAAEC,EAAC,GAAE,KAAK,uBAAuB;AAAA,YAAC;AAAA,YAAC,SAASF,IAAE;AAAC,oBAAM,SAASA,EAAC,GAAE,KAAK,SAAS,SAAO,KAAG,QAAM,KAAK,QAAM,KAAK,WAAW,MAAI,KAAK,KAAK,aAAa,IAAI,GAAE,KAAK,KAAK,OAAO;AAAA,YAAE;AAAA,UAAC;AAAE,YAAE,WAAS,aAAY,EAAE,QAAMG,GAAE;AAAW,gBAAM,IAAE,GAAE,IAAE,cAAc,EAAC;AAAA,YAAC,OAAO,QAAQH,IAAEC,IAAE;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,oBAAM,SAAS,GAAE,KAAK,OAAO,GAAED,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,oBAAIH,MAAGC,OAAI,KAAK,OAAO,IAAE,KAAK,OAAOC,IAAEC,EAAC,IAAE,MAAM,SAASH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,UAAS;AAAC,qBAAO,KAAK,QAAQ,QAAQ,KAAK,SAAQ,KAAK,MAAM;AAAA,YAAC;AAAA,UAAC,GAAE,IAAE,EAAC,YAAW,MAAG,eAAc,MAAG,uBAAsB,MAAG,WAAU,MAAG,SAAQ,KAAE,GAAE,IAAE,cAAc,EAAC;AAAA,YAAC,YAAYH,IAAEC,IAAE;AAAC,oBAAM,MAAKA,EAAC,GAAE,KAAK,WAASD,IAAE,KAAK,SAAO,MAAK,KAAK,MAAM,GAAE,KAAK,WAAS,IAAI,iBAAkB,CAAAA,OAAG;AAAC,qBAAK,OAAOA,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,SAAS,QAAQ,KAAK,SAAQ,CAAC,GAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,qBAAO,KAAK,SAAS,OAAO,MAAKD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,KAAKD,IAAEC,KAAE,OAAG;AAAC,oBAAMC,KAAE,KAAK,SAAS,KAAKF,IAAEC,EAAC;AAAE,qBAAOC,KAAEA,GAAE,WAAS,OAAKA,KAAED,KAAE,KAAK,KAAKC,GAAE,OAAO,QAAQ,YAAW,IAAE,IAAE,OAAK;AAAA,YAAI;AAAA,YAAC,MAAMF,IAAEC,KAAEE,GAAE,KAAI;AAAC,qBAAO,KAAK,SAAS,MAAMH,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAE;AAAC,qBAAO,KAAK,SAAS,SAAS,GAAGA,EAAC;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,sBAAM,KAAK,UAAQ,MAAM,MAAM;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,SAAS,WAAW;AAAA,YAAC;AAAA,YAAC,SAASA,IAAEC,IAAE;AAAC,mBAAK,OAAO,GAAE,MAAID,MAAGC,OAAI,KAAK,OAAO,IAAE,KAAK,SAAS,QAAS,CAAAD,OAAG;AAAC,gBAAAA,GAAE,OAAO;AAAA,cAAC,CAAE,IAAE,MAAM,SAASA,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,mBAAK,OAAO,GAAE,MAAM,SAASH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAASH,IAAEC,IAAEC,IAAE;AAAC,mBAAK,OAAO,GAAE,MAAM,SAASF,IAAEC,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAASF,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAE;AAAC,oBAAM,SAASA,EAAC;AAAE,oBAAMC,KAAED,GAAE,gBAAc,oBAAI;AAAQ,kBAAIE,KAAE,MAAM,KAAK,KAAK,SAAS,YAAY,CAAC;AAAE,qBAAKA,GAAE,SAAO,IAAG,CAAAH,GAAE,KAAKG,GAAE,IAAI,CAAC;AAAE,oBAAMC,KAAE,CAACJ,IAAEC,KAAE,SAAK;AAAC,wBAAMD,MAAGA,OAAI,QAAM,QAAMA,GAAE,QAAQ,eAAaE,GAAE,IAAIF,GAAE,OAAO,KAAGE,GAAE,IAAIF,GAAE,SAAQ,CAAC,CAAC,GAAEC,MAAGG,GAAEJ,GAAE,MAAM;AAAA,cAAE,GAAEK,KAAE,CAAAL,OAAG;AAAC,gBAAAE,GAAE,IAAIF,GAAE,OAAO,MAAIA,cAAa,KAAGA,GAAE,SAAS,QAAQK,EAAC,GAAEH,GAAE,OAAOF,GAAE,OAAO,GAAEA,GAAE,SAASC,EAAC;AAAA,cAAE;AAAE,kBAAIK,KAAEN;AAAE,uBAAQC,KAAE,GAAEK,GAAE,SAAO,GAAEL,MAAG,GAAE;AAAC,oBAAGA,MAAG,IAAI,OAAM,IAAI,MAAM,iDAAiD;AAAE,qBAAIK,GAAE,QAAS,CAAAN,OAAG;AAAC,wBAAMC,KAAE,KAAK,KAAKD,GAAE,QAAO,IAAE;AAAE,0BAAMC,OAAIA,GAAE,YAAUD,GAAE,WAAS,gBAAcA,GAAE,QAAMI,GAAE,KAAK,KAAKJ,GAAE,iBAAgB,KAAE,CAAC,GAAE,MAAM,KAAKA,GAAE,UAAU,EAAE,QAAS,CAAAA,OAAG;AAAC,0BAAMC,KAAE,KAAK,KAAKD,IAAE,KAAE;AAAE,oBAAAI,GAAEH,IAAE,KAAE,GAAEA,cAAa,KAAGA,GAAE,SAAS,QAAS,CAAAD,OAAG;AAAC,sBAAAI,GAAEJ,IAAE,KAAE;AAAA,oBAAC,CAAE;AAAA,kBAAC,CAAE,KAAG,iBAAeA,GAAE,QAAMI,GAAEH,GAAE,IAAI,IAAGG,GAAEH,EAAC;AAAA,gBAAE,CAAE,GAAE,KAAK,SAAS,QAAQI,EAAC,GAAEC,KAAE,MAAM,KAAK,KAAK,SAAS,YAAY,CAAC,GAAEH,KAAEG,GAAE,MAAM,GAAEH,GAAE,SAAO,IAAG,CAAAH,GAAE,KAAKG,GAAE,IAAI,CAAC;AAAA,cAAC;AAAA,YAAC;AAAA,YAAC,OAAOH,IAAEC,KAAE,CAAC,GAAE;AAAC,cAAAD,KAAEA,MAAG,KAAK,SAAS,YAAY;AAAE,oBAAME,KAAE,oBAAI;AAAQ,cAAAF,GAAE,IAAK,CAAAA,OAAG;AAAC,sBAAMC,KAAE,KAAK,KAAKD,GAAE,QAAO,IAAE;AAAE,uBAAO,QAAMC,KAAE,OAAKC,GAAE,IAAID,GAAE,OAAO,KAAGC,GAAE,IAAID,GAAE,OAAO,EAAE,KAAKD,EAAC,GAAE,SAAOE,GAAE,IAAID,GAAE,SAAQ,CAACD,EAAC,CAAC,GAAEC;AAAA,cAAE,CAAE,EAAE,QAAS,CAAAD,OAAG;AAAC,wBAAMA,MAAGA,OAAI,QAAME,GAAE,IAAIF,GAAE,OAAO,KAAGA,GAAE,OAAOE,GAAE,IAAIF,GAAE,OAAO,KAAG,CAAC,GAAEC,EAAC;AAAA,cAAC,CAAE,GAAEA,GAAE,eAAaC,IAAEA,GAAE,IAAI,KAAK,OAAO,KAAG,MAAM,OAAOA,GAAE,IAAI,KAAK,OAAO,GAAED,EAAC,GAAE,KAAK,SAASD,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAE,YAAE,WAAS,UAAS,EAAE,eAAa,GAAE,EAAE,kBAAgB,CAAC,GAAE,CAAC,GAAE,EAAE,QAAME,GAAE,YAAW,EAAE,UAAQ;AAAM,gBAAM,IAAE,GAAE,IAAE,MAAMH,WAAU,EAAC;AAAA,YAAC,OAAO,OAAOA,IAAE;AAAC,qBAAO,SAAS,eAAeA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,MAAMA,IAAE;AAAC,qBAAOA,GAAE;AAAA,YAAI;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,OAAK,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,SAASD,IAAEC,IAAE;AAAC,mBAAK,QAAQ,OAAK,KAAK,OAAK,KAAK,KAAK,MAAM,GAAED,EAAC,IAAE,KAAK,KAAK,MAAMA,KAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,MAAMD,IAAEC,IAAE;AAAC,qBAAO,KAAK,YAAUD,KAAEC,KAAE;AAAA,YAAE;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAE;AAAC,sBAAMA,MAAG,KAAK,OAAK,KAAK,KAAK,MAAM,GAAEF,EAAC,IAAEC,KAAE,KAAK,KAAK,MAAMD,EAAC,GAAE,KAAK,QAAQ,OAAK,KAAK,QAAM,MAAM,SAASA,IAAEC,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,qBAAO,KAAK,KAAK;AAAA,YAAM;AAAA,YAAC,SAASD,IAAE;AAAC,oBAAM,SAASA,EAAC,GAAE,KAAK,OAAK,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAE,MAAI,KAAK,KAAK,SAAO,KAAK,OAAO,IAAE,KAAK,gBAAgBD,MAAG,KAAK,KAAK,SAAO,SAAO,KAAK,SAAS,KAAK,OAAO,GAAE,KAAK,KAAK,MAAM,CAAC,GAAE,KAAK,KAAK,OAAO;AAAA,YAAE;AAAA,YAAC,SAASA,IAAEC,KAAE,OAAG;AAAC,qBAAM,CAAC,KAAK,SAAQD,EAAC;AAAA,YAAC;AAAA,YAAC,MAAMA,IAAEC,KAAE,OAAG;AAAC,kBAAG,CAACA,IAAE;AAAC,oBAAG,MAAID,GAAE,QAAO;AAAK,oBAAGA,OAAI,KAAK,OAAO,EAAE,QAAO,KAAK;AAAA,cAAI;AAAC,oBAAME,KAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,UAAUF,EAAC,CAAC;AAAE,qBAAO,KAAK,OAAO,aAAaE,IAAE,KAAK,QAAM,MAAM,GAAE,KAAK,OAAK,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAEA;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,cAAAD,GAAE,KAAM,CAAAA,OAAG,oBAAkBA,GAAE,QAAMA,GAAE,WAAS,KAAK,OAAQ,MAAI,KAAK,OAAK,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,YAAE;AAAA,YAAC,QAAO;AAAC,qBAAO,KAAK;AAAA,YAAI;AAAA,UAAC;AAAE,YAAE,WAAS,QAAO,EAAE,QAAMG,GAAE;AAAY,gBAAM,IAAE;AAAA,QAAC,EAAC,GAAE,IAAE,CAAC;AAAE,iBAAS,EAAEA,IAAE;AAAC,cAAI,IAAE,EAAEA,EAAC;AAAE,cAAG,WAAS,EAAE,QAAO,EAAE;AAAQ,cAAI,IAAE,EAAEA,EAAC,IAAE,EAAC,IAAGA,IAAE,QAAO,OAAG,SAAQ,CAAC,EAAC;AAAE,iBAAO,EAAEA,EAAC,EAAE,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAE,SAAO,MAAG,EAAE;AAAA,QAAO;AAAC,UAAE,IAAE,SAASH,IAAE;AAAC,cAAIC,KAAED,MAAGA,GAAE,aAAW,WAAU;AAAC,mBAAOA,GAAE;AAAA,UAAO,IAAE,WAAU;AAAC,mBAAOA;AAAA,UAAC;AAAE,iBAAO,EAAE,EAAEC,IAAE,EAAC,GAAEA,GAAC,CAAC,GAAEA;AAAA,QAAC,GAAE,EAAE,IAAE,SAASD,IAAEC,IAAE;AAAC,mBAAQE,MAAKF,GAAE,GAAE,EAAEA,IAAEE,EAAC,KAAG,CAAC,EAAE,EAAEH,IAAEG,EAAC,KAAG,OAAO,eAAeH,IAAEG,IAAE,EAAC,YAAW,MAAG,KAAIF,GAAEE,EAAC,EAAC,CAAC;AAAA,QAAC,GAAE,EAAE,IAAE,WAAU;AAAC,cAAG,YAAU,OAAO,WAAW,QAAO;AAAW,cAAG;AAAC,mBAAO,QAAM,IAAI,SAAS,aAAa,EAAE;AAAA,UAAC,SAAOH,IAAE;AAAC,gBAAG,YAAU,OAAO,OAAO,QAAO;AAAA,UAAM;AAAA,QAAC,EAAE,GAAE,EAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,iBAAO,OAAO,UAAU,eAAe,KAAKD,IAAEC,EAAC;AAAA,QAAC,GAAE,EAAE,IAAE,SAASD,IAAE;AAAC,yBAAa,OAAO,UAAQ,OAAO,eAAa,OAAO,eAAeA,IAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,QAAC,GAAE,EAAE,MAAI,SAASA,IAAE;AAAC,iBAAOA,GAAE,QAAM,CAAC,GAAEA,GAAE,aAAWA,GAAE,WAAS,CAAC,IAAGA;AAAA,QAAC;AAAE,YAAI,IAAE,CAAC;AAAE,eAAO,WAAU;AAAC;AAAa,YAAE,EAAE,GAAE,EAAC,SAAQ,WAAU;AAAC,mBAAO;AAAA,UAAE,EAAC,CAAC;AAAE,cAAIA,KAAE,EAAE,IAAI,GAAEC,KAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,gBAAe;AAAA,YAAC,IAAID,IAAEC,IAAE;AAAC,kBAAIC,KAAE;AAAE,kBAAG,SAAOD,MAAG,SAAOA,IAAE;AAAC,sBAAME,KAAE,KAAK,MAAMH,EAAC,KAAG;AAAE,gBAAAE,KAAE,SAAOD,KAAEE,KAAE,IAAEA,KAAE;AAAA,cAAC,MAAK,aAAU,OAAOF,OAAIC,KAAED;AAAG,qBAAO,MAAIC,MAAG,KAAK,OAAOF,EAAC,GAAE,QAAI,MAAM,IAAIA,IAAEE,GAAE,SAAS,CAAC;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,qBAAO,MAAM,OAAOD,IAAEC,EAAC,KAAG,MAAM,OAAOD,IAAE,SAASC,IAAE,EAAE,CAAC;AAAA,YAAC;AAAA,YAAC,MAAMD,IAAE;AAAC,qBAAO,SAAS,MAAM,MAAMA,EAAC,GAAE,EAAE,KAAG;AAAA,YAAM;AAAA,UAAC;AAAC,cAAI,IAAE,IAAI,EAAE,UAAS,aAAY,EAAC,OAAM,EAAE,MAAM,OAAM,WAAU,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,EAAC,CAAC,GAAE,IAAE,EAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,OAAO,WAAS;AAAA,YAAa,OAAO,UAAQ;AAAA,UAAY;AAAC,cAAI,IAAE;AAAA,UAAE,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,OAAO,WAAS;AAAA,YAAS,OAAO,UAAQ,CAAC,MAAK,MAAK,MAAK,MAAK,MAAK,IAAI;AAAA,YAAE,OAAO,QAAQA,IAAE;AAAC,qBAAO,KAAK,QAAQ,QAAQA,GAAE,OAAO,IAAE;AAAA,YAAC;AAAA,UAAC;AAAC,cAAI,IAAE,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,UAAC;AAAC,YAAE,WAAS,kBAAiB,EAAE,UAAQ;AAAA,UAAK,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,OAAO,OAAOA,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAO;AAAE,qBAAOA,GAAE,aAAa,aAAYD,EAAC,GAAEC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQD,IAAE;AAAC,qBAAOA,GAAE,aAAa,WAAW,KAAG;AAAA,YAAM;AAAA,YAAC,OAAO,WAAU;AAAC,gBAAE,GAAG,SAAS,CAAC;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC;AAAE,oBAAMC,KAAED,GAAE,cAAc,cAAc,MAAM,GAAEE,KAAE,CAAAD,OAAG;AAAC,oBAAG,CAACF,GAAE,UAAU,EAAE;AAAO,sBAAMG,KAAE,KAAK,QAAQ,QAAQF,IAAED,EAAC;AAAE,8BAAYG,MAAG,KAAK,OAAO,QAAO,WAAW,GAAED,GAAE,eAAe,KAAG,gBAAcC,OAAI,KAAK,OAAO,QAAO,SAAS,GAAED,GAAE,eAAe;AAAA,cAAE;AAAE,cAAAA,GAAE,iBAAiB,aAAYC,EAAC,GAAED,GAAE,iBAAiB,cAAaC,EAAC,GAAE,KAAK,SAASD,EAAC;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,cAAAD,OAAI,KAAK,QAAQ,YAAUC,KAAE,KAAK,QAAQ,aAAa,aAAYA,EAAC,IAAE,MAAM,OAAOD,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,WAAS,QAAO,EAAE,UAAQ,MAAK,EAAE,kBAAgB,CAAC,CAAC,GAAE,EAAE,oBAAkB;AAAE,cAAI,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,GAAG,GAAE,IAAE,EAAE,IAAI;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAO,OAAO,UAAQ,CAAC,UAAS,GAAG;AAAA,YAAE,OAAO,SAAQ;AAAC,qBAAO,MAAM,OAAO;AAAA,YAAC;AAAA,YAAC,OAAO,UAAS;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,SAASD,IAAE;AAAC,oBAAM,SAASA,EAAC,GAAE,KAAK,QAAQ,YAAU,KAAK,QAAQ,QAAQ,CAAC,KAAG,KAAK,YAAY,KAAK,QAAQ,QAAQ;AAAA,YAAC;AAAA,UAAC;AAAC,cAAI,IAAE;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAO,OAAO,UAAQ;AAAA,YAAI,OAAO,gBAAc;AAAA,YAAc,OAAO,qBAAmB,CAAC,QAAO,SAAQ,UAAS,OAAM,KAAK;AAAA,YAAE,OAAO,OAAOA,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAOD,EAAC;AAAE,qBAAOC,GAAE,aAAa,QAAO,KAAK,SAASD,EAAC,CAAC,GAAEC,GAAE,aAAa,OAAM,qBAAqB,GAAEA,GAAE,aAAa,UAAS,QAAQ,GAAEA;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQD,IAAE;AAAC,qBAAOA,GAAE,aAAa,MAAM;AAAA,YAAC;AAAA,YAAC,OAAO,SAASA,IAAE;AAAC,qBAAO,EAAEA,IAAE,KAAK,kBAAkB,IAAEA,KAAE,KAAK;AAAA,YAAa;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,cAAAD,OAAI,KAAK,QAAQ,YAAUC,KAAE,KAAK,QAAQ,aAAa,QAAO,KAAK,YAAY,SAASA,EAAC,CAAC,IAAE,MAAM,OAAOD,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,EAAED,IAAEC,IAAE;AAAC,kBAAMC,KAAE,SAAS,cAAc,GAAG;AAAE,YAAAA,GAAE,OAAKF;AAAE,kBAAMG,KAAED,GAAE,KAAK,MAAM,GAAEA,GAAE,KAAK,QAAQ,GAAG,CAAC;AAAE,mBAAOD,GAAE,QAAQE,EAAC,IAAE;AAAA,UAAE;AAAA,UAAC,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAS,OAAO,UAAQ,CAAC,OAAM,KAAK;AAAA,YAAE,OAAO,OAAOH,IAAE;AAAC,qBAAM,YAAUA,KAAE,SAAS,cAAc,KAAK,IAAE,UAAQA,KAAE,SAAS,cAAc,KAAK,IAAE,MAAM,OAAOA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQA,IAAE;AAAC,qBAAM,UAAQA,GAAE,UAAQ,QAAM,UAAQA,GAAE,UAAQ,UAAQ;AAAA,YAAM;AAAA,UAAC;AAAC,cAAI,IAAE;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAY,OAAO,UAAQ;AAAA,UAAG;AAAC,cAAI,IAAE,GAAE,IAAE,EAAE,GAAG;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAU,OAAO,YAAU;AAAA,YAAa,OAAO,UAAQ;AAAA,YAAO,OAAO,OAAOA,IAAE;AAAC,kBAAG,QAAM,OAAO,MAAM,OAAM,IAAI,MAAM,gCAAgC;AAAE,oBAAMC,KAAE,MAAM,OAAOD,EAAC;AAAE,qBAAM,YAAU,OAAOA,OAAI,OAAO,MAAM,OAAOA,IAAEC,IAAE,EAAC,cAAa,OAAG,YAAW,OAAM,CAAC,GAAEA,GAAE,aAAa,cAAaD,EAAC,IAAGC;AAAA,YAAC;AAAA,YAAC,OAAO,MAAMD,IAAE;AAAC,qBAAOA,GAAE,aAAa,YAAY;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,oBAAK,EAAC,SAAQA,GAAC,IAAE,KAAK,MAAM;AAAE,qBAAM,SAASA,EAAC;AAAA,YAAS;AAAA,UAAC;AAAC,cAAI,IAAE;AAAE,gBAAM,IAAE,CAAC,OAAM,UAAS,OAAO;AAAA,UAAE,MAAM,UAAU,EAAE,UAAS;AAAA,YAAC,OAAO,WAAS;AAAA,YAAQ,OAAO,UAAQ;AAAA,YAAM,OAAO,OAAOA,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAOD,EAAC;AAAE,qBAAM,YAAU,OAAOA,MAAGC,GAAE,aAAa,OAAM,KAAK,SAASD,EAAC,CAAC,GAAEC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQD,IAAE;AAAC,qBAAO,EAAE,OAAQ,CAACC,IAAEC,QAAKF,GAAE,aAAaE,EAAC,MAAID,GAAEC,EAAC,IAAEF,GAAE,aAAaE,EAAC,IAAGD,KAAI,CAAC,CAAC;AAAA,YAAC;AAAA,YAAC,OAAO,MAAMD,IAAE;AAAC,qBAAM,qBAAqB,KAAKA,EAAC,KAAG,yBAAyB,KAAKA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,SAASA,IAAE;AAAC,qBAAO,EAAEA,IAAE,CAAC,QAAO,SAAQ,MAAM,CAAC,IAAEA,KAAE;AAAA,YAAM;AAAA,YAAC,OAAO,MAAMA,IAAE;AAAC,qBAAOA,GAAE,aAAa,KAAK;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,gBAAE,QAAQD,EAAC,IAAE,KAAGC,KAAE,KAAK,QAAQ,aAAaD,IAAEC,EAAC,IAAE,KAAK,QAAQ,gBAAgBD,EAAC,IAAE,MAAM,OAAOA,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,cAAI,IAAE;AAAE,gBAAM,IAAE,CAAC,UAAS,OAAO;AAAA,UAAE,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,OAAO,WAAS;AAAA,YAAQ,OAAO,YAAU;AAAA,YAAW,OAAO,UAAQ;AAAA,YAAS,OAAO,OAAOD,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAOD,EAAC;AAAE,qBAAOC,GAAE,aAAa,eAAc,GAAG,GAAEA,GAAE,aAAa,mBAAkB,MAAM,GAAEA,GAAE,aAAa,OAAM,KAAK,SAASD,EAAC,CAAC,GAAEC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQD,IAAE;AAAC,qBAAO,EAAE,OAAQ,CAACC,IAAEC,QAAKF,GAAE,aAAaE,EAAC,MAAID,GAAEC,EAAC,IAAEF,GAAE,aAAaE,EAAC,IAAGD,KAAI,CAAC,CAAC;AAAA,YAAC;AAAA,YAAC,OAAO,SAASD,IAAE;AAAC,qBAAO,EAAE,SAASA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAO,MAAMA,IAAE;AAAC,qBAAOA,GAAE,aAAa,KAAK;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,gBAAE,QAAQD,EAAC,IAAE,KAAGC,KAAE,KAAK,QAAQ,aAAaD,IAAEC,EAAC,IAAE,KAAK,QAAQ,gBAAgBD,EAAC,IAAE,MAAM,OAAOA,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,oBAAK,EAAC,OAAMD,GAAC,IAAE,KAAK,MAAM;AAAE,qBAAM,YAAYA,EAAC,KAAKA,EAAC;AAAA,YAAM;AAAA,UAAC;AAAC,cAAI,IAAE,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,IAAI,GAAE,IAAE,EAAE,GAAG;AAAE,gBAAM,IAAE,IAAI,EAAE,gBAAgB,cAAa,QAAO,EAAC,OAAM,EAAE,MAAM,OAAM,CAAC;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,QAAQA,IAAEC,IAAE;AAAC,qBAAK,QAAMD,MAAGA,OAAIC,GAAE,WAAS;AAAC,oBAAGD,GAAE,aAAWA,GAAE,UAAU,SAAS,EAAE,GAAG,SAAS,EAAE,QAAO,MAAM,QAAQA,IAAEC,EAAC;AAAE,gBAAAD,KAAEA,GAAE;AAAA,cAAU;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,oBAAMF,IAAEC,IAAEC,EAAC,GAAE,EAAE,IAAI,KAAK,SAAQA,EAAC;AAAA,YAAC;AAAA,YAAC,OAAOF,IAAEC,IAAE;AAAC,cAAAD,OAAI,EAAE,WAAS,MAAM,OAAOA,IAAEC,EAAC,IAAEA,KAAE,EAAE,IAAI,KAAK,SAAQA,EAAC,KAAG,EAAE,OAAO,KAAK,OAAO,GAAE,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,SAAS;AAAA,YAAE;AAAA,YAAC,WAAU;AAAC,oBAAM,SAAS,GAAG,SAAS,GAAE,EAAE,MAAM,KAAK,OAAO,KAAG,KAAK,OAAO;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,WAAS,cAAa,EAAE,YAAU;AAAA,UAAW,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,OAAO,OAAOD,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAOD,EAAC;AAAE,qBAAM,YAAU,OAAOA,MAAGC,GAAE,aAAa,iBAAgBD,EAAC,GAAEC;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQD,IAAE;AAAC,qBAAOA,GAAE,aAAa,eAAe,KAAG;AAAA,YAAO;AAAA,YAAC,OAAO,WAAU;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,cAAAD,OAAI,KAAK,QAAQ,YAAUC,KAAE,KAAK,QAAQ,aAAa,iBAAgBA,EAAC,IAAE,MAAM,OAAOD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,qBAAO,KAAK,SAAS,GAAE,KAAK,OAAO,GAAE,EAAE,UAAS,KAAE,GAAE,MAAM,YAAYD,IAAEC,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,YAAU,OAAG,KAAK,OAAO,UAAU,IAAI;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAEC,IAAE;AAAC,cAAAD,OAAI,EAAE,aAAW,KAAK,YAAU,MAAG,KAAK,SAAS,QAAS,CAAAE,OAAG;AAAC,gBAAAA,GAAE,OAAOF,IAAEC,EAAC;AAAA,cAAC,CAAE;AAAA,YAAE;AAAA,YAAC,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAAD,OAAI,EAAE,aAAW,KAAK,YAAU,OAAI,MAAM,SAASF,IAAEC,IAAEC,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,UAAUH,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,kBAAG,QAAM,KAAK,SAAS,KAAK;AAAO,oBAAMC,KAAE,GAAG,MAAM,KAAK,KAAK,QAAQ,UAAU,EAAE,OAAQ,CAAAF,OAAGA,OAAI,KAAK,MAAO,EAAE,IAAK,CAAAA,OAAGA,GAAE,WAAY,EAAE,KAAK,IAAI,CAAC;AAAA,GAAKG,KAAE,EAAE,QAAQ,KAAK,SAAS,KAAK,OAAO;AAAE,kBAAGF,MAAG,KAAK,aAAW,KAAK,eAAaC,IAAE;AAAC,oBAAGA,GAAE,KAAK,EAAE,SAAO,KAAG,QAAM,KAAK,YAAW;AAAC,wBAAMD,KAAE,KAAK,SAAS,OAAQ,CAACD,IAAEC,OAAID,GAAE,QAAQ,GAAE,EAAE,IAAIC,IAAE,KAAE,CAAC,GAAG,KAAI,EAAE,IAAE,GAAEG,KAAEJ,GAAEE,IAAEC,EAAC;AAAE,kBAAAF,GAAE,KAAKG,EAAC,EAAE,OAAQ,CAACJ,IAAEC,OAAI;AAAC,wBAAG,EAAC,QAAOC,IAAE,YAAWC,GAAC,IAAEF;AAAE,2BAAOC,MAAGC,MAAG,OAAO,KAAKA,EAAC,EAAE,QAAS,CAAAF,OAAG;AAAC,uBAAC,EAAE,UAAS,EAAE,QAAQ,EAAE,SAASA,EAAC,KAAG,KAAK,SAASD,IAAEE,IAAED,IAAEE,GAAEF,EAAC,CAAC;AAAA,oBAAC,CAAE,GAAED,KAAEE,MAAGF;AAAA,kBAAC,GAAG,CAAC;AAAA,gBAAC;AAAC,qBAAK,aAAWE,IAAE,KAAK,YAAU;AAAA,cAAE;AAAA,YAAC;AAAA,YAAC,KAAKF,IAAEC,IAAE;AAAC,oBAAK,CAACC,EAAC,IAAE,KAAK,SAAS,KAAKF,EAAC;AAAE,qBAAM,uBAAuBE,KAAE,EAAE,QAAQA,GAAE,OAAO,IAAE,OAAO;AAAA,GAAQ,GAAE,EAAE,GAAG,KAAK,KAAKF,IAAEC,EAAC,CAAC,CAAC;AAAA;AAAA,YAAU;AAAA,YAAC,SAASD,IAAE;AAAC,kBAAG,MAAM,SAASA,EAAC,GAAE,QAAM,KAAK,UAAQ,QAAM,KAAK,SAAS,QAAM,QAAM,KAAK,QAAO;AAAC,sBAAMA,KAAE,EAAE,QAAQ,KAAK,SAAS,KAAK,OAAO;AAAE,gBAAAA,OAAI,KAAK,OAAO,UAAQ,KAAK,OAAO,QAAMA;AAAA,cAAE;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,kBAAgB,CAAC,CAAC,GAAE,EAAE,oBAAkB,GAAE,EAAE,kBAAgB,CAAC,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,CAAC;AAAA,UAAE,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAU;AAAC,gBAAE,GAAG,SAAS,GAAE,IAAE,GAAE,EAAE,GAAG,SAAS,GAAE,IAAE,GAAE,EAAE,GAAG,SAAS,GAAE,IAAE;AAAA,YAAC;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,kBAAG,MAAMD,IAAEC,EAAC,GAAE,QAAM,KAAK,QAAQ,KAAK,OAAM,IAAI,MAAM,2FAA2F;AAAE,mBAAK,YAAU,KAAK,QAAQ,UAAU,OAAQ,CAACD,IAAEC,OAAI;AAAC,oBAAG,EAAC,KAAIC,GAAC,IAAED;AAAE,uBAAOD,GAAEE,EAAC,IAAE,MAAGF;AAAA,cAAC,GAAG,CAAC,CAAC,GAAE,KAAK,gBAAc,KAAK,cAAc,KAAK,IAAI,GAAE,KAAK,aAAa,GAAE,KAAK,UAAU;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,mBAAK,MAAM,GAAG,EAAE,GAAG,OAAO,mBAAmB,CAAAA,OAAG;AAAC,oBAAG,EAAEA,cAAa,GAAG;AAAO,sBAAMC,KAAE,KAAK,MAAM,KAAK,cAAc,cAAc,QAAQ;AAAE,qBAAK,QAAQ,UAAU,QAAS,CAAAD,OAAG;AAAC,sBAAG,EAAC,KAAIE,IAAE,OAAMC,GAAC,IAAEH;AAAE,wBAAMI,KAAEH,GAAE,cAAc,cAAc,QAAQ;AAAE,kBAAAG,GAAE,cAAYD,IAAEC,GAAE,aAAa,SAAQF,EAAC,GAAED,GAAE,YAAYG,EAAC;AAAA,gBAAC,CAAE,GAAEH,GAAE,iBAAiB,UAAU,MAAI;AAAC,kBAAAD,GAAE,OAAO,EAAE,UAASC,GAAE,KAAK,GAAE,KAAK,MAAM,KAAK,MAAM,GAAE,KAAK,UAAUD,IAAE,IAAE;AAAA,gBAAC,CAAE,GAAE,QAAMA,GAAE,WAASA,GAAE,SAASC,EAAC,GAAED,GAAE,SAAS,SAAOC,GAAE,QAAM,EAAE,QAAQD,GAAE,SAAS,KAAK,OAAO;AAAA,cAAG,CAAE;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,kBAAIA,KAAE;AAAK,mBAAK,MAAM,GAAG,EAAE,GAAG,OAAO,iBAAiB,MAAI;AAAC,gBAAAA,MAAG,aAAaA,EAAC,GAAEA,KAAE,WAAY,MAAI;AAAC,uBAAK,UAAU,GAAEA,KAAE;AAAA,gBAAI,GAAG,KAAK,QAAQ,QAAQ;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,kBAAIA,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,MAAKC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,kBAAG,KAAK,MAAM,UAAU,UAAU;AAAO,mBAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI;AAAE,oBAAMC,KAAE,KAAK,MAAM,aAAa;AAAE,eAAC,QAAMF,KAAE,KAAK,MAAM,OAAO,YAAY,CAAC,IAAE,CAACA,EAAC,GAAG,QAAS,CAAAA,OAAG;AAAC,gBAAAA,GAAE,UAAU,KAAK,eAAcC,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,MAAM,GAAE,QAAMC,MAAG,KAAK,MAAM,aAAaA,IAAE,EAAE,GAAG,QAAQ,MAAM;AAAA,YAAC;AAAA,YAAC,cAAcF,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAQ,kBAAGA,KAAE,KAAK,UAAUA,EAAC,IAAEA,KAAE,SAAQ,YAAUA,GAAE,SAAO,GAAE,EAAE,GAAGD,EAAC,EAAE,MAAM,IAAI,EAAE,OAAQ,CAACA,IAAEE,IAAEC,QAAK,MAAIA,MAAGH,GAAE,OAAO,MAAK,EAAC,CAAC,EAAE,GAAG,QAAQ,GAAEC,GAAC,CAAC,GAAED,GAAE,OAAOE,EAAC,IAAI,KAAI,EAAE,IAAE;AAAE,oBAAMA,KAAE,KAAK,MAAM,KAAK,cAAc,cAAc,KAAK;AAAE,qBAAOA,GAAE,UAAU,IAAI,EAAE,GAAG,SAAS,GAAEA,GAAE,aAAW,CAACF,IAAEC,IAAEC,OAAI;AAAC,oBAAG,YAAU,OAAOF,GAAE,eAAc;AAAC,wBAAMG,KAAEH,GAAE,cAAc,MAAM,GAAG,EAAE,CAAC;AAAE,sBAAG,SAASG,IAAE,EAAE,KAAG,GAAG,QAAOH,GAAE,UAAUE,IAAE,EAAC,UAASD,GAAC,CAAC,EAAE;AAAA,gBAAK;AAAC,uBAAOD,GAAE,UAAUC,IAAEC,EAAC,EAAE;AAAA,cAAK,GAAG,KAAK,QAAQ,MAAKD,IAAED,EAAC,IAAG,GAAE,EAAE,IAAI,KAAK,MAAM,QAAOE,IAAE,CAAC,CAACF,IAAEC,OAAI;AAAC,sBAAMC,KAAE,EAAE,MAAMF,EAAC;AAAE,uBAAOE,KAAED,GAAE,QAAS,KAAI,EAAE,KAAI,OAAOA,GAAE,OAAO,GAAE,EAAC,CAAC,EAAE,QAAQ,GAAEC,GAAC,CAAC,CAAC,IAAED;AAAA,cAAC,CAAC,GAAE,CAAC,CAACD,IAAEE,OAAIF,GAAE,KAAK,MAAM,IAAI,EAAE,OAAQ,CAACA,IAAEE,IAAEC,QAAK,MAAIA,MAAGH,GAAE,OAAO,MAAK,EAAC,CAAC,EAAE,GAAG,QAAQ,GAAEC,GAAC,CAAC,GAAED,GAAE,OAAOE,EAAC,IAAIA,EAAC,CAAC,GAAE,oBAAI,SAAO;AAAA,YAAC;AAAA,UAAC;AAAC,YAAE,WAAS,EAAC,MAAK,OAAO,MAAK,UAAS,KAAI,WAAU,CAAC,EAAC,KAAI,SAAQ,OAAM,QAAO,GAAE,EAAC,KAAI,QAAO,OAAM,OAAM,GAAE,EAAC,KAAI,OAAM,OAAM,MAAK,GAAE,EAAC,KAAI,MAAK,OAAM,KAAI,GAAE,EAAC,KAAI,OAAM,OAAM,MAAK,GAAE,EAAC,KAAI,QAAO,OAAM,OAAM,GAAE,EAAC,KAAI,OAAM,OAAM,WAAU,GAAE,EAAC,KAAI,QAAO,OAAM,OAAM,GAAE,EAAC,KAAI,cAAa,OAAM,aAAY,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU,GAAE,EAAC,KAAI,OAAM,OAAM,MAAK,GAAE,EAAC,KAAI,UAAS,OAAM,SAAQ,GAAE,EAAC,KAAI,QAAO,OAAM,OAAM,GAAE,EAAC,KAAI,OAAM,OAAM,MAAK,CAAC,EAAC;AAAA,UAAE,MAAM,UAAU,EAAE,GAAE;AAAA,YAAC,OAAO,WAAS;AAAA,YAAQ,OAAO,UAAQ;AAAA,YAAK,OAAO,OAAOF,IAAE;AAAC,oBAAMC,KAAE,MAAM,OAAO;AAAE,qBAAOD,KAAEC,GAAE,aAAa,YAAWD,EAAC,IAAEC,GAAE,aAAa,YAAW,GAAG,CAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,OAAO,QAAQD,IAAE;AAAC,kBAAGA,GAAE,aAAa,UAAU,EAAE,QAAOA,GAAE,aAAa,UAAU;AAAA,YAAC;AAAA,YAAC,aAAY;AAAC,qBAAO,KAAK,SAAO,KAAK,OAAO,SAAS,QAAQ,IAAI,IAAE;AAAA,YAAE;AAAA,YAAC,OAAOA,IAAEC,IAAE;AAAC,cAAAD,OAAI,EAAE,YAAUC,KAAE,KAAK,QAAQ,aAAa,YAAWA,EAAC,IAAE,MAAM,OAAOD,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,MAAK;AAAC,qBAAO,KAAK;AAAA,YAAM;AAAA,YAAC,YAAW;AAAC,qBAAO,KAAK,IAAI,IAAE,KAAK,IAAI,EAAE,UAAU,IAAE;AAAA,YAAE;AAAA,YAAC,QAAO;AAAC,qBAAO,KAAK,IAAI,KAAG,KAAK,IAAI,EAAE,MAAM;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,MAAM,UAAU,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAY,OAAO,UAAQ;AAAA,YAAK,aAAY;AAAC,kBAAG,MAAM,WAAW,KAAG,QAAM,KAAK,KAAK,SAAS,MAAK;AAAC,sBAAMD,KAAE,KAAK,SAAS,KAAK,QAAQ,GAAEC,KAAE,KAAK,SAAS,KAAK,QAAQ,GAAEC,KAAE,KAAK,KAAK,SAAS,KAAK,QAAQ,GAAEC,KAAE,KAAK,KAAK,SAAS,KAAK,QAAQ;AAAE,uBAAOH,GAAE,UAAQC,GAAE,SAAOD,GAAE,UAAQE,GAAE,SAAOF,GAAE,UAAQG,GAAE;AAAA,cAAK;AAAC,qBAAM;AAAA,YAAE;AAAA,YAAC,SAASH,IAAE;AAAC,oBAAM,SAASA,EAAC,GAAE,KAAK,SAAS,QAAS,CAAAA,OAAG;AAAC,oBAAG,QAAMA,GAAE,KAAK;AAAO,sBAAMC,KAAED,GAAE,QAAQ,GAAEE,KAAEF,GAAE,KAAK,QAAQ;AAAE,oBAAGC,GAAE,UAAQC,GAAE,OAAM;AAAC,wBAAMD,KAAE,KAAK,WAAWD,EAAC;AAAE,kBAAAC,MAAGA,GAAE,SAAS,GAAE,KAAK,QAAM,KAAK,KAAK,SAAS;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,YAAW;AAAC,qBAAO,KAAK,SAAO,KAAK,OAAO,SAAS,QAAQ,IAAI,IAAE;AAAA,YAAE;AAAA,YAAC,QAAO;AAAC,qBAAO,KAAK,UAAQ,KAAK,OAAO;AAAA,YAAM;AAAA,UAAC;AAAA,UAAC,MAAM,WAAW,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAa,OAAO,UAAQ;AAAA,UAAO;AAAA,UAAC,MAAM,WAAW,EAAE,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAkB,OAAO,UAAQ;AAAA,YAAQ,eAAc;AAAC,oBAAMD,KAAE,KAAK,YAAY,CAAC,GAAEC,KAAED,GAAE,OAAQ,CAACA,IAAEC,OAAI,KAAK,IAAIA,GAAE,SAAS,QAAOD,EAAC,GAAG,CAAC;AAAE,cAAAA,GAAE,QAAS,CAAAA,OAAG;AAAC,oBAAI,MAAMC,KAAED,GAAE,SAAS,MAAM,EAAE,KAAK,CAAC,EAAE,QAAS,MAAI;AAAC,sBAAIC;AAAE,0BAAMD,GAAE,SAAS,SAAOC,KAAE,EAAE,QAAQD,GAAE,SAAS,KAAK,OAAO;AAAG,wBAAME,KAAE,KAAK,OAAO,OAAO,EAAE,UAASD,EAAC;AAAE,kBAAAD,GAAE,YAAYE,EAAC,GAAEA,GAAE,SAAS;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,MAAMF,IAAE;AAAC,qBAAO,KAAK,KAAK,EAAE,IAAK,CAAAC,OAAGA,GAAE,SAAS,GAAGD,EAAC,CAAE;AAAA,YAAC;AAAA,YAAC,aAAaA,IAAE;AAAC,oBAAK,CAACC,EAAC,IAAE,KAAK,WAAW,EAAE;AAAE,sBAAMA,MAAG,QAAMA,GAAE,SAAS,QAAMA,GAAE,SAAS,QAAS,CAAAA,OAAG;AAAC,sBAAMC,KAAED,GAAE,SAAS,GAAGD,EAAC;AAAE,wBAAME,MAAGA,GAAE,OAAO;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,aAAaF,IAAE;AAAC,oBAAK,CAACC,EAAC,IAAE,KAAK,WAAW,EAAE;AAAE,sBAAMA,MAAG,QAAMA,GAAE,SAAS,QAAMA,GAAE,SAAS,QAAS,CAAAA,OAAG;AAAC,sBAAMC,KAAED,GAAE,SAAS,GAAGD,EAAC,GAAEG,KAAE,EAAE,QAAQF,GAAE,SAAS,KAAK,OAAO,GAAEG,KAAE,KAAK,OAAO,OAAO,EAAE,UAASD,EAAC;AAAE,gBAAAF,GAAE,aAAaG,IAAEF,EAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,UAAUF,IAAE;AAAC,oBAAK,CAACC,EAAC,IAAE,KAAK,WAAW,EAAE;AAAE,kBAAG,QAAMA,MAAG,QAAMA,GAAE,SAAS,KAAK;AAAO,oBAAMC,KAAE,GAAG,GAAEC,KAAE,KAAK,OAAO,OAAO,EAAE,QAAQ;AAAE,cAAAF,GAAE,SAAS,KAAK,SAAS,QAAS,MAAI;AAAC,sBAAMD,KAAE,KAAK,OAAO,OAAO,EAAE,UAASE,EAAC;AAAE,gBAAAC,GAAE,YAAYH,EAAC;AAAA,cAAC,CAAE;AAAE,oBAAMI,KAAEH,GAAE,SAAS,GAAGD,EAAC;AAAE,cAAAC,GAAE,aAAaE,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,oBAAMJ,KAAE,KAAK,SAAS;AAAK,qBAAO,QAAMA,KAAE,CAAC,IAAEA,GAAE,SAAS,IAAK,CAAAA,OAAGA,EAAE;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,KAAI;AAAC,mBAAM,OAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAE,CAAC,CAAC;AAAA,UAAE;AAAC,aAAG,kBAAgB,CAAC,EAAE,GAAE,GAAG,oBAAkB,IAAG,GAAG,kBAAgB,CAAC,CAAC,GAAE,EAAE,oBAAkB,IAAG,EAAE,kBAAgB,CAAC,CAAC,GAAE,EAAE,oBAAkB;AAAA,UAAE,MAAM,WAAW,EAAE,EAAC;AAAA,YAAC,OAAO,WAAU;AAAC,gBAAE,GAAG,SAAS,CAAC,GAAE,EAAE,GAAG,SAAS,CAAC,GAAE,EAAE,GAAG,SAAS,EAAE,GAAE,EAAE,GAAG,SAAS,EAAE;AAAA,YAAC;AAAA,YAAC,cAAa;AAAC,oBAAM,GAAG,SAAS,GAAE,KAAK,mBAAmB;AAAA,YAAC;AAAA,YAAC,gBAAe;AAAC,mBAAK,MAAM,OAAO,YAAY,EAAE,EAAE,QAAS,CAAAA,OAAG;AAAC,gBAAAA,GAAE,aAAa;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,oBAAK,CAACA,IAAE,EAACC,EAAC,IAAE,KAAK,SAAS;AAAE,sBAAMA,OAAID,GAAE,aAAaC,GAAE,WAAW,CAAC,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI;AAAA,YAAE;AAAA,YAAC,YAAW;AAAC,oBAAK,CAAC,EAACD,EAAC,IAAE,KAAK,SAAS;AAAE,sBAAMA,OAAIA,GAAE,OAAO,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI;AAAA,YAAE;AAAA,YAAC,cAAa;AAAC,oBAAK,CAACA,EAAC,IAAE,KAAK,SAAS;AAAE,kBAAG,QAAMA,GAAE;AAAO,oBAAMC,KAAED,GAAE,OAAO;AAAE,cAAAA,GAAE,OAAO,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaC,IAAE,EAAE,GAAG,QAAQ,MAAM;AAAA,YAAC;AAAA,YAAC,WAAU;AAAC,kBAAID,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,KAAK,MAAM,aAAa;AAAE,kBAAG,QAAMA,GAAE,QAAM,CAAC,MAAK,MAAK,MAAK,EAAE;AAAE,oBAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,MAAM,QAAQF,GAAE,KAAK;AAAE,kBAAG,QAAMC,MAAGA,GAAE,QAAQ,aAAW,EAAE,SAAS,QAAM,CAAC,MAAK,MAAK,MAAK,EAAE;AAAE,oBAAME,KAAEF,GAAE;AAAO,qBAAM,CAACE,GAAE,OAAO,QAAOA,IAAEF,IAAEC,EAAC;AAAA,YAAC;AAAA,YAAC,aAAaF,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAM,aAAa;AAAE,kBAAG,CAACA,GAAE;AAAO,oBAAK,CAACC,IAAEC,IAAEC,EAAC,IAAE,KAAK,SAASH,EAAC;AAAE,kBAAG,QAAMG,GAAE;AAAO,oBAAMC,KAAED,GAAE,WAAW;AAAE,cAAAF,GAAE,aAAaG,KAAEL,EAAC,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI;AAAE,kBAAIM,KAAEH,GAAE,UAAU;AAAE,oBAAIH,OAAIM,MAAG,IAAG,KAAK,MAAM,aAAaL,GAAE,QAAMK,IAAEL,GAAE,QAAO,EAAE,GAAG,QAAQ,MAAM;AAAA,YAAC;AAAA,YAAC,mBAAkB;AAAC,mBAAK,aAAa,CAAC;AAAA,YAAC;AAAA,YAAC,oBAAmB;AAAC,mBAAK,aAAa,CAAC;AAAA,YAAC;AAAA,YAAC,UAAUD,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAM,aAAa;AAAE,kBAAG,CAACA,GAAE;AAAO,oBAAK,CAACC,IAAEC,IAAEC,EAAC,IAAE,KAAK,SAASH,EAAC;AAAE,kBAAG,QAAMG,GAAE;AAAO,oBAAMC,KAAEF,GAAE,UAAU;AAAE,cAAAD,GAAE,UAAUG,KAAEL,EAAC,GAAE,KAAK,MAAM,OAAO,EAAE,GAAG,QAAQ,IAAI,GAAEA,KAAE,IAAE,KAAK,MAAM,aAAaC,IAAE,EAAE,GAAG,QAAQ,MAAM,IAAE,KAAK,MAAM,aAAaA,GAAE,QAAME,GAAE,SAAS,QAAOF,GAAE,QAAO,EAAE,GAAG,QAAQ,MAAM;AAAA,YAAC;AAAA,YAAC,iBAAgB;AAAC,mBAAK,UAAU,CAAC;AAAA,YAAC;AAAA,YAAC,iBAAgB;AAAC,mBAAK,UAAU,CAAC;AAAA,YAAC;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,oBAAMC,KAAE,KAAK,MAAM,aAAa;AAAE,kBAAG,QAAMA,GAAE;AAAO,oBAAMC,KAAE,IAAI,MAAMH,EAAC,EAAE,KAAK,CAAC,EAAE,OAAQ,CAAAA,OAAG;AAAC,sBAAME,KAAE,IAAI,MAAMD,EAAC,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;AAAE,uBAAOD,GAAE,OAAOE,IAAE,EAAC,OAAM,GAAG,EAAC,CAAC;AAAA,cAAC,GAAI,KAAI,EAAE,KAAI,OAAOA,GAAE,KAAK,CAAC;AAAE,mBAAK,MAAM,eAAeC,IAAE,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaD,GAAE,OAAM,EAAE,GAAG,QAAQ,MAAM,GAAE,KAAK,cAAc;AAAA,YAAC;AAAA,YAAC,qBAAoB;AAAC,mBAAK,MAAM,GAAG,EAAE,GAAG,OAAO,iBAAiB,CAAAF,OAAG;AAAC,gBAAAA,GAAE,KAAM,CAAAA,OAAG,CAAC,CAAC,CAAC,MAAK,MAAK,SAAQ,OAAO,EAAE,SAASA,GAAE,OAAO,OAAO,MAAI,KAAK,MAAM,KAAK,EAAE,GAAG,OAAO,aAAa,CAACA,IAAEC,IAAEC,OAAI;AAAC,kBAAAA,OAAI,EAAE,GAAG,QAAQ,QAAM,KAAK,cAAc;AAAA,gBAAC,CAAE,GAAE,KAAI;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAC,cAAI,KAAG;AAAG,gBAAM,MAAI,GAAE,EAAE,IAAI,EAAE,GAAG,eAAe;AAAA,UAAE,MAAM,WAAW,EAAE,EAAC;AAAA,YAAC,YAAYF,IAAEC,IAAE;AAAC,kBAAG,MAAMD,IAAEC,EAAC,GAAE,MAAM,QAAQ,KAAK,QAAQ,SAAS,GAAE;AAAC,sBAAMA,KAAE,SAAS,cAAc,KAAK;AAAE,gBAAAA,GAAE,aAAa,QAAO,SAAS,GAAE,SAASD,IAAEC,IAAE;AAAC,wBAAM,QAAQA,GAAE,CAAC,CAAC,MAAIA,KAAE,CAACA,EAAC,IAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,0BAAMC,KAAE,SAAS,cAAc,MAAM;AAAE,oBAAAA,GAAE,UAAU,IAAI,YAAY,GAAED,GAAE,QAAS,CAAAD,OAAG;AAAC,0BAAG,YAAU,OAAOA,GAAE,IAAGE,IAAEF,EAAC;AAAA,2BAAM;AAAC,8BAAMC,KAAE,OAAO,KAAKD,EAAC,EAAE,CAAC,GAAEG,KAAEH,GAAEC,EAAC;AAAE,8BAAM,QAAQE,EAAC,IAAE,SAASH,IAAEC,IAAEC,IAAE;AAAC,gCAAMC,KAAE,SAAS,cAAc,QAAQ;AAAE,0BAAAA,GAAE,UAAU,IAAI,MAAMF,EAAC,EAAE,GAAEC,GAAE,QAAS,CAAAF,OAAG;AAAC,kCAAMC,KAAE,SAAS,cAAc,QAAQ;AAAE,sCAAKD,KAAEC,GAAE,aAAa,SAAQ,OAAOD,EAAC,CAAC,IAAEC,GAAE,aAAa,YAAW,UAAU,GAAEE,GAAE,YAAYF,EAAC;AAAA,0BAAC,CAAE,GAAED,GAAE,YAAYG,EAAC;AAAA,wBAAC,EAAED,IAAED,IAAEE,EAAC,IAAE,GAAGD,IAAED,IAAEE,EAAC;AAAA,sBAAC;AAAA,oBAAC,CAAE,GAAEH,GAAE,YAAYE,EAAC;AAAA,kBAAC,CAAE;AAAA,gBAAC,EAAED,IAAE,KAAK,QAAQ,SAAS,GAAED,GAAE,WAAW,YAAY,aAAaC,IAAED,GAAE,SAAS,GAAE,KAAK,YAAUC;AAAA,cAAC,MAAK,aAAU,OAAO,KAAK,QAAQ,YAAU,KAAK,YAAU,SAAS,cAAc,KAAK,QAAQ,SAAS,IAAE,KAAK,YAAU,KAAK,QAAQ;AAAU,mBAAK,qBAAqB,eAAa,KAAK,UAAU,UAAU,IAAI,YAAY,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,QAAQ,YAAU,OAAO,KAAK,KAAK,QAAQ,QAAQ,EAAE,QAAS,CAAAD,OAAG;AAAC,sBAAMC,KAAE,KAAK,QAAQ,WAAWD,EAAC;AAAE,gBAAAC,MAAG,KAAK,WAAWD,IAAEC,EAAC;AAAA,cAAC,CAAE,GAAE,MAAM,KAAK,KAAK,UAAU,iBAAiB,gBAAgB,CAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,qBAAK,OAAOA,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,GAAG,EAAE,GAAG,OAAO,eAAe,MAAI;AAAC,sBAAK,CAACA,EAAC,IAAE,KAAK,MAAM,UAAU,SAAS;AAAE,qBAAK,OAAOA,EAAC;AAAA,cAAC,CAAE,KAAG,GAAG,MAAM,kCAAiC,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAEC,IAAE;AAAC,mBAAK,SAASD,EAAC,IAAEC;AAAA,YAAC;AAAA,YAAC,OAAOD,IAAE;AAAC,kBAAIC,KAAE,MAAM,KAAKD,GAAE,SAAS,EAAE,KAAM,CAAAA,OAAG,MAAIA,GAAE,QAAQ,KAAK,CAAE;AAAE,kBAAG,CAACC,GAAE;AAAO,kBAAGA,KAAEA,GAAE,MAAM,CAAC,GAAE,aAAWD,GAAE,WAASA,GAAE,aAAa,QAAO,QAAQ,GAAE,QAAM,KAAK,SAASC,EAAC,KAAG,QAAM,KAAK,MAAM,OAAO,MAAMA,EAAC,EAAE,QAAO,KAAK,GAAG,KAAK,4CAA2CA,IAAED,EAAC;AAAE,oBAAME,KAAE,aAAWF,GAAE,UAAQ,WAAS;AAAQ,cAAAA,GAAE,iBAAiBE,IAAG,CAAAA,OAAG;AAAC,oBAAIC;AAAE,oBAAG,aAAWH,GAAE,SAAQ;AAAC,sBAAGA,GAAE,gBAAc,EAAE;AAAO,wBAAMC,KAAED,GAAE,QAAQA,GAAE,aAAa;AAAE,kBAAAG,KAAE,CAACF,GAAE,aAAa,UAAU,MAAIA,GAAE,SAAO;AAAA,gBAAG,MAAM,CAAAE,KAAE,CAACH,GAAE,UAAU,SAAS,WAAW,MAAIA,GAAE,SAAO,CAACA,GAAE,aAAa,OAAO,IAAGE,GAAE,eAAe;AAAE,qBAAK,MAAM,MAAM;AAAE,sBAAK,CAACE,EAAC,IAAE,KAAK,MAAM,UAAU,SAAS;AAAE,oBAAG,QAAM,KAAK,SAASH,EAAC,EAAE,MAAK,SAASA,EAAC,EAAE,KAAK,MAAKE,EAAC;AAAA,yBAAU,KAAK,MAAM,OAAO,MAAMF,EAAC,EAAE,qBAAqB,EAAE,WAAU;AAAC,sBAAGE,KAAE,OAAO,SAASF,EAAC,EAAE,GAAE,CAACE,GAAE;AAAO,uBAAK,MAAM,eAAgB,KAAI,EAAE,KAAI,OAAOC,GAAE,KAAK,EAAE,OAAOA,GAAE,MAAM,EAAE,OAAO,EAAC,CAACH,EAAC,GAAEE,GAAC,CAAC,GAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,gBAAC,MAAM,MAAK,MAAM,OAAOF,IAAEE,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAE,qBAAK,OAAOC,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,SAAS,KAAK,CAACH,IAAED,EAAC,CAAC;AAAA,YAAC;AAAA,YAAC,OAAOA,IAAE;AAAC,oBAAMC,KAAE,QAAMD,KAAE,CAAC,IAAE,KAAK,MAAM,UAAUA,EAAC;AAAE,mBAAK,SAAS,QAAS,CAAAE,OAAG;AAAC,sBAAK,CAACC,IAAEC,EAAC,IAAEF;AAAE,oBAAG,aAAWE,GAAE,SAAQ;AAAC,sBAAIF,KAAE;AAAK,sBAAG,QAAMF,GAAE,CAAAE,KAAE;AAAA,2BAAa,QAAMD,GAAEE,EAAC,EAAE,CAAAD,KAAEE,GAAE,cAAc,kBAAkB;AAAA,2BAAU,CAAC,MAAM,QAAQH,GAAEE,EAAC,CAAC,GAAE;AAAC,wBAAIH,KAAEC,GAAEE,EAAC;AAAE,gCAAU,OAAOH,OAAIA,KAAEA,GAAE,QAAQ,MAAK,KAAK,IAAGE,KAAEE,GAAE,cAAc,iBAAiBJ,EAAC,IAAI;AAAA,kBAAC;AAAC,0BAAME,MAAGE,GAAE,QAAM,IAAGA,GAAE,gBAAc,MAAIF,GAAE,WAAS;AAAA,gBAAE,WAAS,QAAMF,GAAE,CAAAI,GAAE,UAAU,OAAO,WAAW,GAAEA,GAAE,aAAa,gBAAe,OAAO;AAAA,yBAAUA,GAAE,aAAa,OAAO,GAAE;AAAC,wBAAMJ,KAAEC,GAAEE,EAAC,GAAED,KAAEF,OAAII,GAAE,aAAa,OAAO,KAAG,QAAMJ,MAAGA,GAAE,SAAS,MAAII,GAAE,aAAa,OAAO,KAAG,QAAMJ,MAAG,CAACI,GAAE,aAAa,OAAO;AAAE,kBAAAA,GAAE,UAAU,OAAO,aAAYF,EAAC,GAAEE,GAAE,aAAa,gBAAeF,GAAE,SAAS,CAAC;AAAA,gBAAC,OAAK;AAAC,wBAAMF,KAAE,QAAMC,GAAEE,EAAC;AAAE,kBAAAC,GAAE,UAAU,OAAO,aAAYJ,EAAC,GAAEI,GAAE,aAAa,gBAAeJ,GAAE,SAAS,CAAC;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,kBAAMC,KAAE,SAAS,cAAc,QAAQ;AAAE,YAAAA,GAAE,aAAa,QAAO,QAAQ,GAAEA,GAAE,UAAU,IAAI,MAAMF,EAAC,EAAE,GAAEE,GAAE,aAAa,gBAAe,OAAO,GAAE,QAAMD,MAAGC,GAAE,QAAMD,IAAEC,GAAE,aAAa,cAAa,GAAGF,EAAC,KAAKC,EAAC,EAAE,KAAGC,GAAE,aAAa,cAAaF,EAAC,GAAED,GAAE,YAAYG,EAAC;AAAA,UAAC;AAAC,aAAG,WAAS,CAAC,GAAE,GAAG,WAAS,EAAC,WAAU,MAAK,UAAS,EAAC,QAAO;AAAC,kBAAMH,KAAE,KAAK,MAAM,aAAa;AAAE,gBAAG,QAAMA,GAAE,KAAG,MAAIA,GAAE,QAAO;AAAC,oBAAMA,KAAE,KAAK,MAAM,UAAU;AAAE,qBAAO,KAAKA,EAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,wBAAM,KAAK,MAAM,OAAO,MAAMA,IAAE,EAAE,MAAM,MAAM,KAAG,KAAK,MAAM,OAAOA,IAAE,OAAG,EAAE,GAAG,QAAQ,IAAI;AAAA,cAAC,CAAE;AAAA,YAAC,MAAM,MAAK,MAAM,aAAaA,GAAE,OAAMA,GAAE,QAAO,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,GAAE,UAAUA,IAAE;AAAC,kBAAK,EAAC,OAAMC,GAAC,IAAE,KAAK,MAAM,UAAU;AAAE,sBAAQD,MAAG,QAAMC,KAAE,KAAK,MAAM,OAAO,SAAQ,SAAQ,EAAE,GAAG,QAAQ,IAAI,IAAED,MAAG,YAAUC,MAAG,KAAK,MAAM,OAAO,SAAQ,OAAG,EAAE,GAAG,QAAQ,IAAI,GAAE,KAAK,MAAM,OAAO,aAAYD,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,GAAE,OAAOA,IAAE;AAAC,kBAAMC,KAAE,KAAK,MAAM,aAAa,GAAEC,KAAE,KAAK,MAAM,UAAUD,EAAC,GAAEE,KAAE,SAASD,GAAE,UAAQ,GAAE,EAAE;AAAE,gBAAG,SAAOF,MAAG,SAAOA,IAAE;AAAC,kBAAIC,KAAE,SAAOD,KAAE,IAAE;AAAG,wBAAQE,GAAE,cAAYD,MAAG,KAAI,KAAK,MAAM,OAAO,UAASE,KAAEF,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,YAAC;AAAA,UAAC,GAAE,KAAKD,IAAE;AAAC,qBAAKA,OAAIA,KAAE,OAAO,iBAAiB,IAAG,KAAK,MAAM,OAAO,QAAOA,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,GAAE,KAAKA,IAAE;AAAC,kBAAMC,KAAE,KAAK,MAAM,aAAa,GAAEC,KAAE,KAAK,MAAM,UAAUD,EAAC;AAAE,wBAAUD,KAAE,cAAYE,GAAE,QAAM,gBAAcA,GAAE,OAAK,KAAK,MAAM,OAAO,QAAO,OAAG,EAAE,GAAG,QAAQ,IAAI,IAAE,KAAK,MAAM,OAAO,QAAO,aAAY,EAAE,GAAG,QAAQ,IAAI,IAAE,KAAK,MAAM,OAAO,QAAOF,IAAE,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,EAAC;AAAE,gBAAM,KAAG;AAAkN,cAAI,KAAG,EAAC,OAAM,EAAC,IAAG,sMAAqM,QAAO,uMAAsM,OAAM,uMAAsM,SAAQ,sMAAqM,GAAE,YAAW,w1EAAu1E,YAAW,6SAA4S,MAAK,0QAAyQ,OAAM,wVAAuV,MAAK,IAAG,cAAa,IAAG,OAAM,8OAA6O,WAAU,EAAC,IAAG,4UAA2U,KAAI,0UAAyU,GAAE,SAAQ,03CAAy3C,QAAO,EAAC,GAAE,ikBAAgkB,GAAE,ioBAAgoB,GAAE,m1BAAk1B,GAAE,+nBAA8nB,GAAE,yzBAAwzB,GAAE,4sBAA2sB,GAAE,QAAO,wMAAuM,OAAM,gOAA+N,QAAO,EAAC,MAAK,sQAAqQ,MAAK,6PAA4P,GAAE,MAAK,uZAAsZ,MAAK,EAAC,QAAO,wWAAuW,OAAM,0WAAyW,SAAQ,+rBAA8rB,GAAE,QAAO,EAAC,KAAI,2qBAA0qB,OAAM,mjBAAkjB,GAAE,QAAO,ugBAAsgB,OAAM,uZAAsZ,WAAU,mMAAkM,OAAM,itBAAgtB;AAAE,cAAI,KAAG;AAAE,mBAAS,GAAGA,IAAEC,IAAE;AAAC,YAAAD,GAAE,aAAaC,IAAE,GAAG,EAAE,WAASD,GAAE,aAAaC,EAAC,EAAE,EAAE;AAAA,UAAC;AAAC,cAAI,KAAG,MAAK;AAAA,YAAC,YAAYD,IAAE;AAAC,mBAAK,SAAOA,IAAE,KAAK,YAAU,SAAS,cAAc,MAAM,GAAE,KAAK,YAAY,GAAE,KAAK,OAAO,MAAM,UAAQ,QAAO,KAAK,OAAO,WAAW,aAAa,KAAK,WAAU,KAAK,MAAM,GAAE,KAAK,MAAM,iBAAiB,aAAa,MAAI;AAAC,qBAAK,aAAa;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,iBAAiB,WAAW,CAAAA,OAAG;AAAC,wBAAOA,GAAE,KAAI;AAAA,kBAAC,KAAI;AAAQ,yBAAK,aAAa;AAAE;AAAA,kBAAM,KAAI;AAAS,yBAAK,OAAO,GAAEA,GAAE,eAAe;AAAA,gBAAC;AAAA,cAAC,CAAE,GAAE,KAAK,OAAO,iBAAiB,UAAS,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,mBAAK,UAAU,UAAU,OAAO,aAAa,GAAE,GAAG,KAAK,OAAM,eAAe,GAAE,GAAG,KAAK,SAAQ,aAAa;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAE;AAAC,oBAAMC,KAAE,SAAS,cAAc,MAAM;AAAE,cAAAA,GAAE,WAAS,KAAIA,GAAE,aAAa,QAAO,QAAQ,GAAEA,GAAE,UAAU,IAAI,gBAAgB;AAAE,oBAAMC,KAAEF,GAAE,aAAa,OAAO;AAAE,qBAAOE,MAAGD,GAAE,aAAa,cAAaC,EAAC,GAAEF,GAAE,eAAaC,GAAE,aAAa,cAAaD,GAAE,WAAW,GAAEC,GAAE,iBAAiB,SAAS,MAAI;AAAC,qBAAK,WAAWA,IAAE,IAAE;AAAA,cAAC,CAAE,GAAEA,GAAE,iBAAiB,WAAW,CAAAD,OAAG;AAAC,wBAAOA,GAAE,KAAI;AAAA,kBAAC,KAAI;AAAQ,yBAAK,WAAWC,IAAE,IAAE,GAAED,GAAE,eAAe;AAAE;AAAA,kBAAM,KAAI;AAAS,yBAAK,OAAO,GAAEA,GAAE,eAAe;AAAA,gBAAC;AAAA,cAAC,CAAE,GAAEC;AAAA,YAAC;AAAA,YAAC,aAAY;AAAC,oBAAMD,KAAE,SAAS,cAAc,MAAM;AAAE,qBAAOA,GAAE,UAAU,IAAI,iBAAiB,GAAEA,GAAE,YAAU,mJAAkJA,GAAE,WAAS,KAAIA,GAAE,aAAa,QAAO,QAAQ,GAAEA,GAAE,aAAa,iBAAgB,OAAO,GAAE,KAAK,UAAU,YAAYA,EAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,oBAAMA,KAAE,SAAS,cAAc,MAAM;AAAE,cAAAA,GAAE,UAAU,IAAI,mBAAmB,GAAEA,GAAE,aAAa,eAAc,MAAM,GAAEA,GAAE,WAAS,MAAKA,GAAE,KAAG,qBAAqB,EAAE,IAAG,MAAI,GAAE,KAAK,MAAM,aAAa,iBAAgBA,GAAE,EAAE,GAAE,KAAK,UAAQA,IAAE,MAAM,KAAK,KAAK,OAAO,OAAO,EAAE,QAAS,CAAAC,OAAG;AAAC,sBAAMC,KAAE,KAAK,UAAUD,EAAC;AAAE,gBAAAD,GAAE,YAAYE,EAAC,GAAE,SAAKD,GAAE,YAAU,KAAK,WAAWC,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,UAAU,YAAYF,EAAC;AAAA,YAAC;AAAA,YAAC,cAAa;AAAC,oBAAM,KAAK,KAAK,OAAO,UAAU,EAAE,QAAS,CAAAA,OAAG;AAAC,qBAAK,UAAU,aAAaA,GAAE,MAAKA,GAAE,KAAK;AAAA,cAAC,CAAE,GAAE,KAAK,UAAU,UAAU,IAAI,WAAW,GAAE,KAAK,QAAM,KAAK,WAAW,GAAE,KAAK,aAAa;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,MAAM,GAAE,WAAY,MAAI,KAAK,MAAM,MAAM,GAAG,CAAC;AAAA,YAAC;AAAA,YAAC,QAAO;AAAC,mBAAK,UAAU,UAAU,OAAO,aAAa,GAAE,KAAK,MAAM,aAAa,iBAAgB,OAAO,GAAE,KAAK,QAAQ,aAAa,eAAc,MAAM;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAE;AAAC,kBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,oBAAMC,KAAE,KAAK,UAAU,cAAc,cAAc;AAAE,cAAAF,OAAIE,OAAI,QAAMA,MAAGA,GAAE,UAAU,OAAO,aAAa,GAAE,QAAMF,OAAIA,GAAE,UAAU,IAAI,aAAa,GAAE,KAAK,OAAO,gBAAc,MAAM,KAAKA,GAAE,WAAW,QAAQ,EAAE,QAAQA,EAAC,GAAEA,GAAE,aAAa,YAAY,IAAE,KAAK,MAAM,aAAa,cAAaA,GAAE,aAAa,YAAY,CAAC,IAAE,KAAK,MAAM,gBAAgB,YAAY,GAAEA,GAAE,aAAa,YAAY,IAAE,KAAK,MAAM,aAAa,cAAaA,GAAE,aAAa,YAAY,CAAC,IAAE,KAAK,MAAM,gBAAgB,YAAY,GAAEC,OAAI,KAAK,OAAO,cAAc,IAAI,MAAM,QAAQ,CAAC,GAAE,KAAK,MAAM;AAAA,YAAI;AAAA,YAAC,SAAQ;AAAC,kBAAID;AAAE,kBAAG,KAAK,OAAO,gBAAc,IAAG;AAAC,sBAAMC,KAAE,KAAK,UAAU,cAAc,oBAAoB,EAAE,SAAS,KAAK,OAAO,aAAa;AAAE,gBAAAD,KAAE,KAAK,OAAO,QAAQ,KAAK,OAAO,aAAa,GAAE,KAAK,WAAWC,EAAC;AAAA,cAAC,MAAM,MAAK,WAAW,IAAI;AAAE,oBAAMA,KAAE,QAAMD,MAAGA,OAAI,KAAK,OAAO,cAAc,kBAAkB;AAAE,mBAAK,MAAM,UAAU,OAAO,aAAYC,EAAC;AAAA,YAAC;AAAA,UAAC,GAAE,KAAG,cAAc,GAAE;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,oBAAMD,EAAC,GAAE,KAAK,MAAM,YAAUC,IAAE,KAAK,UAAU,UAAU,IAAI,iBAAiB,GAAE,MAAM,KAAK,KAAK,UAAU,iBAAiB,iBAAiB,CAAC,EAAE,MAAM,GAAE,CAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,gBAAAA,GAAE,UAAU,IAAI,YAAY;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,UAAUA,IAAE;AAAC,oBAAMC,KAAE,MAAM,UAAUD,EAAC;AAAE,qBAAOC,GAAE,MAAM,kBAAgBD,GAAE,aAAa,OAAO,KAAG,IAAGC;AAAA,YAAC;AAAA,YAAC,WAAWD,IAAEC,IAAE;AAAC,oBAAM,WAAWD,IAAEC,EAAC;AAAE,oBAAMC,KAAE,KAAK,MAAM,cAAc,iBAAiB,GAAEC,KAAEH,MAAGA,GAAE,aAAa,YAAY,KAAG;AAAG,cAAAE,OAAI,WAASA,GAAE,UAAQA,GAAE,MAAM,SAAOC,KAAED,GAAE,MAAM,OAAKC;AAAA,YAAE;AAAA,UAAC,GAAE,KAAG,cAAc,GAAE;AAAA,YAAC,YAAYH,IAAEC,IAAE;AAAC,oBAAMD,EAAC,GAAE,KAAK,UAAU,UAAU,IAAI,gBAAgB,GAAE,MAAM,KAAK,KAAK,UAAU,iBAAiB,iBAAiB,CAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,gBAAAA,GAAE,YAAUC,GAAED,GAAE,aAAa,YAAY,KAAG,EAAE;AAAA,cAAC,CAAE,GAAE,KAAK,cAAY,KAAK,UAAU,cAAc,cAAc,GAAE,KAAK,WAAW,KAAK,WAAW;AAAA,YAAC;AAAA,YAAC,WAAWA,IAAEC,IAAE;AAAC,oBAAM,WAAWD,IAAEC,EAAC;AAAE,oBAAMC,KAAEF,MAAG,KAAK;AAAY,kBAAG,QAAME,IAAE;AAAC,oBAAG,KAAK,MAAM,cAAYA,GAAE,UAAU;AAAO,qBAAK,MAAM,YAAUA,GAAE;AAAA,cAAS;AAAA,YAAC;AAAA,UAAC,GAAE,KAAG,MAAK;AAAA,YAAC,YAAYF,IAAEC,IAAE;AAAC,mBAAK,QAAMD,IAAE,KAAK,kBAAgBC,MAAG,SAAS,MAAK,KAAK,OAAKD,GAAE,aAAa,YAAY,GAAE,KAAK,KAAK,YAAU,KAAK,YAAY,WAAU,CAAAA,OAAG;AAAC,sBAAK,EAAC,WAAUC,GAAC,IAAE,iBAAiBD,IAAE,IAAI;AAAE,uBAAM,cAAYC,MAAG,WAASA;AAAA,cAAC,GAAG,KAAK,MAAM,IAAI,KAAG,KAAK,MAAM,KAAK,iBAAiB,UAAU,MAAI;AAAC,qBAAK,KAAK,MAAM,YAAU,KAAG,KAAK,MAAM,KAAK,YAAU;AAAA,cAAI,CAAE,GAAE,KAAK,KAAK;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,mBAAK,KAAK,UAAU,IAAI,WAAW;AAAA,YAAC;AAAA,YAAC,SAASD,IAAE;AAAC,oBAAMC,KAAED,GAAE,OAAKA,GAAE,QAAM,IAAE,KAAK,KAAK,cAAY,GAAEE,KAAEF,GAAE,SAAO,KAAK,MAAM,KAAK;AAAU,mBAAK,KAAK,MAAM,OAAK,GAAGC,EAAC,MAAK,KAAK,KAAK,MAAM,MAAI,GAAGC,EAAC,MAAK,KAAK,KAAK,UAAU,OAAO,SAAS;AAAE,oBAAMC,KAAE,KAAK,gBAAgB,sBAAsB,GAAEC,KAAE,KAAK,KAAK,sBAAsB;AAAE,kBAAIC,KAAE;AAAE,kBAAGD,GAAE,QAAMD,GAAE,UAAQE,KAAEF,GAAE,QAAMC,GAAE,OAAM,KAAK,KAAK,MAAM,OAAK,GAAGH,KAAEI,EAAC,OAAMD,GAAE,OAAKD,GAAE,SAAOE,KAAEF,GAAE,OAAKC,GAAE,MAAK,KAAK,KAAK,MAAM,OAAK,GAAGH,KAAEI,EAAC,OAAMD,GAAE,SAAOD,GAAE,QAAO;AAAC,sBAAMF,KAAEG,GAAE,SAAOA,GAAE,KAAID,KAAEH,GAAE,SAAOA,GAAE,MAAIC;AAAE,qBAAK,KAAK,MAAM,MAAIC,KAAEC,KAAE,MAAK,KAAK,KAAK,UAAU,IAAI,SAAS;AAAA,cAAC;AAAC,qBAAOE;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,mBAAK,KAAK,UAAU,OAAO,YAAY,GAAE,KAAK,KAAK,UAAU,OAAO,WAAW;AAAA,YAAC;AAAA,UAAC,GAAE,KAAG,EAAE,IAAI,GAAE,KAAG,EAAE,IAAI,GAAE,KAAG,EAAE,IAAI;AAAE,gBAAM,KAAG,CAAC,OAAG,UAAS,SAAQ,SAAS,GAAE,KAAG,CAAC,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,WAAU,SAAS,GAAE,KAAG,CAAC,OAAG,SAAQ,WAAW,GAAE,KAAG,CAAC,KAAI,KAAI,KAAI,KAAE,GAAE,KAAG,CAAC,SAAQ,OAAG,SAAQ,MAAM;AAAA,UAAE,MAAM,WAAW,GAAG,EAAC;AAAA,YAAC,YAAYL,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC;AAAE,oBAAMC,KAAE,CAAAD,OAAG;AAAC,yBAAS,KAAK,SAASD,GAAE,IAAI,KAAG,QAAM,KAAK,WAAS,KAAK,QAAQ,KAAK,SAASC,GAAE,MAAM,KAAG,SAAS,kBAAgB,KAAK,QAAQ,WAAS,KAAK,MAAM,SAAS,KAAG,KAAK,QAAQ,KAAK,GAAE,QAAM,KAAK,WAAS,KAAK,QAAQ,QAAS,CAAAD,OAAG;AAAC,kBAAAA,GAAE,UAAU,SAASC,GAAE,MAAM,KAAGD,GAAE,MAAM;AAAA,gBAAC,CAAE,KAAG,SAAS,KAAK,oBAAoB,SAAQE,EAAC;AAAA,cAAC;AAAE,cAAAF,GAAE,QAAQ,UAAU,SAAQ,SAAS,MAAKE,EAAC;AAAA,YAAC;AAAA,YAAC,UAAUF,IAAE;AAAC,oBAAMC,KAAE,MAAM,UAAUD,EAAC;AAAE,qBAAM,cAAYA,MAAG,KAAK,cAAcC,EAAC,GAAEA;AAAA,YAAC;AAAA,YAAC,aAAaD,IAAEC,IAAE;AAAC,oBAAM,KAAKD,EAAC,EAAE,QAAS,CAAAA,OAAG;AAAC,iBAACA,GAAE,aAAa,OAAO,KAAG,IAAI,MAAM,KAAK,EAAE,QAAS,CAAAE,OAAG;AAAC,sBAAGA,GAAE,WAAW,KAAK,MAAIA,KAAEA,GAAE,MAAM,CAAC,GAAE,QAAMD,GAAEC,EAAC,GAAG,KAAG,gBAAcA,GAAE,CAAAF,GAAE,YAAUC,GAAEC,EAAC,EAAE,EAAE,IAAED,GAAEC,EAAC,EAAE;AAAA,2BAAY,YAAU,OAAOD,GAAEC,EAAC,EAAE,CAAAF,GAAE,YAAUC,GAAEC,EAAC;AAAA,uBAAM;AAAC,0BAAMC,KAAEH,GAAE,SAAO;AAAG,4BAAMG,MAAGF,GAAEC,EAAC,EAAEC,EAAC,MAAIH,GAAE,YAAUC,GAAEC,EAAC,EAAEC,EAAC;AAAA,kBAAE;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,aAAaH,IAAEC,IAAE;AAAC,mBAAK,UAAQ,MAAM,KAAKD,EAAC,EAAE,IAAK,CAAAA,OAAG;AAAC,oBAAGA,GAAE,UAAU,SAAS,UAAU,MAAI,QAAMA,GAAE,cAAc,QAAQ,KAAG,GAAGA,IAAE,EAAE,GAAE,YAAU,OAAOC,GAAE,OAAO,QAAO,IAAI,GAAGD,IAAEC,GAAE,KAAK;AAAE,oBAAGD,GAAE,UAAU,SAAS,eAAe,KAAGA,GAAE,UAAU,SAAS,UAAU,GAAE;AAAC,wBAAME,KAAEF,GAAE,UAAU,SAAS,eAAe,IAAE,eAAa;AAAQ,yBAAO,QAAMA,GAAE,cAAc,QAAQ,KAAG,GAAGA,IAAE,IAAG,iBAAeE,KAAE,YAAU,SAAS,GAAE,IAAI,GAAGF,IAAEC,GAAEC,EAAC,CAAC;AAAA,gBAAC;AAAC,uBAAO,QAAMF,GAAE,cAAc,QAAQ,MAAIA,GAAE,UAAU,SAAS,SAAS,IAAE,GAAGA,IAAE,EAAE,IAAEA,GAAE,UAAU,SAAS,WAAW,IAAE,GAAGA,IAAE,EAAE,IAAEA,GAAE,UAAU,SAAS,SAAS,KAAG,GAAGA,IAAE,EAAE,IAAG,IAAI,GAAGA,EAAC;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,GAAG,GAAG,EAAE,OAAO,eAAe,MAAI;AAAC,qBAAK,QAAQ,QAAS,CAAAA,OAAG;AAAC,kBAAAA,GAAE,OAAO;AAAA,gBAAC,CAAE;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,UAAC;AAAC,aAAG,YAAU,GAAE,GAAG,GAAG,CAAC,GAAE,GAAG,EAAE,UAAS,EAAC,SAAQ,EAAC,SAAQ,EAAC,UAAS,EAAC,UAAS;AAAC,iBAAK,MAAM,MAAM,QAAQ,KAAK,SAAS;AAAA,UAAC,GAAE,QAAO;AAAC,gBAAIA,KAAE,KAAK,UAAU,cAAc,2BAA2B;AAAE,oBAAMA,OAAIA,KAAE,SAAS,cAAc,OAAO,GAAEA,GAAE,aAAa,QAAO,MAAM,GAAEA,GAAE,aAAa,UAAS,KAAK,MAAM,SAAS,QAAQ,UAAU,KAAK,IAAI,CAAC,GAAEA,GAAE,UAAU,IAAI,UAAU,GAAEA,GAAE,iBAAiB,UAAU,MAAI;AAAC,oBAAMC,KAAE,KAAK,MAAM,aAAa,IAAE;AAAE,mBAAK,MAAM,SAAS,OAAOA,IAAED,GAAE,KAAK,GAAEA,GAAE,QAAM;AAAA,YAAE,CAAE,GAAE,KAAK,UAAU,YAAYA,EAAC,IAAGA,GAAE,MAAM;AAAA,UAAC,GAAE,QAAO;AAAC,iBAAK,MAAM,MAAM,QAAQ,KAAK,OAAO;AAAA,UAAC,EAAC,EAAC,EAAC,EAAC,CAAC;AAAA,UAAE,MAAM,WAAW,GAAE;AAAA,YAAC,YAAYA,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,UAAQ,KAAK,KAAK,cAAc,oBAAoB,GAAE,KAAK,OAAO;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,QAAQ,iBAAiB,WAAW,CAAAD,OAAG;AAAC,4BAAUA,GAAE,OAAK,KAAK,KAAK,GAAEA,GAAE,eAAe,KAAG,aAAWA,GAAE,QAAM,KAAK,OAAO,GAAEA,GAAE,eAAe;AAAA,cAAE,CAAE;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,KAAK,GAAE,KAAK,aAAa;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,kBAAIA,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE,QAAOC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,IAAE,UAAU,CAAC,IAAE;AAAK,kBAAG,KAAK,KAAK,UAAU,OAAO,WAAW,GAAE,KAAK,KAAK,UAAU,IAAI,YAAY,GAAE,QAAM,KAAK,QAAQ;AAAO,sBAAMA,KAAE,KAAK,QAAQ,QAAMA,KAAED,OAAI,KAAK,KAAK,aAAa,WAAW,MAAI,KAAK,QAAQ,QAAM;AAAI,oBAAME,KAAE,KAAK,MAAM,UAAU,KAAK,MAAM,UAAU,UAAU;AAAE,sBAAMA,MAAG,KAAK,SAASA,EAAC,GAAE,KAAK,QAAQ,OAAO,GAAE,KAAK,QAAQ,aAAa,eAAc,KAAK,QAAQ,aAAa,QAAQF,EAAC,EAAE,KAAG,EAAE,GAAE,KAAK,KAAK,aAAa,aAAYA,EAAC;AAAA,YAAC;AAAA,YAAC,eAAc;AAAC,mBAAK,MAAM,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,kBAAG,EAAC,OAAMA,GAAC,IAAE,KAAK;AAAQ,sBAAO,KAAK,KAAK,aAAa,WAAW,GAAE;AAAA,gBAAC,KAAI,QAAO;AAAC,wBAAK,EAAC,WAAUC,GAAC,IAAE,KAAK,MAAM;AAAK,uBAAK,aAAW,KAAK,MAAM,WAAW,KAAK,WAAU,QAAOD,IAAE,GAAG,EAAE,QAAQ,IAAI,GAAE,OAAO,KAAK,cAAY,KAAK,aAAa,GAAE,KAAK,MAAM,OAAO,QAAOA,IAAE,GAAG,EAAE,QAAQ,IAAI,IAAG,KAAK,MAAM,KAAK,YAAUC;AAAE;AAAA,gBAAK;AAAA,gBAAC,KAAI;AAAQ,kBAAAD,KAAE,SAASA,IAAE;AAAC,wBAAIC,KAAED,GAAE,MAAM,4EAA4E,KAAGA,GAAE,MAAM,gEAAgE;AAAE,2BAAOC,KAAE,GAAGA,GAAE,CAAC,KAAG,OAAO,4BAA4BA,GAAE,CAAC,CAAC,iBAAeA,KAAED,GAAE,MAAM,gDAAgD,KAAG,GAAGC,GAAE,CAAC,KAAG,OAAO,6BAA6BA,GAAE,CAAC,CAAC,MAAID;AAAA,kBAAC,EAAEA,EAAC;AAAA,gBAAE,KAAI,WAAU;AAAC,sBAAG,CAACA,GAAE;AAAM,wBAAMC,KAAE,KAAK,MAAM,aAAa,IAAE;AAAE,sBAAG,QAAMA,IAAE;AAAC,0BAAMC,KAAED,GAAE,QAAMA,GAAE;AAAO,yBAAK,MAAM,YAAYC,IAAE,KAAK,KAAK,aAAa,WAAW,GAAEF,IAAE,GAAG,EAAE,QAAQ,IAAI,GAAE,cAAY,KAAK,KAAK,aAAa,WAAW,KAAG,KAAK,MAAM,WAAWE,KAAE,GAAE,KAAI,GAAG,EAAE,QAAQ,IAAI,GAAE,KAAK,MAAM,aAAaA,KAAE,GAAE,GAAG,EAAE,QAAQ,IAAI;AAAA,kBAAC;AAAC;AAAA,gBAAK;AAAA,cAAC;AAAC,mBAAK,QAAQ,QAAM,IAAG,KAAK,KAAK;AAAA,YAAC;AAAA,UAAC;AAAC,mBAAS,GAAGF,IAAEC,IAAE;AAAC,gBAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,YAAAD,GAAE,QAAS,CAAAA,OAAG;AAAC,oBAAME,KAAE,SAAS,cAAc,QAAQ;AAAE,cAAAF,OAAIC,KAAEC,GAAE,aAAa,YAAW,UAAU,IAAEA,GAAE,aAAa,SAAQ,OAAOF,EAAC,CAAC,GAAED,GAAE,YAAYG,EAAC;AAAA,YAAC,CAAE;AAAA,UAAC;AAAC,cAAI,KAAG,EAAE,IAAI;AAAE,gBAAM,KAAG,CAAC,CAAC,QAAO,UAAS,MAAM,GAAE,CAAC,EAAC,QAAO,EAAC,GAAE,EAAC,QAAO,EAAC,GAAE,YAAY,CAAC;AAAA,UAAE,MAAM,WAAW,GAAE;AAAA,YAAC,OAAO,WAAS,CAAC,0CAAyC,mCAAkC,oGAAmG,4BAA2B,QAAQ,EAAE,KAAK,EAAE;AAAA,YAAE,YAAYH,IAAEC,IAAE;AAAC,oBAAMD,IAAEC,EAAC,GAAE,KAAK,MAAM,GAAG,GAAG,EAAE,OAAO,eAAe,CAACD,IAAEC,IAAEC,IAAEC,OAAI;AAAC,oBAAGH,OAAI,GAAG,EAAE,OAAO,iBAAiB,KAAG,QAAMC,MAAGA,GAAE,SAAO,KAAGE,OAAI,GAAG,EAAE,QAAQ,MAAK;AAAC,uBAAK,KAAK,GAAE,KAAK,KAAK,MAAM,OAAK,OAAM,KAAK,KAAK,MAAM,QAAM,IAAG,KAAK,KAAK,MAAM,QAAM,GAAG,KAAK,KAAK,WAAW;AAAK,wBAAMH,KAAE,KAAK,MAAM,SAASC,GAAE,OAAMA,GAAE,MAAM;AAAE,sBAAG,MAAID,GAAE,QAAO;AAAC,0BAAMA,KAAE,KAAK,MAAM,UAAUC,EAAC;AAAE,4BAAMD,MAAG,KAAK,SAASA,EAAC;AAAA,kBAAC,OAAK;AAAC,0BAAME,KAAEF,GAAEA,GAAE,SAAO,CAAC,GAAEG,KAAE,KAAK,MAAM,SAASD,EAAC,GAAEE,KAAE,KAAK,IAAIF,GAAE,OAAO,IAAE,GAAED,GAAE,QAAMA,GAAE,SAAOE,EAAC,GAAEE,KAAE,KAAK,MAAM,UAAU,IAAI,GAAG,EAAEF,IAAEC,EAAC,CAAC;AAAE,4BAAMC,MAAG,KAAK,SAASA,EAAC;AAAA,kBAAC;AAAA,gBAAC,MAAM,UAAS,kBAAgB,KAAK,WAAS,KAAK,MAAM,SAAS,KAAG,KAAK,KAAK;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,KAAK,cAAc,WAAW,EAAE,iBAAiB,SAAS,MAAI;AAAC,qBAAK,KAAK,UAAU,OAAO,YAAY;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,GAAG,GAAG,EAAE,OAAO,iBAAiB,MAAI;AAAC,2BAAY,MAAI;AAAC,sBAAG,KAAK,KAAK,UAAU,SAAS,WAAW,EAAE;AAAO,wBAAML,KAAE,KAAK,MAAM,aAAa;AAAE,sBAAG,QAAMA,IAAE;AAAC,0BAAMC,KAAE,KAAK,MAAM,UAAUD,EAAC;AAAE,4BAAMC,MAAG,KAAK,SAASA,EAAC;AAAA,kBAAC;AAAA,gBAAC,GAAG,CAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,SAAQ;AAAC,mBAAK,KAAK;AAAA,YAAC;AAAA,YAAC,SAASD,IAAE;AAAC,oBAAMC,KAAE,MAAM,SAASD,EAAC,GAAEE,KAAE,KAAK,KAAK,cAAc,mBAAmB;AAAE,qBAAOA,GAAE,MAAM,aAAW,IAAG,MAAID,OAAIC,GAAE,MAAM,aAAW,KAAGD,KAAEC,GAAE,cAAY,IAAE,OAAMD;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,MAAM,WAAW,GAAE;AAAA,YAAC,YAAYD,IAAEC,IAAE;AAAC,sBAAMA,GAAE,QAAQ,WAAS,QAAMA,GAAE,QAAQ,QAAQ,cAAYA,GAAE,QAAQ,QAAQ,YAAU,KAAI,MAAMD,IAAEC,EAAC,GAAE,KAAK,MAAM,UAAU,UAAU,IAAI,WAAW;AAAA,YAAC;AAAA,YAAC,cAAcD,IAAE;AAAC,mBAAK,UAAQ,IAAI,GAAG,KAAK,OAAM,KAAK,QAAQ,MAAM,GAAE,QAAMA,GAAE,cAAY,KAAK,QAAQ,KAAK,YAAYA,GAAE,SAAS,GAAE,KAAK,aAAaA,GAAE,UAAU,iBAAiB,QAAQ,GAAE,EAAE,GAAE,KAAK,aAAaA,GAAE,UAAU,iBAAiB,QAAQ,GAAE,EAAE;AAAA,YAAE;AAAA,UAAC;AAAC,aAAG,YAAU,GAAE,GAAG,GAAG,CAAC,GAAE,GAAG,UAAS,EAAC,SAAQ,EAAC,SAAQ,EAAC,UAAS,EAAC,KAAKA,IAAE;AAAC,YAAAA,KAAE,KAAK,MAAM,MAAM,QAAQ,KAAK,IAAE,KAAK,MAAM,OAAO,QAAO,OAAG,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,EAAC,EAAC,EAAC,CAAC;AAAE,gBAAM,KAAG,CAAC,CAAC,EAAC,QAAO,CAAC,KAAI,KAAI,KAAI,KAAE,EAAC,CAAC,GAAE,CAAC,QAAO,UAAS,aAAY,MAAM,GAAE,CAAC,EAAC,MAAK,UAAS,GAAE,EAAC,MAAK,SAAQ,CAAC,GAAE,CAAC,OAAO,CAAC;AAAA,UAAE,MAAM,WAAW,GAAE;AAAA,YAAC,OAAO,WAAS,CAAC,2FAA0F,oGAAmG,6BAA4B,2BAA2B,EAAE,KAAK,EAAE;AAAA,YAAE,UAAQ,KAAK,KAAK,cAAc,cAAc;AAAA,YAAE,SAAQ;AAAC,oBAAM,OAAO,GAAE,KAAK,KAAK,cAAc,aAAa,EAAE,iBAAiB,SAAS,CAAAA,OAAG;AAAC,qBAAK,KAAK,UAAU,SAAS,YAAY,IAAE,KAAK,KAAK,IAAE,KAAK,KAAK,QAAO,KAAK,QAAQ,WAAW,GAAEA,GAAE,eAAe;AAAA,cAAC,CAAE,GAAE,KAAK,KAAK,cAAc,aAAa,EAAE,iBAAiB,SAAS,CAAAA,OAAG;AAAC,oBAAG,QAAM,KAAK,WAAU;AAAC,wBAAMA,KAAE,KAAK;AAAU,uBAAK,aAAa,GAAE,KAAK,MAAM,WAAWA,IAAE,QAAO,OAAG,GAAG,EAAE,QAAQ,IAAI,GAAE,OAAO,KAAK;AAAA,gBAAS;AAAC,gBAAAA,GAAE,eAAe,GAAE,KAAK,KAAK;AAAA,cAAC,CAAE,GAAE,KAAK,MAAM,GAAG,GAAG,EAAE,OAAO,kBAAkB,CAACA,IAAEC,IAAEC,OAAI;AAAC,oBAAG,QAAMF,IAAE;AAAC,sBAAG,MAAIA,GAAE,UAAQE,OAAI,GAAG,EAAE,QAAQ,MAAK;AAAC,0BAAK,CAACD,IAAEC,EAAC,IAAE,KAAK,MAAM,OAAO,WAAW,GAAEF,GAAE,KAAK;AAAE,wBAAG,QAAMC,IAAE;AAAC,2BAAK,YAAU,IAAI,GAAG,EAAED,GAAE,QAAME,IAAED,GAAE,OAAO,CAAC;AAAE,4BAAME,KAAE,EAAE,QAAQF,GAAE,OAAO;AAAE,2BAAK,QAAQ,cAAYE,IAAE,KAAK,QAAQ,aAAa,QAAOA,EAAC,GAAE,KAAK,KAAK;AAAE,4BAAMC,KAAE,KAAK,MAAM,UAAU,KAAK,SAAS;AAAE,6BAAO,MAAK,QAAMA,MAAG,KAAK,SAASA,EAAC;AAAA,oBAAE;AAAA,kBAAC,MAAM,QAAO,KAAK;AAAU,uBAAK,KAAK;AAAA,gBAAC;AAAA,cAAC,CAAE;AAAA,YAAC;AAAA,YAAC,OAAM;AAAC,oBAAM,KAAK,GAAE,KAAK,KAAK,gBAAgB,WAAW;AAAA,YAAC;AAAA,UAAC;AAAA,UAAC,MAAM,WAAW,GAAE;AAAA,YAAC,YAAYJ,IAAEC,IAAE;AAAC,sBAAMA,GAAE,QAAQ,WAAS,QAAMA,GAAE,QAAQ,QAAQ,cAAYA,GAAE,QAAQ,QAAQ,YAAU,KAAI,MAAMD,IAAEC,EAAC,GAAE,KAAK,MAAM,UAAU,UAAU,IAAI,SAAS;AAAA,YAAC;AAAA,YAAC,cAAcD,IAAE;AAAC,sBAAMA,GAAE,cAAYA,GAAE,UAAU,UAAU,IAAI,SAAS,GAAE,KAAK,aAAaA,GAAE,UAAU,iBAAiB,QAAQ,GAAE,EAAE,GAAE,KAAK,aAAaA,GAAE,UAAU,iBAAiB,QAAQ,GAAE,EAAE,GAAE,KAAK,UAAQ,IAAI,GAAG,KAAK,OAAM,KAAK,QAAQ,MAAM,GAAEA,GAAE,UAAU,cAAc,UAAU,KAAG,KAAK,MAAM,SAAS,WAAW,EAAC,KAAI,KAAI,UAAS,KAAE,GAAG,CAACC,IAAEC,OAAI;AAAC,gBAAAF,GAAE,SAAS,KAAK,KAAKA,IAAE,CAACE,GAAE,OAAO,IAAI;AAAA,cAAC,CAAE;AAAA,YAAE;AAAA,UAAC;AAAC,aAAG,YAAU,GAAE,GAAG,GAAG,CAAC,GAAE,GAAG,UAAS,EAAC,SAAQ,EAAC,SAAQ,EAAC,UAAS,EAAC,KAAKF,IAAE;AAAC,gBAAGA,IAAE;AAAC,oBAAMA,KAAE,KAAK,MAAM,aAAa;AAAE,kBAAG,QAAMA,MAAG,MAAIA,GAAE,OAAO;AAAO,kBAAIC,KAAE,KAAK,MAAM,QAAQD,EAAC;AAAE,+BAAiB,KAAKC,EAAC,KAAG,MAAIA,GAAE,QAAQ,SAAS,MAAIA,KAAE,UAAUA,EAAC;AAAI,oBAAK,EAAC,SAAQC,GAAC,IAAE,KAAK,MAAM;AAAM,cAAAA,GAAE,KAAK,QAAOD,EAAC;AAAA,YAAC,MAAM,MAAK,MAAM,OAAO,QAAO,OAAG,EAAE,GAAG,QAAQ,IAAI;AAAA,UAAC,EAAC,EAAC,EAAC,EAAC,CAAC;AAAE,cAAI,KAAG;AAAG,UAAAD,GAAE,QAAQ,SAAS,EAAC,mCAAkC,EAAE,IAAG,2BAA0BC,GAAE,IAAG,gCAA+B,EAAE,GAAE,2BAA0B,EAAE,IAAG,+BAA8B,EAAE,IAAG,0BAAyB,EAAE,GAAE,0BAAyB,EAAE,GAAE,2BAA0BA,GAAE,IAAG,gCAA+B,EAAE,GAAE,2BAA0B,EAAE,IAAG,+BAA8B,EAAE,IAAG,0BAAyB,EAAE,GAAE,0BAAyB,EAAE,EAAC,GAAE,IAAE,GAAED,GAAE,QAAQ,SAAS,EAAC,iBAAgBC,GAAE,IAAG,qBAAoB,EAAE,IAAG,kBAAiB,GAAE,sBAAqB,EAAE,GAAE,iBAAgB,EAAE,IAAG,gBAAe,EAAE,GAAE,gBAAe,EAAE,GAAE,sBAAqB,GAAE,sBAAqB,EAAE,IAAG,kBAAiB,GAAE,gBAAe,GAAE,gBAAe,GAAE,gBAAe,EAAE,IAAG,kBAAiB,cAAc,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAS,OAAO,UAAQ,CAAC,MAAK,GAAG;AAAA,UAAC,GAAE,gBAAe,GAAE,kBAAiB,GAAE,kBAAiB,cAAc,EAAC;AAAA,YAAC,OAAO,WAAS;AAAA,YAAS,OAAO,UAAQ,CAAC,KAAI,QAAQ;AAAA,UAAC,GAAE,qBAAoB,GAAE,mBAAkB,GAAE,iBAAgB,GAAE,iBAAgB,GAAE,kBAAiB,GAAE,iBAAgB,IAAG,mBAAkB,IAAG,iBAAgB,IAAG,eAAc,IAAG,YAAW,IAAG,aAAY,IAAG,kBAAiB,IAAG,mBAAkB,IAAG,cAAa,GAAE,GAAE,IAAE;AAAE,cAAI,KAAGD,GAAE;AAAA,QAAO,EAAE,GAAE,EAAE;AAAA,MAAO,EAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["t", "e", "n", "r", "i", "s", "o", "l", "a", "c", "u", "h", "d", "f", "p", "g", "m", "b", "y", "A", "x", "v", "N", "w", "E", "T", "j", "R", "I", "B", "M", "q", "L", "S", "O", "C", "k", "_"]}
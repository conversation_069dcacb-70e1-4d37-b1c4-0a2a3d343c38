import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';

import { routes } from './app.routes';
import { API_BASE_URL } from '../shared/service-proxies/service-proxies';
import { HttpInterceptorService } from '../shared/services/http-interceptor.service';
import { en_US, provideNzI18n } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { FormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideMarkdown } from 'ngx-markdown';



registerLocaleData(en);

export function getRemoteServiceBaseUrl(): string {
  let url = (window as any).location.host;
  if (url.indexOf("localhost") >= 0)
    return 'https://localhost:44314';
  else
    return `https://aihub-api.3dbotics.com`;
}


export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(),
    { provide: API_BASE_URL, useFactory: getRemoteServiceBaseUrl },
    provideHttpClient(withInterceptors([HttpInterceptorService])), provideNzI18n(en_US), importProvidersFrom(FormsModule), provideAnimationsAsync(), provideHttpClient(),provideMarkdown()
  ]
};

<!-- Source References Sidebar - Slides in from the right -->
<div *ngIf="showSidebar"
  class="flex-1 w-full h-full overflow-y-auto transition-all duration-300 ease-in-out"
  [ngClass]="{
    'translate-x-0': showSidebar,
    'translate-x-full': !showSidebar,
    'bg-[#2b2b33] text-white border-l border-[#3a3a45]': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)] border-l border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
  }">

  <!-- Search Results Header - Sticky -->
  <div
    class="flex items-center justify-between p-4 sticky top-0 z-10 shadow-sm backdrop-blur-sm transition-all duration-200"
    [ngClass]="{
      'bg-[#2b2b33] border-b border-[#3a3a45]': themeService.isDarkMode(),
      'bg-[var(--background-white)] border-b border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
    }">
    <h3 class="font-semibold flex items-center gap-2"
        [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
      <i class="ri-search-line" [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
      <span>Source References</span>
    </h3>
    <button (click)="onCloseSidebar()"
      [ngClass]="{
        'text-gray-300 hover:text-white hover:bg-[#3a3a45]': themeService.isDarkMode(),
        'text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
      }"
      class="transition-colors p-1 rounded-full">
      <i class="ri-close-line text-xl"></i>
    </button>
  </div>

  <!-- Search Results Content -->
  <div class="p-4">
    <!-- Source References Section -->
    <div class="mb-6">
      <div
        class="sticky top-[57px] pt-2 pb-3 z-[5] shadow-sm backdrop-blur-sm transition-all duration-200"
        [ngClass]="{
          'bg-[#2b2b33]': themeService.isDarkMode(),
          'bg-[var(--background-white)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-2 mb-2">
          <div class="flex items-center gap-1 px-2 py-1 rounded-md"
               [ngClass]="{
                 'bg-[#1E1E1E] text-white': themeService.isDarkMode(),
                 'bg-[var(--hover-blue-gray)] text-[var(--text-dark)]': !themeService.isDarkMode()
               }">
            <i class="ri-file-list-line" [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
            <i class="ri-global-line" [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
          </div>
          <h3 class="font-semibold"
              [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
            <span *ngIf="currentSourceName">{{ currentSourceName }} • </span>{{searchResults.length}} Source References
          </h3>
        </div>

        <div class="text-xs"
             [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
          References used to generate the response
        </div>
      </div>

      <div class="space-y-4">
        <div *ngFor="let result of searchResults"
          class="p-4 rounded-lg shadow-sm transition-all cursor-pointer group"
          [ngClass]="{
            'bg-[#3a3a45] border border-[#4a4a55] hover:border-[#00c39a]': themeService.isDarkMode(),
            'bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)]': !themeService.isDarkMode()
          }">
          <div class="flex items-start gap-2">
            <div
              class="flex-shrink-0 mt-1 w-6 h-6 rounded-full flex items-center justify-center"
              [ngClass]="{
                'bg-[#1E1E1E] text-white': themeService.isDarkMode(),
                'bg-[var(--hover-blue-gray)] text-[var(--text-dark)]': !themeService.isDarkMode()
              }">
              <span class="text-xs">{{ result.domain.charAt(0).toUpperCase() }}</span>
            </div>
            <div class="flex-1">
              <h4 class="font-semibold text-sm"
                  [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
                <span *ngIf="result.url && result.url !== '#'"
                  [routerLink]="result.url"
                  class="hover:underline group-hover:underline transition-colors"
                  [ngClass]="{
                    'text-white hover:text-[#00c39a] group-hover:text-[#00c39a]': themeService.isDarkMode(),
                    'text-[var(--text-dark)] hover:text-[var(--primary-purple)] group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
                  }">
                  {{ result.title }}
                </span>
                <span *ngIf="!result.url || result.url === '#'">{{ result.title }}</span>
              </h4>
              <div class="flex items-center gap-1">
                <p class="text-xs mb-1"
                   [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
                  {{ result.domain }}
                </p>
              </div>
              <p class="text-sm line-clamp-3"
                 [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
                {{ result.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

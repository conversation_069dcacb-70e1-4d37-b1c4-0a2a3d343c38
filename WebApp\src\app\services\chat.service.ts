import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject, Observable, from } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import * as signalR from '@microsoft/signalr';
import { ResponseMessage } from '../../shared/service-proxies/service-proxies';
import { getRemoteServiceBaseUrl } from '../app.config';
import { Router, NavigationStart } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private isChatOpen: boolean;
  private isChatOpenSubject = new BehaviorSubject<boolean>(false);
  private hubConnection!: signalR.HubConnection;
  private messageReceivedSubject = new Subject<{ message: ResponseMessage, isError: boolean, isComplete?: boolean }>();
  private sessionId: string;
  private isConnecting: boolean = false;
  private connectionPromise: Promise<void> | null = null;

  isChatOpen$ = this.isChatOpenSubject.asObservable();
  messageReceived$ = this.messageReceivedSubject.asObservable();

  constructor(private router: Router) {
    this.isChatOpen = false;
    this.sessionId = this.getOrCreateSessionId();

    // Start the SignalR connection
    this.startSignalRConnection();

    // Listen for route changes to maintain connection during navigation
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        // Ensure connection is maintained during navigation
        this.ensureConnection();
      }
    });
  }

  private getOrCreateSessionId(): string {
    let sessionId = crypto.randomUUID();
    return sessionId;
  }

  getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Ensures that the SignalR connection is active
   * @returns Promise that resolves when the connection is established
   */
  public async ensureConnection(): Promise<void> {
    // If we're already connected, return immediately
    if (this.hubConnection?.state === signalR.HubConnectionState.Connected) {
      return Promise.resolve();
    }

    // If we're in the process of connecting, return the existing promise
    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise;
    }

    // Otherwise, start a new connection
    return this.startSignalRConnection();
  }

  /**
   * Starts or restarts the SignalR connection
   * @returns Promise that resolves when the connection is established
   */
  private startSignalRConnection(): Promise<void> {
    // Set connecting flag to prevent multiple simultaneous connection attempts
    this.isConnecting = true;

    // Create the connection if it doesn't exist
    if (!this.hubConnection) {
      let url = getRemoteServiceBaseUrl() + '/chatHub';
      this.hubConnection = new signalR.HubConnectionBuilder()
        .withUrl(url)
        .withAutomaticReconnect([
          0, 2000, 5000, 10000, 15000, 30000 // Retry quickly at first, then with increasing delays
        ])
        .build();

      // Set up event handlers
      this.setupSignalREventHandlers();
    }

    // Start the connection and store the promise
    this.connectionPromise = this.hubConnection.start()
      .then(() => {
        console.log('SignalR Connected!');
        this.isConnecting = false;
      })
      .catch(err => {
        console.error('Error while establishing connection: ', err);
        this.isConnecting = false;
        throw err; // Rethrow to allow caller to handle
      });

    return this.connectionPromise;
  }

  /**
   * Sets up the SignalR event handlers
   */
  private setupSignalREventHandlers(): void {
    if (!this.hubConnection) return;

    // Message handlers
    this.hubConnection.on('ReceiveMessage', (message: ResponseMessage) => {
      this.messageReceivedSubject.next({ message, isError: false, isComplete: false });
    });

    this.hubConnection.on('ReceiveError', (message: ResponseMessage) => {
      this.messageReceivedSubject.next({ message, isError: true, isComplete: true });
    });

    this.hubConnection.on('ReceiveComplete', (message: ResponseMessage) => {
      this.messageReceivedSubject.next({ message, isError: false, isComplete: true });
    });

    // Connection state handlers
    this.hubConnection.onreconnecting(error => {
      console.log('SignalR reconnecting:', error);
    });

    this.hubConnection.onreconnected(connectionId => {
      console.log('SignalR reconnected with ID:', connectionId);
    });

    this.hubConnection.onclose(error => {
      console.log('SignalR connection closed:', error);
      // Attempt to reconnect if closed unexpectedly
      if (error) {
        setTimeout(() => this.ensureConnection(), 5000);
      }
    });
  }

  toggleChat(): boolean {
    this.isChatOpen = !this.isChatOpen;
    this.isChatOpenSubject.next(this.isChatOpen);
    return this.isChatOpen;
  }

  getInitialState(): boolean {
    return this.isChatOpen;
  }

  closeChat() {
    this.isChatOpenSubject.next(false);
  }

  /**
   * Disconnects the SignalR connection
   * @returns Promise that resolves when the connection is stopped
   */
  disconnect(): Promise<void> {
    if (this.hubConnection) {
      return this.hubConnection.stop()
        .then(() => {
          console.log('SignalR connection stopped');
          this.isConnecting = false;
          this.connectionPromise = null;
        })
        .catch(err => {
          console.error('Error stopping SignalR connection:', err);
          // Reset state even if there's an error
          this.isConnecting = false;
          this.connectionPromise = null;
          throw err;
        });
    }
    return Promise.resolve();
  }

  sendMessage(message: ResponseMessage, isError: boolean = false) {
    this.messageReceivedSubject.next({ message, isError, isComplete: false });
  }

  completeMessage(message: ResponseMessage, isError: boolean = false) {
    this.messageReceivedSubject.next({ message, isError, isComplete: true });
  }

  setIsChatOpen(isOpen: boolean): void {
    this.isChatOpenSubject.next(isOpen);
    localStorage.setItem('chatOpen', String(isOpen));
  }
}

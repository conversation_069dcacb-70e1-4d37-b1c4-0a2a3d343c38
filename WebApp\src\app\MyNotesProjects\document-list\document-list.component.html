<div
  class=" border border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)] bg-gradient-to-br from-[var(--background-light-gray)] to-[var(--background-white)] sidebar-container">
  <!-- Sidebar Header -->
  <div
    class="flex items-center justify-between p-4 border-b border-[var(--hover-blue-gray)] bg-[var(--background-white)]">
    <div class="flex items-center gap-4">
      <i class="fas fa-folder text-[var(--primary-purple)] text-xl transition-transform hover:scale-110"></i>
      <h2 class="text-xl font-semibold text-[var(--text-dark)] tracking-tight">
        {{ isjournal ? 'Journal List' : 'Documents List' }}
      </h2>
    </div>
    <button (click)="createNewDocument()"
      class="w-10 h-10 flex items-center justify-center bg-[var(--primary-purple)] text-[var(--background-white)] rounded-full hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] shadow-md"
      title="Create New Document">
      <i class="ri-add-line"></i>
    </button>
  </div>

  <!-- Sidebar Content -->
  <div class="flex-grow overflow-hidden">
    <div *ngIf="isLoading" class="p-6 text-center text-[var(--text-medium-gray)]">
      <div
        class="inline-block w-6 h-6 border-4 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
      </div>
      <span class="ml-3 text-base font-medium">Loading...</span>
    </div>
    <div class="overflow-y-auto sidebar-list px-2 py-4 space-y-2">
      <div *ngFor="let doc of paginatedDocuments"
        class="group flex items-center justify-between px-4 py-3 bg-[var(--background-white)] rounded-[var(--border-radius-large)] hover:bg-[var(--secondary-purple)]/20 hover:shadow-md cursor-pointer transition-[var(--transition-default)]"
        (click)="viewDocument(doc, $event)">
        <div class="flex items-center gap-4">
          <i
            class="fas fa-file-alt text-[var(--text-medium-gray)] text-lg group-hover:text-[var(--primary-purple)] transition-colors"></i>

          <span
            class="text-base font-medium text-[var(--text-dark)] truncate max-w-[200px] group-hover:text-[var(--primary-purple)]"
            title="{{ doc.title }}">
            {{ doc.title }}
          </span>
        </div>
        <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <button
            class="w-9 h-9 flex items-center justify-center text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)] hover:bg-[var(--secondary-purple)]/20 rounded-full transition-[var(--transition-default)]"
            title="Edit" (click)="editDocument(doc, $event)">
            <i class="ri-edit-line"></i>
          </button>
          <button
            class="w-9 h-9 flex items-center justify-center text-[var(--text-medium-gray)] hover:text-red-600 hover:bg-red-100 rounded-full transition-[var(--transition-default)]"
            title="Delete" (click)="deleteDocument(doc.id, $event)">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div
    class="flex items-center justify-between p-4 border-t border-[var(--hover-blue-gray)] bg-[var(--background-white)] rounded-b-[var(--border-radius-large)]">
    <button (click)="previousPage()" [disabled]="currentPage === 1"
      class="px-4 py-2 bg-[var(--background-white)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] hover:border-[var(--primary-purple)] disabled:opacity-50 disabled:cursor-not-allowed transition-[var(--transition-default)]">
      <i class="fas fa-chevron-left mr-2"></i> Previous
    </button>
    <span class="text-sm font-medium text-[var(--text-medium-gray)]">
      Page {{ currentPage }} of {{ totalPages }}
    </span>
    <button (click)="nextPage()" [disabled]="currentPage === totalPages"
      class="px-4 py-2 bg-[var(--background-white)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] hover:border-[var(--primary-purple)] disabled:opacity-50 disabled:cursor-not-allowed transition-[var(--transition-default)]">
      Next <i class="fas fa-chevron-right ml-2"></i>
    </button>
  </div>
</div>

<!-- Modal -->
<div *ngIf="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
  <div
    class="bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] max-w-md w-full p-6 transform transition-[var(--transition-default)] scale-100 hover:scale-105">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-bold text-[var(--text-dark)]">Confirm Delete</h3>
      <button type="button"
        class="w-8 h-8 flex items-center justify-center text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)] rounded-full transition-[var(--transition-default)]"
        (click)="showDeleteModal = false">
        <i class="fas fa-times text-lg"></i>
      </button>
    </div>
    <div class="mb-6">
      <p class="text-[var(--text-medium-gray)] text-base leading-relaxed">Are you sure you want to delete this document?
        This action
        cannot be undone.</p>
    </div>
    <div class="flex justify-end gap-3">
      <button
        class="px-5 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)]"
        (click)="showDeleteModal = false">
        <i class="fas fa-times mr-2"></i> Cancel
      </button>
      <button
        class="px-5 py-2 bg-red-600 text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-red-700 transition-[var(--transition-default)]"
        (click)="confirmDelete()">
        <i class="fas fa-trash mr-2"></i> Delete
      </button>
    </div>
  </div>
</div>

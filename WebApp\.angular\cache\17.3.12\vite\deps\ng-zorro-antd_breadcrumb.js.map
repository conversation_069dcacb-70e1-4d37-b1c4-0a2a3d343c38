{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-breadcrumb.mjs"], "sourcesContent": ["import { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Optional, NgModule } from '@angular/core';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownModule } from 'ng-zorro-antd/dropdown';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { __decorate } from 'tslib';\nimport { Router, ActivatedRoute, NavigationEnd, PRIMARY_OUTLET } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter, startWith } from 'rxjs/operators';\nimport { PREFIX } from 'ng-zorro-antd/core/logger';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nfunction NzBreadCrumbItemComponent_Conditional_0_ng_template_1_Template(rf, ctx) {}\nfunction NzBreadCrumbItemComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵtemplate(1, NzBreadCrumbItemComponent_Conditional_0_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelement(2, \"span\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const noMenuTpl_r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"nzDropdownMenu\", ctx_r0.nzOverlay);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", noMenuTpl_r2);\n  }\n}\nfunction NzBreadCrumbItemComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzBreadCrumbItemComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzBreadCrumbItemComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noMenuTpl_r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", noMenuTpl_r2);\n  }\n}\nfunction NzBreadCrumbItemComponent_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.nzBreadCrumbComponent.nzSeparator, \" \");\n  }\n}\nfunction NzBreadCrumbItemComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-breadcrumb-separator\");\n    i0.ɵɵtemplate(1, NzBreadCrumbItemComponent_Conditional_2_ng_container_1_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzBreadCrumbComponent.nzSeparator);\n  }\n}\nfunction NzBreadCrumbItemComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _forTrack0 = ($index, $item) => $item.url;\nfunction NzBreadCrumbComponent_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-breadcrumb-item\")(1, \"a\", 0);\n    i0.ɵɵlistener(\"click\", function NzBreadCrumbComponent_Conditional_1_For_1_Template_a_click_1_listener($event) {\n      const breadcrumb_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigate(breadcrumb_r2.url, $event));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const breadcrumb_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"href\", breadcrumb_r2.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(breadcrumb_r2.label);\n  }\n}\nfunction NzBreadCrumbComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzBreadCrumbComponent_Conditional_1_For_1_Template, 3, 2, \"nz-breadcrumb-item\", null, _forTrack0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.breadcrumbs);\n  }\n}\nclass NzBreadCrumbSeparatorComponent {\n  static {\n    this.ɵfac = function NzBreadCrumbSeparatorComponent_Factory(t) {\n      return new (t || NzBreadCrumbSeparatorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBreadCrumbSeparatorComponent,\n      selectors: [[\"nz-breadcrumb-separator\"]],\n      hostAttrs: [1, \"ant-breadcrumb-separator\"],\n      exportAs: [\"nzBreadcrumbSeparator\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzBreadCrumbSeparatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbSeparatorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-breadcrumb-separator',\n      exportAs: 'nzBreadcrumbSeparator',\n      standalone: true,\n      template: `<ng-content></ng-content>`,\n      host: {\n        class: 'ant-breadcrumb-separator'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * https://angular.io/errors/NG3003\n * An intermediate interface for {@link NzBreadCrumbComponent} & {@link NzBreadCrumbItemComponent}\n */\nclass NzBreadcrumb {}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbItemComponent {\n  constructor(nzBreadCrumbComponent) {\n    this.nzBreadCrumbComponent = nzBreadCrumbComponent;\n  }\n  static {\n    this.ɵfac = function NzBreadCrumbItemComponent_Factory(t) {\n      return new (t || NzBreadCrumbItemComponent)(i0.ɵɵdirectiveInject(NzBreadcrumb));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBreadCrumbItemComponent,\n      selectors: [[\"nz-breadcrumb-item\"]],\n      inputs: {\n        nzOverlay: \"nzOverlay\"\n      },\n      exportAs: [\"nzBreadcrumbItem\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 2,\n      consts: [[\"noMenuTpl\", \"\"], [\"nz-dropdown\", \"\", 1, \"ant-breadcrumb-overlay-link\", 3, \"nzDropdownMenu\"], [3, \"ngTemplateOutlet\"], [\"nz-icon\", \"\", \"nzType\", \"down\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-breadcrumb-link\"]],\n      template: function NzBreadCrumbItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzBreadCrumbItemComponent_Conditional_0_Template, 3, 2, \"span\", 1)(1, NzBreadCrumbItemComponent_Conditional_1_Template, 1, 1)(2, NzBreadCrumbItemComponent_Conditional_2_Template, 2, 1, \"nz-breadcrumb-separator\")(3, NzBreadCrumbItemComponent_ng_template_3_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, !!ctx.nzOverlay ? 0 : 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx.nzBreadCrumbComponent.nzSeparator ? 2 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzBreadCrumbSeparatorComponent, NzDropDownModule, i2.NzDropDownDirective, NzIconModule, i3.NzIconDirective, NzOutletModule, i4.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbItemComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-breadcrumb-item',\n      exportAs: 'nzBreadcrumbItem',\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NgTemplateOutlet, NzBreadCrumbSeparatorComponent, NzDropDownModule, NzIconModule, NzOutletModule],\n      template: `\n    @if (!!nzOverlay) {\n      <span class=\"ant-breadcrumb-overlay-link\" nz-dropdown [nzDropdownMenu]=\"nzOverlay\">\n        <ng-template [ngTemplateOutlet]=\"noMenuTpl\"></ng-template>\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n    } @else {\n      <ng-template [ngTemplateOutlet]=\"noMenuTpl\" />\n    }\n\n    @if (nzBreadCrumbComponent.nzSeparator) {\n      <nz-breadcrumb-separator>\n        <ng-container *nzStringTemplateOutlet=\"nzBreadCrumbComponent.nzSeparator\">\n          {{ nzBreadCrumbComponent.nzSeparator }}\n        </ng-container>\n      </nz-breadcrumb-separator>\n    }\n\n    <ng-template #noMenuTpl>\n      <span class=\"ant-breadcrumb-link\">\n        <ng-content />\n      </span>\n    </ng-template>\n  `\n    }]\n  }], () => [{\n    type: NzBreadcrumb\n  }], {\n    nzOverlay: [{\n      type: Input\n    }]\n  });\n})();\nclass NzBreadCrumbComponent {\n  constructor(injector, cdr, elementRef, renderer, directionality) {\n    this.injector = injector;\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.directionality = directionality;\n    this.nzAutoGenerate = false;\n    this.nzSeparator = '/';\n    this.nzRouteLabel = 'breadcrumb';\n    this.nzRouteLabelFn = label => label;\n    this.breadcrumbs = [];\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.nzAutoGenerate) {\n      this.registerRouterChange();\n    }\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.prepareComponentForRtl();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.prepareComponentForRtl();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  navigate(url, e) {\n    e.preventDefault();\n    this.injector.get(Router).navigateByUrl(url);\n  }\n  registerRouterChange() {\n    try {\n      const router = this.injector.get(Router);\n      const activatedRoute = this.injector.get(ActivatedRoute);\n      router.events.pipe(filter(e => e instanceof NavigationEnd), takeUntil(this.destroy$), startWith(true) // trigger initial render\n      ).subscribe(() => {\n        this.breadcrumbs = this.getBreadcrumbs(activatedRoute.root);\n        this.cdr.markForCheck();\n      });\n    } catch (e) {\n      throw new Error(`${PREFIX} You should import RouterModule if you want to use 'NzAutoGenerate'.`);\n    }\n  }\n  getBreadcrumbs(route, url = '', breadcrumbs = []) {\n    const children = route.children;\n    // If there's no sub root, then stop the recurse and returns the generated breadcrumbs.\n    if (children.length === 0) {\n      return breadcrumbs;\n    }\n    for (const child of children) {\n      if (child.outlet === PRIMARY_OUTLET) {\n        // Only parse components in primary router-outlet (in another word, router-outlet without a specific name).\n        // Parse this layer and generate a breadcrumb item.\n        const routeUrl = child.snapshot.url.map(segment => segment.path).filter(path => path).join('/');\n        // Do not change nextUrl if routeUrl is falsy. This happens when it's a route lazy loading other modules.\n        const nextUrl = routeUrl ? `${url}/${routeUrl}` : url;\n        const breadcrumbLabel = this.nzRouteLabelFn(child.snapshot.data[this.nzRouteLabel]);\n        // If have data, go to generate a breadcrumb for it.\n        if (routeUrl && breadcrumbLabel) {\n          const breadcrumb = {\n            label: breadcrumbLabel,\n            params: child.snapshot.params,\n            url: nextUrl\n          };\n          breadcrumbs.push(breadcrumb);\n        }\n        return this.getBreadcrumbs(child, nextUrl, breadcrumbs);\n      }\n    }\n    return breadcrumbs;\n  }\n  prepareComponentForRtl() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-breadcrumb-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-breadcrumb-rtl');\n    }\n  }\n  static {\n    this.ɵfac = function NzBreadCrumbComponent_Factory(t) {\n      return new (t || NzBreadCrumbComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBreadCrumbComponent,\n      selectors: [[\"nz-breadcrumb\"]],\n      hostAttrs: [1, \"ant-breadcrumb\"],\n      inputs: {\n        nzAutoGenerate: \"nzAutoGenerate\",\n        nzSeparator: \"nzSeparator\",\n        nzRouteLabel: \"nzRouteLabel\",\n        nzRouteLabelFn: \"nzRouteLabelFn\"\n      },\n      exportAs: [\"nzBreadcrumb\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NzBreadcrumb,\n        useExisting: NzBreadCrumbComponent\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[3, \"click\"]],\n      template: function NzBreadCrumbComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, NzBreadCrumbComponent_Conditional_1_Template, 2, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.nzAutoGenerate && ctx.breadcrumbs.length ? 1 : -1);\n        }\n      },\n      dependencies: [NzBreadCrumbItemComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzBreadCrumbComponent.prototype, \"nzAutoGenerate\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-breadcrumb',\n      exportAs: 'nzBreadcrumb',\n      preserveWhitespaces: false,\n      providers: [{\n        provide: NzBreadcrumb,\n        useExisting: NzBreadCrumbComponent\n      }],\n      standalone: true,\n      imports: [NzBreadCrumbItemComponent],\n      template: `\n    <ng-content />\n    @if (nzAutoGenerate && breadcrumbs.length) {\n      @for (breadcrumb of breadcrumbs; track breadcrumb.url) {\n        <nz-breadcrumb-item>\n          <a [attr.href]=\"breadcrumb.url\" (click)=\"navigate(breadcrumb.url, $event)\">{{ breadcrumb.label }}</a>\n        </nz-breadcrumb-item>\n      }\n    }\n  `,\n      host: {\n        class: 'ant-breadcrumb'\n      }\n    }]\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzAutoGenerate: [{\n      type: Input\n    }],\n    nzSeparator: [{\n      type: Input\n    }],\n    nzRouteLabel: [{\n      type: Input\n    }],\n    nzRouteLabelFn: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbModule {\n  static {\n    this.ɵfac = function NzBreadCrumbModule_Factory(t) {\n      return new (t || NzBreadCrumbModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzBreadCrumbModule,\n      imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent],\n      exports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent],\n      exports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbModule, NzBreadCrumbSeparatorComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,+DAA+D,IAAI,KAAK;AAAC;AAClF,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,eAAe,CAAC;AACvG,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,eAAkB,YAAY,CAAC;AACrC,IAAG,WAAW,kBAAkB,OAAO,SAAS;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,YAAY;AAAA,EAChD;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAAC;AAClF,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,gEAAgE,GAAG,GAAG,eAAe,CAAC;AAAA,EACzG;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,eAAkB,YAAY,CAAC;AACrC,IAAG,WAAW,oBAAoB,YAAY;AAAA,EAChD;AACF;AACA,SAAS,gEAAgE,IAAI,KAAK;AAChF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,sBAAsB,aAAa,GAAG;AAAA,EAC1E;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,yBAAyB;AAC9C,IAAG,WAAW,GAAG,iEAAiE,GAAG,GAAG,gBAAgB,CAAC;AACzG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,sBAAsB,WAAW;AAAA,EAClF;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,oBAAoB,EAAE,GAAG,KAAK,CAAC;AACpD,IAAG,WAAW,SAAS,SAAS,sEAAsE,QAAQ;AAC5G,YAAM,gBAAmB,cAAc,GAAG,EAAE;AAC5C,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,cAAc,KAAK,MAAM,CAAC;AAAA,IAClE,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAgB,IAAI;AAC1B,IAAG,UAAU;AACb,IAAG,YAAY,QAAQ,cAAc,KAAQ,aAAa;AAC1D,IAAG,UAAU;AACb,IAAG,kBAAkB,cAAc,KAAK;AAAA,EAC1C;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,oDAAoD,GAAG,GAAG,sBAAsB,MAAM,UAAU;AAAA,EACzH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,WAAW;AAAA,EAClC;AACF;AACA,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,GAAG;AAC7D,aAAO,KAAK,KAAK,iCAAgC;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,yBAAyB,CAAC;AAAA,MACvC,WAAW,CAAC,GAAG,0BAA0B;AAAA,MACzC,UAAU,CAAC,uBAAuB;AAAA,MAClC,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,wCAAwC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,eAAN,MAAmB;AAAC;AAMpB,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,uBAAuB;AACjC,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA8B,kBAAkB,YAAY,CAAC;AAAA,IAChF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,eAAe,IAAI,GAAG,+BAA+B,GAAG,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,WAAW,IAAI,UAAU,MAAM,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,qBAAqB,CAAC;AAAA,MAC7N,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,kDAAkD,GAAG,CAAC,EAAE,GAAG,kDAAkD,GAAG,GAAG,yBAAyB,EAAE,GAAG,kDAAkD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACnV;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,CAAC,CAAC,IAAI,YAAY,IAAI,CAAC;AAC3C,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,sBAAsB,cAAc,IAAI,EAAE;AAAA,QACpE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,gCAAgC,kBAAqB,qBAAqB,cAAiB,iBAAiB,gBAAmB,+BAA+B;AAAA,MAC/L,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,YAAY;AAAA,MACZ,SAAS,CAAC,kBAAkB,gCAAgC,kBAAkB,cAAc,cAAc;AAAA,MAC1G,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAwBZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,UAAU,KAAK,YAAY,UAAU,gBAAgB;AAC/D,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,iBAAiB,WAAS;AAC/B,SAAK,cAAc,CAAC;AACpB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,QAAI,KAAK,gBAAgB;AACvB,WAAK,qBAAqB;AAAA,IAC5B;AACA,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,uBAAuB;AAC5B,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,SAAS,KAAK,GAAG;AACf,MAAE,eAAe;AACjB,SAAK,SAAS,IAAI,MAAM,EAAE,cAAc,GAAG;AAAA,EAC7C;AAAA,EACA,uBAAuB;AACrB,QAAI;AACF,YAAM,SAAS,KAAK,SAAS,IAAI,MAAM;AACvC,YAAM,iBAAiB,KAAK,SAAS,IAAI,cAAc;AACvD,aAAO,OAAO;AAAA,QAAK,OAAO,OAAK,aAAa,aAAa;AAAA,QAAG,UAAU,KAAK,QAAQ;AAAA,QAAG,UAAU,IAAI;AAAA;AAAA,MACpG,EAAE,UAAU,MAAM;AAChB,aAAK,cAAc,KAAK,eAAe,eAAe,IAAI;AAC1D,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH,SAAS,GAAG;AACV,YAAM,IAAI,MAAM,GAAG,MAAM,sEAAsE;AAAA,IACjG;AAAA,EACF;AAAA,EACA,eAAe,OAAO,MAAM,IAAI,cAAc,CAAC,GAAG;AAChD,UAAM,WAAW,MAAM;AAEvB,QAAI,SAAS,WAAW,GAAG;AACzB,aAAO;AAAA,IACT;AACA,eAAW,SAAS,UAAU;AAC5B,UAAI,MAAM,WAAW,gBAAgB;AAGnC,cAAM,WAAW,MAAM,SAAS,IAAI,IAAI,aAAW,QAAQ,IAAI,EAAE,OAAO,UAAQ,IAAI,EAAE,KAAK,GAAG;AAE9F,cAAM,UAAU,WAAW,GAAG,GAAG,IAAI,QAAQ,KAAK;AAClD,cAAM,kBAAkB,KAAK,eAAe,MAAM,SAAS,KAAK,KAAK,YAAY,CAAC;AAElF,YAAI,YAAY,iBAAiB;AAC/B,gBAAM,aAAa;AAAA,YACjB,OAAO;AAAA,YACP,QAAQ,MAAM,SAAS;AAAA,YACvB,KAAK;AAAA,UACP;AACA,sBAAY,KAAK,UAAU;AAAA,QAC7B;AACA,eAAO,KAAK,eAAe,OAAO,SAAS,WAAW;AAAA,MACxD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,QAAQ,OAAO;AACtB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC5E,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAqB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAC5O;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,gBAAgB;AAAA,MAC/B,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC;AAAA,MACrB,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,8CAA8C,GAAG,CAAC;AAAA,QACrE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,kBAAkB,IAAI,YAAY,SAAS,IAAI,EAAE;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,cAAc,CAAC,yBAAyB;AAAA,MACxC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,kBAAkB,MAAM;AAAA,CACrF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,SAAS,CAAC,yBAAyB;AAAA,MACnC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,uBAAuB,2BAA2B,8BAA8B;AAAA,MAC1F,SAAS,CAAC,uBAAuB,2BAA2B,8BAA8B;AAAA,IAC5F,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,uBAAuB,yBAAyB;AAAA,IAC5D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,uBAAuB,2BAA2B,8BAA8B;AAAA,MAC1F,SAAS,CAAC,uBAAuB,2BAA2B,8BAA8B;AAAA,IAC5F,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
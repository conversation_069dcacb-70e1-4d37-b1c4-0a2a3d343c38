<div class="min-h-screen bg-[#1A1A1A] text-white">
  <!-- Top Navigation -->


  <!-- Main Content -->
  <div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center gap-2">
        <h1 class="text-xl text-white">Functions</h1>
        <span class="bg-[#2A2A2A] px-2 py-0.5 rounded text-sm">0</span>
      </div>
      <div class="flex items-center gap-3">
        <div class="relative">
          <input type="text" placeholder="Search Functions"
                 class="bg-[#2A2A2A] px-4 py-1.5 rounded pl-9 text-sm w-64 focus:outline-none border-none">
          <i class="ri-search-line absolute left-3 top-2 text-gray-400"></i>
        </div>
        <button class=" p-1.5 rounded cursor-pointer hover:text-white bg-transparent text-[#555555] transition-colors outline-none border-none">
          <i class="ri-add-line text-lg"></i>
        </button>
      </div>
    </div>

    <!-- Import/Export Buttons -->
    <div class="flex justify-end gap-3 mb-6">
      <button class="group  text-white flex items-center cursor-pointer transition-all gap-2 bg-[#2A2A2A] px-3 py-1.5 rounded text-sm hover:bg-[#3A3A3A] border-none">
        <i class="ri-download-line text-white "></i>
        Import Functions
      </button>
      <button class="flex text-white outline-none border-none cursor-pointer transition-all items-center gap-2 bg-[#2A2A2A] px-3 py-1.5 rounded text-sm hover:bg-[#3A3A3A]">
        <i class="ri-upload-line text-white"></i>
        Export Functions
      </button>
    </div>

    <!-- Community Section -->
    <div class="mt-8">
      <h2 class="text-xl mb-4 text-white">Made by OpenWebUI Community</h2>
      <div class="bg-[#2A2A2A] rounded-lg p-4 cursor-pointer hover:bg-[#3A3A3A] transition-colors">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-base font-medium text-white">Discover a function</h3>
            <p class="text-sm text-gray-400">Discover, download, and explore custom functions</p>
          </div>
          <i class="ri-arrow-right-s-line text-xl text-gray-400"></i>
        </div>
      </div>
    </div>
  </div>
</div>

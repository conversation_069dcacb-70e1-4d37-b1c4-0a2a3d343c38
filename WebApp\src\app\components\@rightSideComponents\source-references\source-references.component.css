/* Source References Sidebar Styles */
:host {
  display: flex;
  flex: 1;
  height: 100%;
  position: relative;
  top: 0;
  right: 0;
  z-index: 10;
  width: 100%; /* Take parent container width */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
  overflow: hidden; /* Prevent content from spilling out */
}

/* Ensure the inner content expands to fill the container width */
:host > div {
  flex: 1;
  width: 100%; /* Take full width of container */
  height: 100%;
  position: relative;
}

/* Transition styles */
.sidebar-enter {
  transform: translateX(100%);
}

.sidebar-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.sidebar-exit {
  transform: translateX(0);
}

.sidebar-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out;
}

/* Theme-specific styles */
:host-context(.dark-theme) {
  --sidebar-bg: #2b2b33;
  --sidebar-border: #3a3a45;
  --sidebar-text: #ffffff;
  --sidebar-text-secondary: #a0a0a0;
  --sidebar-card-bg: #3a3a45;
  --sidebar-card-border: #4a4a55;
  --sidebar-card-hover-border: #00c39a;
  --sidebar-icon-color: #00c39a;
  --sidebar-link-hover: #00c39a;
}

:host-context(:not(.dark-theme)) {
  --sidebar-bg: var(--background-white);
  --sidebar-border: var(--hover-blue-gray);
  --sidebar-text: var(--text-dark);
  --sidebar-text-secondary: var(--text-medium-gray);
  --sidebar-card-bg: var(--background-white);
  --sidebar-card-border: var(--hover-blue-gray);
  --sidebar-card-hover-border: var(--primary-purple);
  --sidebar-icon-color: var(--primary-purple);
  --sidebar-link-hover: var(--primary-purple);
}

/* Card hover effects */
:host-context(.dark-theme) div[class*="hover:border-[#00c39a]"]:hover {
  border-color: #00c39a !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

:host-context(:not(.dark-theme)) div[class*="hover:border-[var(--primary-purple)]"]:hover {
  border-color: var(--primary-purple) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Link hover effects */
:host-context(.dark-theme) span[class*="hover:text-[#00c39a]"]:hover {
  color: #00c39a !important;
  text-decoration: underline;
}

:host-context(:not(.dark-theme)) span[class*="hover:text-[var(--primary-purple)]"]:hover {
  color: var(--primary-purple) !important;
  text-decoration: underline;
}

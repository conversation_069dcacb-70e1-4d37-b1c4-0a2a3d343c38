<div class="flex p-1">
  <!-- Side Navigation -->
  <div class="w-44">
    <div class="px-3 *:text-sm">
      <div class="flex items-center gap-2 mb-2 cursor-pointer text-[#4f4f4f] hover:text-white transition-all"
        (click)="tabs='overview'" [class]="tabs=='overview'?'text-white':''">
        <i class="ri-user-3-line"></i>
        <span>Overview</span>
      </div>
      <div class="flex items-center gap-2 mb-2 text-[#4f4f4f] cursor-pointer hover:text-white transition-all"
        (click)="tabs='groups'" [class]="tabs=='groups'?'text-white':''">
        <i class="ri-group-line "></i>
        <span class="te">Groups</span>
      </div>
    </div>
  </div>
  <div class="w-full" *ngIf="tabs === 'overview'">
    <div class="flex justify-between w-full  border-b border-[#2A2A2A]">
      <div class="flex items-center gap-3">
        <h1 class="text-lg font-medium  text-white">Users</h1>
        <span class="bg-[#2A2A2A] px-2 py-0.5 rounded text-xs">1</span>
      </div>
      <div class="flex items-center gap-3 justify-between">
        <div class="relative ">
          <input type="text" placeholder="Search"
            class="bg-[#2A2A2A] w-64 text-white outline-none border-none p-1 rounded pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-[#3A3A3A]">
          <i class="ri-search-line absolute left-3 top-1 text-gray-400"></i>
        </div>
        <button
          class=" p-1.5 rounded bg-transparent text-white hover:text-white transition-colors outline-none border-none">
          <i class="ri-add-line text-lg"></i>
        </button>
      </div>
    </div>
    <div class="p-6">
      <div class="bg-[#1F1F1F] rounded-lg overflow-hidden">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-[#262626] text-xs text-gray-400">
              <th class="text-left py-3 px-4 font-medium">ROLE</th>
              <th class="text-left py-3 px-4 font-medium">NAME</th>
              <th class="text-left py-3 px-4 font-medium">EMAIL</th>
              <th class="text-left py-3 px-4 font-medium">LAST ACTIVE</th>
              <th class="text-left py-3 px-4 font-medium">CREATED AT</th>
              <th class="text-left py-3 px-4 font-medium">OAUTH ID</th>
              <th class="text-left py-3 px-4 font-medium w-10"></th>
            </tr>
          </thead>
          <tbody class="divide-y divide-[#2A2A2A]">
            <tr *ngFor="let user of users" class="hover:text-white transition-colors">
              <td class="py-3 px-4">
                <button
                  class="bg-[#1E3A8A] text-[#93C5FD] px-2 py-0.5 rounded text-xs font-medium hover:bg-[#2E4A9A] transition-colors ">
                  {{user.role}}
                </button>
              </td>
              <td class="py-3 px-4">
                <div class="flex items-center gap-3">
                  <div class="w-7 h-7 bg-orange-500 rounded-full flex items-center justify-center text-sm font-medium">
                    {{user.name[0]}}
                  </div>
                  <span class="text-sm">{{user.name}}</span>
                </div>
              </td>
              <td class="py-3 px-4 text-sm text-gray-400">{{user.email}}</td>
              <td class="py-3 px-4 text-sm text-gray-400">{{user.lastActive}}</td>
              <td class="py-3 px-4 text-sm text-gray-400">{{user.createdAt}}</td>
              <td class="py-3 px-4 text-sm text-gray-400"></td>
              <td class="py-3 px-4">
                <button
                  class="text-gray-400 hover:text-white transition-colors outline-none border-none bg-transparent">
                  <i class="ri-pencil-line"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="flex justify-between items-center mt-4 text-xs text-gray-400">
        <p>Click on the user role button to change a user's role.</p>
        <div class="flex items-center gap-4">
          <button class="hover:text-white transition-colors " disabled>
            <i class="ri-arrow-left-s-line text-lg"></i>
          </button>
          <span>1</span>
          <button class="hover:text-white transition-colors" disabled>
            <i class="ri-arrow-right-s-line text-lg"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="flex flex-col h-full w-full" *ngIf="tabs === 'groups'">
    <!-- Header -->
    <div class="flex justify-between items-center border-b border-[#2A2A2A]">
      <div class="flex items-center gap-2">
        <h1 class="text-xl font-medium  text-white">Groups</h1>
        <span class="bg-[#2A2A2A] px-2 py-0.5 rounded text-xs">0</span>
      </div>
      <div class="flex items-center gap-3">
        <div class="relative">
          <input type="text" placeholder="Search"
            class="bg-[#2A2A2A] w-64 text-white outline-none border-none p-2 rounded-md pl-9 text-sm">
          <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
        </div>
        <button
          class="bg-transparent text-white  rounded-md text-sm hover:text-gray-200 transition-colors border-none outline-none cursor-pointer">
          <i class="ri-add-line mr-1"></i>
        </button>
      </div>
    </div>

    <!-- Empty State -->
    <div class="flex-1 flex flex-col items-center justify-center p-8 text-center">
      <h2 class="text-xl font-medium mb-2  text-white">Organize your users</h2>
      <p class="text-[#8C8C8C] text-sm mb-6">Use groups to group your users and assign permissions.</p>
      <button class="bg-white text-black px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
        Create Group
      </button>
    </div>

    <!-- Default Permissions -->
    <div class="p-4 border-t border-[#2A2A2A]">
      <div class="flex items-center justify-between p-3 hover:bg-[#1F1F1F] rounded-lg cursor-pointer transition-colors">
        <div class="flex items-center gap-3">
          <i class="ri-group-line text-xl"></i>
          <div>
            <h3 class="font-medium  text-white">Default permissions</h3>
            <p class="text-sm text-[#8C8C8C]">applies to all users with the "user" role</p>
          </div>
        </div>
        <i class="ri-arrow-right-s-line text-xl"></i>
      </div>
    </div>
  </div>

</div>

import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ActiveDocument {
  id: number;
  title: string;
  workspaceName?: string;
  route?: string; // Optional: track which route/context the document is active in
}

@Injectable({
  providedIn: 'root'
})
export class ActiveDocumentService {
  private activeDocumentSubject = new BehaviorSubject<ActiveDocument | null>(null);
  
  // Observable for components to subscribe to active document changes
  public activeDocument$: Observable<ActiveDocument | null> = this.activeDocumentSubject.asObservable();

  constructor() {
    console.log('🔧 ActiveDocumentService initialized');
  }

  /**
   * Set the currently active document
   * @param document The document to set as active
   * @param context Optional context information (route, workspace, etc.)
   */
  setActiveDocument(document: any, context?: { workspaceName?: string; route?: string }): void {
    if (!document || !document.id) {
      console.warn('⚠️ Attempted to set invalid document as active:', document);
      return;
    }

    const activeDoc: ActiveDocument = {
      id: document.id,
      title: document.title || document.name || `Document ${document.id}`,
      workspaceName: context?.workspaceName,
      route: context?.route
    };

    console.log('📄 Setting active document:', activeDoc);
    this.activeDocumentSubject.next(activeDoc);
  }

  /**
   * Clear the currently active document
   */
  clearActiveDocument(): void {
    console.log('🗑️ Clearing active document');
    this.activeDocumentSubject.next(null);
  }

  /**
   * Get the current active document (synchronous)
   * @returns The currently active document or null
   */
  getCurrentActiveDocument(): ActiveDocument | null {
    return this.activeDocumentSubject.value;
  }

  /**
   * Check if a specific document is currently active
   * @param documentId The ID of the document to check
   * @returns True if the document is active, false otherwise
   */
  isDocumentActive(documentId: number): boolean {
    const activeDoc = this.getCurrentActiveDocument();
    const isActive = activeDoc !== null && activeDoc.id === documentId;
    
    if (isActive) {
      console.log('✅ Document is active:', activeDoc.title, 'ID:', documentId);
    }
    
    return isActive;
  }

  /**
   * Get the active document ID (convenience method)
   * @returns The ID of the active document or null
   */
  getActiveDocumentId(): number | null {
    const activeDoc = this.getCurrentActiveDocument();
    return activeDoc ? activeDoc.id : null;
  }

  /**
   * Update active document properties without changing the ID
   * @param updates Partial updates to apply to the active document
   */
  updateActiveDocument(updates: Partial<ActiveDocument>): void {
    const currentDoc = this.getCurrentActiveDocument();
    if (currentDoc) {
      const updatedDoc = { ...currentDoc, ...updates };
      console.log('🔄 Updating active document:', updatedDoc);
      this.activeDocumentSubject.next(updatedDoc);
    }
  }

  /**
   * Set active document by ID only (useful when you only have the ID)
   * @param documentId The ID of the document to set as active
   * @param title Optional title for the document
   * @param context Optional context information
   */
  setActiveDocumentById(documentId: number, title?: string, context?: { workspaceName?: string; route?: string }): void {
    const activeDoc: ActiveDocument = {
      id: documentId,
      title: title || `Document ${documentId}`,
      workspaceName: context?.workspaceName,
      route: context?.route
    };

    console.log('📄 Setting active document by ID:', activeDoc);
    this.activeDocumentSubject.next(activeDoc);
  }
}

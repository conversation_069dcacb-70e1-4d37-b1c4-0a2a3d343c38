/* Notes Sidebar Styling */
:host {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Simple Active Tab Styling for Filter Buttons */
.active-tab {
  background-color: var(--secondary-purple) !important;
  color: black !important;
}

.active-tab i {
  color: black !important;
}

/* Note Item Styling - Exact Settings Sidebar Pattern */

/* Active indicator bar animation - Exact match */
.absolute.left-0 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced active indicator in light theme - Exact match */
:host-context(:not(.dark)) .absolute.left-0.opacity-100 {
  box-shadow: 0 0 8px rgba(107, 70, 193, 0.5);
  width: 4px !important;
}

/* Enhanced active indicator in dark theme - Exact match */
:host-context(.dark) .absolute.left-0.opacity-100 {
  box-shadow: 0 0 8px rgba(16, 163, 127, 0.5);
  width: 4px !important;
}

/* Hover effects - Exact match from settings sidebar */
.hover\:bg-\[var\(--primary-purple\)\]:hover {
  background-color: var(--primary-purple) !important;
  transform: translateX(2px);
}

.group:hover {
  transform: translateX(2px);
}

/* Note: The HTML uses 'hover:bg-[var(--primary-purple)]': true which takes precedence */
/* This ensures the hover background is var(--primary-purple) as specified in HTML */

/* Improve hover text visibility - Theme-specific colors */
:host-context(:not(.dark)) .group:hover i,
:host-context(:not(.dark)) .group:hover span {
  color: black !important;
}

:host-context(.dark) .group:hover i,
:host-context(.dark) .group:hover span {
  color: white !important;
}

/* Transition effects - Exact match */
.transition-all {
  transition: all 0.3s ease;
}

/* Smooth transitions for all elements - Exact match */
i, span {
  transition: all 0.2s ease;
}

/* Theme-specific active state styling - Exact match */
:host-context(.dark) .bg-\[var\(--secondary-purple\)\],
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\] {
  background-color: #10A37F !important; /* Exact color from the settings sidebar */
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

:host-context(.dark) .bg-\[var\(--secondary-purple\)\] i,
:host-context(.dark) .bg-\[var\(--secondary-purple\)\] span,
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\] i,
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\] span {
  color: white !important;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Light theme active state styling - Exact match */
:host-context(:not(.dark)) .bg-\[var\(--secondary-purple\)\] {
  background-color: #D6BCFA !important; /* Exact color from the settings sidebar */
  border-left: 4px solid var(--primary-purple);
  box-shadow: 0 2px 8px rgba(107, 70, 193, 0.2);
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

:host-context(:not(.dark)) .bg-\[var\(--secondary-purple\)\] i,
:host-context(:not(.dark)) .bg-\[var\(--secondary-purple\)\] span {
  color: black !important;
  font-weight: 600;
}

/* Note: Hover effects are handled by HTML classes and the CSS above */

/* Tooltip styles */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.custom-tooltip {
  visibility: hidden;
  position: absolute;
  background-color: var(--background-light-gray);
  color: var(--text-dark);
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--hover-blue-gray);
}

.bottom-tooltip {
  top: 125%;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-container:hover .custom-tooltip {
  visibility: visible;
  opacity: 1;
}

/* Enhanced Professional Scrollbar Styling - Aligned with Global Styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 9999px; /* rounded-full - matching global styles */
  transition: all 0.2s ease;
  opacity: 0.8;
}

/* Enhanced hover effects - extending global :hover ::-webkit-scrollbar-thumb */
::-webkit-scrollbar-thumb:hover,
:hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
  transform: scaleX(1.1);
}

/* Theme-aware scrollbar styling using CSS variables */
.dark-theme ::-webkit-scrollbar-thumb,
:host(.dark-theme) ::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
}

.dark-theme ::-webkit-scrollbar-thumb:hover,
.dark-theme :hover ::-webkit-scrollbar-thumb,
:host(.dark-theme) ::-webkit-scrollbar-thumb:hover,
:host(.dark-theme) :hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
}

/* Light theme uses global defaults via CSS variables */
.light-theme ::-webkit-scrollbar-thumb,
:host(.light-theme) ::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
}

.light-theme ::-webkit-scrollbar-thumb:hover,
.light-theme :hover ::-webkit-scrollbar-thumb,
:host(.light-theme) ::-webkit-scrollbar-thumb:hover,
:host(.light-theme) :hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
}

/* Scrollbar corner styling */
::-webkit-scrollbar-corner {
  background: transparent;
}

/* Enhanced scrollable content areas - Aligned with Global Styles */
.flex-1.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: var(--hover-blue-gray) transparent;
}

/* Smooth scrolling behavior */
.overflow-y-auto {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

/* Content area specific scrollbar styling - Inherits global patterns */
.flex-grow.overflow-y-auto::-webkit-scrollbar {
  width: 6px; /* Consistent with global width */
}

.flex-grow.overflow-y-auto::-webkit-scrollbar-track {
  background-color: transparent;
}

.flex-grow.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 9999px; /* rounded-full - matching global styles */
  transition: all 0.2s ease;
}

.flex-grow.overflow-y-auto::-webkit-scrollbar-thumb:hover,
.flex-grow.overflow-y-auto:hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
}

/* Theme-aware content area scrollbar using CSS variables */
.dark-theme .flex-grow.overflow-y-auto::-webkit-scrollbar-thumb,
:host(.dark-theme) .flex-grow.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
}

.dark-theme .flex-grow.overflow-y-auto::-webkit-scrollbar-thumb:hover,
.dark-theme .flex-grow.overflow-y-auto:hover ::-webkit-scrollbar-thumb,
:host(.dark-theme) .flex-grow.overflow-y-auto::-webkit-scrollbar-thumb:hover,
:host(.dark-theme) .flex-grow.overflow-y-auto:hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
}

/* Responsive scrollbar for mobile - Maintaining global consistency */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .flex-grow.overflow-y-auto::-webkit-scrollbar {
    width: 4px; /* Consistent with main scrollbar on mobile */
  }
}

/* Smooth transitions for theme changes */
.transition-all {
  transition: all 0.2s ease;
}

/* Button hover effects */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Document item hover effects */
.cursor-pointer:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Theme-specific enhancements */
.dark-theme .cursor-pointer:hover,
:host(.dark-theme) .cursor-pointer:hover {
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

.cursor-pointer:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Empty state styling */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-medium-gray);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Group hover effects */
.group:hover .opacity-0 {
  opacity: 1;
}

/* Card styling */
.bg-white {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}



/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .p-4 {
    padding: 0.75rem;
  }

  .p-3 {
    padding: 0.5rem;
  }

  .text-sm {
    font-size: 0.75rem;
  }
}

/* Animation for document items */
.space-y-2 > * {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Star Icon Interactions */
.ri-star-fill {
  transition: all 0.2s ease;
}

.ri-star-fill:hover {
  transform: scale(1.2);
  filter: brightness(1.1);
}

.ri-star-line {
  transition: all 0.2s ease;
}

.ri-star-line:hover {
  color: #fbbf24 !important;
  transform: scale(1.2);
}

/* Always visible star icons in favorites */
.cursor-pointer .ri-star-fill {
  opacity: 1;
  visibility: visible;
}

/* Active state for buttons */
button:active {
  transform: scale(0.95);
}

/* Border radius consistency */
.rounded-lg {
  border-radius: 0.5rem;
}

/* Text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Flex utilities */
.flex-shrink-0 {
  flex-shrink: 0;
}

.min-w-0 {
  min-width: 0;
}

/* Spacing utilities */
.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

/* Typography */
.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.uppercase {
  text-transform: uppercase;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

/* Enhanced Scrollbar Auto-Hide and Interaction */
.overflow-y-auto::-webkit-scrollbar {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.overflow-y-auto:hover::-webkit-scrollbar {
  opacity: 1;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  opacity: 0;
  transition: opacity 0.3s ease, background 0.2s ease;
}

.overflow-y-auto:hover::-webkit-scrollbar-thumb {
  opacity: 0.6;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  opacity: 0.9 !important;
}

/* Enhanced scrollbar track on hover */
.overflow-y-auto:hover::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 3px;
}

.dark-theme .overflow-y-auto:hover::-webkit-scrollbar-track,
:host(.dark-theme) .overflow-y-auto:hover::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.03);
}

/* Scrollbar active state - Using CSS variables for consistency */
.overflow-y-auto::-webkit-scrollbar-thumb:active {
  background-color: var(--secondary-purple) !important;
  opacity: 1 !important;
  transform: scaleX(1.3);
}

/* Theme-aware active state using CSS variables */
.dark-theme .overflow-y-auto::-webkit-scrollbar-thumb:active,
:host(.dark-theme) .overflow-y-auto::-webkit-scrollbar-thumb:active {
  background-color: var(--secondary-purple) !important;
}

/* Firefox scrollbar styling - Aligned with Global Styles */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: var(--hover-blue-gray) transparent;
  transition: scrollbar-color 0.3s ease;
}

.overflow-y-auto:hover {
  scrollbar-color: var(--secondary-purple) transparent;
}

/* Theme-aware Firefox scrollbar using CSS variables */
.dark-theme .overflow-y-auto,
:host(.dark-theme) .overflow-y-auto {
  scrollbar-color: var(--hover-blue-gray) transparent;
}

.dark-theme .overflow-y-auto:hover,
:host(.dark-theme) .overflow-y-auto:hover {
  scrollbar-color: var(--secondary-purple) transparent;
}

/* Smooth scrolling performance */
.overflow-y-auto {
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

/* Content fade effect when scrolling */
.overflow-y-auto::before {
  content: '';
  position: sticky;
  top: 0;
  height: 10px;
  background: linear-gradient(to bottom, var(--background-white), transparent);
  z-index: 1;
  pointer-events: none;
}

.overflow-y-auto::after {
  content: '';
  position: sticky;
  bottom: 0;
  height: 10px;
  background: linear-gradient(to top, var(--background-white), transparent);
  z-index: 1;
  pointer-events: none;
}

.dark-theme .overflow-y-auto::before,
:host(.dark-theme) .overflow-y-auto::before {
  background: linear-gradient(to bottom, #2b2b33, transparent);
}

.dark-theme .overflow-y-auto::after,
:host(.dark-theme) .overflow-y-auto::after {
  background: linear-gradient(to top, #2b2b33, transparent);
}

/* Enhanced Note Item Styling - Consistent with Settings Sidebar */
.space-y-1 > div.group {
  position: relative;
  background: transparent;
  border: 1px solid transparent;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Active state for note items (can be used for selection) */
.space-y-1 > div.group:active {
  transform: translateX(1px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Note: Hover effects are handled by the main CSS rules above to match settings sidebar exactly */

/* Star icon enhancements for note items */
.space-y-1 > div.group .ri-star-fill,
.space-y-1 > div.group .ri-star-line {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  padding: 2px;
}

.space-y-1 > div.group .ri-star-fill:hover,
.space-y-1 > div.group .ri-star-line:hover {
  background-color: rgba(251, 191, 36, 0.1);
  transform: scale(1.2);
}

/* Time icon styling for recent items */
.space-y-1 > div.group .ri-time-line {
  transition: all 0.2s ease;
  opacity: 0.7;
}

.space-y-1 > div.group:hover .ri-time-line {
  opacity: 1;
  transform: scale(1.05);
}

/* Ensure proper spacing */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* Ensure proper gap */
.gap-3 {
  gap: 0.75rem;
}

/* Ensure proper border radius */
.rounded-md {
  border-radius: 0.375rem;
}

/* Ensure proper font size */
.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

/* Ensure proper font weight */
.font-medium {
  font-weight: 500;
}

/* Focus states for accessibility */
.space-y-1 > div.group:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
  background-color: var(--hover-blue-gray);
}

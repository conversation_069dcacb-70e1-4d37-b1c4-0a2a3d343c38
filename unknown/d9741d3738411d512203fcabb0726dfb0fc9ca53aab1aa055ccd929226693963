import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import QuillMarkdown from 'quilljs-markdown';
import { NotesService, Note } from '../services/notes.service';

import { MarkdownModule } from 'ngx-markdown';
import EditorJS, { ToolConstructable } from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import SimpleImage from '@editorjs/simple-image';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Warning from '@editorjs/warning';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import Link from '@editorjs/link';
import Table from '@editorjs/table';
import ImageTool from '@editorjs/image';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import 'quill/dist/quill';
interface ChatMessage {
  text: string;
  isUser: boolean;
  timestamp: Date;
  hasCodeBlock?: boolean;
  codeLanguage?: string;
  code?: string;
}


@Component({
  selector: 'app-editor',
  imports: [CommonModule, FormsModule, MarkdownModule],
  standalone: true,
  templateUrl: './editor.component.html',
  styleUrl: './editor.component.css'
})
export class EditorComponent {
  @ViewChild('chatContainer') private chatContainer!: ElementRef;
  @ViewChild('chatInput') private chatInput!: ElementRef;
  // @ViewChild('editor') private editorElement!: ElementRef;

  private editor: EditorJS | null = null;
  private quillMarkdown: QuillMarkdown | null = null;
  isToogled = false;
  wordCount = 0;
  markdownContent = '';
  isSaving = false;
  isLoading = false;
  isGenerating = false;
  generateModalVisible = false;
  aiPrompt = '';
  contentType = 'article';
  useCurrentContent = false;
  chatMessages: ChatMessage[] = [
    {
      text: "Hello! How can I help you today?",
      isUser: false,
      timestamp: new Date(),
    }
  ];
  selectedOption = 'article';
  documents: Note[] = [];
  // Add title property
  noteTitle: string = '';
  currentNoteId: number | null = null;

  isDocsOpen = false;  // Set default to false to hide docs section
  showDeleteModal = false;
  noteToDelete: number | null = null;
  sessionId: string = '';
  hasContent = false;
  journal: string = '';
  isjournal: boolean = false;
  private editorInitialized = false;
  private pendingContent: any = null;

  constructor(private notesService: NotesService, private router: Router, private route: ActivatedRoute, private http: HttpClient) { }

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.currentNoteId = params['id'];
      console.log(this.currentNoteId);
      if (this.currentNoteId) {
        this.notesService.getNoteById(this.currentNoteId).subscribe(note => {
          if (note.isJournal === true) {
            {
              this.journal = 'journal';
              this.isjournal = true;
            }
          }

          console.log(note);
          this.noteTitle = note.title;
          if (note?.content) {
            try {
              const parsedContent = JSON.parse(note.content);
              if (this.editorInitialized) {
                this.updateEditorContent(parsedContent);
              } else {
                this.pendingContent = parsedContent;
              }
            } catch (error) {
              console.error('Error parsing note content:', error);
            }
          }
        });
      }

      if (this.route.snapshot.url[0].path === 'journal') {
        console.log('Journal route detected');
        this.journal = 'journal';
        this.isjournal = true;
      }
    });

    this.sessionId = crypto.randomUUID();
  }


  ngAfterViewInit() {
    if (!this.editor) {
      setTimeout(() => {
        this.initializeEditor();
      }, 0);
    }
  }

  createNewDocument() {
    this.isToogled = false;
    this.clearContent();
  }
  isFavourite: boolean = false;

  toggleFavorite() {
    this.isFavourite = !this.isFavourite;
    // Add your API call here to update the favorite status
  }

  private initializeEditor() {
    if (this.editor) return; // Prevent re-initialization

    const editorElement = document.getElementById('editor');
    if (!editorElement) {
      console.error('Element with ID "editor" is missing.');
      return;
    }

    this.editor = new EditorJS({
      holder: 'editor',
      minHeight: 200,
      placeholder: 'Start writing or paste your content here...',
      onChange: () => {
        this.editor?.save().then(data => {
          this.hasContent = data.blocks.length > 0;
        });
      },
      onReady: () => {
        this.editorInitialized = true;
        if (this.pendingContent) {
          this.updateEditorContent(this.pendingContent);
          this.pendingContent = null;
        }
      },
      tools: {
        header: {
          class: Header as unknown as ToolConstructable,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4],
            defaultLevel: 1
          }
        },
        list: List as unknown as ToolConstructable,
        image: {
          class: ImageTool as unknown as { new(): any }, // Ensure type compatibility
          config: {
            field: 'file', // Form field name for the file
            types: 'image/*', // Acceptable file types
            captionPlaceholder: 'Caption', // Placeholder for the caption input
            buttonContent: 'Select an Image', // Button content
            additionalRequestHeaders: {
              'Content-Type': 'multipart/form-data'
            },
            uploader: {
              uploadByFile: (file: File) => {
                return new Promise((resolve, reject) => {
                  const reader = new FileReader();
                  reader.onload = () => {
                    resolve({
                      success: 1,
                      file: {
                        url: reader.result as string
                      }
                    });
                  };
                  reader.onerror = (error) => {
                    reject({
                      success: 0,
                      message: 'Could not read file'
                    });
                  };
                  reader.readAsDataURL(file);
                });
              }
            }
          }
        },
        checklist: Checklist as unknown as ToolConstructable,
        quote: Quote as unknown as ToolConstructable,
        warning: Warning as unknown as ToolConstructable,
        marker: Marker as unknown as ToolConstructable,
        code: CodeTool as unknown as ToolConstructable,
        delimiter: Delimiter as unknown as ToolConstructable,
        inlineCode: InlineCode as unknown as ToolConstructable,
        link: {
          class: Link as unknown as ToolConstructable,
          config: {
            endpoint: 'https://localhost:44350/api/Link/fetch', // Update with your API URL
          }
        },
        table: Table as unknown as ToolConstructable
      }
    });
  }

  // Convert Quill content to Markdown
  private getMarkdownContent(): string {
    if (!this.editor) return '';

    const delta = this.editor.save().then(data => {
      const markdown = this.deltaToMarkdown(data);
      return markdown;
    }).then(markdown => markdown);
    return '';
  }

  // Helper function to convert Delta to Markdown
  private deltaToMarkdown(delta: any): string {
    if (!delta || !delta.ops) {
      console.error('Invalid delta object:', delta);
      return '';
    }

    let markdown = '';
    delta.ops.forEach((op: any) => {
      if (typeof op.insert === 'string') {
        let text = op.insert;
        if (op.attributes) {
          if (op.attributes.bold) text = `***${text}***`;
          if (op.attributes.italic) text = `*${text}*`;
          if (op.attributes.code) text = `\`${text}\``;
          if (op.attributes.header === 1) text = `# ${text}`;
          if (op.attributes.blockquote) text = `> ${text}`;
          if (op.attributes.list === 'bullet') text = `- ${text}`;
          if (op.attributes.list === 'ordered') text = `1. ${text}`;
        }
        markdown += text;
      } else if (op.insert && op.insert.image) {
        markdown += `![Image](${op.insert.image})\n`;
      }
    });
    return markdown;
  }

  generateContent() {
    this.generateModalVisible = true;
    const currentContent = this.getMarkdownContent();
    if (currentContent) {
      this.aiPrompt = `Based on this context:\n${currentContent}\n\nPlease generate a ${this.selectedOption}`;
      this.useCurrentContent = true;
    }
  }

  async saveContent() {
    if (!this.editor) return;

    this.isSaving = true;
    try {
      const editorData = await this.editor.save();
      if (!editorData || !editorData.blocks) {
        throw new Error('Invalid editor data');
      }
      const note: Partial<Note> = {
        id: this.currentNoteId || undefined,
        title: this.noteTitle || 'Untitled Note',
        isFavourite: this.isFavourite,
        content: JSON.stringify(editorData), // Save as JSON string
        isJournal: this.journal ? true : false
      };


      console.log(note);
      await this.notesService.createOrUpdateNote(note).toPromise();
      this.isToogled = true;
      this.clearContent();


    }
    catch (error) {
      console.error('Error saving note:', error);
    } finally {

      if (this.isjournal) {
        this.router.navigate(['student/documents/journal']);
      } else {
        this.router.navigate(['student/all-documents']);
      }
    }
  }

  ngOnDestroy() {
    if (this.quillMarkdown) {
      this.quillMarkdown.destroy();
    }
    this.isjournal = false;
  }

  private updateWordCount() {
    if (this.editor) {
      this.editor.save().then(data => {
        const text = data.blocks.map(block => block.data.text || '').join(' ');
        this.wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
      });
    }
  }

  clearContent() {
    if (this.editor && this.editor.blocks) {
      this.editor.blocks.clear(); // Use blocks.clear() to clear the content
      this.noteTitle = '';
      this.currentNoteId = null;
    }
  }




  deleteDocument(id: number) {
    this.noteToDelete = id;
    this.showDeleteModal = true;
  }

  editDocument(note: Note) {
    if (this.editor) {
      this.noteTitle = note.title;
      this.currentNoteId = note.id;
      // Parse markdown and apply formatting
      this.editor.blocks.clear();
      try {
        const content = JSON.parse(note.content);
        this.editor.render(content);
      } catch (e) {
        console.error('Error parsing document content:', e);
      }
      this.updateWordCount();
    }
  }

  viewDocument(doc: Note) {
    if (this.editor) {
      this.noteTitle = doc.title;
      this.currentNoteId = doc.id;
      try {
        const content = JSON.parse(doc.content);
        this.editor.render(content);
      } catch (e) {
        console.error('Error parsing document content:', e);
      }
      this.isToogled = false;
    }
  }

  toggleDocs() {
    this.isDocsOpen = !this.isDocsOpen;
    if (this.isDocsOpen && !this.editor) {
      this.initializeEditor();
    }
  }

  async confirmDelete() {
    if (this.noteToDelete) {
      try {
        await this.notesService.deleteNote(this.noteToDelete).toPromise();
      } catch (error) {
        console.error('Error deleting document:', error);
      } finally {
        this.showDeleteModal = false;
        this.noteToDelete = null;
      }
    }
  }

  async updateEditorContent(newContent: any) {
    try {
      if (!this.editor) {
        console.warn('Editor not initialized yet');
        return;
      }

      // Wait for any pending operations to complete
      await this.editor.isReady;

      // Clear existing content first
      await this.editor.blocks.clear();

      // Render new content
      await this.editor.render(newContent);

      // Update word count and other UI elements
      this.updateWordCount();
      this.hasContent = true;
    } catch (error) {
      console.error('Error updating editor content:', error);
    }
  }

  // Alternative method if you want to add blocks programmatically
  async addContentToEditor(content: string) {
    if (this.editor) {
      await this.editor.blocks.insert('paragraph', {
        text: content
      });
    }
  }

  // If you need to update specific blocks
  async updateSpecificBlock(blockIndex: number, newData: any) {
    if (this.editor) {
      const currentBlock = await this.editor.blocks.getBlockByIndex(blockIndex);
      if (currentBlock) {
        await this.editor.blocks.update(currentBlock.id, newData);
      }
    }
  }

  async executeCommand(command: string) {
    if (!this.editor) return;

    switch (command) {
      case 'link':
        // The editor instance already has the link tool configured
        // We just need to trigger the link tool UI
        await this.editor.blocks.insert('link', {
          link: '',
          meta: {
            title: '',
            description: '',
            image: {
              url: ''
            }
          }
        });
        break;

      // Add other commands here if needed
      default:
        console.warn(`Unknown command: ${command}`);
    }
  }

  async addBlock(blockType: string) {
    if (!this.editor) return;

    try {
      await this.editor.blocks.insert(blockType);
    } catch (error) {
      console.error(`Error adding block of type ${blockType}:`, error);
    }
  }

}

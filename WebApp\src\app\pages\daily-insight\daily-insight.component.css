/* Card animations and effects */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

:host {
  display: block;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Card styling and hover effects */
:host ::ng-deep .group {
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

:host ::ng-deep .group:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Text truncation for card content */
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Styling for insight content */
.insight-content pre {
  font-family: inherit;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

/* Preserve numbered lists */
.insight-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

/* Preserve bulleted lists */
.insight-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

/* Style for pre tag to preserve formatting but look nice */
.insight-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  padding: 0.5rem 0;
}

/* Add a subtle transition when expanding/collapsing */
.insight-content {
  transition: max-height 0.3s ease-in-out;
}

/* Card layout styles */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 1.5rem;
}

.card-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px; /* Increased minimum height for taller cards */
}

/* Expanded content overlay */
.expanded-content-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-white);
  z-index: 20;
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: auto;
  padding: 1rem;
  border: 1px solid var(--hover-blue-gray);
  animation: fadeIn 0.2s ease-in-out;
}

/* Dark mode support for expanded overlay */
:host-context(.dark) .expanded-content-overlay {
  background-color: #282c34;
  border-color: #3a3f4b;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Overlay backdrop for better focus */
.card-wrapper.expanded-card::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 15;
  animation: fadeIn 0.2s ease-in-out;
}

/* Expanded card styling */
.expanded-card {
  z-index: 30 !important;
}

/* Style for numbered items */
.insight-content pre span.numbered-item {
  display: block;
  margin-bottom: 0.25rem;
}

/* Add some spacing between paragraphs */
.insight-content p {
  margin-bottom: 0.75rem;
}

/* Card hover button animations */
:host ::ng-deep .group .opacity-0 {
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

:host ::ng-deep .group:hover .opacity-0 {
  opacity: 1 !important;
  transform: translateY(0);
}

/* Search section styles */
.search-section {
  position: relative;
  z-index: 10;
}

.search-input {
  transition: all 0.3s ease;
}

.search-input:focus {
  box-shadow: 0 0 0 2px rgba(var(--primary-purple-rgb), 0.2);
}

/* Agent discovery modal styles */
:host ::ng-deep .agent-discovery-modal .ant-modal-content {
  background-color: var(--background-white);
  border-radius: var(--border-radius-large);
}

:host ::ng-deep .dark .agent-discovery-modal .ant-modal-content {
  background-color: #282c34;
  border-radius: var(--border-radius-large);
}

:host ::ng-deep .agent-discovery-modal .ant-modal-body {
  background-color: var(--background-white);
  color: var(--text-dark);
}

:host ::ng-deep .dark .agent-discovery-modal .ant-modal-body {
  background-color: #282c34;
  color: white;
}

:host ::ng-deep .agent-discovery-modal .ant-modal-header {
  background-color: var(--background-white);
  border-bottom: 1px solid var(--hover-blue-gray);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

:host ::ng-deep .dark .agent-discovery-modal .ant-modal-header {
  background-color: #282c34;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

:host ::ng-deep .agent-discovery-modal .ant-modal-title {
  color: var(--text-dark);
  font-weight: var(--font-weight-bold);
}

:host ::ng-deep .dark .agent-discovery-modal .ant-modal-title {
  color: white;
  font-weight: var(--font-weight-bold);
}

:host ::ng-deep .agent-discovery-modal .ant-modal-close {
  color: var(--text-medium-gray);
}

:host ::ng-deep .dark .agent-discovery-modal .ant-modal-close {
  color: rgba(255, 255, 255, 0.7);
}

:host ::ng-deep .agent-discovery-modal .ant-modal-close:hover {
  color: var(--text-dark);
}

:host ::ng-deep .dark .agent-discovery-modal .ant-modal-close:hover {
  color: white;
}

/* Modal footer is now managed in the template */

/* Modal button styles are now managed in the template */

/* Agent category styles */
.agent-category {
  transition: all 0.2s ease;
  cursor: pointer;
}

.agent-category:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Agent list item styles */
.agent-list-item {
  transition: all 0.2s ease;
  cursor: pointer;
}

.agent-list-item:hover {
  transform: translateX(4px);
  background-color: var(--hover-blue-gray);
}

/* Loading and empty states */
.loading-container {
  min-height: 200px;
  transition: all 0.3s ease;
}

.empty-state {
  background-color: var(--background-white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-default);
  transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :host ::ng-deep .group .opacity-0 {
    opacity: 1 !important;
    transform: translateY(0);
  }

  .search-section {
    width: 100%;
    margin-bottom: 1rem;
  }

  /* Make expanded overlay fullscreen on mobile */
  .expanded-content-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    z-index: 1000;
    padding: 1.5rem;
  }

  /* Add some padding to the bottom for mobile scrolling */
  .expanded-content-overlay .insight-content {
    padding-bottom: 2rem;
  }

  /* Ensure the close button is more visible on mobile */
  .expanded-content-overlay .close-button {
    position: sticky;
    top: 0;
    right: 0;
    background-color: var(--background-white);
    z-index: 10;
  }
}

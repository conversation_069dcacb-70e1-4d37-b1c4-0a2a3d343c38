/* Agent Sidebar Styles */
:host {
  display: flex;
  flex: 1;
  height: 100%;
  position: relative;
  top: 0;
  right: 0;
  z-index: 10;
  width: 100%; /* Take parent container width */
  max-width: 100%; /* Ensure it doesn't exceed parent width */
  overflow: hidden; /* Prevent content from spilling out */
}

/* Ensure the inner content expands to fill the container width */
:host > div {
  flex: 1;
  width: 100%; /* Take full width of container */
  height: 100%;
  position: relative;
}

/* Transition styles */
.sidebar-enter {
  transform: translateX(100%);
}

.sidebar-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.sidebar-exit {
  transform: translateX(0);
}

.sidebar-exit-active {
  transform: translateX(100%);
  transition: transform 300ms ease-in-out;
}

/* Agent card hover effect */
.agent-card {
  transition: all 0.2s ease-in-out;
}

.agent-card:hover {
  transform: translateY(-2px);
}

/* Dark theme hover effect */
:host-context(.dark-theme) .agent-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* Light theme hover effect */
:host-context(:not(.dark-theme)) .agent-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Model badge styling */
.model-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

/* Dark theme model badge */
:host-context(.dark-theme) .model-badge {
  background-color: #1E1E1E;
  color: #a0a0a0;
}

/* Light theme model badge */
:host-context(:not(.dark-theme)) .model-badge {
  background-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

/* Agent name styling */
.agent-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Dark theme agent name */
:host-context(.dark-theme) .agent-name {
  color: white;
}

/* Light theme agent name */
:host-context(:not(.dark-theme)) .agent-name {
  color: var(--text-dark);
}

/* Agent description styling */
.agent-description {
  font-size: 0.875rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dark theme description */
:host-context(.dark-theme) .agent-description {
  color: #a0a0a0;
}

/* Light theme description */
:host-context(:not(.dark-theme)) .agent-description {
  color: var(--text-medium-gray);
}

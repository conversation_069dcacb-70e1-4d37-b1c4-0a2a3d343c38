declare module 'quilljs-markdown' {
  import Quill from 'quill';


  interface MarkdownOptions {
    ignoreTags?: string[];
    tags?: {
      blockquote?: { pattern: RegExp };
      bold?: { pattern: RegExp };
      italic?: { pattern: RegExp };
      [key: string]: { pattern: RegExp } | undefined;
    };
  }

  export default class QuillMarkdown {
    constructor(quill: Quill, options?: MarkdownOptions);
    destroy(): void;
  }
}

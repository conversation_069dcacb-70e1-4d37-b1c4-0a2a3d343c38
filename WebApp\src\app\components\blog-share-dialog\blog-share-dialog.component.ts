import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzMessageService } from 'ng-zorro-antd/message';
import { MarkdownModule } from 'ngx-markdown';
import { ChatServiceProxy, SaveBlogRequestDto } from '../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-blog-share-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzModalModule,
    NzInputModule,
    NzButtonModule,
    MarkdownModule
  ],
  templateUrl: './blog-share-dialog.component.html',
  styleUrls: ['./blog-share-dialog.component.css']
})
export class BlogShareDialogComponent implements OnInit {
  blogContent: string = '';
  blogTitle: string = '';
  shareUrl: string = '';
  isLoading: boolean = false;

  constructor(
    private modalRef: NzModalRef,
    private messageService: NzMessageService,
    private chatService: ChatServiceProxy,
    @Inject(NZ_MODAL_DATA) public data: { blogContent: string }
  ) { }

  ngOnInit(): void {
    if (this.data && this.data.blogContent) {
      this.blogContent = this.data.blogContent;
      this.blogTitle = this.extractBlogTitle(this.blogContent);
    }
  }

  /**
   * Extracts the title from a blog post
   * @param response The blog post content
   * @returns The blog title or a default title
   */
  extractBlogTitle(response: string): string {
    if (!response) return 'Blog Post';

    // Look for the first heading (# Heading)
    const titleRegex = /^#\s+(.+)$/m;
    const match = response.match(titleRegex);

    if (match && match[1]) {
      return match[1].trim();
    }

    return 'Blog Post';
  }

  /**
   * Closes the dialog without sending
   */
  cancel(): void {
    this.modalRef.close();
  }

  /**
   * Sends the blog content to the specified URL
   */
  sendBlog(): void {
    if (!this.shareUrl.trim()) {
      this.messageService.error('Please enter a URL to share the blog');
      return;
    }

    if (!this.blogContent.trim()) {
      this.messageService.error('No blog content to share');
      return;
    }

    if (!this.blogTitle.trim()) {
      this.messageService.error('Please enter a blog title');
      return;
    }

    this.isLoading = true;

    // Create the request DTO with title included
    const request = new SaveBlogRequestDto({
      url: this.shareUrl.trim(),
      content: this.blogContent.trim(),
      title: this.blogTitle.trim()
    });

    // Call the API
    this.chatService.saveBlog(request).subscribe({
      next: (response) => {
        this.isLoading = false;

        if (response.isError) {
          this.messageService.error(response.message || 'Failed to share blog');
        } else {
          this.messageService.success(response.message || 'Blog shared successfully!');
          this.modalRef.close({
            url: this.shareUrl,
            content: this.blogContent,
            title: this.blogTitle,
            response: response
          });
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error sharing blog:', error);

        // Handle different types of errors
        let errorMessage = 'Failed to share blog. Please try again.';

        this.messageService.error(errorMessage);
      }
    });
  }

  /**
   * Copies the blog content to clipboard
   */
  copyBlogContent(): void {
    if (!this.blogContent) {
      this.messageService.error('No content to copy');
      return;
    }

    navigator.clipboard.writeText(this.blogContent).then(() => {
      this.messageService.success('Blog content copied to clipboard');
    }).catch(() => {
      this.messageService.error('Failed to copy content');
    });
  }
}

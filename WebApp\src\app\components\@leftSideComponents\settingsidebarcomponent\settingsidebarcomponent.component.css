/* Settings Sidebar Styles */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

/* Active tab styling */
.bg-\[var\(--secondary-purple\)\] {
  background-color: var(--secondary-purple) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateX(2px);
}

.bg-\[var\(--active-tab-bg\)\] {
  background-color: var(--active-tab-bg) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transform: translateX(2px);
}

/* Active text styling for light theme */
.text-black {
  color: black !important;
  font-weight: 500 !important;
}

/* Active text styling for dark theme */
:host-context(.dark) .text-white {
  color: white !important;
  font-weight: 500 !important;
}

/* Ensure proper height calculation */
.max-h-\[calc\(100vh-74px\)\] {
  max-height: calc(100vh - 74px);
}

/* Active indicator bar animation */
.absolute.left-0 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhance active indicator in light theme */
:host-context(:not(.dark)) .absolute.left-0.opacity-100 {
  box-shadow: 0 0 8px rgba(107, 70, 193, 0.5);
  width: 4px !important;
}

/* Enhance active indicator in dark theme */
:host-context(.dark) .absolute.left-0.opacity-100 {
  box-shadow: 0 0 8px rgba(16, 163, 127, 0.5);
  width: 4px !important;
}

/* Enhance the section headers */
.uppercase.font-medium {
  letter-spacing: 0.05em;
}

/* Hover effects */
.hover\:bg-\[var\(--hover-blue-gray\)\]:hover {
  background-color: var(--hover-blue-gray);
}

.hover\:bg-\[var\(--primary-purple\)\]:hover {
  background-color: var(--primary-purple) !important;
  transform: translateX(2px);
}

/* Dark theme hover effect for active buttons */
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\]:hover {
  background-color: var(--primary-purple) !important;
  transform: translateX(3px);
}

.group:hover {
  transform: translateX(2px);
}

/* Theme-specific hover effects */
:host-context(.dark) .group:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

:host-context(:not(.dark)) .group:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Improve hover text visibility */
:host-context(:not(.dark)) .group:hover i,
:host-context(:not(.dark)) .group:hover span {
  color: var(--primary-purple) !important;
}

:host-context(.dark) .group:hover i,
:host-context(.dark) .group:hover span {
  color: white !important;
}

/* Transition effects */
.transition-all {
  transition: all 0.3s ease;
}

/* Smooth transitions for all elements */
i, span {
  transition: all 0.2s ease;
}

/* Ensure proper spacing */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* Ensure proper padding */
.p-4 {
  padding: 1rem;
}

/* Ensure proper margin */
.mb-4 {
  margin-bottom: 1rem;
}

/* Ensure proper gap */
.gap-3 {
  gap: 0.75rem;
}

/* Ensure proper border radius */
.rounded-lg {
  border-radius: 0.5rem;
}

/* Ensure proper font size */
.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

/* Ensure proper font weight */
.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

/* Ensure proper text color */
.text-\[var\(--text-medium-gray\)\] {
  color: var(--text-medium-gray);
}

.text-\[var\(--text-dark\)\] {
  color: var(--text-dark);
}

/* Hover text color */
.group-hover\:text-white {
  color: white !important;
}

.group-hover\:text-\[var\(--primary-purple\)\] {
  color: var(--primary-purple) !important;
}

/* Add subtle hover effect to section headers */
.flex-1.h-\[1px\] {
  transition: opacity 0.3s ease;
}

.mb-4:hover .flex-1.h-\[1px\] {
  opacity: 0.8;
}

/* Enhance the active state with a subtle glow */
.bg-\[var\(--secondary-purple\)\] i,
.bg-\[var\(--secondary-purple\)\] span {
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
}

/* Theme-specific active state styling */
:host-context(.dark) .bg-\[var\(--secondary-purple\)\],
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\] {
  background-color: #10A37F !important; /* Exact color from the screenshot */
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

:host-context(.dark) .bg-\[var\(--secondary-purple\)\] i,
:host-context(.dark) .bg-\[var\(--secondary-purple\)\] span,
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\] i,
:host-context(.dark) .bg-\[var\(--active-tab-bg\)\] span {
  color: white !important;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.2);
}

/* Light theme active state styling */
:host-context(:not(.dark)) .bg-\[var\(--secondary-purple\)\] {
  background-color: #D6BCFA !important; /* Exact color from the screenshot */
  border-left: 4px solid var(--primary-purple);
  box-shadow: 0 2px 8px rgba(107, 70, 193, 0.2);
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

:host-context(:not(.dark)) .bg-\[var\(--secondary-purple\)\] i,
:host-context(:not(.dark)) .bg-\[var\(--secondary-purple\)\] span {
  color: black !important;
  font-weight: 600;
}

/* Ensure proper background color in dark theme */
:host-context(.dark) :host {
  background-color: var(--background-white);
}

/* Ensure proper background color in light theme */
:host-context(:not(.dark)) :host {
  background-color: white;
}

/* Ensure menu items are visible in light theme */
:host-context(:not(.dark)) .group i {
  color: var(--primary-purple);
}

:host-context(:not(.dark)) .group span {
  color: var(--text-dark);
}

/* Improve hover effects in light theme */
:host-context(:not(.dark)) .group:hover {
  background-color: rgba(107, 70, 193, 0.05);
}

:host-context(:not(.dark)) .group:hover i,
:host-context(:not(.dark)) .group:hover span {
  color: var(--primary-purple) !important;
}

import {
  Component,
  ViewChild,
  ElementRef,
  OnInit,
  inject,
  AfterViewInit,
  OnDestroy,
  HostListener,
  Pipe,
  PipeTransform,
  isDevMode,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { marked } from 'marked';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AgentDefinitionServiceProxy,
  WorkspaceServiceProxy,
  AgentChatServiceProxy,
  AgentChatRequestDto,
  AgentChatHistoryDto,
  AgentChatConversationDto,
  AgentChatResponseDto,
  ChatResponseDto,
  ResponseMessage,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { AuthService } from '../../../shared/services/auth.service';
import { ChatService } from '../../services/chat.service';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { MarkdownModule } from 'ngx-markdown';
import { SourceReferencesComponent } from '../../components/@rightSideComponents/source-references/source-references.component';
import { AgentSidebarComponent } from '../../components/@rightSideComponents/agent-sidebar/agent-sidebar.component';
import { AngularSplitModule } from 'angular-split';
import { ChatListService } from '../../services/chat-list.service';
import { DateTime } from 'luxon';

// Simple RelativeTime pipe for timestamp display
@Pipe({
  name: 'relativeTime',
  standalone: true,
})
export class RelativeTimePipe implements PipeTransform {
  transform(value: string | Date): any {
    if (!value) return '';

    const date = new Date(value);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) {
      return Promise.resolve('just now');
    } else if (diffInMinutes < 60) {
      return Promise.resolve(
        `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`
      );
    } else if (diffInHours < 24) {
      return Promise.resolve(
        `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
      );
    } else if (diffInDays < 7) {
      return Promise.resolve(
        `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
      );
    } else {
      return Promise.resolve(date.toLocaleDateString());
    }
  }
}

@Component({
  selector: 'app-agent-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ServiceProxyModule,
    NzDrawerModule,
    MarkdownModule,
    NzSelectModule,
    NzBadgeModule,
    NzToolTipModule,
    NzModalModule,
    SourceReferencesComponent,
    AgentSidebarComponent,
    AngularSplitModule,
  ],
  providers: [NzModalService],
  templateUrl: './agent-chat.component.html',
  styleUrl: './agent-chat.component.css',
})
export class AgentChatComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chatInput') chatInput!: ElementRef;
  @ViewChild('chatContainer') chatContainer!: ElementRef;
  @ViewChild('promptDialog') promptDialog!: ElementRef;
  @ViewChild('toggleWorkspaceList') toggleWorkspaceList!: ElementRef;
  @ViewChild('agentListDialog') agentListDialog!: ElementRef;

  // User message input
  userInput: AgentChatRequestDto = new AgentChatRequestDto();

  // Chat data
  conversations: any[] = [];
  currentConversation: AgentChatConversationDto = new AgentChatConversationDto({
    agentName: '',
    histories: []
  });
  isMessageLoading = false;
  previousResponse = '';

  // Search results and source references
  searchResults: any[] = [];
  currentSourceName = '';

  // Response navigation tracking
  currentResponseIndexes: { [key: string]: number } = {}


  // Workspaces and agents
  workspaces: any = [];
  selectedWorkspace = '';
  workspaceAgents: any = [];
  // UI state
  showScrollButton: boolean = false;
  isChatHistoryOpen: boolean = false;
  showAgentDropdown = false;
  selectedAgent = '';
  isAgentSidebarOpen = false;
  agentSidebarTitle = 'Agent Tools';
  mentionDropdownVisible = false;
  mentionFilteredAgents: any[] = [];
  selectedMentionIndex = 0;
  showPromptDialog = false;
  filteredPrompts: any[] = [];
  selectedPromptIndex = 0;
  isRecording = false;
  hasNoHistories = true;

  // Splitter properties
  mainContentSplitSize = 100;
  rightSidebarSplitSize = 0;
  rightSidebarWidth = 350;
  isDragging = false;
  showSearchResultsSidebar = false;

  // Message subscription and streaming state
  private messageSubscription?: Subscription;
  private currentStreamingMessageId?: string;
  private isStreamingActive = false;

  constructor(
    private router: Router,
    private authService: AuthService,
    private agentDefinition: AgentDefinitionServiceProxy,
    private sanitizer: DomSanitizer,
    private chatService: ChatService,
    public auth: AuthService,
    private message: NzMessageService,
    private workspaceService: WorkspaceServiceProxy,
    private modal: NzModalService,
    private agentChatService: AgentChatServiceProxy
  ) {
    // Configure marked options
    marked.setOptions({
      breaks: true,
      gfm: true,
    });

    // Subscribe to SignalR streaming messages
    this.messageSubscription = this.chatService.messageReceived$.subscribe(
      ({ message, isError, isComplete }) => {
        console.log('Agent chat - Message received:', message);
        this.handleStreamingMessage(message, isError || false, isComplete || false);
      }
    );
  }

  route = inject(ActivatedRoute);
  chatListService = inject(ChatListService);
  ngOnInit(): void {
    // Initialize component
    this.loadSavedRightSidebarWidth();
    this.loadWorkspaces();

    // Get agent name from route parameter
    this.route.paramMap.subscribe(params => {
      const agentName = params.get('name');
      if (agentName) {
        this.selectedAgent = agentName;
        this.userInput.agentName = agentName;
      }

      // Load agent chat histories using the route parameter if available
      this.loadAgentChatHistories(agentName || undefined);
    });
  }

  ngAfterViewInit(): void {
    this.scrollToBottom();
    this.adjustInputHeight();
  }

  ngOnDestroy(): void {
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
    }

    // Clear streaming state
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;
  }  /**
   * Loads agent chat histories from API
   * @param agentName Optional agent name to filter histories
   */
  loadAgentChatHistories(agentName?: string) {
    this.isMessageLoading = true;

    // Log the agent name for debugging
    console.log('Loading chat histories for agent:', agentName || 'all agents');

    this.agentChatService.histories(agentName).subscribe({
      next: (conversations) => {
        // Reverse the conversations array so the last conversation appears first
        let convArr = Array.isArray(conversations) ? conversations : [conversations];
        convArr = convArr.reverse();
        console.log(convArr)

        // Reverse the histories inside each conversation so the latest message is first
        convArr.forEach(c => {
          if (c.histories && Array.isArray(c.histories)) {
            c.histories = c.histories.reverse();
            // Set the current response index to the latest response for each history
            // and initialize message states for loaded conversations
            c.histories.forEach(history => {
              if (history.responses && history.responses.length > 0) {
                this.currentResponseIndexes[history.id || ''] = history.responses.length - 1;
              }

              // Initialize message states for loaded conversations to ensure action buttons show properly
              // Loaded messages should not be in loading or streaming state
              history.isLoading = false;
              history.isStreaming = false;
              history.editingMode = false;
              history.streamingText = '';
            });
          }
        });

        this.conversations = convArr;

        // Set hasNoHistories flag based on conversations returned
        this.hasNoHistories = this.conversations.length === 0 ||
          this.conversations.every(c => !c.histories || c.histories.length === 0);

        if (this.conversations.length > 0) {
          // Set current conversation to first agent or selected agent
          const foundConversation = agentName
            ? this.conversations.find(c => c.agentName === agentName)
            : this.conversations[0];          if (foundConversation) {
            this.currentConversation = foundConversation;

            // Check if this conversation has any histories
            if (foundConversation.histories && foundConversation.histories.length > 0) {
              this.hasNoHistories = false;
            }

            // Set selected agent from conversation if not already set from URL
            if (this.currentConversation.agentName && !this.selectedAgent) {
              this.selectedAgent = this.currentConversation.agentName;
              this.userInput.agentName = this.currentConversation.agentName;
            }
          } else if (agentName) {
            // If we're searching for a specific agent but didn't find conversations,
            // create a new conversation for that agent
            this.currentConversation = new AgentChatConversationDto({
              agentName: agentName,
              histories: []
            });
            this.conversations.push(this.currentConversation);
          }
        } else if (agentName) {
          // No conversations exist but we have an agent name, create a new conversation
          this.currentConversation = new AgentChatConversationDto({
            agentName: agentName,
            histories: []
          });
          this.conversations.push(this.currentConversation);
          this.hasNoHistories = true;
        } else {
          // No conversations at all
          this.hasNoHistories = true;
        }

        this.isMessageLoading = false;
        setTimeout(() => this.scrollToBottom(), 100);
      },
      error: (error) => {
        console.error('Error loading agent chat histories:', error);
        this.message.error('Failed to load chat histories');
        this.isMessageLoading = false;
      }
    });
  }

  /**
   * Loads workspaces from the service
   */
  loadWorkspaces() {
    this.workspaceService.getAll().subscribe({
      next: (workspaces) => {
        this.workspaces = workspaces;
        if (this.workspaces.length > 0) {
          this.selectedWorkspace = this.workspaces[0].title;
          this.loadAgentsForWorkspace(this.selectedWorkspace);
        }
      },
      error: (error) => {
        console.error('Error loading workspaces:', error);
      }
    });
  }

  /**
   * Loads agents for a specific workspace
   */
  loadAgentsForWorkspace(workspaceName: string) {
    if (!workspaceName) return;

    this.agentDefinition.getAllByWorkspace(workspaceName).subscribe({
      next: (agents) => {
        this.workspaceAgents = agents;
        // Update the mentionFilteredAgents with our agents
        this.mentionFilteredAgents = [...this.workspaceAgents];
      },
      error: (error) => {
        console.error('Error loading agents for workspace:', error);
      }
    });
  }

  /**
   * Adds a suggestion to the chat input
   */
  addSuggestionToChat(suggestion: any) {
    this.userInput.question = suggestion.text;
    if (suggestion.isUserPrompt) {
      this.sendMessage();
    }
  }

  /**
   * Helper function to extract content for different response types
   * These functions would normally parse special format responses
   * For now they act as basic stubs
   */  hasSqlContent(text: string): boolean {
    return !!text && text.includes('```sql');
  }

  extractNonSqlContent(text: string): string {
    if (!text) return '';
    return text.replace(/```sql[\s\S]*?```/g, '');
  }

  extractSqlContent(text: string): string {
    if (!text || !this.hasSqlContent(text)) return '';
    const match = text.match(/```sql([\s\S]*?)```/);
    return match ? match[1].trim() : '';
  }

  extractBlogTitle(text: string): string {
    if (!text) return 'Blog Post';
    const titleMatch = text.match(/^#\s*(.*?)$/m);
    return titleMatch ? titleMatch[1] : 'Blog Post';
  }

  extractEmailSubject(text: string): string {
    if (!text) return '';
    const subjectMatch = text.match(/Subject:(.*?)$/m);
    return subjectMatch ? subjectMatch[1].trim() : '';
  }

  extractEmailTo(text: string): string {
    if (!text) return '';
    const toMatch = text.match(/To:(.*?)$/m);
    return toMatch ? toMatch[1].trim() : '';
  }

  extractEmailCc(text: string): string {
    if (!text) return '';
    const ccMatch = text.match(/Cc:(.*?)$/m);
    return ccMatch ? ccMatch[1].trim() : '';
  }

  extractEmailBody(text: string): string {
    if (!text) return '';
    const parts = text.split(/^Body:/m);
    return parts.length > 1 ? parts[1].trim() : text;
  }

  /**
   * Mentions and prompt handling
   */
  selectMentionedAgent(agent: any) {
    if (!agent) return;
    this.userInput.agentName = agent.agentName;
    this.mentionDropdownVisible = false;
  }

  selectPrompt(prompt: any) {
    if (!prompt) return;
    this.userInput.question = prompt.prompt;
    this.showPromptDialog = false;
    this.adjustInputHeight();
  }

  /**
   * Loads and saves right sidebar width from localStorage
   */
  loadSavedRightSidebarWidth() {
    const savedWidth = localStorage.getItem('rightSidebarWidth');
    if (savedWidth) {
      this.rightSidebarWidth = parseInt(savedWidth, 10);
    }
  }

  /**
   * Scrolls the chat container to the bottom
   */
  scrollToBottom() {
    try {
      if (this.chatContainer) {
        setTimeout(() => {
          this.chatContainer.nativeElement.scrollTop =
            this.chatContainer.nativeElement.scrollHeight;
        }, 100);
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  /**
   * Adjusts the height of the input textarea
   */
  adjustInputHeight() {
    if (this.chatInput && this.chatInput.nativeElement) {
      const element = this.chatInput.nativeElement;
      element.style.height = 'auto';
      element.style.height = Math.min(element.scrollHeight, 200) + 'px';
    }
  }

  /**
   * Sends a message to agent chat API
   */
  async sendMessage() {
    if (!this.userInput.question?.trim()) return;
    if (!this.userInput.agentName && this.selectedAgent) {
      this.userInput.agentName = this.selectedAgent;
    }

    // Store the original message for immediate display
    const originalMessage = this.userInput.question;
    const agentName = this.userInput.agentName || this.selectedAgent;

    // Find current conversation or create a new one
    if (this.currentConversation.agentName !== agentName) {
      const foundConversation = this.conversations.find(c => c.agentName === agentName);

      if (foundConversation) {
        this.currentConversation = foundConversation;
      } else {
        this.currentConversation = new AgentChatConversationDto({
          agentName: agentName,
          histories: []
        });

        this.conversations.push(this.currentConversation);
      }
    }

    // Ensure histories array exists
    if (!this.currentConversation.histories) {
      this.currentConversation.histories = [];
    }

    // Create a temporary message object to show immediately in the UI
    const tempMessage = new AgentChatHistoryDto({
      id: 'temp-' + Date.now(), // Temporary ID
      question: originalMessage,
      responses: [], // Empty responses initially
      timestamp: DateTime.now()
    });
    tempMessage.isLoading = true;

    // Add the temporary message to the conversation immediately
    this.currentConversation.histories.push(tempMessage);

    // Update hasNoHistories flag since we now have a conversation
    this.hasNoHistories = false;

    // Clear input immediately for better UX
    this.userInput.question = '';
    this.adjustInputHeight();

    // Set loading state
    this.isMessageLoading = true;

    // Scroll to show the new message
    setTimeout(() => this.scrollToBottom(), 100);

    console.log('Sending message to agent:', agentName);

    // Set up streaming for this message
    this.currentStreamingMessageId = tempMessage.id;
    this.isStreamingActive = true;

    // Ensure SignalR connection for streaming responses
    this.chatService.ensureConnection().then(() => {
      console.log('SignalR connected, sending agent message');

      // Send the actual request using the agent chat service
      // The server will handle streaming responses via SignalR
      const requestDto = new AgentChatRequestDto({
        question: originalMessage,
        agentName: agentName
      });

      this.agentChatService.sendAgentMessage(requestDto).subscribe({
        next: (response) => {
          // If we get a direct response (non-streaming), handle it
          if (response && !this.isStreamingActive) {
            this.handleDirectResponse(response, tempMessage);
          }
          // If streaming is active, the response will come via SignalR
        },
        error: (error) => {
          console.error('Error sending agent message:', error);
          this.handleStreamingError(error, tempMessage, originalMessage);
        }
      });

    }).catch((error: any) => {
      console.error('Failed to connect to SignalR, falling back to direct API call:', error);

      // Fallback to direct API call if SignalR fails
      this.fallbackToDirectApiCall(originalMessage, agentName, tempMessage);
    });
  }

  /**
   * Regenerate response for a specific chat message
   */
  regenerateResponse(history: AgentChatHistoryDto) {
    if (!history || !history.id) return;

    // Set loading state for this specific message
    history.isLoading = true;
    const agentName = this.currentConversation?.agentName;

    // Set up streaming for regeneration
    const originalStreamingId = this.currentStreamingMessageId;
    this.currentStreamingMessageId = history.id;
    this.isStreamingActive = true;

    // Ensure SignalR connection for streaming responses
    this.chatService.ensureConnection().then(() => {
      console.log('SignalR connected, regenerating response');

      this.agentChatService.agentChatRegenerate(history.id, agentName).subscribe({
        next: (response) => {
          // If we get a direct response (non-streaming), handle it
          if (response && !this.isStreamingActive) {
            // Clear loading state for this message
            history.isLoading = false;

            // Update the responses in the history
            if (!history.responses) {
              history.responses = [];
            }

            // Create a new response from regenerated content
            if (response.responseText) {
              const newResponse = new AgentChatResponseDto({
                id: response.id || '',
                responseText: response.responseText,
                chatSource: 'Regenerated',
                timestamp: DateTime.now()
              });
              history.responses.push(newResponse);

              // Update the current response index to show the latest response
              const historyId = history.id || '';
              this.currentResponseIndexes[historyId] = history.responses.length - 1;
            }

            this.scrollToBottom();
          }
          // If streaming is active, the response will come via SignalR
        },
        error: (error) => {
          console.error('Error regenerating response:', error);
          this.message.error('Failed to regenerate response');

          // Clear loading and streaming state for this message on error
          history.isLoading = false;
          history.isStreaming = false;
          this.isStreamingActive = false;
          this.currentStreamingMessageId = originalStreamingId;
        }
      });

    }).catch((error: any) => {
      console.error('Failed to connect to SignalR for regeneration, using direct API call:', error);

      // Fallback to direct API call
      this.isStreamingActive = false;
      this.currentStreamingMessageId = originalStreamingId;

      this.agentChatService.agentChatRegenerate(history.id, agentName).subscribe({
        next: (response) => {
          // Clear loading state for this message
          history.isLoading = false;

          // Update the responses in the history
          if (!history.responses) {
            history.responses = [];
          }

          // Create a new response from regenerated content
          if (response.responseText) {
            const newResponse = new AgentChatResponseDto({
              id: response.id || '',
              responseText: response.responseText,
              chatSource: 'Regenerated',
              timestamp: DateTime.now()
            });
            history.responses.push(newResponse);

            // Update the current response index to show the latest response
            const historyId = history.id || '';
            this.currentResponseIndexes[historyId] = history.responses.length - 1;
          }

          this.scrollToBottom();
        },
        error: (error) => {
          console.error('Error regenerating response:', error);
          this.message.error('Failed to regenerate response');

          // Clear loading state for this message on error
          history.isLoading = false;
        }
      });
    });
  }

  /**
   * Edit an existing message
   */
  editMessage(history: AgentChatHistoryDto) {
    if (!history) return;

    // Store the edited question
    const editedQuestion = history.question;

    // Exit editing mode
    history.editingMode = false;

    // Set the input to the edited question and send it
    this.userInput.question = editedQuestion;
    this.sendMessage();
  }

  /**
   * Functions for voice commands and recording
   */
  startRecording() {
    this.isRecording = true;
    // Placeholder for actual recording implementation
    this.message.info('Recording started');
  }

  stopRecording() {
    this.isRecording = false;
    // Placeholder for actual recording implementation
    this.message.info('Recording stopped');
  }

  /**
   * Text-to-speech functionality
   */
  textToSpeech(text: string) {
    // Placeholder for actual TTS implementation
    this.message.info('Text-to-speech started');
  }

  stopSpeech() {
    // Placeholder for stopping TTS
    this.message.info('Text-to-speech stopped');
  }

  // Input handling methods
  handleKeyDown(event: any) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onInput(event: any) {
    this.adjustInputHeight();
  }

  onChatScroll() {
    if (this.chatContainer) {
      const element = this.chatContainer.nativeElement;
      this.showScrollButton = element.scrollHeight - element.scrollTop - element.clientHeight > 100;
    }
  }

  // Splitter methods
  onSplitDragEnd(event: any) {
    localStorage.setItem('rightSidebarWidth', String(this.rightSidebarWidth));
  }

  onSplitDragProgress(event: any) {
    this.isDragging = true;
    setTimeout(() => {
      this.isDragging = false;
    }, 1000);
  }

  onGutterDoubleClick(event: any) {
    if (this.rightSidebarSplitSize > 0) {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    } else {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    }
  }

  // UI control methods
  toggleAgentSidebar() {
    this.isAgentSidebarOpen = !this.isAgentSidebarOpen;
    if (this.isAgentSidebarOpen) {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    } else {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }

  toggleSearchResultsSidebar() {
    this.showSearchResultsSidebar = !this.showSearchResultsSidebar;
    if (this.showSearchResultsSidebar) {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    } else {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }

  selectWorkspace(workspace: string) {
    this.selectedWorkspace = workspace;
    this.loadAgentsForWorkspace(workspace);
  }
  selectAgent(agent: any) {
    this.selectedAgent = agent.agentName || agent;
    this.userInput.agentName = this.selectedAgent;

    // Navigate to the agent's chat URL
    this.router.navigate(['/agent-chat', this.selectedAgent]);

    // Close sidebar if open
    if (this.isAgentSidebarOpen) {
      this.isAgentSidebarOpen = false;
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }
  clearSelectedAgent() {
    this.selectedAgent = '';
    this.userInput.agentName = '';

    // Navigate back to the base agent-chat URL
    this.router.navigate(['/agent-chat']);

    // Load all chat histories
    this.loadAgentChatHistories();
  }
  // Helper methods
  copyContent(content: string | undefined) {
    if (content) {
      navigator.clipboard.writeText(content);
      this.message.success('Content copied to clipboard');
    }
  }

  copySqlContent(content: string) {
    this.copyContent(content);
  }

  copyBlogContent(content: string) {
    this.copyContent(content);
  }

  copyEmailContent(content: string) {
    this.copyContent(content);
  }

  // Response navigation methods
  getCurrentResponseIndex(historyId: string): number {
    return this.currentResponseIndexes[historyId] || 0;
  }

  getCurrentResponse(history: AgentChatHistoryDto): AgentChatResponseDto | null {
    if (!history || !history.responses || history.responses.length === 0) return null;

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);
    const response = history.responses[currentIndex];

    return response || null;
  }

  goToPreviousResponse(history: AgentChatHistoryDto) {
    if (!history || !history.responses || history.responses.length <= 1) return;

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);

    if (currentIndex > 0) {
      this.currentResponseIndexes[historyId] = currentIndex - 1;
    }
  }

  nextResponse(history: AgentChatHistoryDto) {
    if (!history || !history.responses || history.responses.length <= 1) return;

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);

    if (currentIndex < history.responses.length - 1) {
      this.currentResponseIndexes[historyId] = currentIndex + 1;
    }
  }

  getResponseNavigation(history: AgentChatHistoryDto): string {
    if (!history || !history.responses || history.responses.length <= 1) return '';

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);

    return `${currentIndex + 1}/${history.responses.length}`;
  }

  openSqlConnectionDialog(query: string) {
    // Placeholder for SQL connection dialog
    this.message.info('SQL Connection dialog would open here');
  }

  openBlogShareDialog(content: string) {
    // Placeholder for blog sharing
    this.message.info('Blog sharing dialog would open here');
  }

  sendEmail(content: string) {
    // Placeholder for email sending
    this.message.info('Email sending would happen here');
  }

  /**
   * Handle direct API response (non-streaming)
   */
  private handleDirectResponse(response: AgentChatHistoryDto, tempMessage: AgentChatHistoryDto) {
    if (!response) {
      console.error('Received null response from agent chat service');
      this.message.error('Received invalid response from server');
      this.isMessageLoading = false;
      return;
    }

    // Find and replace the temporary message with the actual response
    if (this.currentConversation.histories) {
      const tempIndex = this.currentConversation.histories.findIndex(h => h.id === tempMessage.id);
      if (tempIndex !== -1) {
        // Replace the temporary message with the actual response
        this.currentConversation.histories[tempIndex] = response;

        // Set the current response index to show the latest response
        if (response.id) {
          this.currentResponseIndexes[response.id] = (response.responses || []).length - 1;
        }
      } else {
        // Fallback: just add the response if temp message not found
        this.currentConversation.histories.push(response);
        if (response.id) {
          this.currentResponseIndexes[response.id] = (response.responses || []).length - 1;
        }
      }
    }

    // Clear loading and streaming state
    this.isMessageLoading = false;
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;
    this.scrollToBottom();
  }

  /**
   * Handle streaming errors
   */
  private handleStreamingError(error: any, tempMessage: AgentChatHistoryDto, originalMessage: string) {
    this.message.error('Failed to send message');

    // Remove the temporary message on error
    if (this.currentConversation.histories) {
      const tempIndex = this.currentConversation.histories.findIndex(h => h.id === tempMessage.id);
      if (tempIndex !== -1) {
        this.currentConversation.histories.splice(tempIndex, 1);
      }
    }

    // Restore the original message to the input
    this.userInput.question = originalMessage;
    this.adjustInputHeight();

    // Clear loading and streaming state
    this.isMessageLoading = false;
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;
  }

  /**
   * Fallback to direct API call when SignalR is not available
   */
  private fallbackToDirectApiCall(originalMessage: string, agentName: string, tempMessage: AgentChatHistoryDto) {
    console.log('Using fallback direct API call');

    // Clear streaming state since we're falling back
    this.isStreamingActive = false;
    this.currentStreamingMessageId = undefined;

    const requestDto = new AgentChatRequestDto({
      question: originalMessage,
      agentName: agentName
    });

    this.agentChatService.sendAgentMessage(requestDto).subscribe({
      next: (response) => {
        this.handleDirectResponse(response, tempMessage);
      },
      error: (error) => {
        this.handleStreamingError(error, tempMessage, originalMessage);
      }
    });
  }

  /**
   * Handle streaming messages from SignalR
   */
  private handleStreamingMessage(message: ResponseMessage, isError: boolean, isComplete: boolean) {
    if (!this.currentStreamingMessageId) {
      console.log('No current streaming message ID, ignoring message');
      return;
    }

    // Find the message being streamed
    const streamingMessage = this.currentConversation.histories?.find(
      h => h.id === this.currentStreamingMessageId
    );

    if (!streamingMessage) {
      console.log('Streaming message not found in conversation');
      return;
    }

    if (isError) {
      console.error('Streaming error:', message);
      this.message.error('Error during response generation');

      // Clear streaming state
      streamingMessage.isStreaming = false;
      streamingMessage.isLoading = false;
      this.isStreamingActive = false;
      this.currentStreamingMessageId = undefined;
      return;
    }

    if (isComplete) {
      console.log('Streaming complete');

      // Create the final response from the streamed text
      if (streamingMessage.streamingText) {
        const finalResponse = new AgentChatResponseDto({
          id: 'streamed-' + Date.now(),
          responseText: streamingMessage.streamingText,
          chatSource: 'Agent',
          timestamp: DateTime.now()
        });

        // Initialize responses array if needed
        if (!streamingMessage.responses) {
          streamingMessage.responses = [];
        }

        // Add the final response
        streamingMessage.responses.push(finalResponse);

        // Set the current response index
        if (streamingMessage.id) {
          this.currentResponseIndexes[streamingMessage.id] = streamingMessage.responses.length - 1;
        }
      }

      // Clear streaming state
      streamingMessage.isStreaming = false;
      streamingMessage.isLoading = false;
      streamingMessage.streamingText = '';
      this.isStreamingActive = false;
      this.currentStreamingMessageId = undefined;
      this.isMessageLoading = false;

      this.scrollToBottom();
      return;
    }

    // Handle streaming text
    const messageText = this.extractMessageText(message);
    if (messageText) {
      if (!streamingMessage.streamingText) {
        streamingMessage.streamingText = '';
      }
      streamingMessage.streamingText += messageText;

      // Transition from loading to streaming state
      if (streamingMessage.isLoading) {
        streamingMessage.isLoading = false;
        streamingMessage.isStreaming = true;
      }

      // Auto-scroll as content streams in
      setTimeout(() => this.scrollToBottom(), 50);
    }
  }

  /**
   * Extract message text from ResponseMessage (similar to hero component)
   */
  private extractMessageText(message: ResponseMessage): string {
    if (!message) return '';

    // Handle different message formats
    if (typeof message.message === 'string') {
      return message.message;
    }

    // Handle object format if needed
    if (message.message && typeof message.message === 'object') {
      return JSON.stringify(message.message);
    }

    return '';
  }
}

// Add this to the AgentChatHistoryDto class to support editing, loading, and streaming states
declare module '../../../shared/service-proxies/service-proxies' {
  interface AgentChatHistoryDto {
    editingMode?: boolean;
    isLoading?: boolean;
    isStreaming?: boolean;
    streamingText?: string;
  }

  interface AgentChatResponseDto {
    copied?: boolean;
  }
}

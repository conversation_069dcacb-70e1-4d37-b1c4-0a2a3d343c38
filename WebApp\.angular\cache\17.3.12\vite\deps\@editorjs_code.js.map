{"version": 3, "sources": ["../../../../../node_modules/@editorjs/code/dist/code.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(\".ce-code__textarea{min-height:200px;font-family:Menlo,Monaco,Consolas,Courier New,monospace;color:#41314e;line-height:1.6em;font-size:12px;background:#f8f7fa;border:1px solid #f1f1f4;box-shadow:none;white-space:pre;word-wrap:normal;overflow-x:auto;resize:vertical}\")),document.head.appendChild(e)}}catch(o){console.error(\"vite-plugin-css-injected-by-js\",o)}})();\nfunction c(l, t) {\n  let a = \"\";\n  for (; a !== `\n` && t > 0; )\n    t = t - 1, a = l.substr(t, 1);\n  return a === `\n` && (t += 1), t;\n}\nconst h = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 8L5 12L9 16\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 8L19 12L15 16\"/></svg>';\n/**\n * CodeTool for Editor.js\n * @version 2.0.0\n * @license MIT\n */\nclass d {\n  /**\n   * Notify core that read-only mode is supported\n   * @returns true if read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allows pressing Enter key to create line breaks inside the CodeTool textarea\n   * This enables multi-line input within the code editor.\n   * @returns true if line breaks are allowed in the textarea\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   * @param options - tool constricting options\n   * @param options.data — previously saved plugin code\n   * @param options.config - user config for Tool\n   * @param options.api - Editor.js API\n   * @param options.readOnly - read only mode flag\n   */\n  constructor({ data: t, config: e, api: a, readOnly: r }) {\n    this.api = a, this.readOnly = r, this.placeholder = this.api.i18n.t(e.placeholder || d.DEFAULT_PLACEHOLDER), this.CSS = {\n      baseClass: this.api.styles.block,\n      input: this.api.styles.input,\n      wrapper: \"ce-code\",\n      textarea: \"ce-code__textarea\"\n    }, this.nodes = {\n      holder: null,\n      textarea: null\n    }, this.data = {\n      code: t.code ?? \"\"\n    }, this.nodes.holder = this.drawView();\n  }\n  /**\n   * Return Tool's view\n   * @returns this.nodes.holder - Code's wrapper\n   */\n  render() {\n    return this.nodes.holder;\n  }\n  /**\n   * Extract Tool's data from the view\n   * @param codeWrapper - CodeTool's wrapper, containing textarea with code\n   * @returns - saved plugin code\n   */\n  save(t) {\n    return {\n      code: t.querySelector(\"textarea\").value\n    };\n  }\n  /**\n   * onPaste callback fired from Editor`s core\n   * @param event - event with pasted content\n   */\n  onPaste(t) {\n    const e = t.detail;\n    if (\"data\" in e) {\n      const a = e.data;\n      this.data = {\n        code: a || \"\"\n      };\n    }\n  }\n  /**\n   * Returns Tool`s data from private property\n   * @returns\n   */\n  get data() {\n    return this._data;\n  }\n  /**\n   * Set Tool`s data to private property and update view\n   * @param data - saved tool data\n   */\n  set data(t) {\n    this._data = t, this.nodes.textarea && (this.nodes.textarea.value = t.code);\n  }\n  /**\n   * Get Tool toolbox settings.\n   * Provides the icon and title to display in the toolbox for the CodeTool.\n   * @returns An object containing:\n   * - icon: SVG representation of the Tool's icon\n   * - title: Title to show in the toolbox\n   */\n  static get toolbox() {\n    return {\n      icon: h,\n      title: \"Code\"\n    };\n  }\n  /**\n   * Default placeholder for CodeTool's textarea\n   * @returns\n   */\n  static get DEFAULT_PLACEHOLDER() {\n    return \"Enter a code\";\n  }\n  /**\n   *  Used by Editor.js paste handling API.\n   *  Provides configuration to handle CODE tag.\n   * @returns\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"pre\"]\n    };\n  }\n  /**\n   * Automatic sanitize config\n   * @returns\n   */\n  static get sanitize() {\n    return {\n      code: !0\n      // Allow HTML tags\n    };\n  }\n  /**\n   * Handles Tab key pressing (adds/removes indentations)\n   * @param event - keydown\n   */\n  tabHandler(t) {\n    t.stopPropagation(), t.preventDefault();\n    const e = t.target, a = t.shiftKey, r = e.selectionStart, s = e.value, n = \"  \";\n    let i;\n    if (!a)\n      i = r + n.length, e.value = s.substring(0, r) + n + s.substring(r);\n    else {\n      const o = c(s, r);\n      if (s.substr(o, n.length) !== n)\n        return;\n      e.value = s.substring(0, o) + s.substring(o + n.length), i = r - n.length;\n    }\n    e.setSelectionRange(i, i);\n  }\n  /**\n   * Create Tool's view\n   * @returns\n   */\n  drawView() {\n    const t = document.createElement(\"div\"), e = document.createElement(\"textarea\");\n    return t.classList.add(this.CSS.baseClass, this.CSS.wrapper), e.classList.add(this.CSS.textarea, this.CSS.input), e.value = this.data.code, e.placeholder = this.placeholder, this.readOnly && (e.disabled = !0), t.appendChild(e), e.addEventListener(\"keydown\", (a) => {\n      switch (a.code) {\n        case \"Tab\":\n          this.tabHandler(a);\n          break;\n      }\n    }), this.nodes.textarea = e, t;\n  }\n}\nexport {\n  d as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,0QAA0Q,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AAC1e,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,IAAI;AACR,SAAO,MAAM;AAAA,KACV,IAAI;AACL,QAAI,IAAI,GAAG,IAAI,EAAE,OAAO,GAAG,CAAC;AAC9B,SAAO,MAAM;AAAA,MACT,KAAK,IAAI;AACf;AACA,IAAM,IAAI;AAMV,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKN,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,EAAE,GAAG;AACvD,SAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,cAAc,KAAK,IAAI,KAAK,EAAE,EAAE,eAAe,GAAE,mBAAmB,GAAG,KAAK,MAAM;AAAA,MACtH,WAAW,KAAK,IAAI,OAAO;AAAA,MAC3B,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,GAAG,KAAK,QAAQ;AAAA,MACd,QAAQ;AAAA,MACR,UAAU;AAAA,IACZ,GAAG,KAAK,OAAO;AAAA,MACb,MAAM,EAAE,QAAQ;AAAA,IAClB,GAAG,KAAK,MAAM,SAAS,KAAK,SAAS;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,GAAG;AACN,WAAO;AAAA,MACL,MAAM,EAAE,cAAc,UAAU,EAAE;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,GAAG;AACT,UAAM,IAAI,EAAE;AACZ,QAAI,UAAU,GAAG;AACf,YAAM,IAAI,EAAE;AACZ,WAAK,OAAO;AAAA,QACV,MAAM,KAAK;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK,GAAG;AACV,SAAK,QAAQ,GAAG,KAAK,MAAM,aAAa,KAAK,MAAM,SAAS,QAAQ,EAAE;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,cAAc;AACvB,WAAO;AAAA,MACL,MAAM,CAAC,KAAK;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,MAAM;AAAA;AAAA,IAER;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,GAAG;AACZ,MAAE,gBAAgB,GAAG,EAAE,eAAe;AACtC,UAAM,IAAI,EAAE,QAAQ,IAAI,EAAE,UAAU,IAAI,EAAE,gBAAgB,IAAI,EAAE,OAAO,IAAI;AAC3E,QAAI;AACJ,QAAI,CAAC;AACH,UAAI,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,GAAG,CAAC,IAAI,IAAI,EAAE,UAAU,CAAC;AAAA,SAC9D;AACH,YAAM,IAAI,EAAE,GAAG,CAAC;AAChB,UAAI,EAAE,OAAO,GAAG,EAAE,MAAM,MAAM;AAC5B;AACF,QAAE,QAAQ,EAAE,UAAU,GAAG,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE,MAAM,GAAG,IAAI,IAAI,EAAE;AAAA,IACrE;AACA,MAAE,kBAAkB,GAAG,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,IAAI,SAAS,cAAc,KAAK,GAAG,IAAI,SAAS,cAAc,UAAU;AAC9E,WAAO,EAAE,UAAU,IAAI,KAAK,IAAI,WAAW,KAAK,IAAI,OAAO,GAAG,EAAE,UAAU,IAAI,KAAK,IAAI,UAAU,KAAK,IAAI,KAAK,GAAG,EAAE,QAAQ,KAAK,KAAK,MAAM,EAAE,cAAc,KAAK,aAAa,KAAK,aAAa,EAAE,WAAW,OAAK,EAAE,YAAY,CAAC,GAAG,EAAE,iBAAiB,WAAW,CAAC,MAAM;AACvQ,cAAQ,EAAE,MAAM;AAAA,QACd,KAAK;AACH,eAAK,WAAW,CAAC;AACjB;AAAA,MACJ;AAAA,IACF,CAAC,GAAG,KAAK,MAAM,WAAW,GAAG;AAAA,EAC/B;AACF;", "names": []}
/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Model card specific styling */
.model-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #2b2b33;
  color: white;
  padding: 1rem;
  position: relative;
}

.model-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Model icon styling */
.model-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  background-color: #00c39a;
  color: white;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
}

/* Provider badge styling */
.provider-badge {
  font-size: 0.75rem;
  color: #a0a0a0;
  margin-top: 0.25rem;
}

/* Status indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 0.5rem;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #00c39a;
}

.status-text {
  font-size: 0.75rem;
  color: #a0a0a0;
}
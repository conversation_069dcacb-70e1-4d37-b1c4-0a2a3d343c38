<!-- Chat Sidebar Content -->
<div class="flex-1 flex flex-col h-full">
  <!-- Header -->
  <div
    class="px-[var(--padding-small)] py-[var(--padding-small)] border-b border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2 ">
    <div class="flex items-center justify-between w-full">
      <div class="flex flex-col">
        <div class="flex items-center gap-1.5">
          <span class="font-bold text-[var(--text-dark)] text-lg">Chat</span>
        </div>
      </div>

      <!-- Tab buttons -->
      <div class="flex items-center ms-6">
        <div class="tooltip-container">
          <button id="all-tab-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeTab === 'all'
            }" (click)="toggleTab('all')">
            <i class="ri-list-check text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">All</span>
        </div>
        <div class="tooltip-container">
          <button id="pinned-tab-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeTab === 'pinned-history'
            }" (click)="toggleTab('pinned-history')">
            <i class="ri-pushpin-line text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">Pinned</span>
        </div>
        <div class="tooltip-container">
          <button id="favorite-tab-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeTab === 'favorite'
            }" (click)="toggleTab('favorite')">
            <i class="ri-star-line text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">Favorites</span>
        </div>
        <div class="tooltip-container archive-tooltip-container">
          <button id="archive-tab-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeTab === 'archive'
            }" (click)="toggleTab('archive')">
            <i class="ri-archive-line text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">Archive</span>
        </div>

      <div class="flex items-center gap-2">
        <button
          class="w-6 h-6 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex sm:hidden justify-center items-center outline-none border-none bg-transparent text-lg font-bold cursor-pointer"
          (click)="addNewChats($event);">
          <i class="ri-add-line text-[var(--text-dark)]"></i>
        </button>
        <button
          class="w-6 h-6 rounded-[var(--border-radius-small)] hidden hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] sm:flex justify-center items-center outline-none border-none bg-transparent text-lg font-bold cursor-pointer"
          (click)="addNewChats($event);">
          <i class="ri-add-line text-[var(--text-dark)]"></i>
        </button>
      </div>
      </div>

    </div>
  </div>

  <!-- Search Bar -->
  <div class="px-[var(--padding-small)]">
    <div class="flex items-center relative bg-[var(--header-bg)] px-2 rounded-md">
      <i class="ri-search-line text-[var(--text-medium-gray)]"></i>
      <input type="text" placeholder="Search chats..."
        class="w-full px-2 py-1 text-[var(--text-dark)] rounded-[var(--border-radius-small)] outline-none border-none bg-transparent"
        (input)="filterChats($event)" #searchInput />
      <button *ngIf="searchInput.value.length" (click)="searchInput.value = ''; filterChats($event)"
        class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] absolute right-2 bg-transparent border-none outline-none cursor-pointer text-xl">
        <i class="ri-close-line"></i>
      </button>
    </div>
  </div>

  <!-- Chat List Template -->
  <ng-template #chatListTemplate let-chats="chats" let-isGrouped="isGrouped">
    <div class="py-[var(--padding-small)]">
      <div *ngIf="isGrouped">
        <div *ngFor="let group of chats | keyvalue : originalOrder">
          <div class="py-2 text-[var(--text-medium-gray)]" *ngIf="group.value.length > 1">{{ group.key }}</div>
          <div *ngFor="let chat of group.value; trackBy: trackByChatId">
            <ng-container *ngTemplateOutlet="chatItem; context: { $implicit: chat }"></ng-container>
          </div>
        </div>
      </div>
      <div *ngIf="!isGrouped">
        <div *ngFor="let chat of chats; trackBy: trackByChatId">
          <ng-container *ngTemplateOutlet="chatItem; context: { $implicit: chat }"></ng-container>
        </div>
      </div>
      <div *ngIf="!isGrouped && chats.length === 0"
        class="flex items-center justify-center px-2 py-4 flex-col gap-2 h-full">
        <span class="text-[var(--text-medium-gray)]">No {{ getCurrentTabTitle() }} for now.</span>
        <i class="ri-emotion-sad-line text-[var(--text-medium-gray)] text-4xl"></i>
      </div>
    </div>
  </ng-template>

  <!-- Chat Item Template -->
  <ng-template #chatItem let-chat>
    <div
      class="w-auto py-1 px-2 rounded-[8px] border border-[var(--hover-blue-gray)] flex sm:hidden items-center justify-between text-[var(--text-dark)] font-medium hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)] mb-1 cursor-pointer"
      [class.!bg-[var(--secondary-purple)]]="chat.id == chatListService.chatId"
      [class.!text-black]="chat.id == chatListService.chatId"
      (click)="toggleChat($event, chat); togglingService.toggleNavbar()">
      <div class="flex items-center gap-2">
        <div class="flex flex-col text-left text-black" [class.!text-black]="chat.id == chatListService.chatId">
          <span class="text-[var(--text-dark)] text-sm truncate"
            [class.text-dark]="chat.id == chatListService.chatId" style="
              max-width: 8rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            ">{{ chat.title }}</span>
        </div>
      </div>
    </div>
    <div
      class="w-auto hidden sm:flex py-1 px-2 rounded-[8px] border border-[var(--hover-blue-gray)] items-center justify-between text-[var(--text-dark)] font-medium hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)] mb-1 cursor-pointer group"
      [class.!bg-[var(--secondary-purple)]]="chat.id == chatListService.chatId"
      (click)="toggleChat($event, chat)">
      <div class="flex items-center gap-2">
        <div class="flex flex-col text-left text-xl h-7 items-center justify-center">
          <span class="text-[var(--text-dark)] text-sm truncate"
            [class.!text-black]="chat.id == chatListService.chatId" style="
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            "
            [ngClass]="{'w-[9rem]': chat.workspaceName?.length > 1, 'w-[12rem]': chat.workspaceName?.length <= 1}">{{
            chat.title }}</span>
        </div>
      </div>

      <span
        class="hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] rounded-[var(--border-radius-small)] text-[var(--text-dark)] flex items-center justify-center gap-2 p-0 group-hover:opacity-0 group-hover:hidden cursor-pointer"
        [class.!text-black]="chat.id == chatListService.chatId">
        {{ chat.workspaceName?.length > 10 ? chat.workspaceName + '...' : chat.workspaceName }}
      </span>

      <div>
        <button nz-popover [nzPopoverContent]="contentTemplate" nzPopoverTrigger="click"
          class="!bg-transparent !border-none !outline-none hover:bg-[var(--hover-blue-gray)] hidden group-hover:flex transition-[var(--transition-default)] rounded-[var(--border-radius-small)] text-[var(--text-dark)] items-center justify-center gap-2 p-0 opacity-0 group-hover:opacity-100 cursor-pointer">
          <i class="ri-more-fill text-[var(--dark-text)] text-[18px]"
            [class.!text-black]="chat.id == chatListService.chatId"></i>
        </button>
      </div>
      <ng-template #contentTemplate>
        <div>
          <div
            class="py-1 group flex items-center justify-between gap-2 hover:bg-slate-300 px-2 cursor-pointer rounded-sm text-[var(--text-dark)] hover:text-black"
            (click)="addToPinnedChat(chat)">
            Pin Chat <i class="ri-pushpin-2-line group-hover:text-black" [class.ri-pushpin-fill]="chat.isPinned"
              [class.ri-pushpin-line]="!chat.isPinned" [class.text-[var(--text-medium-gray)]]="!chat.isPinned"
              [class.text-[var(--primary-purple)]]="chat.isPinned"></i>
          </div>
          <div
            class="py-1 group flex items-center justify-between gap-2 hover:bg-slate-300 px-2 cursor-pointer rounded-sm text-[var(--text-dark)] hover:text-black"
            (click)="addToFavChat(chat)">
            Add to Favorites <i class="ri-star-line group-hover:text-black"
              [class.ri-star-fill]="chat.isFavorite" [class.ri-star-line]="!chat.isFavorite"
              [class.text-[var(--text-medium-gray)]]="!chat.isFavorite"
              [class.text-[var(--primary-purple)]]="chat.isFavorite"></i>
          </div>
          <div
            class="py-1 group flex items-center justify-between gap-2 hover:bg-slate-300 px-2 cursor-pointer rounded-sm text-[var(--text-dark)] hover:text-black"
            (click)="addToArchiveChat(chat)">
            Add to Archive <i class="ri-archive-line group-hover:text-black"
              [class.ri-archive-fill]="chat.isArchived" [class.ri-archive-line]="!chat.isArchived"
              [class.text-[var(--text-medium-gray)]]="!chat.isArchived"
              [class.text-[var(--primary-purple)]]="chat.isArchived"></i>
          </div>
        </div>
      </ng-template>
    </div>
  </ng-template>

  <!-- Dynamic Tab Content -->
  <div class="flex-1 flex flex-col">
    <div class="flex items-center px-[var(--padding-small)] mb-1"
      *ngIf="activeTab !== 'all' && activeTab !== 'pinned-history'">
      <span class="text-[var(--text-medium-gray)]">{{ getCurrentTabTitle() }}</span>
    </div>
    <div class="flex-1 px-[var(--padding-small)] overflow-y-auto"
      [class]="!isAllChatsOpen && (activeTab === 'all' || activeTab === 'pinned-history') ? 'h-0 overflow-hidden transition-[var(--transition-default)] duration-300 ease-in-out' : 'h-auto transition-[var(--transition-default)] duration-300 ease-in-out max-h-[78vh] overflow-auto'">

      <!-- Other Tabs -->
      <ng-container *ngIf="activeTab !== 'notes'" [ngTemplateOutlet]="chatListTemplate"
        [ngTemplateOutletContext]="{ chats: getCurrentTabChats(), isGrouped: isCurrentTabGrouped() }">
      </ng-container>

      <div *ngIf="hasMoreForCurrentTab()"
        class="px-[var(--padding-small)] py-[var(--padding-small)] border-t border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2">
        <button (click)="loadMoreChatList()"
          class="w-full py-2 px-3 rounded-[var(--border-radius-small)] bg-[var(--header-bg)] text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] mt-2 flex items-center justify-center outline-none border-none cursor-pointer">
          Show more <i class="ri-arrow-down-s-line ml-2"></i>
        </button>
      </div>
    </div>
  </div>
</div>

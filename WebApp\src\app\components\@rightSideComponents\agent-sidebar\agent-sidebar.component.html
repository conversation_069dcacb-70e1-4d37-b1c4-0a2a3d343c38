<!-- Agent <PERSON><PERSON> - Slides in from the right -->
<div *ngIf="showSidebar"
  class="flex-1 w-full h-full overflow-y-auto transition-all duration-300 ease-in-out"
  [ngClass]="{
    'translate-x-0': showSidebar,
    'translate-x-full': !showSidebar,
    'bg-[#2b2b33] text-white border-l border-[#3a3a45]': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)] border-l border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
  }">

  <!-- Agent Sidebar Header - Sticky -->
  <div
    class="flex items-center justify-between p-4 sticky top-0 z-10 shadow-sm backdrop-blur-sm transition-all duration-200"
    [ngClass]="{
      'bg-[#2b2b33] border-b border-[#3a3a45]': themeService.isDarkMode(),
      'bg-[var(--background-white)] border-b border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
    }">
    <h3 class="font-semibold flex items-center gap-2"
        [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
      <i class="ri-tools-line" [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
      <span>{{agentSidebarTitle}}</span>
    </h3>
    <button (click)="onCloseSidebar()"
      [ngClass]="{
        'text-gray-300 hover:text-white hover:bg-[#3a3a45]': themeService.isDarkMode(),
        'text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] hover:bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
      }"
      class="transition-colors p-1 rounded-full">
      <i class="ri-close-line text-xl"></i>
    </button>
  </div>

  <!-- Agent List Content -->
  <div class="p-4">
    <!-- Empty State -->
    <div *ngIf="workspaceAgents.length === 0" class="text-center py-8"
         [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
      <i class="ri-tools-line text-4xl mb-2 block"
         [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
      <p>No agents available for this workspace</p>
    </div>

    <!-- Agent List -->
    <div *ngIf="workspaceAgents.length > 0" class="space-y-4">
      <div *ngFor="let agent of workspaceAgents" (click)="selectAgent(agent)"
        class="p-4 rounded-lg shadow-sm transition-all cursor-pointer agent-card"
        [ngClass]="{
          'bg-[#3a3a45] border border-[#4a4a55] hover:border-[#00c39a]': themeService.isDarkMode(),
          'bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center justify-between mb-2">
          <h3 class="font-semibold agent-name"
              [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
            {{agent.agentName}}
          </h3>
          <span class="text-xs px-2 py-1 rounded-full model-badge"
                [ngClass]="{
                  'bg-[#1E1E1E] text-gray-300': themeService.isDarkMode(),
                  'bg-[var(--hover-blue-gray)] text-[var(--text-dark)]': !themeService.isDarkMode()
                }">
            {{agent.modelName | removeProviderPrefix}}
          </span>
        </div>
        <p class="text-sm line-clamp-2 agent-description"
           [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
          {{agent.instructions || 'No description available'}}
        </p>
      </div>
    </div>
  </div>
</div>

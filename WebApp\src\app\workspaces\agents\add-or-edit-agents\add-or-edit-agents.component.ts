import { Component, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import {
  AgentDefinitionServiceProxy,
  AiServiceProxy,
  ModelDetailsServiceProxy,
  PluginServiceProxy,
  ResponseMessage
} from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { finalize } from 'rxjs/operators';
import { RemoveProviderPrefixPipe } from "../../../../shared/pipes/remove-provider-prefix.pipe";

// Define interface for chat messages
interface ChatMessage {
  sender: 'user' | 'agent' | 'loading';
  content: string;
  timestamp: Date;
}

@Component({
  selector: 'app-add-or-edit-agent',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzAutocompleteModule,
    NzSelectModule,
    NzRadioModule,
    NzBreadCrumbModule,
    ServiceProxyModule,
    RouterLink,
    RemoveProviderPrefixPipe
  ],
  templateUrl: './add-or-edit-agents.component.html', // Updated to match selector,
  styles: [
    `
      ::ng-deep .ant-select-multiple .ant-select-selection-item{
        background-color: var(--secondary-purple) !important;
      }
    `,
  ],
})
export class AddOrEditAgentsComponent implements AfterViewChecked {
  @ViewChild('sidebarContainer') private sidebarContainer!: ElementRef;

  // Renamed to singular for consistency
  agents: any[] = [];
  isEditing: boolean = false;
  currentAgent: any = {
    agentName: '',
    instructions: '',
    userInstructions: '',
    modelName: '',
    tools: [],
    workspace: '',
  };
  modelSearchQuery: string = '';
  filteredModels: any[] = [];
  models: any[] = [];
  agentName: string = '';
  workspaceName: string = '';
  plugins: any[] = [];
  selectedPlugins: string[] = [];
  routerParts: string[] = [];

  // Agent description for AI generation
  isGeneratingInstructions: boolean = false;
  isInstructionsDisabled: boolean = true; // Initially disable instructions field

  // Test agent properties
  testQuestion: string = '';
  testResponse: string = '';
  testError: string = '';
  isTestingAgent: boolean = false;
  testSuccessful: boolean = true; // Always enabled as testing is optional

  // Chat sidebar properties
  showTestResults: boolean = false;

  // Chat properties
  chatMessages: ChatMessage[] = [];
  chatInput: string = '';

  constructor(
    private agentService: AgentDefinitionServiceProxy,
    private router: Router,
    private aiService: AiServiceProxy,
    private modelDetailsService: ModelDetailsServiceProxy,
    private _pluginService: PluginServiceProxy
  ) { }

  /**
   * Scroll to the bottom of the chat container when new messages are added
   */
  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  /**
   * Scroll to the bottom of the sidebar container
   */
  scrollToBottom(): void {
    try {
      if (this.sidebarContainer && this.showTestResults) {
        setTimeout(() => {
          if (this.sidebarContainer && this.sidebarContainer.nativeElement) {
            this.sidebarContainer.nativeElement.scrollTop = this.sidebarContainer.nativeElement.scrollHeight;
            console.log('Scrolled to bottom, height:', this.sidebarContainer.nativeElement.scrollHeight);
          }
        }, 200);
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  ngOnInit(): void {
    // Extract workspace from URL
    this.routerParts = this.router.url.split('/');
    console.log(this.routerParts);

    if (this.routerParts[2] == 'agents') {
      if (this.routerParts[3] == 'new') {
        this.agentName = 'new';
        this.isEditing = false;
        this.workspaceName = "Default"
      } else {
        this.agentName = decodeURIComponent(this.routerParts[3]);
        this.isEditing = true;
        this.loadAgent(this.agentName); // Load existing agent data
      }
      // if (this.routerParts.length >= 4) {
      //   this.workspaceName = decodeURIComponent(this.routerParts[3]);
      // }
      // else {
      // }
      // this.workspaceName = decodeURIComponent(this.routerParts[3]);
      // this.isEditing = false; // Explicitly set for new agent
    } else {
      if (this.routerParts[4] == 'new') {
        this.agentName = 'new';
        this.isEditing = false;
        this.workspaceName = decodeURIComponent(this.routerParts[2]);
      } else {
        this.agentName = decodeURIComponent(this.routerParts[4]);
        this.isEditing = true;
        this.loadAgent(this.agentName); // Load existing agent data
      }
    }
    console.log(
      'Workspace Name: ',
      this.agentName
    );
    this.loadModels();
    this.loadPlugins();

    // // Check if we're editing based on route params

    // if (!(this.workspaceName == 'new')) {
    //   this.isEditing = true;
    //   this.loadAgent(this.workspaceName); // Load existing agent data
    // } else {
    // }

    // Load models and plugins

  }

  loadAgent(agentName: string) {
    // Fetch agent details for editing
    this.agentService.getByAgentName(agentName).subscribe((agent: any) => {
      this.currentAgent = { ...agent };
      this.modelSearchQuery = this.currentAgent.modelName || '';
      this.selectedPlugins = [...(this.currentAgent.tools || [])];
      this.workspaceName = this.currentAgent.workspace || 'Default';

      // For editing existing agents, we still require testing before saving
      // to ensure any changes don't break the agent
      this.testSuccessful = false;
    });
  }

  loadModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((models: any) => {
      this.filteredModels = models;
      this.models = models;
    });
  }

  loadPlugins() {
    this._pluginService.getAllPluginNames().subscribe((res: any) => {
      this.plugins = [...res.message];
    });
  }

  onChange(event: any) {
    const query = event.target.value.toLowerCase();
    this.filteredModels = this.models.filter((m: any) =>
      m.modelName.toLowerCase().includes(query)
    );
  }

  updateModel(modelName: string) {
    this.currentAgent.modelName = modelName;
    // Also update the search box to show only the model name (without provider) for user display
    this.modelSearchQuery = this.removeProviderPrefix(modelName);
  }

  /**
   * Remove provider prefix for display (e.g., azure_gpt-4o-mini -> gpt-4o-mini)
   */
  removeProviderPrefix(value: string): string {
    if (!value) return '';
    const parts = value.split('_');
    return parts.slice(1).join('_');
  }

  /**
   * Check if the agent can generate instructions
   * @returns boolean indicating if the agent has the required fields to generate instructions
   */
  canGenerateInstructions(): boolean {
    return (
      this.currentAgent.agentName?.trim() !== '' &&
      this.currentAgent.modelName?.trim() !== ''
    );
  }

  /**
   * Generate agent instructions using AI
   */
  generateInstructions() {
    this.isGeneratingInstructions = true;

    // Prepare the prompt for the instruction generator agent
    const prompt = `Generate detailed instructions for an AI agent with the following description:

Agent Name: ${this.currentAgent.agentName || 'Unnamed Agent'}
Description: ${this.currentAgent.userInstructions || 'No description provided'}
Selected Plugins: ${this.selectedPlugins.join(', ')}

The instructions should be comprehensive and clearly define the agent's purpose, capabilities, and limitations.
Format the instructions in a clear, professional manner that will guide the AI in responding appropriately to user queries.
`;

    // Call the instruction generator agent
    this.aiService.callAgent('InstructionGeneratorAgent', prompt)
      .pipe(
        finalize(() => {
          this.isGeneratingInstructions = false;
        })
      )
      .subscribe({
        next: (response: ResponseMessage) => {
          if (response && response.message) {
            // Update the instructions field with the generated content
            this.currentAgent.instructions = response.message;
            // Enable the instructions field for editing
            this.isInstructionsDisabled = false;
            console.log('Instructions generated successfully');
          } else {
            console.error('Failed to generate instructions: Empty response');
          }
        },
        error: (error) => {
          console.error('Error generating instructions:', error);
          // If there's an error with the instruction generator agent, try a fallback approach
          this.generateFallbackInstructions();
        }
      });
  }

  /**
   * Generate fallback instructions if the instruction generator agent fails
   */
  private generateFallbackInstructions() {
    // Create a basic template based on the agent description
    this.currentAgent.instructions = `You are ${this.currentAgent.agentName || 'an AI assistant'}.

Purpose: ${this.currentAgent.userInstructions || 'No description provided'}

${this.selectedPlugins.length > 0 ? 'You have access to the following plugins: ' + this.selectedPlugins.join(', ') : ''}

When responding to user queries:
1. Be helpful, accurate, and concise
2. Stay within your defined purpose and capabilities
3. If you're unsure about something, acknowledge your limitations
4. Provide clear, well-structured responses
`;
    // Enable the instructions field for editing
    this.isInstructionsDisabled = false;
  }

  saveAgent() {
    this.currentAgent.tools = this.selectedPlugins;
    this.currentAgent.workspace = this.workspaceName;
    console.log(this.workspaceName);

    console.log(this.currentAgent);
    this.agentService
      .createOrUpdate(this.currentAgent)
      .subscribe((res: any) => {
        console.log(res);
        if (this.routerParts[2] == 'agents') {
          this.router.navigate(['settings', 'agents']);
        }
        else {
          this.router.navigate(['workspaces', this.workspaceName, 'agents']);
        }
      });
  }

  /**
   * Check if the agent has required fields filled
   * @returns boolean indicating if the agent has required fields
   */
  hasRequiredFields(): boolean {
    // Check if all required fields are filled
    return (
      this.currentAgent.agentName?.trim() !== '' &&
      this.currentAgent.instructions?.trim() !== '' &&
      this.currentAgent.modelName?.trim() !== '' &&
      this.workspaceName?.trim() !== '' &&
      this.currentAgent.userInstructions?.trim() !== ''
    );
  }

  /**
   * Open the chat interface for testing the agent
   */
  testAgent(): void {
    console.log("Opening chat interface");

    // Make sure we have the required fields
    if (!this.hasRequiredFields()) {
      console.error("Missing required fields");
      return;
    }

    // Clear previous chat messages when starting a new test
    this.chatMessages = [];

    // Add a welcome message
    const welcomeMessage: ChatMessage = {
      sender: 'agent',
      content: `Hello! I'm your ${this.currentAgent.agentName || 'AI'} agent. How can I help you today?`,
      timestamp: new Date()
    };
    this.chatMessages.push(welcomeMessage);

    // Show the chat sidebar with a slight delay to ensure smooth animation
    setTimeout(() => {
      this.showTestResults = true;

      // Force scroll to bottom after sidebar is shown
      setTimeout(() => {
        this.scrollToBottom();
        // Try one more time after a longer delay to ensure content is fully rendered
        setTimeout(() => {
          this.scrollToBottom();
        }, 500);
      }, 300);
    }, 50);
  }

  /**
   * Send a message in the chat
   */
  sendChatMessage(): void {
    if (!this.chatInput.trim()) {
      return;
    }

    console.log("Sending message:", this.chatInput);

    // Add user message to chat
    const userMessage: ChatMessage = {
      sender: 'user',
      content: this.chatInput.trim(),
      timestamp: new Date()
    };
    this.chatMessages.push(userMessage);

    // Store the question
    const question = this.chatInput.trim();

    // Clear input field
    this.chatInput = '';

    // Add loading message
    const loadingMessage: ChatMessage = {
      sender: 'loading',
      content: '',
      timestamp: new Date()
    };
    this.chatMessages.push(loadingMessage);

    // Force scroll to bottom with multiple attempts to ensure it works
    setTimeout(() => {
      this.scrollToBottom();
      // Try again after a short delay
      setTimeout(() => {
        this.scrollToBottom();
      }, 300);
    }, 100);

    // Set testing state
    this.isTestingAgent = true;

    // Make sure we have an agent name
    if (!this.currentAgent.agentName) {
      this.chatMessages.pop(); // Remove loading message
      const errorMessage: ChatMessage = {
        sender: 'agent',
        content: 'Error: Agent name is required',
        timestamp: new Date()
      };
      this.chatMessages.push(errorMessage);
      this.isTestingAgent = false;
      return;
    }

    // Call the agent
    console.log("Calling agent:", this.currentAgent.agentName);
    this.agentService.testAgent(question, this.currentAgent)
      .subscribe({
        next: (response: ResponseMessage) => {
          // Remove loading message
          this.chatMessages.pop();

          // Add agent response
          const agentMessage: ChatMessage = {
            sender: 'agent',
            content: response.message || 'No response from agent',
            timestamp: new Date()
          };
          this.chatMessages.push(agentMessage);

          // Update test state
          this.isTestingAgent = false;
          this.testSuccessful = true;

          // Scroll to bottom with multiple attempts
          this.scrollToBottom();
          setTimeout(() => {
            this.scrollToBottom();
          }, 300);
        },
        error: (error: any) => {
          // Remove loading message
          this.chatMessages.pop();

          // Add error message
          const errorMessage: ChatMessage = {
            sender: 'agent',
            content: 'Error: ' + (error.message || 'Unknown error occurred'),
            timestamp: new Date()
          };
          this.chatMessages.push(errorMessage);

          // Update test state
          this.isTestingAgent = false;

          // Scroll to bottom with multiple attempts
          this.scrollToBottom();
          setTimeout(() => {
            this.scrollToBottom();
          }, 300);
        }
      });
  }
}

import { Component, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TogglingService } from '../toggling.service';
import { CommonModule } from '@angular/common';
import { AiAgentComponent } from "./ai-agent/ai-agent.component";
import { ChatModelComponent } from "./chat-model/chat-model.component";

@Component({
  selector: 'app-ai-settings',
  standalone: true,
  imports: [CommonModule, AiAgentComponent, ChatModelComponent],
  templateUrl: './ai-settings.component.html',
  styleUrl: './ai-settings.component.css'
})
export class AiSettingsComponent {
constructor(private route: ActivatedRoute) {}
  adminTab:'chatModel'|"aiAgent" = 'aiAgent';
  togglingservice=inject(TogglingService)
}

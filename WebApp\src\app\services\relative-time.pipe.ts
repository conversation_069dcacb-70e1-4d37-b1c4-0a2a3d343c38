import { Pipe, PipeTransform } from '@angular/core';
import { TimeFormatService } from './time-format.service';
import { Observable } from 'rxjs';

@Pipe({
  name: 'relativeTime',
  pure: false,
  standalone: true
})
export class RelativeTimePipe implements PipeTransform {
  constructor(private timeFormatService: TimeFormatService) {}

  transform(timestamp: number): Observable<string> {
    return this.timeFormatService.getFormattedTimeObservable(timestamp);
  }
}

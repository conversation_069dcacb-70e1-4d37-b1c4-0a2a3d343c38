import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnInit, OnChanges, OnDestroy, inject } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { firstValueFrom, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { ThemeService } from '../../../../shared/services/theam.service';
import { DocsServiceProxy, UpdateFavoriteDto, TrackDocumentOpenDto } from '../../../../shared/service-proxies/service-proxies';
import { DocumentSyncService, DocumentUpdateEvent } from '../../../shared/services/document-sync.service';
import { ActiveDocumentService } from '../../../shared/services/active-document.service';

@Component({
  selector: 'app-notes-sidebar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './notes-sidebar.component.html',
  styleUrl: './notes-sidebar.component.css'
})
export class NotesSidebarComponent implements OnInit, OnChanges, OnDestroy {
  @Input() workspaceName: string = '';

  favorites: any[] = [];
  recentDocuments: any[] = [];
  allDocuments: any[] = [];
  allNotes: any[] = []; // Complete list of all notes for the workspace
  isPublicRoute: boolean = false;
  activeFilter: 'all' | 'favorites' | 'recent' = 'all';

  private subscriptions: Subscription[] = [];
  private lastSelectedDocumentId: number | null = null;
  private selectionDebounceTimeout: any = null;

  // Inject services
  themeService = inject(ThemeService);
  documentSyncService = inject(DocumentSyncService);
  activeDocumentService = inject(ActiveDocumentService);

  constructor(
    private docsService: DocsServiceProxy,
    private router: Router
  ) { }

  ngOnInit() {
    // Determine if this is a public route
    this.isPublicRoute = this.router.url.includes('/notes');

    // Set workspace name based on route
    if (this.isPublicRoute) {
      this.workspaceName = 'GlobalNotes';
    } else {
      // Extract workspace name from URL if not provided via @Input
      if (!this.workspaceName) {
        const urlSegments = this.router.url.split('/');
        if (urlSegments.includes('workspaces') && urlSegments.length > 2) {
          const workspaceIndex = urlSegments.indexOf('workspaces');
          this.workspaceName = decodeURIComponent(urlSegments[workspaceIndex + 1]);
        } else {
          this.workspaceName = 'GlobalNotes'; // Fallback
        }
      }
    }

    console.log('🚀 Notes sidebar initialized for workspace:', this.workspaceName);
    console.log('📍 Is public route:', this.isPublicRoute);
    console.log('🔗 Current URL:', this.router.url);

    this.loadSidebarData();

    // Subscribe to active document changes from the service
    const activeDocumentSubscription = this.activeDocumentService.activeDocument$.subscribe(activeDoc => {
      console.log('📄 Active document changed:', activeDoc);
      // The component will automatically update through the isDocumentActive method
    });

    // Subscribe to route changes to refresh data when navigating
    const routeSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        const currentRoute = this.router.url;
        const wasPublicRoute = this.isPublicRoute;
        this.isPublicRoute = currentRoute.includes('/notes');

        // Update workspace name if route type changed
        if (this.isPublicRoute !== wasPublicRoute) {
          if (this.isPublicRoute) {
            this.workspaceName = 'GlobalNotes';
          }
          this.loadSidebarData();
        }
      });

    // Subscribe to document updates to refresh sidebar data
    const documentUpdateSubscription = this.documentSyncService.documentUpdated$.subscribe((event: DocumentUpdateEvent) => {
      console.log('Notes sidebar received document update event:', event);
      console.log('Current workspace:', this.workspaceName);
      console.log('Event workspace:', event.workspaceName);

      // Check workspace matching more carefully
      const isWorkspaceMatch = !event.workspaceName ||
                              event.workspaceName === this.workspaceName ||
                              (event.workspaceName === 'GlobalNotes' && this.workspaceName === 'GlobalNotes');

      if (isWorkspaceMatch) {
        console.log('✅ Workspace match - processing event for workspace:', this.workspaceName);

        // Handle different types of document events with optimized reloading
        if (event.type === 'created' && event.document) {
          console.log('🆕 Adding new document to recent list immediately:', event.document);
          this.addNewDocumentToRecent(event.document);
          // Only reload all notes for new documents to include them in the complete list
          setTimeout(() => {
            console.log('🔄 Reloading all notes for new document');
            this.loadAllNotes();
          }, 1000);
        } else if (event.type === 'favorited' && event.document) {
          console.log('🌟 Document favorite status changed:', event.document);
          this.handleFavoriteChange(event.document);
          // Only reload favorites for favorite changes
          setTimeout(() => {
            console.log('🔄 Reloading favorites for favorite change');
            this.loadFavoritesOnly();
          }, 200);
        } else if (event.type === 'deleted' && event.document) {
          console.log('🗑️ Document deleted, refreshing all data');
          // For deletions, we need to refresh all data
          setTimeout(() => {
            console.log('🔄 Refreshing all data for deletion');
            this.loadSidebarData();
          }, 100);
        }
        // Note: No automatic reload for 'updated' events to prevent unnecessary reloads on document selection
      } else {
        console.log('❌ Workspace mismatch - ignoring event');
      }
    });

    this.subscriptions.push(activeDocumentSubscription, routeSubscription, documentUpdateSubscription);
  }

  ngOnChanges() {
    // Reload data when workspace changes
    if (this.workspaceName) {
      console.log('🔄 Workspace changed, reloading data for:', this.workspaceName);
      this.loadSidebarData();
    }
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions to prevent memory leaks
    this.subscriptions.forEach(subscription => subscription.unsubscribe());

    // Clear any pending debounce timeout
    if (this.selectionDebounceTimeout) {
      clearTimeout(this.selectionDebounceTimeout);
    }
  }

  // Filter methods
  toggleFilter(filter: 'all' | 'favorites' | 'recent') {
    this.activeFilter = filter;
  }

  getFilteredDocuments(): any[] {
    switch (this.activeFilter) {
      case 'favorites':
        return this.favorites;
      case 'recent':
        return this.recentDocuments;
      case 'all':
      default:
        return this.allNotes; // Use complete notes list for "All Notes" filter
    }
  }

  getCurrentFilterTitle(): string {
    switch (this.activeFilter) {
      case 'favorites':
        return 'Favorites';
      case 'recent':
        return 'Recent';
      case 'all':
      default:
        return 'All Notes';
    }
  }

  loadSidebarData() {
    this.updateFavoritesAndRecent();
    this.loadAllNotes();
  }

  // Public method to refresh data (can be called from parent components)
  refreshData() {
    this.loadSidebarData();
  }

  updateFavoritesAndRecent() {
    console.log('Updating favorites and recent documents for workspace:', this.workspaceName);

    // Get favorites from API
    this.docsService.getFavorites(this.workspaceName).subscribe(
      (data: any) => {
        console.log('Loaded favorites:', data);
        this.favorites = data;
        this.updateAllDocuments();
        // Update favorite status in all notes after loading favorites
        this.updateFavoriteStatusInAllNotes();
      },
      (error: any) => {
        console.error('Error loading favorites:', error);
        this.favorites = [];
      }
    );

    // Get recently opened documents from API
    this.docsService.getRecentlyOpened(this.workspaceName, 5).subscribe(
      (data: any) => {
        console.log('Loaded recent documents:', data);
        this.recentDocuments = data;
        this.updateAllDocuments();
      },
      (error: any) => {
        console.error('Error loading recent documents:', error);
        this.recentDocuments = [];
      }
    );
  }

  private updateAllDocuments() {
    // Combine favorites and recent documents, removing duplicates
    const allDocs = [...this.favorites, ...this.recentDocuments];
    const uniqueDocs = allDocs.filter((doc, index, self) =>
      index === self.findIndex(d => d.id === doc.id)
    );
    this.allDocuments = uniqueDocs;
  }

  loadAllNotes() {
    console.log('Loading all notes for workspace:', this.workspaceName);

    // Get all documents from API using getByWorkspaceName
    this.docsService.getByWorkspaceName(this.workspaceName).subscribe(
      (data: any) => {
        console.log('Loaded all notes:', data);
        this.allNotes = data || [];

        // Update favorite status for all notes based on favorites list
        this.updateFavoriteStatusInAllNotes();
      },
      (error: any) => {
        console.error('Error loading all notes:', error);
        this.allNotes = [];
      }
    );
  }

  loadFavoritesOnly() {
    console.log('Loading favorites only for workspace:', this.workspaceName);

    // Get favorites from API
    this.docsService.getFavorites(this.workspaceName).subscribe(
      (data: any) => {
        console.log('Loaded favorites only:', data);
        this.favorites = data;
        // Update favorite status in all notes after loading favorites
        this.updateFavoriteStatusInAllNotes();
      },
      (error: any) => {
        console.error('Error loading favorites only:', error);
        this.favorites = [];
      }
    );
  }

  private updateFavoriteStatusInAllNotes() {
    // Update the favorite status for all notes based on the favorites list
    this.allNotes = this.allNotes.map(note => ({
      ...note,
      isFavorite: this.favorites.some(fav => fav.id === note.id)
    }));
  }

  selectDocument(document: any) {
    // Set the active document using the service for instant UI feedback
    this.activeDocumentService.setActiveDocument(document, {
      workspaceName: this.workspaceName,
      route: this.isPublicRoute ? 'notes' : 'workspace-documents'
    });
    console.log('📄 Document selected, setting as active:', document.title);

    this.addToRecentDocuments(document);

    // Navigate to the document
    if (this.isPublicRoute) {
      this.router.navigate(['/notes'], { queryParams: { docId: document.id } });
    } else {
      this.router.navigate(['/workspaces', this.workspaceName, 'documents'], { queryParams: { docId: document.id } });
    }
  }

  addDocument(event: Event) {
    event.stopImmediatePropagation();

    // Navigate to add document
    if (this.isPublicRoute) {
      this.router.navigate(['/notes'], { queryParams: { action: 'add' } });
    } else {
      this.router.navigate(['/workspaces', this.workspaceName, 'documents'], { queryParams: { action: 'add' } });
    }
  }

  showDocumentsList() {
    // Navigate to documents list
    console.log('🔗 View All button clicked');
    console.log('📍 Is public route:', this.isPublicRoute);
    console.log('📁 Workspace name:', this.workspaceName);

    if (this.isPublicRoute) {
      console.log('🔗 Navigating to public notes: /notes');
      this.router.navigate(['/notes']);
    } else {
      console.log('🔗 Navigating to workspace documents:', `/workspaces/${this.workspaceName}/documents`);
      this.router.navigate(['/workspaces', this.workspaceName, 'documents']);
    }
  }

  isDocumentFavorite(document: any): boolean {
    return this.favorites.some((fav) => fav.id === document.id);
  }

  isDocumentActive(document: any): boolean {
    return this.activeDocumentService.isDocumentActive(document.id);
  }

  toggleFavorite(document: any) {
    const isFavorite = this.isDocumentFavorite(document);

    // Immediately update local state for better UX
    if (isFavorite) {
      this.favorites = this.favorites.filter((fav) => fav.id !== document.id);
    } else {
      this.favorites.push({ ...document, isFavorite: true });
    }

    // Update favorite status in all notes array immediately
    this.updateFavoriteStatusInAllNotes();

    // Use the API to update favorite status (fire and forget for performance)
    this.docsService.updateFavoriteStatus({
      id: document.id,
      isFavorite: !isFavorite
    } as UpdateFavoriteDto).subscribe(
      () => {
        // API call successful - no additional action needed since we already updated local state
        console.log('Favorite status updated successfully for:', document.title);

        // Notify other components about the favorite change
        this.documentSyncService.notifyDocumentFavorited(
          { ...document, isFavorite: !isFavorite },
          this.workspaceName
        );
      },
      (error: any) => {
        console.error('Error updating favorite status:', error);
        // Revert local state changes on API error
        if (!isFavorite) {
          this.favorites = this.favorites.filter((fav) => fav.id !== document.id);
        } else {
          this.favorites.push({ ...document, isFavorite: true });
        }
        this.updateFavoriteStatusInAllNotes();
      }
    );
  }

  addToRecentDocuments(document: any) {
    // Immediately update local state for better UX
    this.recentDocuments = this.recentDocuments.filter(
      (doc) => doc.id !== document.id
    );
    this.recentDocuments.unshift(document);
    if (this.recentDocuments.length > 5) {
      this.recentDocuments = this.recentDocuments.slice(0, 5);
    }

    // Debounce API calls to prevent excessive requests when users click rapidly
    if (this.selectionDebounceTimeout) {
      clearTimeout(this.selectionDebounceTimeout);
    }

    // Only make API call if this is a different document or after debounce delay
    if (this.lastSelectedDocumentId !== document.id) {
      this.lastSelectedDocumentId = document.id;

      this.selectionDebounceTimeout = setTimeout(() => {
        // Use the API to track document opens (fire and forget for performance)
        this.docsService.trackDocumentOpen({
          id: document.id
        } as TrackDocumentOpenDto).subscribe(
          () => {
            // API call successful - no additional action needed since we already updated local state
            console.log('Document open tracked successfully for:', document.title);
          },
          (error: any) => {
            console.error('Error tracking document open:', error);
            // Note: We don't revert local state changes on API error to maintain good UX
          }
        );
      }, 300); // 300ms debounce delay
    }
  }

  private addNewDocumentToRecent(document: any) {
    // Immediately add the new document to the recent list for instant visibility
    if (document && document.id) {
      console.log('🆕 Adding new document to recent list:', document);

      // Remove if already exists (shouldn't happen for new docs, but just in case)
      this.recentDocuments = this.recentDocuments.filter(
        (doc) => doc.id !== document.id
      );

      // Add to the beginning of the list
      this.recentDocuments.unshift(document);

      // Keep only the latest 5
      if (this.recentDocuments.length > 5) {
        this.recentDocuments = this.recentDocuments.slice(0, 5);
      }

      console.log('✅ Updated recent documents list:', this.recentDocuments);

      // Force change detection to ensure UI updates
      setTimeout(() => {
        console.log('🔄 Forcing UI update for new document');
      }, 0);
    }
  }

  private handleFavoriteChange(document: any) {
    // Handle immediate favorite status changes in the local lists
    if (document && document.id) {
      console.log('🌟 Handling favorite change for document:', document);

      const isFavorite = document.isFavorite || document.isFavourite;

      if (isFavorite) {
        // Add to favorites if not already there
        const existsInFavorites = this.favorites.some(fav => fav.id === document.id);
        if (!existsInFavorites) {
          this.favorites.push(document);
          console.log('✅ Added to favorites list');
        }
      } else {
        // Remove from favorites
        this.favorites = this.favorites.filter(fav => fav.id !== document.id);
        console.log('✅ Removed from favorites list');
      }

      // Update the document in recent list if it exists there
      const recentIndex = this.recentDocuments.findIndex(doc => doc.id === document.id);
      if (recentIndex !== -1) {
        this.recentDocuments[recentIndex] = { ...this.recentDocuments[recentIndex], ...document };
        console.log('✅ Updated document in recent list');
      }
    }
  }
}

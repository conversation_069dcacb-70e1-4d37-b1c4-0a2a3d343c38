import { CommonModule } from '@angular/common';
import { Component, Inject, Input } from '@angular/core';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import {
  NZ_MODAL_DATA,
  NzModalModule,
  NzModalRef,
  NzModalService,
} from 'ng-zorro-antd/modal';
import {
  AssignWorkspaceServiceProxy,
  UserAccountServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
@Component({
  selector: 'app-add-user',
  standalone: true,
  imports: [CommonModule, NzTableModule, NzButtonModule, NzModalModule],
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.css',
  providers: [AssignWorkspaceServiceProxy],
})
export class AddUserComponent {
  users: any = []; // Array to hold user data
  addedUsers: any = []; // Array to hold added users
  constructor(
    private modalRef: NzModalRef,
    private userAccountService: UserAccountServiceProxy,
    private modalService: NzModalService,
    private assignWorkspaceService: AssignWorkspaceServiceProxy,
    @Inject(NZ_MODAL_DATA) public data: { id: number; users: any } // Inject the workspace ID and user list
  ) {}
  ngOnInit(): void {
    this.loadUsers();
    console.log(this.data.id);
    console.log(this.data.users);
  }
  loadUsers() {
    this.userAccountService.getAll().subscribe((users: any) => {
      this.users = users.filter((user: any) => !this.data.users.some((u: any) => u.email === user.email));
    });
  }
  addUser(user: any) {
    this.assignWorkspaceService
      .assignUser(this.data.id, user.email)
      .subscribe((response: any) => {
        console.log('User added:', response);
        this.addedUsers.push(user);
        // remove added user from the list
        this.users = this.users.filter((u: any) => u.email !== user.email);
      });
  }
  closeDialog(): void {
    this.modalRef.close({
      addedUsers: this.addedUsers, // Return the added users to the parent component
    });
  }
}

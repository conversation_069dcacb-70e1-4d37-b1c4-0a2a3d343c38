import { Component } from '@angular/core';
import { ModelDetailsServiceProxy, ApiCredentialsServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-old-settings',
  standalone: true,
  imports: [ServiceProxyModule,CommonModule,FormsModule],
  templateUrl: './old-settings.component.html',
  styleUrl: './old-settings.component.css'
})
export class OldSettingsComponent {
// Add any necessary component logic here
  activeTab:
    | 'general'
    | 'connections'
    | 'models'
    | 'evaluations'
    | 'documents'
    | 'web search'
    | 'interface'
    | 'audio'
    | 'images'
    | 'pipelines'
    | 'database' = 'general';
  models: any = [];
  apiLists: any = [];
  apiData: any = {
    tokenUrl: '',
    apiKey: '',
  };
  showForm = false;
  isCredentialsValid = false;

  constructor(
    private modelDetails: ModelDetailsServiceProxy,
    private apiCredentials: ApiCredentialsServiceProxy
  ) {}

  ngOnInit() {
    this.loadModels();
    this.loadApiCredentials();
  }
  ///////////////Models Detais code//////////////////////
  loadModels() {
    this.modelDetails.getAll().subscribe((res: any) => {
      if (res) {
        this.models = res;
      }
    });
  }
  updateModelIsActive(model: any) {
    this.modelDetails
      .updateIsActive(model.modelName, !model.isActive)
      .subscribe((res) => {
        console.log(res);
      });
  }




  // ***********************************************************************************************

  ////////////////Api Credentials code//////////////////////
  loadApiCredentials() {
    this.apiCredentials.getAll().subscribe((res: any) => {
      if (res) {
        this.apiLists = res;
      }
    });
  }
  onAddApi() {
    this.showForm = true;
  }

  async validateCredentials() {
    if (this.apiData.tokenUrl && this.apiData.apiKey) {
      let res = await this.apiCredentials.validate(this.apiData).toPromise();
      if (res) {
        if (res.isError) {
          this.isCredentialsValid = false;
          alert(res.message);
        } else {
          this.isCredentialsValid = true;
        }
      }
    }
  }

  saveApi() {
    this.apiCredentials.create(this.apiData).subscribe((res: any) => {
      if (res) {
        this.apiLists.push(res);
        this.resetForm();
      }
    });
  }
  editApi(api: any) {
    this.apiData = api;
    this.showForm = true;
  }
  onEditApi() {
    this.apiCredentials.getById(this.apiData).subscribe((res: any) => {
      if (res) {
        this.resetForm();
      }
    });
  }
  deleteApi(api: any) {
    console.log(api);
    this.apiCredentials.delete(api.id).subscribe((res: any) => {
      if (res) {
        this.apiLists = this.apiLists.filter((item: any) => item.id != api.id);
      }
    });
  }
  /////////////General methods//////////////////////

  resetForm() {
    this.apiData = {
      tokenUrl: '',
      apiKey: '',
    };
    this.isCredentialsValid = false;
    this.showForm = false;
  }

  setActiveTab(tab: any) {
    this.activeTab = tab;
  }
}

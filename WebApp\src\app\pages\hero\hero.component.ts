import {
  Component,
  ViewChild,
  ElementRef,
  OnInit,
  inject,
  AfterViewInit,
  OnDestroy,
  HostListener,
  Pipe, PipeTransform,
  isDevMode
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { marked, use } from 'marked';
import { HeaderComponent } from '../../components/header/header.component';
import { timestamp, firstValueFrom, Subscription, Observable } from 'rxjs';
import { ActivatedRoute, Router, RouterEvent, RouterLink } from '@angular/router';
import {
  AgentDefinitionServiceProxy,
  ChatContinueRequestDto,
  ChatHistoryDto,
  ChatHistoryResponseDto,
  ChatMessageDto,
  ChatRequestDto,
  ChatServiceProxy,
  EmailServiceProxy,
  ModelDetailsServiceProxy,
  PromptLibraryServiceProxy,
  RegenerateResponseRequestDto,
  ResponseMessage,
  SendEmailDto,
  WorkspaceServiceProxy,
  WorkspaceDto,
  ChatEditRequestDto
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { ChatListService } from '../../services/chat-list.service';
import { AuthService } from '../../../shared/services/auth.service';
import { ChatService } from '../../services/chat.service';
import { TimeFormatService } from '../../services/time-format.service';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { SidebarComponent } from '../../components/sidebar/sidebar.component';
import { WorkspaceSidebarComponent } from '../../workspaces/workspace-sidebar/workspace-sidebar.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalModule, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { MarkdownModule } from 'ngx-markdown';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { SourceReferencesComponent } from '../../components/@rightSideComponents/source-references/source-references.component';
import { AgentSidebarComponent } from '../../components/@rightSideComponents/agent-sidebar/agent-sidebar.component';
import { SqlConnectionDialogComponent } from '../../components/sql-connection-dialog/sql-connection-dialog.component';
import { BlogShareDialogComponent } from '../../components/blog-share-dialog/blog-share-dialog.component';

import { NzSelectModule } from 'ng-zorro-antd/select';
import { RemoveProviderPrefixPipe } from "../../../shared/pipes/remove-provider-prefix.pipe";
import { RelativeTimePipe } from "../../services/relative-time.pipe";
import { AngularSplitModule } from 'angular-split';

@Component({
  selector: 'app-hero',
  standalone: true, imports: [
    CommonModule,
    FormsModule,
    ServiceProxyModule,
    NzDrawerModule, // Still needed for other drawers
    WorkspaceSidebarComponent,
    MarkdownModule, // Added MarkdownModule
    NzSelectModule,
    NzBadgeModule,
    NzToolTipModule,
    NzModalModule, // Added NzModalModule
    RemoveProviderPrefixPipe,
    RelativeTimePipe,
    RouterLink,
    SourceReferencesComponent,
    AgentSidebarComponent,
    AngularSplitModule, // Added for splitter functionality
    SqlConnectionDialogComponent, // Added SQL connection dialog
    BlogShareDialogComponent // Added blog share dialog
  ],
  providers: [
    NzModalService // Add NzModalService as a provider
  ],
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.css', './hero.splitter.css'], // Added splitter CSS
})
export class HeroComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chatInput') chatInput!: ElementRef;
  @ViewChild('chatContainer') chatContainer!: ElementRef;
  @ViewChild('promptDialog') promptDialog!: ElementRef;
  @ViewChild('toggleWorkspaceList') toggleWorkspaceList!: ElementRef;
  @ViewChild('agentListDialog') agentListDialog!: ElementRef; // Reference to the dialog element

  userInput: ChatRequestDto = new ChatRequestDto();

  chatHistory: any;
  chatList: any = [];
  chatId: any;
  isMessageLoading = false;
  previouseMsg: any;
  activeMessage: any = null;
  lastMessageId: string | null = null;
  currentResponseIndexMap: Map<string, number> = new Map();
  isSpeaking = false;
  isRecording = false;
  isRegenerating = false;
  private messageSubscription: Subscription;

  recognition: any;
  agents: any = []; // Replace with actual agent names
  showAgentDropdown = false;
  filteredAgents: any[] = [];
  suggestionList: any[] = [];
  showScrollButton: boolean = false; // new property
  hasWorkspace: boolean = false; // new property
  isChatHistoryOpen: boolean = false; // new property
  models: any = [];
  filteredModels: any = [];
  searchModelQuery = '';
  selectedModel = '';
  searchAgentQuery = '';
  selectedAgent = '';
  isAgentListVisible = false; // Flag to control the visibility of the agent list dialog

  // For @ mention functionality
  mentionDropdownVisible = false;
  mentionFilterText = '';
  mentionFilteredAgents: any[] = [];
  cursorPosition = 0;
  mentionStartIndex = -1;
  selectedMentionIndex = 0; // For keyboard navigation in mention dropdown

  // Sidebar and source properties
  showSearchResultsSidebar: boolean = false;
  searchResults: any[] = [];
  currentSourceName: string = '';

  showPromptDialog = false;
  prompts: any[] = [];
  filteredPrompts: any[] = [];
  searchPromptQuery = '';
  selectedPromptIndex = 0;

  workspaces: WorkspaceDto[] = [];
  selectedWorkspace: string = '';

  // Agent sidebar properties
  isAgentSidebarOpen: boolean = false;
  workspaceAgents: any[] = [];
  agentSidebarTitle: string = 'Available Agents';
  private _currentWorkspaceForAgents: string = '';

  // Splitter properties
  readonly DEFAULT_RIGHT_SIDEBAR_WIDTH: number = 350; // Fixed initial width in pixels
  readonly RIGHT_SIDEBAR_STORAGE_KEY: string = 'heroRightSidebarWidth'; // Key for localStorage
  rightSidebarWidth: number = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Current width in pixels
  isDragging: boolean = false;
  minRightSidebarWidth: number = 250; // Minimum width in pixels
  maxRightSidebarWidth: number = 800; // Maximum width in pixels (increased to allow more space)
  autoCloseThreshold: number = 200; // Width below which sidebar auto-closes
  private lastValidWidth: number = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Store last valid width for recovery

  // Split sizes for the main content and right sidebar
  mainContentSplitSize: number = 100; // Default to 100% when no sidebar is visible
  rightSidebarSplitSize: number = 0; // Default to 0% when no sidebar is visible

  // Reference to Math for use in the template
  Math = Math;

  // Property to track if we're in development mode
  public readonly isDevMode = isDevMode();
  constructor(
    private _chatService: ChatServiceProxy,
    private router: Router,
    private authService: AuthService,
    private agentDefinition: AgentDefinitionServiceProxy,
    private sanitizer: DomSanitizer,
    private chatService: ChatService,
    public auth: AuthService,
    public timeFormatService: TimeFormatService,
    private modelDetailsService: ModelDetailsServiceProxy,
    private message: NzMessageService,
    private promptService: PromptLibraryServiceProxy,
    private workspaceService: WorkspaceServiceProxy,
    private emailService: EmailServiceProxy,
    private modal: NzModalService // Inject NzModalService
  ) {
    // Configure marked options
    // Subscribe to SignalR message updates
    this.messageSubscription = this.chatService.messageReceived$.subscribe(
      ({ message, isError, isComplete }) => {
        console.log('Message received:', message);
        if (isError) {
          this.isMessageLoading = false;
          // Handle error message
          if (this.chatHistory?.history) {
            const lastMessage =
              this.chatHistory.history[this.chatHistory.history.length - 1];
            if (lastMessage) {
              const messageText = this.extractMessageText(message);
              console.log(messageText);
              lastMessage.responses.push(messageText);
              console.log(messageText);
            }
          }
        } else {
          const messageText = this.extractMessageText(message);
          console.log(messageText);

          if (messageText && this.isMessageLoading) {
            this.isMessageLoading = false;
            console.log('in loading');
          }
          console.log('chatHistory');

          if (this.chatHistory?.history) {
            console.log('in history');

            const lastMessage =
              this.chatHistory.history[this.chatHistory.history.length - 1];
            if (lastMessage) {
              // console.log(lastMessage);

              if (lastMessage.responses.length > 0) {
                const lastResponse =
                  lastMessage.responses[lastMessage.responses.length - 1];
                lastResponse.response =
                  (lastResponse.response || '') + messageText;
              } else {
                lastMessage.responses.push({ response: messageText });
              }
            }
          }
          this.scrollToBottom();
        }
      }
    );
  }
  route = inject(ActivatedRoute);
  chatListSrvice = inject(ChatListService); ngOnInit(): void {
    // Comment out agent and model logic
    /*
    this.userInput.modelName = this.authService.modelName;
    this.selectedAgent = 'AnswerAgent';
    this.userInput.agentName = this.selectedAgent;
    */
    this.userInput.workspace = '';

    // Load saved right sidebar width from localStorage
    this.loadSavedRightSidebarWidth();

    // Comment out model selection
    /*
    this.selectedModel = this.auth.modelName;
    console.log(this.selectedAgent);
    */

    // Always initialize chat history with an empty object structure
    // This ensures a consistent view across both new chats and existing chats
    this.chatHistory = { title: '', history: [] };

    this.loadSuggestionList();
    this.loadWorkspaces();
    let router = this.router.url.split('/');
    console.log(router[router.length - 1]);
    console.log(router[router.length - 2]);

    if (router[router.length - 1] === 'chat') {
      this.userInput.workspace = router[router.length - 2];
      this.hasWorkspace = true;
    } else if (router[router.length - 2] === 'chat') {
      this.userInput.workspace = router[router.length - 3];
      this.hasWorkspace = true;
    }

    console.log('Workspace:', this.userInput.workspace);
    console.log('Has Workspace:', this.hasWorkspace);

    // Check if we have navigation state with preloaded chat data
    const navigation = this.router.getCurrentNavigation();
    const hasPreloadedChat = navigation?.extras?.state?.['preloadedChat'];

    // We don't need to load agents on init
    // They will be loaded on demand when needed
    this.setupSpeechRecognition();

    // Subscribe to route params to get the chat ID
    this.route.params.subscribe((params) => {
      this.chatId = params['id'];
      console.log('Chat ID from route:', this.chatId); if (this.chatId) {
        this.chatListSrvice.chatId = this.chatId;        // Check if we have preloaded chat data in the service
        if (hasPreloadedChat || this.chatListSrvice.hasCurrentChatHistory()) {
          console.log('Using preloaded chat data');
          this.loadChatHistory();
        } else {
          // Otherwise, load the chat normally
          this.loadChat();
        }

        // Always ensure chatHistory is properly initialized, even for new chats (id=0)
        // This maintains a consistent data structure for all states
        if (this.chatId == 0) {
          this.chatHistory = { title: '', history: [] };
        }
      }
    });

    this.loadPromptLibrary();
  }

  // Load workspaces based on user role
  loadWorkspaces() {
    // Determine which API endpoint to use based on user role
    const workspaceObservable = this.authService.isAdmin()
      ? this.workspaceService.getAll()
      : this.workspaceService.getWorkspacesByUserEmail();

    workspaceObservable.subscribe({
      next: (workspaces: WorkspaceDto[]) => {
        console.log('Workspaces:', workspaces);
        this.workspaces = workspaces;

        // Only set a workspace if we have workspaces from the API
        if (workspaces && workspaces.length > 0) {
          // Try to find a default workspace
          const defaultWorkspace = workspaces.find(w => w.isDefault);

          if (defaultWorkspace?.title) {
            // If there's a default workspace, select it
            this.selectWorkspace(defaultWorkspace.title);
          } else {
            // Otherwise select the first workspace in the list
            this.selectWorkspace(workspaces[0].title || '');
          }
        } else {
          // If no workspaces are available, clear the selection
          this.selectedWorkspace = '';
          this.userInput.workspace = '';
        }
      },
      error: (error) => {
        console.error('Error loading workspaces:', error);
        // Clear the workspace selection on error
        this.selectedWorkspace = '';
        this.userInput.workspace = '';
      }
    });
  }

  // Select a workspace and get its agent and model
  selectWorkspace(workspaceTitle: string) {
    this.selectedWorkspace = workspaceTitle;
    this.userInput.workspace = workspaceTitle;

    // Load agents for the selected workspace
    this.loadAgentsForWorkspace(workspaceTitle);

    // Update the sidebar title
    this.agentSidebarTitle = `${workspaceTitle} - Available Agents`;
  }

  /**
   * Loads agents for the selected workspace
   * @param workspaceName The name of the workspace to load agents for
   */
  loadAgentsForWorkspace(workspaceName: string) {
    if (!workspaceName) {
      this.workspaceAgents = [];
      return;
    }

    // Only load agents if we haven't already loaded them for this workspace
    // or if we're explicitly forcing a refresh
    if (this._currentWorkspaceForAgents !== workspaceName || this.workspaceAgents.length === 0) {
      console.log(`Loading agents for workspace: ${workspaceName}`);

      this.agentDefinition.getAllByWorkspace(workspaceName).subscribe({
        next: (agents) => {
          this.workspaceAgents = agents || [];
          this._currentWorkspaceForAgents = workspaceName;
          console.log(`Loaded ${this.workspaceAgents.length} agents for workspace ${workspaceName}`);
        },
        error: (err) => {
          console.error('Error loading agents for workspace:', err);
          this.workspaceAgents = [];
        }
      });
    } else {
      console.log(`Using cached agents for workspace: ${workspaceName}`);
    }
  }

  /**
   * Toggles the agent sidebar
   */
  toggleAgentSidebar() {
    console.log('Toggling agent sidebar. Current state:', this.isAgentSidebarOpen);
    console.log('Current workspace:', this.selectedWorkspace);
    console.log('Current agents count:', this.workspaceAgents.length);

    // Toggle the sidebar visibility
    this.isAgentSidebarOpen = !this.isAgentSidebarOpen;

    // Close search results sidebar if agent sidebar is opening
    if (this.isAgentSidebarOpen && this.showSearchResultsSidebar) {
      this.showSearchResultsSidebar = false;
    }

    // Always ensure agents are loaded when the sidebar is open
    if (this.isAgentSidebarOpen && this.selectedWorkspace) {
      // Force reload agents if none are loaded
      if (this.workspaceAgents.length === 0) {
        console.log('Loading agents for workspace:', this.selectedWorkspace);
        this.loadAgentsForWorkspace(this.selectedWorkspace);
      } else {
        console.log('Using existing agents for workspace:', this.selectedWorkspace);
      }
    }

    // Update split sizes based on sidebar visibility
    this.updateSplitSizes();

    // Add a small delay to allow the DOM to update before any animations
    setTimeout(() => {
      // Force a layout recalculation to ensure smooth transitions
      window.dispatchEvent(new Event('resize'));
    }, 10);
  }

  /**
   * Selects an agent to use for the chat
   * @param agent The agent to select
   */
  selectAgent(agent: any) {
    console.log('Selecting agent:', agent);
    if (!agent) return;

    // Set the agent name in the selected agent property
    this.selectedAgent = agent.agentName;

    // Store the agent in a custom property for later use
    // Note: We're using any type to add a custom property
    (this.userInput as any).selectedAgentName = agent.agentName;

    // Close the sidebar
    this.isAgentSidebarOpen = false;

    // Show a success message
    this.message.success(`Agent ${agent.agentName} selected`);

    // Add the agent mention to the message if the input is empty
    if (!this.userInput.message || this.userInput.message.trim() === '') {
      this.userInput.message = `@${agent.agentName} `;

      // Focus the input and set cursor at the end
      setTimeout(() => {
        try {
          if (this.chatInput && this.chatInput.nativeElement) {
            this.chatInput.nativeElement.focus();
            if (this.userInput.message) {
              const length = this.userInput.message.length;
              this.chatInput.nativeElement.setSelectionRange(length, length);
            }
          }
        } catch (error) {
          console.error('Error focusing chat input:', error);
        }
      }, 100);
    } else {
      // Focus the chat input if available
      setTimeout(() => {
        try {
          if (this.chatInput && this.chatInput.nativeElement) {
            this.chatInput.nativeElement.focus();
          }
        } catch (error) {
          console.error('Error focusing chat input:', error);
        }
      }, 100);
    }
  }

  /**
   * Formats a date for display
   * @param date The date to format
   * @returns Formatted date string
   */
  formatDate(date: string | Date): string {
    if (!date) return 'Unknown';

    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Get agent and model based on workspace name
  /*
  getWorkspaceAgentAndModel(workspaceTitle: string | undefined) {
    if (!workspaceTitle) {
      return;
    }

    // Call API to get workspace by title
    this.workspaceService.getByTitle(workspaceTitle).subscribe({
      next: (workspace: WorkspaceDto) => {
        if (workspace && workspace.modelName) {
          this.selectedModel = workspace.modelName;
          this.userInput.modelName = workspace.modelName;

          // Get agents for this workspace
          this.loadAgentsForWorkspace(workspaceTitle as string);
        }
      },
      error: (err) => console.error('Error loading workspace:', err)
    });
  }

  // Helper method to load agents for a workspace
  loadAgentsForWorkspace(workspaceName: string) {
    this.agentDefinition.getAllByWorkspace(workspaceName).subscribe({
      next: (agents) => {
        if (agents && agents.length > 0) {
          // We'll just use the first agent from the list for this workspace
          this.selectedAgent = agents[0].agentName;
          this.userInput.agentName = this.selectedAgent;
        }
      },
      error: (err) => console.error('Error loading agents:', err)
    });
  }
  */

  // code by me
  promptUsedCount(prompt: any) {
    this.promptService.incrementUsage(prompt.id).subscribe((res: any) => {
      console.log(res);
    });
  }

  /**
   * Gets the user's name from the JWT token
   * @returns The user's name or undefined if not found
   */
  getUserName(): string {
    // Use the auth service's getUserName method
    const userName = this.auth.getUserName();
    console.log('User name from hero component:', userName);
    return userName;
  }

  loadPromptLibrary() {
    // Get the current user's email
    const userEmail = this.auth.getUser()?.email;

    // Pass the user's email to get personalized prompts
    this.promptService.getAll(undefined, userEmail).subscribe((res: any) => {
      this.prompts = res;
      this.filteredPrompts = res;

      // After loading prompts, update the suggestion list with top prompts
      this.updateSuggestionListWithTopPrompts();
    });
  }

  /**
   * Gets the top 4 most frequently used prompts, prioritizing user's own prompts over admin prompts
   * @returns Array of the top 4 prompts formatted for the suggestion list
   */
  getTopPrompts(): any[] {
    if (!this.prompts || this.prompts.length === 0) {
      return [];
    }

    const userEmail = this.auth.getUser()?.email;

    // Create a copy of the prompts array to avoid modifying the original
    const sortedPrompts = [...this.prompts];

    // Sort the prompts:
    // 1. First by user ownership (user's own prompts first)
    // 2. Then by usage count (highest first)
    sortedPrompts.sort((a, b) => {
      // If one is user's own prompt and the other isn't, prioritize user's own
      if (a.userEmail === userEmail && b.userEmail !== userEmail) return -1;
      if (a.userEmail !== userEmail && b.userEmail === userEmail) return 1;

      // If both are from the same user (either both user's own or both admin's),
      // sort by usage count (highest first)
      return b.usageCount - a.usageCount;
    });

    // Take the top 4 prompts and format them for the suggestion list
    return sortedPrompts.slice(0, 4).map((prompt) => ({
      id: prompt.id,
      text: prompt.shortMessage || prompt.prompt.substring(0, 100) + (prompt.prompt.length > 100 ? '...' : ''),
      prompt: prompt.prompt,
      isUserPrompt: prompt.userEmail === userEmail,
      usageCount: prompt.usageCount || 0,
      isDefault: false
    }));
  }

  /**
   * Updates the suggestion list with the top prompts from the prompt library
   * If there are no prompts or fewer than 4, it will fill the rest with default suggestions
   */
  updateSuggestionListWithTopPrompts() {
    // Get the top prompts from the prompt library
    const topPrompts = this.getTopPrompts();

    // If we have top prompts, use them
    if (topPrompts.length > 0) {
      this.suggestionList = topPrompts;
    }

    // If we have fewer than 4 prompts, fill the rest with default suggestions
    if (this.suggestionList.length < 4) {
      const defaultSuggestions = this.getDefaultSuggestions();
      const neededCount = 4 - this.suggestionList.length;

      // Add default suggestions to fill up to 4 total
      this.suggestionList = [
        ...this.suggestionList,
        ...defaultSuggestions.slice(0, neededCount)
      ];
    }
  }

  /**
   * Returns a list of default suggestions to use when there aren't enough prompts
   */
  getDefaultSuggestions() {
    return [
      {
        id: 'default-1',
        text: 'Tell me something about the Big Bang so that I can explain it to my 5-year-old child',
        prompt: 'Tell me something about the Big Bang so that I can explain it to my 5-year-old child',
        isDefault: true
      },
      {
        id: 'default-2',
        text: "Please provide me with 10 gift ideas for my friend's birthday",
        prompt: "Please provide me with 10 gift ideas for my friend's birthday",
        isDefault: true
      },
      {
        id: 'default-3',
        text: 'Generate five catchy titles for my writing about the use case of ChatGPT',
        prompt: 'Generate five catchy titles for my writing about the use case of ChatGPT',
        isDefault: true
      },
      {
        id: 'default-4',
        text: 'Can you suggest top attractions and local dining in Paris?',
        prompt: 'Can you suggest top attractions and local dining in Paris?',
        isDefault: true
      },
    ];
  }

  /**
   * Initializes the suggestion list with default suggestions
   * These will be replaced with top prompts when the prompt library loads
   */
  loadSuggestionList() {
    // Start with default suggestions
    this.suggestionList = this.getDefaultSuggestions();
  }
  addSuggestionToChat(suggestion: any) {
    // Use the prompt text if available, otherwise use the display text
    this.userInput.message = suggestion.prompt || suggestion.text;

    // If this is a prompt from the library (not a default suggestion), increment its usage count
    if (suggestion.id && !suggestion.isDefault) {
      this.promptUsedCount(suggestion);
    }

    // Send the message
    this.sendMessage();

    // Clear the input and focus
    this.userInput.message = '';
    this.chatInput.nativeElement.focus();
  }
  loadChat() {
    // Reset web search indicator when loading a new chat
    // this.resetWebSearchIndicator();

    this._chatService.list('', 1, 10).subscribe((res) => {
      console.log(res);
      this.chatList = res;
      if (res && this.chatId != 0) {
        this.loadChatHistory();
      }
    });
  }

  /**
   * Loads agent names only when needed (e.g., when adding an agent)
   * This prevents unnecessary API calls on initial page load
   */
  loadAgents() {
    // Only load agents if we haven't already loaded them
    if (!this.agents || this.agents.length === 0) {
      console.log('Loading agent names on demand');
      this.agentDefinition.getAllAgentName().subscribe({
        next: (res: any) => {
          this.agents = res;
          this.filteredAgents = this.agents; // Initialize filtered agents with all agents
          console.log(`Loaded ${this.agents.length} agent names`);
        },
        error: (err) => {
          console.error('Error loading agent names:', err);
          this.agents = [];
          this.filteredAgents = [];
        }
      });
    } else {
      console.log('Using cached agent names');
    }
  }

  scrollToBottom(): void {
    setTimeout(() => {
      if (this.chatContainer) {
        const element = this.chatContainer.nativeElement;
        // smooth scrolling to bottom
        element.scrollTo({ top: element.scrollHeight, behavior: 'smooth' });
        this.showScrollButton = false; // hide arrow after scrolling down
      }
    }, 100); // Small delay to ensure content is rendered
  }

  onChatScroll(): void {
    const element = this.chatContainer.nativeElement;
    const threshold = 50;
    if (
      element.scrollTop + element.clientHeight <
      element.scrollHeight - threshold
    ) {
      this.showScrollButton = true;
    } else {
      this.showScrollButton = false;
    }
  }
  handleKeyDown(event: KeyboardEvent): void {
    // Check if the key pressed is "/"
    if (event.key === '/' && !this.mentionDropdownVisible && !this.showPromptDialog) {
      // Prevent the default action to avoid adding "/" to the input
      event.preventDefault();

      // Open the prompt dialog
      this.openPromptDialog();

      // Add the "/" to the input manually
      this.userInput.message = this.userInput.message ? this.userInput.message + '/' : '/';

      // Set cursor position after the "/"
      setTimeout(() => {
        const inputElement = this.chatInput.nativeElement;
        const position = inputElement.selectionStart;
        inputElement.setSelectionRange(position, position);
        this.checkForPrompt();
      }, 0);

      return;
    }

    // Handle Enter key for sending messages (not when prompt dialog is open)
    if (event.key === 'Enter' && !event.shiftKey) {
      if (this.showPromptDialog && this.filteredPrompts.length > 0) {
        // Select the currently highlighted prompt
        event.preventDefault();
        this.selectPrompt(this.filteredPrompts[this.selectedPromptIndex]);
        return;
      } else if (this.mentionDropdownVisible && this.mentionFilteredAgents.length > 0) {
        // Select the currently highlighted agent from the mention dropdown
        event.preventDefault();
        this.selectMentionedAgent(this.mentionFilteredAgents[this.selectedMentionIndex]);
        return;
      } else if (!this.showPromptDialog && !this.mentionDropdownVisible) {
        // Only send message if neither prompt dialog nor mention dropdown is open
        event.preventDefault();
        this.sendMessage();
        return;
      } else {
        event.preventDefault();
      }
    }

    // Handle keyboard navigation for mention dropdown
    if (this.mentionDropdownVisible && this.mentionFilteredAgents.length > 0) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          this.selectedMentionIndex = Math.min(this.selectedMentionIndex + 1, this.mentionFilteredAgents.length - 1);
          this.scrollToSelectedMention();
          return;
        case 'ArrowUp':
          event.preventDefault();
          this.selectedMentionIndex = Math.max(this.selectedMentionIndex - 1, 0);
          this.scrollToSelectedMention();
          return;
        case 'Escape':
          event.preventDefault();
          this.mentionDropdownVisible = false;
          return;
        case 'Tab':
          if (this.mentionFilteredAgents.length > 0) {
            event.preventDefault();
            this.selectMentionedAgent(this.mentionFilteredAgents[this.selectedMentionIndex]);
          }
          return;
      }
    }

    // Handle keyboard navigation for prompt dialog
    if (this.showPromptDialog) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          this.selectedPromptIndex = Math.min(this.selectedPromptIndex + 1, this.filteredPrompts.length - 1);
          this.scrollToSelectedPrompt();
          return;
        case 'ArrowUp':
          event.preventDefault();
          this.selectedPromptIndex = Math.max(this.selectedPromptIndex - 1, 0);
          this.scrollToSelectedPrompt();
          return;
        case 'Escape':
          event.preventDefault();
          this.showPromptDialog = false;
          return;
        case 'Tab':
          if (this.filteredPrompts.length > 0) {
            event.preventDefault();
            this.selectPrompt(this.filteredPrompts[this.selectedPromptIndex]);
          }
          return;
      }
    }

    // Check if the message contains "/"
    this.checkForPrompt();

    this.adjustInputHeight();
  }

  /**
   * Checks if the user has typed '/' to trigger prompt library functionality
   */
  checkForPrompt(): void {
    const inputElement = this.chatInput?.nativeElement;
    if (!inputElement) return;

    const text = this.userInput.message || '';
    const cursorPosition = inputElement.selectionStart;

    // Find the position of / before the cursor
    let slashIndex = -1;
    for (let i = cursorPosition - 1; i >= 0; i--) {
      if (text[i] === '/') {
        slashIndex = i;
        break;
      } else if (text[i] === ' ' || text[i] === '\n') {
        // Stop searching if we hit a space or newline
        break;
      }
    }

    if (slashIndex >= 0) {
      // Extract the text between / and cursor
      const promptText = text.substring(slashIndex + 1, cursorPosition);

      // Show the prompt dialog
      if (!this.showPromptDialog) {
        this.showPromptDialog = true;
      }

      // Update search query
      this.searchPromptQuery = promptText;
      this.searchPrompts();
      this.selectedPromptIndex = 0;
    } else if (this.showPromptDialog) {
      // Hide the dialog if no / is found
      this.showPromptDialog = false;
    }
  }

  // Helper method to scroll to the selected prompt in the dropdown
  scrollToSelectedPrompt(): void {
    setTimeout(() => {
      const promptElements = document.querySelectorAll('.prompt-item');
      if (promptElements && promptElements.length > this.selectedPromptIndex) {
        const selectedElement = promptElements[this.selectedPromptIndex] as HTMLElement;
        if (selectedElement) {
          selectedElement.scrollIntoView({ block: 'nearest' });
        }
      }
    }, 0);
  }

  // Helper method to scroll to the selected agent in the mention dropdown
  scrollToSelectedMention(): void {
    setTimeout(() => {
      const mentionElements = document.querySelectorAll('.mention-item');
      if (mentionElements && mentionElements.length > this.selectedMentionIndex) {
        const selectedElement = mentionElements[this.selectedMentionIndex] as HTMLElement;
        if (selectedElement) {
          selectedElement.scrollIntoView({ block: 'nearest' });
        }
      }
    }, 0);
  }

  // Helper method to preload chat history before navigation
  private async preloadChatHistory(chatId: string): Promise<any> {
    try {
      return await this._chatService.history(chatId).toPromise();
    } catch (error) {
      console.error('Error preloading chat history:', error);
      return null;
    }
  }

  async sendMessage() {
    this.scrollToBottom();
    if (!this.userInput.message?.trim()) return;

    this.userInput.workspace = this.selectedWorkspace;

    // Reset any previous search indicators
    // this.resetWebSearchIndicator();

    // Check for @mentions in the message to extract agent name
    this.extractMentionedAgent();

    // If we have a selected agent, include it in the request
    const chatRequest: any = { ...this.userInput };

    // Add the selected agent if available
    if (this.selectedAgent) {
      chatRequest.agentName = this.selectedAgent;
      console.log('Using agent:', this.selectedAgent);
    }

    // Web search indicator is now handled at the beginning of the method

    console.log('Sending message with workspace:', this.userInput.workspace);

    let obj: ChatContinueRequestDto = new ChatContinueRequestDto();
    obj.chatMessageId = this.chatId;
    obj.message = this.userInput.message;
    obj.workspace = this.userInput.workspace;

    // Add the selected agent if available
    if (this.selectedAgent) {
      (obj as any).agentName = this.selectedAgent;
      console.log('Adding agent to request:', this.selectedAgent);
    }

    this.isMessageLoading = true;
    if (this.chatId == 0 || this.chatId == undefined) {
      try {
        // Create a temporary message object to show in the UI immediately
        let dummyObj = {
          editingMode: false,
          isEdited: false,
          isRegenerating: false,
          originalMessageId: null,
          message: this.userInput.message,
          responses: [],
        };

        // Initialize chat history with the user's message
        this.chatHistory = new ChatHistoryDto();
        this.chatHistory = {
          title: this.userInput.message,
          history: [],
        };
        this.chatHistory.history.push(dummyObj);

        // Store the original message for reference
        const originalMessage = this.userInput.message;
        // Clear the input field immediately to improve UX
        this.userInput.message = '';
        this.adjustInputHeight();

        // Send the message to the server
        const chatRequest = new ChatRequestDto({
          message: originalMessage,
          workspace: this.userInput.workspace,
          agentName: this.selectedAgent,
        });

        // Add the selected agent if available
        if (this.selectedAgent) {
          (chatRequest as any).agentName = this.selectedAgent;
          console.log('Adding agent to new chat request:', this.selectedAgent);
        }
        const res: any = await this._chatService
          .send(chatRequest)
          .toPromise();

        // Update the chat list with the new chat
        this.chatListSrvice.chatList.unshift(res);

        // Reset the grouped chats
        this.chatListSrvice.groupedChats = {
          Today: [],
          Yesterday: [],
          'Last 7 Days': [],
          'Last 30 Days': [],
          Older: [],
        };
        this.chatListSrvice.groupChatsByDate();

        // Update the chat ID in the service
        this.chatListSrvice.chatId = res.id;
        this.lastMessageId = res?.id ?? null;

        // IMPORTANT: Load the chat history BEFORE navigating
        // This ensures we have the data ready when the new route loads
        const preloadedHistory = await this.preloadChatHistory(res.id);
        if (preloadedHistory) {
          // Store the chat history in the service's BehaviorSubject
          // This is the key change - we're storing the history in a service
          // that persists across route changes
          this.chatListSrvice.updateCurrentChatHistory(preloadedHistory);
          console.log('Stored chat history in service:', preloadedHistory);
        }

        // Prepare the navigation options to prevent flickering
        const navigationExtras = {
          skipLocationChange: false,  // We want to update the URL
          replaceUrl: true,          // Replace current URL instead of adding to history
          state: {
            noAnimation: true,        // Signal no animation needed
            preloadedChat: true,      // Signal that chat data is already loaded
            chatId: res.id            // Include the chat ID in the state
          }
        };

        // Determine the target route
        if (this.hasWorkspace) {
          // For workspace chats, navigate to the chat with the new ID
          await this.router.navigate([
            this.router.url,
            res.id,
          ], navigationExtras);
        } else {
          // For regular chats, navigate to the chat route with the new ID
          await this.router.navigate(['chat', res.id], navigationExtras);
        }
      } finally {
        this.isMessageLoading = false;
        this.scrollToBottom();
      }
    } else {
      try {
        let dummyObj = {
          editingMode: false,
          isEdited: false,
          isRegenerating: false,
          originalMessageId: null,
          message: this.userInput.message,
          responses: [],
        };
        this.chatHistory.history.push(dummyObj);
        this.isMessageLoading = true;
        this.activeMessage = obj;
        this.chatInput.nativeElement.focus();

        this.userInput.message = '';
        // Log the request object for debugging
        console.log('Sending continue request with:', {
          chatId: obj.chatMessageId,
          message: obj.message,
          workspace: obj.workspace,
          agentName: (obj as any).agentName
        });

        let res = await this._chatService.continue(obj).toPromise();
        if (res) {
          // this.chatHistory.history.push(res);

          // Update the chat history with the new response
          const lastMessageIndex = this.chatHistory.history.length - 1;
          this.chatHistory.history[lastMessageIndex] = res;

          // this.chatHistory.history.push(res);

          this.lastMessageId = this.chatId;
        }

        this.nextResponse(res);
        // console.log(this.chatHistory);
      } finally {
        this.isMessageLoading = false;
        this.userInput.message = '';
        this.adjustInputHeight();
        this.scrollToBottom();
      }
    }
  }
  toggleChatHistory() {
    this.isChatHistoryOpen = !this.isChatHistoryOpen;
  }
  startRecording() {
    this.isRecording = false;

    if (this.recognition) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then(() => {
          this.setupSpeechRecognition();
          this.isRecording = true;
          this.recognition.start();
        })
        .catch((err) => {
          console.error('Microphone access denied:', err);
        });
    }
  }
  setupSpeechRecognition() {
    const SpeechRecognition =
      (window as any).SpeechRecognition ||
      (window as any).webkitSpeechRecognition;

    if (SpeechRecognition) {
      this.recognition = new SpeechRecognition();
      this.recognition.continuous = true;
      this.recognition.interimResults = true;
      this.recognition.lang = 'en-US';
      this.recognition.onresult = (event: any) => {
        this.userInput.message = '';
        for (let i = 0; i < event.results.length; i++) {
          this.userInput.message += event.results[i][0].transcript;
        }
      };
      this.recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        if (event.error === 'not-allowed') {
          alert('Please allow microphone access in your browser settings.');
        }
      };

      this.recognition.onend = () => {
        this.isRecording = false;
      };
    } else {
      console.error('Speech Recognition API is not supported in this browser.');
    }
  }
  stopRecording() {
    this.isRecording = false;
    if (this.recognition) {
      this.recognition.start();
    }
  }
  async loadChatHistory() {
    // Check if we have preloaded history in the service
    if (this.chatListSrvice.hasCurrentChatHistory() && (this.chatId || this.lastMessageId)) {
      // Use the preloaded history from the service
      console.log('Using preloaded chat history from service');
      this.chatHistory = this.chatListSrvice.getCurrentChatHistory();
      console.log('Preloaded chat history:', this.chatHistory);

      // Ensure chatHistory always has a defined history array
      if (!this.chatHistory.history) {
        this.chatHistory.history = [];
      }

      // Clear the service cache to avoid using stale data in future
      this.chatListSrvice.clearCurrentChatHistory();

      // Process the history
      this.manuplateChatHistory();

      if (this.chatHistory?.history?.length > 0) {
        // Set active message to the last message in history
        this.activeMessage =
          this.chatHistory.history[this.chatHistory.history.length - 1];
      }

      // Scroll to bottom after a short delay to ensure UI is updated
      setTimeout(() => this.scrollToBottom(), 100);
    } else {
      // Fetch the history from the server
      this._chatService
        .history(this.chatId || this.lastMessageId)
        .subscribe((res: any) => {
          this.chatHistory = res;

          // Ensure chatHistory always has a defined history array
          if (!this.chatHistory.history) {
            this.chatHistory.history = [];
          }

          this.manuplateChatHistory();

          if (this.chatHistory?.history?.length > 0) {
            // Set active message to the last message in history
            this.activeMessage =
              this.chatHistory.history[this.chatHistory.history.length - 1];
          }
          console.log(this.chatHistory);
          // this.getLastestAgentUsed();

          this.scrollToBottom();
        });
    }
  }
  manuplateChatHistory() {
    this.chatHistory.history = this.chatHistory.history.map((message: any) => {
      message.responses = message.responses || [];
      message.editingMode = false;
      // Initialize individual regenerate loading flag
      message.isRegenerating = false;
      return message;
    });
  }

  regenerateResponse(message: any) {
    // Set individual loader flag for this message
    message.isRegenerating = true;

    // Comment out agent and model logic
    /*
    let agentName;
    if (message.message[0] === '@') {
      agentName = message.message.split(' ')[0].replace('@', '');
    } else {
      agentName = 'AnswerAgent';
    }
    */

    let obj: RegenerateResponseRequestDto = new RegenerateResponseRequestDto(
      {
        chatMessageId: this.chatId,
        chatHistoryId: message.id,
        workspace: this.userInput.workspace,
        oldMessage: message.message,
        oldResponse: message.responses[message.responses.length - 1],
      });
    // Comment out agent and model as they'll be handled by backend
    /*
    obj.agentName = agentName;
    obj.modelName = this.userInput.modelName;
      */
    //   workspace: this.userInput.workspace
    // };
    // console.log(obj);

    this._chatService.regenerate(obj).subscribe((res) => {
      if (res) {
        message.responses.push(res);
        // Clear only this message's loader
        message.isRegenerating = false;
        this.lastest(message);
        console.log(res);
      }
    });
  }

  lastest(message: any) {
    const currentIndex = this.currentResponseIndexMap.get(message.id) || 0;
    console.log(message.responses.length - 1);
    console.log(message);

    if (currentIndex < message.responses.length - 1) {
      this.currentResponseIndexMap.set(
        message.id,
        message.responses.length - 1
      );
    }
  }
  editMessage(message: any) {
    // Comment out agent and model logic
    /*
    let agentName;
    if (message.message[0] == '@') {
      agentName = message.message.split(' ')[0].replace('@', '');
    } else {
      agentName = 'AnswerAgent';
    }
    */

    let msg: ChatEditRequestDto = new ChatEditRequestDto({
      chatHistoryId: message.id,
      chatMessageId: this.chatId,
      newMessage: message.message,
      workspace: this.userInput.workspace
    });
    // Comment out agent and model as they'll be handled by backend
    /*
    msg.agentName = agentName;
    msg.modelName = this.userInput.modelName;
    */
    msg.workspace = this.userInput.workspace;
    console.log(message);
    this._chatService.edit(msg).subscribe((res) => {
      this.chatHistory.history.push(res);
      message.editingMode = false;
      this.activeMessage = res;
      console.log(res);
    });
  }
  copyContent(message: any, icon: any) {
    console.log(icon);

    navigator.clipboard.writeText(message);
    setTimeout(() => {
      icon = false;
      console.log(icon);
    }, 1000);
  }
  textToSpeech(message: any) {
    this.isSpeaking = true;
    const utterance = new SpeechSynthesisUtterance(message);
    utterance.rate = 1.0;
    utterance.pitch = 1.2;
    speechSynthesis.speak(utterance);
    utterance.onend = () => {
      this.isSpeaking = false;
    };
  }
  stopSpeech() {
    ``
    this.isSpeaking = false;
    speechSynthesis.cancel();
  }
  getPaginatedResponse(message: any): any {
    const currentIndex = this.currentResponseIndexMap.get(message.id) || 0;
    const response = message.responses[currentIndex];

    return response !== undefined ? response : null;
  }

  nextResponse(message: any) {
    const currentIndex = this.currentResponseIndexMap.get(message.id) || 0;
    if (currentIndex < message.responses.length - 1) {
      this.currentResponseIndexMap.set(message.id, currentIndex + 1);
    }
  }

  previousResponse(message: any) {
    const currentIndex = this.currentResponseIndexMap.get(message.id) || 0;
    if (currentIndex > 0) {
      this.currentResponseIndexMap.set(message.id, currentIndex - 1);
    }
  }
  ngAfterViewInit() {
    this.scrollToBottom();
    this.adjustInputHeight();

    // Add event listeners for cut and paste operations
    if (this.chatInput && this.chatInput.nativeElement) {
      this.chatInput.nativeElement.addEventListener('cut', () => {
        setTimeout(() => this.checkAgentMentionRemoval(), 0);
      });

      this.chatInput.nativeElement.addEventListener('paste', () => {
        setTimeout(() => this.checkAgentMentionRemoval(), 0);
      });
    }
  }

  /**
   * Helper method to check if agent mention has been removed from the input
   */
  checkAgentMentionRemoval(): void {
    if (this.selectedAgent && this.userInput.message) {
      if (!this.userInput.message.includes('@' + this.selectedAgent)) {
        console.log('Agent mention removed via cut/paste');
        this.selectedAgent = '';
      }
    }
  }  // Time formatting moved to TimeFormatService

  adjustInputHeight(): void {
    const inputElement = this.chatInput.nativeElement;
    inputElement.style.height = 'auto';
    inputElement.style.height = inputElement.scrollHeight + 'px';
  }

  onInput(event: any) {
    this.adjustInputHeight();
    this.checkForMention();
    this.checkForPrompt();

    // Additional check for agent mention removal
    if (this.selectedAgent && this.userInput.message) {
      if (!this.userInput.message.includes('@' + this.selectedAgent)) {
        console.log('Agent mention removed from input');
        this.selectedAgent = '';
      }
    }
  }

  /**
   * Checks if the user has typed '@' to trigger agent mention functionality
   */
  checkForMention(): void {
    const inputElement = this.chatInput?.nativeElement;
    if (!inputElement) return;

    const text = this.userInput.message || '';
    const cursorPosition = inputElement.selectionStart;
    this.cursorPosition = cursorPosition;

    // Find the position of @ before the cursor
    let atIndex = -1;
    for (let i = cursorPosition - 1; i >= 0; i--) {
      if (text[i] === '@') {
        atIndex = i;
        break;
      } else if (text[i] === ' ' || text[i] === '\n') {
        // Stop searching if we hit a space or newline
        break;
      }
    }

    if (atIndex >= 0) {
      // Extract the text between @ and cursor
      const mentionText = text.substring(atIndex + 1, cursorPosition);
      this.mentionStartIndex = atIndex;
      this.mentionFilterText = mentionText;

      // If we have a selected workspace, use workspace agents
      if (this.selectedWorkspace) {
        // Load workspace agents if not already loaded
        if (this.workspaceAgents.length === 0) {
          this.loadAgentsForWorkspace(this.selectedWorkspace);
        }

        // Use workspace agents for mention filtering
        this.filterMentionedAgents(true);
      } else {
        // Fallback to all agents if no workspace is selected
        if (this.agents.length === 0) {
          this.loadAgents();
        }

        // Filter all agents
        this.filterMentionedAgents(false);
      }

      // Show the dropdown and reset selection index
      this.mentionDropdownVisible = true;
      this.selectedMentionIndex = 0;
    } else {
      // Hide the dropdown if no @ is found
      this.mentionDropdownVisible = false;
      this.mentionStartIndex = -1;
      this.selectedMentionIndex = 0;

      // Check if the user has deleted the @mention from the input
      if (this.selectedAgent) {
        // If there's no message or the message doesn't contain the @mention, clear the selected agent
        if (!this.userInput.message || !this.userInput.message.includes('@' + this.selectedAgent)) {
          console.log('Mention removed from text, clearing selected agent');
          this.selectedAgent = '';
        }
      }
    }
  }

  /**
   * Filters agents based on the text typed after @
   * @param useWorkspaceAgents If true, filters workspace agents instead of all agents
   */
  filterMentionedAgents(useWorkspaceAgents: boolean = false): void {
    // Determine which agent list to use
    const agentList = useWorkspaceAgents ? this.workspaceAgents : this.agents;

    // If no filter text, use all agents from the selected list
    if (!this.mentionFilterText) {
      this.mentionFilteredAgents = [...agentList];
      this.selectedMentionIndex = 0;
      return;
    }

    // Filter the agents based on the filter text
    const filterText = this.mentionFilterText.toLowerCase();
    this.mentionFilteredAgents = agentList.filter((agent: any) =>
      agent.agentName.toLowerCase().includes(filterText)
    );

    // Reset the selection index when the filtered list changes
    this.selectedMentionIndex = 0;

    console.log(`Filtered ${useWorkspaceAgents ? 'workspace' : 'all'} agents. Found ${this.mentionFilteredAgents.length} matches.`);
  }

  /**
   * Selects an agent from the mention dropdown
   */
  selectMentionedAgent(agent: any): void {
    if (!agent) return;

    // Get the current message text
    const text = this.userInput.message || '';

    // Replace the @mention with the selected agent name
    if (this.mentionStartIndex >= 0) {
      const beforeMention = text.substring(0, this.mentionStartIndex);
      const afterMention = text.substring(this.cursorPosition);

      // Replace with the agent name
      this.userInput.message = beforeMention + '@' + agent.agentName + ' ' + afterMention;

      // Store the selected agent
      this.selectedAgent = agent.agentName;

      // Close the dropdown
      this.mentionDropdownVisible = false;

      // Focus the input and set cursor position after the inserted agent name
      setTimeout(() => {
        const newCursorPosition = this.mentionStartIndex + agent.agentName.length + 2; // +2 for @ and space
        this.chatInput.nativeElement.focus();
        this.chatInput.nativeElement.setSelectionRange(newCursorPosition, newCursorPosition);
      }, 0);
    }
  }

  /**
   * Extracts mentioned agent from the message
   * This is called before sending the message to ensure we capture any @mentions
   */
  extractMentionedAgent(): void {
    const text = this.userInput.message || '';

    // Look for @mentions in the format @AgentName
    const mentionRegex = /@(\w+)/g;
    const mentions = text.match(mentionRegex);

    if (mentions && mentions.length > 0) {
      // Extract the first mentioned agent name (without the @ symbol)
      const firstMention = mentions[0].substring(1);

      // First check if this agent exists in the workspace agents list
      let mentionedAgent = null;

      if (this.selectedWorkspace && this.workspaceAgents.length > 0) {
        // Check workspace agents first
        mentionedAgent = this.workspaceAgents.find((agent: any) =>
          agent.agentName.toLowerCase() === firstMention.toLowerCase()
        );
      }

      // If not found in workspace agents, check all agents as fallback
      if (!mentionedAgent && this.agents.length > 0) {
        mentionedAgent = this.agents.find((agent: any) =>
          agent.agentName.toLowerCase() === firstMention.toLowerCase()
        );
      }

      if (mentionedAgent) {
        // Set the selected agent
        this.selectedAgent = mentionedAgent.agentName;
        console.log('Found mentioned agent:', this.selectedAgent);
      }
    }
  }

  /**
   * Clears the selected agent and removes any @mention from the message
   */
  clearSelectedAgent(): void {
    if (!this.selectedAgent) return;

    // Store the agent name before clearing it
    const agentName = this.selectedAgent;

    // Clear the selected agent
    this.selectedAgent = '';

    // Remove @agentName from the message if it exists
    if (this.userInput.message) {
      // Look for the pattern @agentName in the message
      const mentionPattern = new RegExp(`@${agentName}\\s*`, 'g');
      this.userInput.message = this.userInput.message.replace(mentionPattern, '');

      // Focus the input after clearing
      setTimeout(() => {
        if (this.chatInput && this.chatInput.nativeElement) {
          this.chatInput.nativeElement.focus();
        }
      }, 100);
    }

    console.log('Cleared selected agent');
  }

  /**
   * Checks if a response is a blog post
   * @param response The response text or object
   * @returns True if the response is a blog post
   */
  isBlogContent(response: string | any): boolean {
    if (!response) return false;

    // If response is an object with responseType property
    if (typeof response === 'object' && response.responseType === 'blogView') {
      return true;
    }

    // If it's a string, check if it has blog-like structure (headings, sections)
    if (typeof response === 'string') {
      // Look for markdown headings (# or ## or ###)
      const headingRegex = /^#+\s+.+$/m;
      return headingRegex.test(response);
    }

    return false;
  }

  /**
   * Extracts the title from a blog post
   * @param response The blog post content
   * @returns The blog title or a default title
   */
  extractBlogTitle(response: string): string {
    if (!response) return 'Blog Post';

    // Look for the first heading (# Heading)
    const titleRegex = /^#\s+(.+)$/m;
    const match = response.match(titleRegex);

    if (match && match[1]) {
      return match[1].trim();
    }

    return 'Blog Post';
  }

  /**
   * Copies blog content to clipboard
   * @param blogContent The blog content to copy
   */
  copyBlogContent(blogContent: string): void {
    if (!blogContent) return;

    navigator.clipboard.writeText(blogContent);
    this.message.success('Blog content copied to clipboard');
  }
  /**
   * Extracts email subject from a response
   * @param response The email response content
   * @returns The email subject or a default subject
   */
  extractEmailSubject(response: string): string {
    if (!response) return 'No Subject';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Look for the Subject line with different formats
    // Format 1: **Subject:** Text
    const subjectRegex1 = /\*\*Subject:?\*\*\s*([^\n]+)/i;
    // Format 2: Subject: Text
    const subjectRegex2 = /Subject:\s*([^\n]+)/i;

    let match = textContent.match(subjectRegex1) || textContent.match(subjectRegex2);

    if (match && match[1]) {
      return match[1].trim();
    }

    return 'No Subject';
  }

  /**
   * Extracts email recipients (To field) from a response
   * @param response The email response content
   * @returns The email recipients or empty string
   */
  extractEmailTo(response: string): string {
    if (!response) return '';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Look for the To line with different formats
    // Format 1: **To:** <EMAIL>
    const toRegex1 = /\*\*To:?\*\*\s*([^\n]+)/i;
    // Format 2: To: <EMAIL>
    const toRegex2 = /To:\s*([^\n]+)/i;

    let match = textContent.match(toRegex1) || textContent.match(toRegex2);

    if (match && match[1]) {
      return match[1].trim();
    }

    return '';
  }

  /**
   * Extracts email CC recipients from a response
   * @param response The email response content
   * @returns The CC recipients or empty string
   */
  extractEmailCc(response: string): string {
    if (!response) return '';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Look for the CC line with different formats
    // Format 1: **Cc:** <EMAIL>
    const ccRegex1 = /\*\*Cc:?\*\*\s*([^\n]+)/i;
    // Format 2: Cc: <EMAIL>
    const ccRegex2 = /Cc:\s*([^\n]+)/i;
    // Format 3: CC: <EMAIL>
    const ccRegex3 = /CC:\s*([^\n]+)/i;

    let match = textContent.match(ccRegex1) || textContent.match(ccRegex2) || textContent.match(ccRegex3);

    if (match && match[1]) {
      return match[1].trim();
    }

    return '';
  }

  /**
   * Extracts email body content from a response
   * @param response The email response content
   * @returns The email body or the full response if body can't be extracted
   */
  extractEmailBody(response: string): string {
    // console.log('Extracting email body from:', response);
    if (!response) return '';

    // First, check if response is an object or string
    let textContent = typeof response === 'string' ? response : JSON.stringify(response);

    // Step 1: If there's a markdown email structure with To and CC,
    // find the actual body (starting after CC or To line and ending before signature)
    // This more specific approach should handle most AI-generated email formats

    // Remove the intro text if present
    textContent = textContent.replace(/^Hello!.*?email:\s*\n+/i, '');

    // Look for specific email structure with To: and Cc: lines
    let bodyText = '';

    // First find the To: line and CC: line if they exist
    const toLine = textContent.match(/\*\*To:?\*\*\s*[^\n]+|To:\s*[^\n]+/i);
    const ccLine = textContent.match(/\*\*Cc:?\*\*\s*[^\n]+|Cc:\s*[^\n]+/i);

    if (toLine || ccLine) {    // Find the last header line (either To or CC, whichever comes last)
      const lastHeader = ccLine ? ccLine[0] : (toLine ? toLine[0] : '');
      const lastHeaderIndex = textContent.indexOf(lastHeader) + lastHeader.length;

      // Extract everything after the last header
      let afterHeaders = textContent.substring(lastHeaderIndex).trim();

      // If there's a blank line after headers, trim everything before it
      if (afterHeaders.startsWith('\n\n')) {
        afterHeaders = afterHeaders.substring(2).trim();
      } else if (afterHeaders.startsWith('\n')) {
        afterHeaders = afterHeaders.substring(1).trim();
      }

      // Find where the signature starts (if any)
      const signatureMatch = afterHeaders.match(/\n(?:Best,|Regards,|Sincerely,|Thanks|Thank you|Cheers|Yours truly)/i);

      if (signatureMatch) {
        // Extract everything before the signature
        bodyText = afterHeaders.substring(0, signatureMatch.index).trim();
      } else {
        // If no signature found, check for markdown closure
        const markdownEndMatch = afterHeaders.match(/\n---/i);
        if (markdownEndMatch) {
          bodyText = afterHeaders.substring(0, markdownEndMatch.index).trim();
        } else {
          bodyText = afterHeaders;
        }
      }

      return bodyText;
    }

    // If the specific extraction failed, try the standard markdown section approach
    const markdownSectionRegex = /---\s*([\s\S]+?)(?:---|\Z)/;
    const sectionMatch = textContent.match(markdownSectionRegex);

    if (sectionMatch && sectionMatch[1]) {
      // Further clean up by removing header lines
      const cleanedBody = sectionMatch[1]
        .replace(/\*\*(?:Subject|To|Cc|CC):?\*\*\s*[^\n]+\n*/gi, '')
        .replace(/(?:Subject|To|Cc|CC):\s*[^\n]+\n*/gi, '')
        .trim();

      return cleanedBody;
    }

    // If all extraction methods fail, return a basic cleaned version of the response
    return textContent
      .replace(/^Hello!.*?email:\s*\n+---\s*/i, '')
      .replace(/---\s*\n+Feel free to modify.*$/i, '')
      .replace(/\*\*(?:Subject|To|Cc|CC):?\*\*\s*[^\n]+\n*/gi, '')
      .replace(/(?:Subject|To|Cc|CC):\s*[^\n]+\n*/gi, '')
      .trim();
  }

  /**
   * Sends the email (currently just logs to console)
   * @param subject Email subject
   * @param to Email recipients
   * @param cc Email CC recipients
   * @param body Email body
   */  sendEmail(subject: string, to: string, cc: string, body: string): void {
    if (!subject || !to || !body) {
      this.message.error('Please fill in all required fields');
      return;
    }

    // Convert plain text to HTML if it doesn't already have HTML formatting
    let htmlBody = body;
    if (!body.includes('<')) {
      // Convert newlines to <br> tags for proper HTML formatting
      htmlBody = body.replace(/\n/g, '<br>');
    }

    // Create a SendEmailDto object
    const emailDto = new SendEmailDto({
      subject: subject,
      to: to,
      cc: cc ? cc.split(',').map(email => email.trim()) : [], // Convert comma-separated string to array
      htmlBody: htmlBody // Send as HTML body to preserve formatting
    });

    this.message.loading('Sending email...');

    // Call the email service to send the email
    this.emailService.sendCustomEmail(emailDto).subscribe(
      () => {
        // Show success message
        this.message.success('Email sent successfully');
      },
      (error) => {
        console.error('Error sending email:', error);
        this.message.error('Failed to send email: ' + (error.message || 'Please try again later'));
      }
    );
  }

  /**
   * Copies all email content to clipboard in a formatted way
   * @param subject Email subject
   * @param to Email recipients
   * @param cc Email CC recipients
   * @param body Email body
   */
  copyEmailContent(subject: string, to: string, cc: string, body: string): void {
    const emailContent = `Subject: ${subject}
    To: ${to}
    ${cc ? `CC: ${cc}` : ''}

    ${body}`;

    navigator.clipboard.writeText(emailContent);
    this.message.success('Email content copied to clipboard');
  }

  private extractMessageText(message: ResponseMessage) {
    return message.isError ? ' ' : message.message;
  }
  loadActiveModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((result: any) => {
      this.models = result;
      // console.log(this.models);
      this.filteredModels = result;
    });
  }

  searchModels() {
    this.filteredModels = this.models.filter((model: any) =>
      model.modelName
        .toLowerCase()
        .includes(this.searchModelQuery.toLowerCase())
    );
  }
  getModels() {
    this.selectedModel = this.auth.modelName;
    this.auth.setModel(this.selectedModel);
    this.message.success('Model Changed Successfully!');
  }
  searchAgents() {
    // Filter agents based on the search query
    this.filteredAgents = this.agents.filter((agent: any) =>
      agent.agentName
        .toLowerCase()
        .includes(this.searchAgentQuery.toLowerCase())
    );

    // Show dropdown only if there are filtered agents
    this.showAgentDropdown = this.filteredAgents.length > 0;
  }
  // getLastestAgentUsed() {
  //   // If chatId is available, return the last used agent from the chat history
  //   const lastMessage =
  //     this.chatHistory?.history?.[this.chatHistory.history.length - 1];
  //   let lastestAgentName;
  //   console.log(lastMessage);

  //   if (lastMessage && lastMessage.agentName) {
  //     lastestAgentName = lastMessage.agentName; // Return the last used agent name
  //     if (lastestAgentName) {
  //       this.userInput.agentName = lastestAgentName; // Update userInput with the last used agent name
  //       this.selectedAgent = lastestAgentName; // Update selectedAgent for display purposes
  //       console.log(`Last used agent: ${lastestAgentName}`);
  //     }
  //   }
  // }
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (this.isAgentListVisible && this.agentListDialog) {
      const clickedInsideDialog = this.agentListDialog.nativeElement.contains(
        event.target as Node
      );
      const clickedToggleButton = (event.target as HTMLElement).closest(
        '.toggle-button'
      ); // Class added to button
      if (!clickedInsideDialog && !clickedToggleButton) {
        this.isAgentListVisible = false; // Hide dialog if click is outside both dialog and button
      }
    }
    if (this.showPromptDialog && this.promptDialog) {
      const clickedInsideDialog = this.promptDialog.nativeElement.contains(event.target as Node);
      if (!clickedInsideDialog) {
        this.showPromptDialog = false;
      }
    }
  }
  toggleAgentList() {
    this.isAgentListVisible = !this.isAgentListVisible;
  }

  searchPrompts() {
    const query = this.searchPromptQuery.toLowerCase();

    if (!query) {
      // If no query, return all prompts sorted by usage count
      this.filteredPrompts = [...this.prompts].sort((a, b) => b.usageCount - a.usageCount);
      return;
    }

    // First, look for exact matches in shortMessage (shortcodes)
    const shortCodeExactMatches = this.prompts.filter((prompt: any) =>
      prompt.shortMessage && prompt.shortMessage.toLowerCase() === query
    );

    // Then, look for partial matches in shortMessage
    const shortCodePartialMatches = this.prompts.filter((prompt: any) =>
      prompt.shortMessage &&
      prompt.shortMessage.toLowerCase().includes(query) &&
      prompt.shortMessage.toLowerCase() !== query // Exclude exact matches
    );

    // Finally, look for matches in the prompt text
    const promptTextMatches = this.prompts.filter((prompt: any) =>
      prompt.prompt.toLowerCase().includes(query) &&
      !(prompt.shortMessage && (
        prompt.shortMessage.toLowerCase() === query ||
        prompt.shortMessage.toLowerCase().includes(query)
      )) // Exclude any shortMessage matches
    );

    // Combine all matches with priority order
    this.filteredPrompts = [
      ...shortCodeExactMatches,
      ...shortCodePartialMatches,
      ...promptTextMatches
    ];

    // If no matches found, show a message or empty list
    if (this.filteredPrompts.length === 0) {
      console.log('No matching prompts found for query:', query);
    }
  }

  selectPrompt(prompt: any) {
    // Replace the slash command with the selected prompt
    if (this.userInput.message && this.userInput.message.startsWith('/')) {
      this.userInput.message = prompt.prompt + ' ';
    } else {
      // If somehow the prompt dialog is open but the message doesn't start with /
      this.userInput.message = prompt.prompt + ' ';
    }

    // Close the dialog and reset state
    this.showPromptDialog = false;
    this.searchPromptQuery = '';
    this.selectedPromptIndex = 0;

    // Focus the chat input and adjust height
    setTimeout(() => {
      this.chatInput.nativeElement.focus();
      this.adjustInputHeight();
    }, 0);

    // Increment usage count
    this.promptUsedCount(prompt);
  }

  /**
   * Opens the prompt library dialog
   */
  openPromptDialog() {
    console.log('openPromptDialog');

    // Make sure prompts are loaded
    if (this.prompts.length === 0) {
      this.loadPromptLibrary();
    }

    this.showPromptDialog = true;
    this.searchPromptQuery = '';
    this.filteredPrompts = this.prompts;
    this.selectedPromptIndex = 0;

    setTimeout(() => {
      this.chatInput.nativeElement.focus();
    }, 0);
  }

  /**
   * Toggles the search results sidebar visibility and populates it with data
   * @param chatSourceDescriptions Optional array of source descriptions to display
   * @param sourceName Optional name of the source (e.g., "Google", "Bing")
   */
  toggleSearchResultsSidebar(chatSourceDescriptions?: any[], sourceName: string = ''): void {
    console.log('Toggling search results sidebar. Current state:', this.showSearchResultsSidebar);

    // If we have chat source descriptions, use them
    if (chatSourceDescriptions && chatSourceDescriptions.length > 0) {
      console.log('Using provided chat source descriptions:', chatSourceDescriptions);

      // Store the source name
      this.currentSourceName = sourceName;

      // Format the search results
      this.searchResults = chatSourceDescriptions.map(desc => ({
        title: desc.title || 'No Title',
        url: desc.url || '',
        domain: desc.url,
        description: desc.description || 'No description available'
      }));

      // Make sure the sidebar is open
      this.showSearchResultsSidebar = true;

      // Close agent sidebar if it's open
      if (this.isAgentSidebarOpen) {
        this.isAgentSidebarOpen = false;
      }
    } else {
      // If no descriptions provided, toggle the sidebar visibility
      this.showSearchResultsSidebar = !this.showSearchResultsSidebar;
    }

    // Update split sizes based on sidebar visibility
    this.updateSplitSizes();

    // Add a small delay to allow the DOM to update before any animations
    setTimeout(() => {
      // Force a layout recalculation to ensure smooth transitions
      window.dispatchEvent(new Event('resize'));
    }, 10);
  }

  /**
   * Updates the split sizes based on sidebar visibility
   */
  private updateSplitSizes(): void {
    if (this.showSearchResultsSidebar || this.isAgentSidebarOpen) {
      // Calculate the percentage for the right sidebar
      const windowWidth = window.innerWidth;
      // Ensure the sidebar takes at least 20% of the window width for better visibility
      const minPercentage = 20;
      const calculatedPercentage = (this.rightSidebarWidth / windowWidth) * 100;
      this.rightSidebarSplitSize = Math.max(calculatedPercentage, minPercentage);
      this.mainContentSplitSize = 100 - this.rightSidebarSplitSize;
    } else {
      // If no sidebar is visible, set main content to 100%
      this.mainContentSplitSize = 100;
      this.rightSidebarSplitSize = 0;
    }
  }

  /**
   * Loads the saved width from localStorage with validation
   */
  private loadSavedRightSidebarWidth(): void {
    try {
      const savedWidth = localStorage.getItem(this.RIGHT_SIDEBAR_STORAGE_KEY);
      if (savedWidth) {
        const parsedWidth = parseInt(savedWidth, 10);
        // Validate the saved width to ensure it's within acceptable range
        if (!isNaN(parsedWidth) && parsedWidth >= this.minRightSidebarWidth && parsedWidth <= this.maxRightSidebarWidth) {
          this.rightSidebarWidth = parsedWidth;
          this.lastValidWidth = parsedWidth; // Store as last valid width
        } else {
          // If saved width is invalid, use the default width
          this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;
          // Update localStorage with the default width
          this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);
        }
      } else {
        // If no saved width, use the default width
        this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;
        // Save the default width to localStorage
        this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);
      }

      // Update split sizes based on sidebar visibility
      this.updateSplitSizes();
    } catch (error) {
      console.error('Error loading saved width:', error);
      this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;
      this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);
    }
  }

  /**
   * Saves the width to localStorage
   * @param width The width to save
   */
  private saveRightSidebarWidth(width: number): void {
    try {
      localStorage.setItem(this.RIGHT_SIDEBAR_STORAGE_KEY, width.toString());
    } catch (error) {
      console.error('Error saving width to localStorage:', error);
    }
  }

  /**
   * Opens a search result URL in a new tab
   * @param url The URL to open
   */
  openSearchResult(url: string): void {
    window.open(url, '_blank');
  }

  /**
   * Handles the drag end event from the splitter



   * @param event The drag end event
   */
  onSplitDragEnd(event: any): void {
    this.isDragging = false;

    try {
      // Get the new width from the event (right area size)
      const rightAreaPercentage = event.sizes[1];

      // Convert percentage to pixels (assuming container width is window width)
      const containerWidth = window.innerWidth;
      const newWidthPx = Math.round((rightAreaPercentage / 100) * containerWidth);

      // Check if width is below auto-close threshold
      if (newWidthPx < this.autoCloseThreshold) {
        // Store the last valid width before closing
        this.lastValidWidth = this.rightSidebarWidth > this.autoCloseThreshold ?
          this.rightSidebarWidth : this.DEFAULT_RIGHT_SIDEBAR_WIDTH;

        // Close both sidebars
        this.showSearchResultsSidebar = false;
        this.isAgentSidebarOpen = false;

        // Update split sizes
        this.updateSplitSizes();
      } else {
        // Save the new width (rounded to nearest integer)
        // No need to cap at maximum width since we want it to expand fully
        this.rightSidebarWidth = newWidthPx;
        this.saveRightSidebarWidth(this.rightSidebarWidth);
        this.lastValidWidth = this.rightSidebarWidth;

        // Ensure the width is at least the minimum
        if (newWidthPx < this.minRightSidebarWidth) {
          this.rightSidebarWidth = this.minRightSidebarWidth;
          this.saveRightSidebarWidth(this.rightSidebarWidth);
          this.lastValidWidth = this.rightSidebarWidth;
        }
      }

      // Force a layout recalculation
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 10);
    } catch (error) {
      console.error('Error in onSplitDragEnd:', error);
      // Recover using last valid width
      this.rightSidebarWidth = this.lastValidWidth;
    }
  }

  /**
   * Handles the drag progress event from the splitter
   * @param _ The drag progress event (unused)
   */
  onSplitDragProgress(_: any): void {
    this.isDragging = true;
  }

  /**
   * Handles double-click on the gutter
   * @param _ The double-click event (unused)
   */
  onGutterDoubleClick(_: any): void {
    // Toggle between default and max width
    if (this.rightSidebarWidth < this.maxRightSidebarWidth - 50) {
      this.rightSidebarWidth = this.maxRightSidebarWidth;
      this.lastValidWidth = this.maxRightSidebarWidth;
    } else {
      this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Reset to default width
      this.lastValidWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;
    }

    // Save the new width
    this.saveRightSidebarWidth(this.rightSidebarWidth);

    // Update split sizes
    this.updateSplitSizes();

    // Force a layout recalculation
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 10);
  }

  /**
   * Gets the current window width
   * @returns The window width in pixels
   */
  getWindowWidth(): number {
    return window.innerWidth;
  }

  /**
   * Listen for window resize events to recalculate sidebar width percentages
   */
  @HostListener('window:resize')
  onResize(): void {
    // Only update if a sidebar is visible
    if (this.showSearchResultsSidebar || this.isAgentSidebarOpen) {
      // Ensure the sidebar width stays within bounds after resize
      if (this.rightSidebarWidth > this.maxRightSidebarWidth) {
        this.rightSidebarWidth = this.maxRightSidebarWidth;
      } else if (this.rightSidebarWidth < this.minRightSidebarWidth && this.rightSidebarWidth > this.autoCloseThreshold) {
        this.rightSidebarWidth = this.minRightSidebarWidth;
      }

      // Update split sizes
      this.updateSplitSizes();
    }
  }

  ngOnDestroy(): void {
    // Unsubscribe from SignalR messages
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
    }

    // Clean up speech recognition
    if (this.recognition) {
      this.recognition.stop();
    }

    // Clean up speech synthesis
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
  }

  /**
   * Extracts SQL content from a response
   * @param response The response text
   * @returns The SQL code
   */
  extractSqlContent(response: string): string {
    if (!response) return '';

    // Look for SQL code blocks in markdown format
    const sqlRegex = /```sql\s*([\s\S]*?)\s*```/i;
    const match = response.match(sqlRegex);

    if (match && match[1]) {
      return match[1].trim();
    }

    return '';
  }

  /**
   * Checks if a response contains SQL code
   * @param response The response text
   * @returns True if the response contains SQL code
   */
  hasSqlContent(response: string): boolean {
    if (!response) return false;

    // Check if the response contains SQL code blocks
    const sqlRegex = /```sql\s*([\s\S]*?)\s*```/i;
    const match = response.match(sqlRegex);

    return !!(match && match[1] && match[1].trim());
  }

  /**
   * Extracts non-SQL content from a response
   * @param response The response text
   * @returns The response without SQL code blocks
   */
  extractNonSqlContent(response: string): string {
    if (!response) return '';

    // Replace SQL code blocks with a placeholder
    return response.replace(/```sql\s*([\s\S]*?)\s*```/gi, '');
  }

  /**
   * Copies SQL content to clipboard
   * @param sqlContent The SQL content to copy
   */
  copySqlContent(sqlContent: string): void {
    if (!sqlContent) return;

    navigator.clipboard.writeText(sqlContent);
    this.message.success('SQL query copied to clipboard');
  }

  /**
   * Opens the SQL connection dialog when the "Run SQL" button is clicked
   * @param sqlContent The SQL content to show in the dialog
   */
  openSqlConnectionDialog(sqlContent: string): void {
    if (!sqlContent) {
      this.message.error('No SQL content to execute');
      return;
    }

    // Create a modal with our SQL connection dialog component
    const modalRef = this.modal.create({
      nzTitle: '',
      nzContent: SqlConnectionDialogComponent,
      nzFooter: null,
      nzWidth: '600px',
      nzClassName: 'sql-connection-modal',
      nzMaskClosable: false,
      nzData: {
        sql: sqlContent // Pass the SQL content to the dialog
      }
    });
  }

  /**
   * Opens the blog share dialog when the "Share" button is clicked
   * @param blogContent The blog content to share
   */
  openBlogShareDialog(blogContent: string): void {
    if (!blogContent) {
      this.message.error('No blog content to share');
      return;
    }

    // Create a modal with our blog share dialog component
    const modalRef = this.modal.create({
      nzTitle: '',
      nzContent: BlogShareDialogComponent,
      nzFooter: null,
      nzWidth: '800px',
      nzClassName: 'blog-share-modal',
      nzMaskClosable: false,
      nzData: {
        blogContent: blogContent // Pass the blog content to the dialog
      }
    });

    // Handle the result when the dialog is closed
    modalRef.afterClose.subscribe((result) => {
      if (result) {
        console.log('Blog shared successfully:', result);
        // The success message is already shown in the dialog component
      }
    });
  }


}

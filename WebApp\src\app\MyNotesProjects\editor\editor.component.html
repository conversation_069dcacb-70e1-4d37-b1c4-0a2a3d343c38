<!-- Middle Editor with Full Height -->
<div class="flex flex-col h-[calc(100vh-74px)] bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] overflow-hidden" [class.hidden]="isToogled">
  <!-- Editor Header -->
  <div class="border-b border-[var(--hover-blue-gray)] px-[var(--padding-small)]">
    <div class="flex items-center justify-between w-full p-3 gap-4">
      @if(isjournal) {
      <input type="text" readonly [(ngModel)]="noteTitle"
             class="w-full border-0 border-b border-[var(--hover-blue-gray)] text-xl font-[var(--font-weight-medium)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)] focus:outline-none focus:ring-0 bg-transparent"
             placeholder="Enter note title...">
      } @else {
      <input type="text" [(ngModel)]="noteTitle"
             class="w-full border-0 border-b border-[var(--hover-blue-gray)] text-xl font-[var(--font-weight-medium)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)] focus:outline-none focus:ring-0 bg-transparent"
             placeholder="Enter note title...">
      }

      <div class="flex items-center mx-2">
        <button (click)="toggleFavorite()"
                class="p-2 rounded-full border-none transition-[var(--transition-default)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50"
                [ngClass]="{
                  'text-yellow-400 bg-yellow-50': isFavourite,
                  'text-[var(--text-medium-gray)] bg-[var(--hover-blue-gray)]': !isFavourite,
                  'hover:bg-yellow-100': isFavourite,
                  'hover:bg-[var(--background-light-gray)]': !isFavourite
                }"
                [title]="isFavourite ? 'Remove from favorites' : 'Add to favorites'">
          <i [ngClass]="isFavourite ? 'ri-star-fill' : 'ri-star-line'" class="text-current text-lg"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Editor Content - Expanded to fill available space -->
  <div id="editor" class="flex-grow overflow-auto p-[var(--padding-small)] text-[var(--text-dark)]"></div>

  <!-- Editor Actions -->
  <div class="flex justify-center gap-3 p-3 border-t border-[var(--hover-blue-gray)]">
    <button (click)="clearContent()"
            class="flex items-center gap-2 px-4 py-2 border border-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50 transition-[var(--transition-default)]">
      <i class="ri-eraser-line"></i>
      Clear
    </button>
    <button (click)="saveContent()"
            class="flex items-center gap-2 px-4 py-2 bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-black transition-[var(--transition-default)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50">
      <i class="ri-save-line"></i>
      Save
    </button>
  </div>
</div>

{"version": 3, "sources": ["../../../../../node_modules/@editorjs/image/dist/image.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var o=document.createElement(\"style\");o.appendChild(document.createTextNode('.image-tool{--bg-color: #cdd1e0;--front-color: #388ae5;--border-color: #e8e8eb}.image-tool__image{border-radius:3px;overflow:hidden;margin-bottom:10px;padding-bottom:0}.image-tool__image-picture{max-width:100%;vertical-align:bottom;display:block}.image-tool__image-preloader{width:50px;height:50px;border-radius:50%;background-size:cover;margin:auto;position:relative;background-color:var(--bg-color);background-position:center center}.image-tool__image-preloader:after{content:\"\";position:absolute;z-index:3;width:60px;height:60px;border-radius:50%;border:2px solid var(--bg-color);border-top-color:var(--front-color);left:50%;top:50%;margin-top:-30px;margin-left:-30px;animation:image-preloader-spin 2s infinite linear;box-sizing:border-box}.image-tool__caption{visibility:hidden;position:absolute;bottom:0;left:0;margin-bottom:10px}.image-tool__caption[contentEditable=true][data-placeholder]:before{position:absolute!important;content:attr(data-placeholder);color:#707684;font-weight:400;display:none}.image-tool__caption[contentEditable=true][data-placeholder]:empty:before{display:block}.image-tool__caption[contentEditable=true][data-placeholder]:empty:focus:before{display:none}.image-tool--empty .image-tool__image,.image-tool--empty .image-tool__image-preloader{display:none}.image-tool--empty .image-tool__caption,.image-tool--uploading .image-tool__caption{visibility:hidden!important}.image-tool .cdx-button{display:flex;align-items:center;justify-content:center}.image-tool .cdx-button svg{height:auto;margin:0 6px 0 0}.image-tool--filled .cdx-button,.image-tool--filled .image-tool__image-preloader{display:none}.image-tool--uploading .image-tool__image{min-height:200px;display:flex;border:1px solid var(--border-color);background-color:#fff}.image-tool--uploading .image-tool__image-picture,.image-tool--uploading .cdx-button{display:none}.image-tool--withBorder .image-tool__image{border:1px solid var(--border-color)}.image-tool--withBackground .image-tool__image{padding:15px;background:var(--bg-color)}.image-tool--withBackground .image-tool__image-picture{max-width:60%;margin:0 auto}.image-tool--stretched .image-tool__image-picture{width:100%}.image-tool--caption .image-tool__caption{visibility:visible}.image-tool--caption{padding-bottom:50px}@keyframes image-preloader-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}')),document.head.appendChild(o)}}catch(e){console.error(\"vite-plugin-css-injected-by-js\",e)}})();\nconst R = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 19V19C9.13623 19 8.20435 19 7.46927 18.6955C6.48915 18.2895 5.71046 17.5108 5.30448 16.5307C5 15.7956 5 14.8638 5 13V12C5 9.19108 5 7.78661 5.67412 6.77772C5.96596 6.34096 6.34096 5.96596 6.77772 5.67412C7.78661 5 9.19108 5 12 5H13.5C14.8956 5 15.5933 5 16.1611 5.17224C17.4395 5.56004 18.44 6.56046 18.8278 7.83886C19 8.40666 19 9.10444 19 10.5V10.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 13V16M16 19V16M19 16H16M16 16H13\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6.5 17.5L17.5 6.5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.9919 10.5H19.0015\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.9919 19H11.0015\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13L13 5\"/></svg>', I = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.9919 9.5H19.0015\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.5 5H14.5096\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M14.625 5H15C17.2091 5 19 6.79086 19 9V9.375\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M9.375 5L9 5C6.79086 5 5 6.79086 5 9V9.375\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.3725 5H9.38207\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 9.5H5.00957\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M9.375 19H9C6.79086 19 5 17.2091 5 15V14.625\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.3725 19H9.38207\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 14.55H5.00957\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 13V16M16 19V16M19 16H16M16 16H13\"/></svg>', L = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5.13968 15.32L8.69058 11.5661C9.02934 11.2036 9.48873 11 9.96774 11C10.4467 11 10.9061 11.2036 11.2449 11.5661L15.3871 16M13.5806 14.0664L15.0132 12.533C15.3519 12.1705 15.8113 11.9668 16.2903 11.9668C16.7693 11.9668 17.2287 12.1705 17.5675 12.533L18.841 13.9634\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.7778 9.33331H13.7867\"/></svg>', x = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L20 12L17 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 12H20\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L4 12L7 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 12H10\"/></svg>', B = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14\"/></svg>';\nfunction S(C, a = null, i = {}) {\n  const s = document.createElement(C);\n  Array.isArray(a) ? s.classList.add(...a) : a !== null && s.classList.add(a);\n  for (const r in i)\n    i.hasOwnProperty(r) && (s[r] = i[r]);\n  return s;\n}\nvar _ = /* @__PURE__ */ ((C) => (C.Empty = \"empty\", C.Uploading = \"uploading\", C.Filled = \"filled\", C))(_ || {});\nclass D {\n  /**\n   * @param ui - image tool Ui module\n   * @param ui.api - Editor.js API\n   * @param ui.config - user config\n   * @param ui.onSelectFile - callback for clicks on Select file button\n   * @param ui.readOnly - read-only mode flag\n   */\n  constructor({ api: a, config: i, onSelectFile: s, readOnly: r }) {\n    this.api = a, this.config = i, this.onSelectFile = s, this.readOnly = r, this.nodes = {\n      wrapper: S(\"div\", [this.CSS.baseClass, this.CSS.wrapper]),\n      imageContainer: S(\"div\", [this.CSS.imageContainer]),\n      fileButton: this.createFileButton(),\n      imageEl: void 0,\n      imagePreloader: S(\"div\", this.CSS.imagePreloader),\n      caption: S(\"div\", [this.CSS.input, this.CSS.caption], {\n        contentEditable: !this.readOnly\n      })\n    }, this.nodes.caption.dataset.placeholder = this.config.captionPlaceholder, this.nodes.imageContainer.appendChild(this.nodes.imagePreloader), this.nodes.wrapper.appendChild(this.nodes.imageContainer), this.nodes.wrapper.appendChild(this.nodes.caption), this.nodes.wrapper.appendChild(this.nodes.fileButton);\n  }\n  /**\n   * Apply visual representation of activated tune\n   * @param tuneName - one of available tunes {@link Tunes.tunes}\n   * @param status - true for enable, false for disable\n   */\n  applyTune(a, i) {\n    this.nodes.wrapper.classList.toggle(`${this.CSS.wrapper}--${a}`, i);\n  }\n  /**\n   * Renders tool UI\n   */\n  render() {\n    return this.toggleStatus(\n      \"empty\"\n      /* Empty */\n    ), this.nodes.wrapper;\n  }\n  /**\n   * Shows uploading preloader\n   * @param src - preview source\n   */\n  showPreloader(a) {\n    this.nodes.imagePreloader.style.backgroundImage = `url(${a})`, this.toggleStatus(\n      \"uploading\"\n      /* Uploading */\n    );\n  }\n  /**\n   * Hide uploading preloader\n   */\n  hidePreloader() {\n    this.nodes.imagePreloader.style.backgroundImage = \"\", this.toggleStatus(\n      \"empty\"\n      /* Empty */\n    );\n  }\n  /**\n   * Shows an image\n   * @param url - image source\n   */\n  fillImage(a) {\n    const i = /\\.mp4$/.test(a) ? \"VIDEO\" : \"IMG\", s = {\n      src: a\n    };\n    let r = \"load\";\n    i === \"VIDEO\" && (s.autoplay = !0, s.loop = !0, s.muted = !0, s.playsinline = !0, r = \"loadeddata\"), this.nodes.imageEl = S(i, this.CSS.imageEl, s), this.nodes.imageEl.addEventListener(r, () => {\n      this.toggleStatus(\n        \"filled\"\n        /* Filled */\n      ), this.nodes.imagePreloader !== void 0 && (this.nodes.imagePreloader.style.backgroundImage = \"\");\n    }), this.nodes.imageContainer.appendChild(this.nodes.imageEl);\n  }\n  /**\n   * Shows caption input\n   * @param text - caption content text\n   */\n  fillCaption(a) {\n    this.nodes.caption !== void 0 && (this.nodes.caption.innerHTML = a);\n  }\n  /**\n   * Changes UI status\n   * @param status - see {@link Ui.status} constants\n   */\n  toggleStatus(a) {\n    for (const i in _)\n      if (Object.prototype.hasOwnProperty.call(_, i)) {\n        const s = _[i];\n        this.nodes.wrapper.classList.toggle(`${this.CSS.wrapper}--${s}`, s === a);\n      }\n  }\n  /**\n   * CSS classes\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      loading: this.api.styles.loader,\n      input: this.api.styles.input,\n      button: this.api.styles.button,\n      /**\n       * Tool's classes\n       */\n      wrapper: \"image-tool\",\n      imageContainer: \"image-tool__image\",\n      imagePreloader: \"image-tool__image-preloader\",\n      imageEl: \"image-tool__image-picture\",\n      caption: \"image-tool__caption\"\n    };\n  }\n  /**\n   * Creates upload-file button\n   */\n  createFileButton() {\n    const a = S(\"div\", [this.CSS.button]);\n    return a.innerHTML = this.config.buttonContent ?? `${L} ${this.api.i18n.t(\"Select an Image\")}`, a.addEventListener(\"click\", () => {\n      this.onSelectFile();\n    }), a;\n  }\n}\nfunction U(C) {\n  return C && C.__esModule && Object.prototype.hasOwnProperty.call(C, \"default\") ? C.default : C;\n}\nvar H = { exports: {} };\n(function(C, a) {\n  (function(i, s) {\n    C.exports = s();\n  })(window, function() {\n    return function(i) {\n      var s = {};\n      function r(o) {\n        if (s[o]) return s[o].exports;\n        var e = s[o] = { i: o, l: !1, exports: {} };\n        return i[o].call(e.exports, e, e.exports, r), e.l = !0, e.exports;\n      }\n      return r.m = i, r.c = s, r.d = function(o, e, d) {\n        r.o(o, e) || Object.defineProperty(o, e, { enumerable: !0, get: d });\n      }, r.r = function(o) {\n        typeof Symbol < \"u\" && Symbol.toStringTag && Object.defineProperty(o, Symbol.toStringTag, { value: \"Module\" }), Object.defineProperty(o, \"__esModule\", { value: !0 });\n      }, r.t = function(o, e) {\n        if (1 & e && (o = r(o)), 8 & e || 4 & e && typeof o == \"object\" && o && o.__esModule) return o;\n        var d = /* @__PURE__ */ Object.create(null);\n        if (r.r(d), Object.defineProperty(d, \"default\", { enumerable: !0, value: o }), 2 & e && typeof o != \"string\") for (var v in o) r.d(d, v, (function(l) {\n          return o[l];\n        }).bind(null, v));\n        return d;\n      }, r.n = function(o) {\n        var e = o && o.__esModule ? function() {\n          return o.default;\n        } : function() {\n          return o;\n        };\n        return r.d(e, \"a\", e), e;\n      }, r.o = function(o, e) {\n        return Object.prototype.hasOwnProperty.call(o, e);\n      }, r.p = \"\", r(r.s = 3);\n    }([function(i, s) {\n      var r;\n      r = /* @__PURE__ */ function() {\n        return this;\n      }();\n      try {\n        r = r || new Function(\"return this\")();\n      } catch {\n        typeof window == \"object\" && (r = window);\n      }\n      i.exports = r;\n    }, function(i, s, r) {\n      (function(o) {\n        var e = r(2), d = setTimeout;\n        function v() {\n        }\n        function l(n) {\n          if (!(this instanceof l)) throw new TypeError(\"Promises must be constructed via new\");\n          if (typeof n != \"function\") throw new TypeError(\"not a function\");\n          this._state = 0, this._handled = !1, this._value = void 0, this._deferreds = [], t(n, this);\n        }\n        function f(n, c) {\n          for (; n._state === 3; ) n = n._value;\n          n._state !== 0 ? (n._handled = !0, l._immediateFn(function() {\n            var u = n._state === 1 ? c.onFulfilled : c.onRejected;\n            if (u !== null) {\n              var g;\n              try {\n                g = u(n._value);\n              } catch (m) {\n                return void y(c.promise, m);\n              }\n              p(c.promise, g);\n            } else (n._state === 1 ? p : y)(c.promise, n._value);\n          })) : n._deferreds.push(c);\n        }\n        function p(n, c) {\n          try {\n            if (c === n) throw new TypeError(\"A promise cannot be resolved with itself.\");\n            if (c && (typeof c == \"object\" || typeof c == \"function\")) {\n              var u = c.then;\n              if (c instanceof l) return n._state = 3, n._value = c, void w(n);\n              if (typeof u == \"function\") return void t((g = u, m = c, function() {\n                g.apply(m, arguments);\n              }), n);\n            }\n            n._state = 1, n._value = c, w(n);\n          } catch (h) {\n            y(n, h);\n          }\n          var g, m;\n        }\n        function y(n, c) {\n          n._state = 2, n._value = c, w(n);\n        }\n        function w(n) {\n          n._state === 2 && n._deferreds.length === 0 && l._immediateFn(function() {\n            n._handled || l._unhandledRejectionFn(n._value);\n          });\n          for (var c = 0, u = n._deferreds.length; c < u; c++) f(n, n._deferreds[c]);\n          n._deferreds = null;\n        }\n        function b(n, c, u) {\n          this.onFulfilled = typeof n == \"function\" ? n : null, this.onRejected = typeof c == \"function\" ? c : null, this.promise = u;\n        }\n        function t(n, c) {\n          var u = !1;\n          try {\n            n(function(g) {\n              u || (u = !0, p(c, g));\n            }, function(g) {\n              u || (u = !0, y(c, g));\n            });\n          } catch (g) {\n            if (u) return;\n            u = !0, y(c, g);\n          }\n        }\n        l.prototype.catch = function(n) {\n          return this.then(null, n);\n        }, l.prototype.then = function(n, c) {\n          var u = new this.constructor(v);\n          return f(this, new b(n, c, u)), u;\n        }, l.prototype.finally = e.a, l.all = function(n) {\n          return new l(function(c, u) {\n            if (!n || n.length === void 0) throw new TypeError(\"Promise.all accepts an array\");\n            var g = Array.prototype.slice.call(n);\n            if (g.length === 0) return c([]);\n            var m = g.length;\n            function h(T, E) {\n              try {\n                if (E && (typeof E == \"object\" || typeof E == \"function\")) {\n                  var M = E.then;\n                  if (typeof M == \"function\") return void M.call(E, function(F) {\n                    h(T, F);\n                  }, u);\n                }\n                g[T] = E, --m == 0 && c(g);\n              } catch (F) {\n                u(F);\n              }\n            }\n            for (var k = 0; k < g.length; k++) h(k, g[k]);\n          });\n        }, l.resolve = function(n) {\n          return n && typeof n == \"object\" && n.constructor === l ? n : new l(function(c) {\n            c(n);\n          });\n        }, l.reject = function(n) {\n          return new l(function(c, u) {\n            u(n);\n          });\n        }, l.race = function(n) {\n          return new l(function(c, u) {\n            for (var g = 0, m = n.length; g < m; g++) n[g].then(c, u);\n          });\n        }, l._immediateFn = typeof o == \"function\" && function(n) {\n          o(n);\n        } || function(n) {\n          d(n, 0);\n        }, l._unhandledRejectionFn = function(n) {\n          typeof console < \"u\" && console && console.warn(\"Possible Unhandled Promise Rejection:\", n);\n        }, s.a = l;\n      }).call(this, r(5).setImmediate);\n    }, function(i, s, r) {\n      s.a = function(o) {\n        var e = this.constructor;\n        return this.then(function(d) {\n          return e.resolve(o()).then(function() {\n            return d;\n          });\n        }, function(d) {\n          return e.resolve(o()).then(function() {\n            return e.reject(d);\n          });\n        });\n      };\n    }, function(i, s, r) {\n      function o(t) {\n        return (o = typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? function(n) {\n          return typeof n;\n        } : function(n) {\n          return n && typeof Symbol == \"function\" && n.constructor === Symbol && n !== Symbol.prototype ? \"symbol\" : typeof n;\n        })(t);\n      }\n      r(4);\n      var e, d, v, l, f, p, y, w = r(8), b = (d = function(t) {\n        return new Promise(function(n, c) {\n          t = l(t), (t = f(t)).beforeSend && t.beforeSend();\n          var u = window.XMLHttpRequest ? new window.XMLHttpRequest() : new window.ActiveXObject(\"Microsoft.XMLHTTP\");\n          u.open(t.method, t.url), u.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\"), Object.keys(t.headers).forEach(function(m) {\n            var h = t.headers[m];\n            u.setRequestHeader(m, h);\n          });\n          var g = t.ratio;\n          u.upload.addEventListener(\"progress\", function(m) {\n            var h = Math.round(m.loaded / m.total * 100), k = Math.ceil(h * g / 100);\n            t.progress(Math.min(k, 100));\n          }, !1), u.addEventListener(\"progress\", function(m) {\n            var h = Math.round(m.loaded / m.total * 100), k = Math.ceil(h * (100 - g) / 100) + g;\n            t.progress(Math.min(k, 100));\n          }, !1), u.onreadystatechange = function() {\n            if (u.readyState === 4) {\n              var m = u.response;\n              try {\n                m = JSON.parse(m);\n              } catch {\n              }\n              var h = w.parseHeaders(u.getAllResponseHeaders()), k = { body: m, code: u.status, headers: h };\n              y(u.status) ? n(k) : c(k);\n            }\n          }, u.send(t.data);\n        });\n      }, v = function(t) {\n        return t.method = \"POST\", d(t);\n      }, l = function() {\n        var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        if (t.url && typeof t.url != \"string\") throw new Error(\"Url must be a string\");\n        if (t.url = t.url || \"\", t.method && typeof t.method != \"string\") throw new Error(\"`method` must be a string or null\");\n        if (t.method = t.method ? t.method.toUpperCase() : \"GET\", t.headers && o(t.headers) !== \"object\") throw new Error(\"`headers` must be an object or null\");\n        if (t.headers = t.headers || {}, t.type && (typeof t.type != \"string\" || !Object.values(e).includes(t.type))) throw new Error(\"`type` must be taken from module's «contentType» library\");\n        if (t.progress && typeof t.progress != \"function\") throw new Error(\"`progress` must be a function or null\");\n        if (t.progress = t.progress || function(n) {\n        }, t.beforeSend = t.beforeSend || function(n) {\n        }, t.ratio && typeof t.ratio != \"number\") throw new Error(\"`ratio` must be a number\");\n        if (t.ratio < 0 || t.ratio > 100) throw new Error(\"`ratio` must be in a 0-100 interval\");\n        if (t.ratio = t.ratio || 90, t.accept && typeof t.accept != \"string\") throw new Error(\"`accept` must be a string with a list of allowed mime-types\");\n        if (t.accept = t.accept || \"*/*\", t.multiple && typeof t.multiple != \"boolean\") throw new Error(\"`multiple` must be a true or false\");\n        if (t.multiple = t.multiple || !1, t.fieldName && typeof t.fieldName != \"string\") throw new Error(\"`fieldName` must be a string\");\n        return t.fieldName = t.fieldName || \"files\", t;\n      }, f = function(t) {\n        switch (t.method) {\n          case \"GET\":\n            var n = p(t.data, e.URLENCODED);\n            delete t.data, t.url = /\\?/.test(t.url) ? t.url + \"&\" + n : t.url + \"?\" + n;\n            break;\n          case \"POST\":\n          case \"PUT\":\n          case \"DELETE\":\n          case \"UPDATE\":\n            var c = function() {\n              return (arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}).type || e.JSON;\n            }(t);\n            (w.isFormData(t.data) || w.isFormElement(t.data)) && (c = e.FORM), t.data = p(t.data, c), c !== b.contentType.FORM && (t.headers[\"content-type\"] = c);\n        }\n        return t;\n      }, p = function() {\n        var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        switch (arguments.length > 1 ? arguments[1] : void 0) {\n          case e.URLENCODED:\n            return w.urlEncode(t);\n          case e.JSON:\n            return w.jsonEncode(t);\n          case e.FORM:\n            return w.formEncode(t);\n          default:\n            return t;\n        }\n      }, y = function(t) {\n        return t >= 200 && t < 300;\n      }, { contentType: e = { URLENCODED: \"application/x-www-form-urlencoded; charset=utf-8\", FORM: \"multipart/form-data\", JSON: \"application/json; charset=utf-8\" }, request: d, get: function(t) {\n        return t.method = \"GET\", d(t);\n      }, post: v, transport: function(t) {\n        return t = l(t), w.selectFiles(t).then(function(n) {\n          for (var c = new FormData(), u = 0; u < n.length; u++) c.append(t.fieldName, n[u], n[u].name);\n          w.isObject(t.data) && Object.keys(t.data).forEach(function(m) {\n            var h = t.data[m];\n            c.append(m, h);\n          });\n          var g = t.beforeSend;\n          return t.beforeSend = function() {\n            return g(n);\n          }, t.data = c, v(t);\n        });\n      }, selectFiles: function(t) {\n        return delete (t = l(t)).beforeSend, w.selectFiles(t);\n      } });\n      i.exports = b;\n    }, function(i, s, r) {\n      r.r(s);\n      var o = r(1);\n      window.Promise = window.Promise || o.a;\n    }, function(i, s, r) {\n      (function(o) {\n        var e = o !== void 0 && o || typeof self < \"u\" && self || window, d = Function.prototype.apply;\n        function v(l, f) {\n          this._id = l, this._clearFn = f;\n        }\n        s.setTimeout = function() {\n          return new v(d.call(setTimeout, e, arguments), clearTimeout);\n        }, s.setInterval = function() {\n          return new v(d.call(setInterval, e, arguments), clearInterval);\n        }, s.clearTimeout = s.clearInterval = function(l) {\n          l && l.close();\n        }, v.prototype.unref = v.prototype.ref = function() {\n        }, v.prototype.close = function() {\n          this._clearFn.call(e, this._id);\n        }, s.enroll = function(l, f) {\n          clearTimeout(l._idleTimeoutId), l._idleTimeout = f;\n        }, s.unenroll = function(l) {\n          clearTimeout(l._idleTimeoutId), l._idleTimeout = -1;\n        }, s._unrefActive = s.active = function(l) {\n          clearTimeout(l._idleTimeoutId);\n          var f = l._idleTimeout;\n          f >= 0 && (l._idleTimeoutId = setTimeout(function() {\n            l._onTimeout && l._onTimeout();\n          }, f));\n        }, r(6), s.setImmediate = typeof self < \"u\" && self.setImmediate || o !== void 0 && o.setImmediate || this && this.setImmediate, s.clearImmediate = typeof self < \"u\" && self.clearImmediate || o !== void 0 && o.clearImmediate || this && this.clearImmediate;\n      }).call(this, r(0));\n    }, function(i, s, r) {\n      (function(o, e) {\n        (function(d, v) {\n          if (!d.setImmediate) {\n            var l, f, p, y, w, b = 1, t = {}, n = !1, c = d.document, u = Object.getPrototypeOf && Object.getPrototypeOf(d);\n            u = u && u.setTimeout ? u : d, {}.toString.call(d.process) === \"[object process]\" ? l = function(h) {\n              e.nextTick(function() {\n                m(h);\n              });\n            } : function() {\n              if (d.postMessage && !d.importScripts) {\n                var h = !0, k = d.onmessage;\n                return d.onmessage = function() {\n                  h = !1;\n                }, d.postMessage(\"\", \"*\"), d.onmessage = k, h;\n              }\n            }() ? (y = \"setImmediate$\" + Math.random() + \"$\", w = function(h) {\n              h.source === d && typeof h.data == \"string\" && h.data.indexOf(y) === 0 && m(+h.data.slice(y.length));\n            }, d.addEventListener ? d.addEventListener(\"message\", w, !1) : d.attachEvent(\"onmessage\", w), l = function(h) {\n              d.postMessage(y + h, \"*\");\n            }) : d.MessageChannel ? ((p = new MessageChannel()).port1.onmessage = function(h) {\n              m(h.data);\n            }, l = function(h) {\n              p.port2.postMessage(h);\n            }) : c && \"onreadystatechange\" in c.createElement(\"script\") ? (f = c.documentElement, l = function(h) {\n              var k = c.createElement(\"script\");\n              k.onreadystatechange = function() {\n                m(h), k.onreadystatechange = null, f.removeChild(k), k = null;\n              }, f.appendChild(k);\n            }) : l = function(h) {\n              setTimeout(m, 0, h);\n            }, u.setImmediate = function(h) {\n              typeof h != \"function\" && (h = new Function(\"\" + h));\n              for (var k = new Array(arguments.length - 1), T = 0; T < k.length; T++) k[T] = arguments[T + 1];\n              var E = { callback: h, args: k };\n              return t[b] = E, l(b), b++;\n            }, u.clearImmediate = g;\n          }\n          function g(h) {\n            delete t[h];\n          }\n          function m(h) {\n            if (n) setTimeout(m, 0, h);\n            else {\n              var k = t[h];\n              if (k) {\n                n = !0;\n                try {\n                  (function(T) {\n                    var E = T.callback, M = T.args;\n                    switch (M.length) {\n                      case 0:\n                        E();\n                        break;\n                      case 1:\n                        E(M[0]);\n                        break;\n                      case 2:\n                        E(M[0], M[1]);\n                        break;\n                      case 3:\n                        E(M[0], M[1], M[2]);\n                        break;\n                      default:\n                        E.apply(v, M);\n                    }\n                  })(k);\n                } finally {\n                  g(h), n = !1;\n                }\n              }\n            }\n          }\n        })(typeof self > \"u\" ? o === void 0 ? this : o : self);\n      }).call(this, r(0), r(7));\n    }, function(i, s) {\n      var r, o, e = i.exports = {};\n      function d() {\n        throw new Error(\"setTimeout has not been defined\");\n      }\n      function v() {\n        throw new Error(\"clearTimeout has not been defined\");\n      }\n      function l(u) {\n        if (r === setTimeout) return setTimeout(u, 0);\n        if ((r === d || !r) && setTimeout) return r = setTimeout, setTimeout(u, 0);\n        try {\n          return r(u, 0);\n        } catch {\n          try {\n            return r.call(null, u, 0);\n          } catch {\n            return r.call(this, u, 0);\n          }\n        }\n      }\n      (function() {\n        try {\n          r = typeof setTimeout == \"function\" ? setTimeout : d;\n        } catch {\n          r = d;\n        }\n        try {\n          o = typeof clearTimeout == \"function\" ? clearTimeout : v;\n        } catch {\n          o = v;\n        }\n      })();\n      var f, p = [], y = !1, w = -1;\n      function b() {\n        y && f && (y = !1, f.length ? p = f.concat(p) : w = -1, p.length && t());\n      }\n      function t() {\n        if (!y) {\n          var u = l(b);\n          y = !0;\n          for (var g = p.length; g; ) {\n            for (f = p, p = []; ++w < g; ) f && f[w].run();\n            w = -1, g = p.length;\n          }\n          f = null, y = !1, function(m) {\n            if (o === clearTimeout) return clearTimeout(m);\n            if ((o === v || !o) && clearTimeout) return o = clearTimeout, clearTimeout(m);\n            try {\n              o(m);\n            } catch {\n              try {\n                return o.call(null, m);\n              } catch {\n                return o.call(this, m);\n              }\n            }\n          }(u);\n        }\n      }\n      function n(u, g) {\n        this.fun = u, this.array = g;\n      }\n      function c() {\n      }\n      e.nextTick = function(u) {\n        var g = new Array(arguments.length - 1);\n        if (arguments.length > 1) for (var m = 1; m < arguments.length; m++) g[m - 1] = arguments[m];\n        p.push(new n(u, g)), p.length !== 1 || y || l(t);\n      }, n.prototype.run = function() {\n        this.fun.apply(null, this.array);\n      }, e.title = \"browser\", e.browser = !0, e.env = {}, e.argv = [], e.version = \"\", e.versions = {}, e.on = c, e.addListener = c, e.once = c, e.off = c, e.removeListener = c, e.removeAllListeners = c, e.emit = c, e.prependListener = c, e.prependOnceListener = c, e.listeners = function(u) {\n        return [];\n      }, e.binding = function(u) {\n        throw new Error(\"process.binding is not supported\");\n      }, e.cwd = function() {\n        return \"/\";\n      }, e.chdir = function(u) {\n        throw new Error(\"process.chdir is not supported\");\n      }, e.umask = function() {\n        return 0;\n      };\n    }, function(i, s, r) {\n      function o(d, v) {\n        for (var l = 0; l < v.length; l++) {\n          var f = v[l];\n          f.enumerable = f.enumerable || !1, f.configurable = !0, \"value\" in f && (f.writable = !0), Object.defineProperty(d, f.key, f);\n        }\n      }\n      var e = r(9);\n      i.exports = function() {\n        function d() {\n          (function(p, y) {\n            if (!(p instanceof y)) throw new TypeError(\"Cannot call a class as a function\");\n          })(this, d);\n        }\n        var v, l, f;\n        return v = d, f = [{ key: \"urlEncode\", value: function(p) {\n          return e(p);\n        } }, { key: \"jsonEncode\", value: function(p) {\n          return JSON.stringify(p);\n        } }, { key: \"formEncode\", value: function(p) {\n          if (this.isFormData(p)) return p;\n          if (this.isFormElement(p)) return new FormData(p);\n          if (this.isObject(p)) {\n            var y = new FormData();\n            return Object.keys(p).forEach(function(w) {\n              var b = p[w];\n              y.append(w, b);\n            }), y;\n          }\n          throw new Error(\"`data` must be an instance of Object, FormData or <FORM> HTMLElement\");\n        } }, { key: \"isObject\", value: function(p) {\n          return Object.prototype.toString.call(p) === \"[object Object]\";\n        } }, { key: \"isFormData\", value: function(p) {\n          return p instanceof FormData;\n        } }, { key: \"isFormElement\", value: function(p) {\n          return p instanceof HTMLFormElement;\n        } }, { key: \"selectFiles\", value: function() {\n          var p = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n          return new Promise(function(y, w) {\n            var b = document.createElement(\"INPUT\");\n            b.type = \"file\", p.multiple && b.setAttribute(\"multiple\", \"multiple\"), p.accept && b.setAttribute(\"accept\", p.accept), b.style.display = \"none\", document.body.appendChild(b), b.addEventListener(\"change\", function(t) {\n              var n = t.target.files;\n              y(n), document.body.removeChild(b);\n            }, !1), b.click();\n          });\n        } }, { key: \"parseHeaders\", value: function(p) {\n          var y = p.trim().split(/[\\r\\n]+/), w = {};\n          return y.forEach(function(b) {\n            var t = b.split(\": \"), n = t.shift(), c = t.join(\": \");\n            n && (w[n] = c);\n          }), w;\n        } }], (l = null) && o(v.prototype, l), f && o(v, f), d;\n      }();\n    }, function(i, s) {\n      var r = function(e) {\n        return encodeURIComponent(e).replace(/[!'()*]/g, escape).replace(/%20/g, \"+\");\n      }, o = function(e, d, v, l) {\n        return d = d || null, v = v || \"&\", l = l || null, e ? function(f) {\n          for (var p = new Array(), y = 0; y < f.length; y++) f[y] && p.push(f[y]);\n          return p;\n        }(Object.keys(e).map(function(f) {\n          var p, y, w = f;\n          if (l && (w = l + \"[\" + w + \"]\"), typeof e[f] == \"object\" && e[f] !== null) p = o(e[f], null, v, w);\n          else {\n            d && (y = w, w = !isNaN(parseFloat(y)) && isFinite(y) ? d + Number(w) : w);\n            var b = e[f];\n            b = (b = (b = (b = b === !0 ? \"1\" : b) === !1 ? \"0\" : b) === 0 ? \"0\" : b) || \"\", p = r(w) + \"=\" + r(b);\n          }\n          return p;\n        })).join(v).replace(/[!'()*]/g, \"\") : \"\";\n      };\n      i.exports = o;\n    }]);\n  });\n})(H);\nvar q = H.exports;\nconst j = /* @__PURE__ */ U(q);\nfunction O(C) {\n  return C !== void 0 && typeof C.then == \"function\";\n}\nclass A {\n  /**\n   * @param params - uploader module params\n   * @param params.config - image tool config\n   * @param params.onUpload - one callback for all uploading (file, url, d-n-d, pasting)\n   * @param params.onError - callback for uploading errors\n   */\n  constructor({ config: a, onUpload: i, onError: s }) {\n    this.config = a, this.onUpload = i, this.onError = s;\n  }\n  /**\n   * Handle clicks on the upload file button\n   * Fires ajax.transport()\n   * @param onPreview - callback fired when preview is ready\n   */\n  uploadSelectedFile({ onPreview: a }) {\n    const i = function(r) {\n      const o = new FileReader();\n      o.readAsDataURL(r), o.onload = (e) => {\n        a(e.target.result);\n      };\n    };\n    let s;\n    if (this.config.uploader && typeof this.config.uploader.uploadByFile == \"function\") {\n      const r = this.config.uploader.uploadByFile;\n      s = j.selectFiles({ accept: this.config.types ?? \"image/*\" }).then((o) => {\n        i(o[0]);\n        const e = r(o[0]);\n        return O(e) || console.warn(\"Custom uploader method uploadByFile should return a Promise\"), e;\n      });\n    } else\n      s = j.transport({\n        url: this.config.endpoints.byFile,\n        data: this.config.additionalRequestData,\n        accept: this.config.types ?? \"image/*\",\n        headers: this.config.additionalRequestHeaders,\n        beforeSend: (r) => {\n          i(r[0]);\n        },\n        fieldName: this.config.field ?? \"image\"\n      }).then((r) => r.body);\n    s.then((r) => {\n      this.onUpload(r);\n    }).catch((r) => {\n      this.onError(r);\n    });\n  }\n  /**\n   * Handle clicks on the upload file button\n   * Fires ajax.post()\n   * @param url - image source url\n   */\n  uploadByUrl(a) {\n    let i;\n    this.config.uploader && typeof this.config.uploader.uploadByUrl == \"function\" ? (i = this.config.uploader.uploadByUrl(a), O(i) || console.warn(\"Custom uploader method uploadByUrl should return a Promise\")) : i = j.post({\n      url: this.config.endpoints.byUrl,\n      data: Object.assign({\n        url: a\n      }, this.config.additionalRequestData),\n      type: j.contentType.JSON,\n      headers: this.config.additionalRequestHeaders\n    }).then((s) => s.body), i.then((s) => {\n      this.onUpload(s);\n    }).catch((s) => {\n      this.onError(s);\n    });\n  }\n  /**\n   * Handle clicks on the upload file button\n   * Fires ajax.post()\n   * @param file - file pasted by drag-n-drop\n   * @param onPreview - file pasted by drag-n-drop\n   */\n  uploadByFile(a, { onPreview: i }) {\n    const s = new FileReader();\n    s.readAsDataURL(a), s.onload = (o) => {\n      i(o.target.result);\n    };\n    let r;\n    if (this.config.uploader && typeof this.config.uploader.uploadByFile == \"function\")\n      r = this.config.uploader.uploadByFile(a), O(r) || console.warn(\"Custom uploader method uploadByFile should return a Promise\");\n    else {\n      const o = new FormData();\n      o.append(this.config.field ?? \"image\", a), this.config.additionalRequestData && Object.keys(this.config.additionalRequestData).length && Object.entries(this.config.additionalRequestData).forEach(([e, d]) => {\n        o.append(e, d);\n      }), r = j.post({\n        url: this.config.endpoints.byFile,\n        data: o,\n        type: j.contentType.JSON,\n        headers: this.config.additionalRequestHeaders\n      }).then((e) => e.body);\n    }\n    r.then((o) => {\n      this.onUpload(o);\n    }).catch((o) => {\n      this.onError(o);\n    });\n  }\n}\n/**\n * Image Tool for the Editor.js\n * <AUTHOR> <<EMAIL>>\n * @license MIT\n * @see {@link https://github.com/editor-js/image}\n *\n * To developers.\n * To simplify Tool structure, we split it to 4 parts:\n *  1) index.ts — main Tool's interface, public API and methods for working with data\n *  2) uploader.ts — module that has methods for sending files via AJAX: from device, by URL or File pasting\n *  3) ui.ts — module for UI manipulations: render, showing preloader, etc\n *\n * For debug purposes there is a testing server\n * that can save uploaded files and return a Response {@link UploadResponseFormat}\n *\n *       $ node dev/server.js\n *\n * It will expose 8008 port, so you can pass http://localhost:8008 with the Tools config:\n *\n * image: {\n *   class: ImageTool,\n *   config: {\n *     endpoints: {\n *       byFile: 'http://localhost:8008/uploadFile',\n *       byUrl: 'http://localhost:8008/fetchUrl',\n *     }\n *   },\n * },\n */\nclass P {\n  /**\n   * @param tool - tool properties got from editor.js\n   * @param tool.data - previously saved data\n   * @param tool.config - user config for Tool\n   * @param tool.api - Editor.js API\n   * @param tool.readOnly - read-only mode flag\n   * @param tool.block - current Block API\n   */\n  constructor({ data: a, config: i, api: s, readOnly: r, block: o }) {\n    this.isCaptionEnabled = null, this.api = s, this.block = o, this.config = {\n      endpoints: i.endpoints,\n      additionalRequestData: i.additionalRequestData,\n      additionalRequestHeaders: i.additionalRequestHeaders,\n      field: i.field,\n      types: i.types,\n      captionPlaceholder: this.api.i18n.t(i.captionPlaceholder ?? \"Caption\"),\n      buttonContent: i.buttonContent,\n      uploader: i.uploader,\n      actions: i.actions,\n      features: i.features || {}\n    }, this.uploader = new A({\n      config: this.config,\n      onUpload: (e) => this.onUpload(e),\n      onError: (e) => this.uploadingFailed(e)\n    }), this.ui = new D({\n      api: s,\n      config: this.config,\n      onSelectFile: () => {\n        this.uploader.uploadSelectedFile({\n          onPreview: (e) => {\n            this.ui.showPreloader(e);\n          }\n        });\n      },\n      readOnly: r\n    }), this._data = {\n      caption: \"\",\n      withBorder: !1,\n      withBackground: !1,\n      stretched: !1,\n      file: {\n        url: \"\"\n      }\n    }, this.data = a;\n  }\n  /**\n   * Notify core that read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   */\n  static get toolbox() {\n    return {\n      icon: L,\n      title: \"Image\"\n    };\n  }\n  /**\n   * Available image tools\n   */\n  static get tunes() {\n    return [\n      {\n        name: \"withBorder\",\n        icon: I,\n        title: \"With border\",\n        toggle: !0\n      },\n      {\n        name: \"stretched\",\n        icon: x,\n        title: \"Stretch image\",\n        toggle: !0\n      },\n      {\n        name: \"withBackground\",\n        icon: R,\n        title: \"With background\",\n        toggle: !0\n      }\n    ];\n  }\n  /**\n   * Renders Block content\n   */\n  render() {\n    var a, i, s;\n    return (((a = this.config.features) == null ? void 0 : a.caption) === !0 || ((i = this.config.features) == null ? void 0 : i.caption) === void 0 || ((s = this.config.features) == null ? void 0 : s.caption) === \"optional\" && this.data.caption) && (this.isCaptionEnabled = !0), this.ui.render();\n  }\n  /**\n   * Validate data: check if Image exists\n   * @param savedData — data received after saving\n   * @returns false if saved data is not correct, otherwise true\n   */\n  validate(a) {\n    return !!a.file.url;\n  }\n  /**\n   * Return Block data\n   */\n  save() {\n    const a = this.ui.nodes.caption;\n    return this._data.caption = a.innerHTML, this.data;\n  }\n  /**\n   * Returns configuration for block tunes: add background, add border, stretch image\n   * @returns TunesMenuConfig\n   */\n  renderSettings() {\n    var o;\n    const a = P.tunes.concat(this.config.actions || []), i = {\n      border: \"withBorder\",\n      background: \"withBackground\",\n      stretch: \"stretched\",\n      caption: \"caption\"\n    };\n    ((o = this.config.features) == null ? void 0 : o.caption) === \"optional\" && a.push({\n      name: \"caption\",\n      icon: B,\n      title: \"With caption\",\n      toggle: !0\n    });\n    const s = a.filter((e) => {\n      var v, l;\n      const d = Object.keys(i).find((f) => i[f] === e.name);\n      return d === \"caption\" ? ((v = this.config.features) == null ? void 0 : v.caption) !== !1 : d == null || ((l = this.config.features) == null ? void 0 : l[d]) !== !1;\n    }), r = (e) => {\n      let d = this.data[e.name];\n      return e.name === \"caption\" && (d = this.isCaptionEnabled ?? d), d;\n    };\n    return s.map((e) => ({\n      icon: e.icon,\n      label: this.api.i18n.t(e.title),\n      name: e.name,\n      toggle: e.toggle,\n      isActive: r(e),\n      onActivate: () => {\n        if (typeof e.action == \"function\") {\n          e.action(e.name);\n          return;\n        }\n        let d = !r(e);\n        e.name === \"caption\" && (this.isCaptionEnabled = !(this.isCaptionEnabled ?? !1), d = this.isCaptionEnabled), this.tuneToggled(e.name, d);\n      }\n    }));\n  }\n  /**\n   * Fires after clicks on the Toolbox Image Icon\n   * Initiates click on the Select File button\n   */\n  appendCallback() {\n    this.ui.nodes.fileButton.click();\n  }\n  /**\n   * Specify paste substitutes\n   * @see {@link https://github.com/codex-team/editor.js/blob/master/docs/tools.md#paste-handling}\n   */\n  static get pasteConfig() {\n    return {\n      /**\n       * Paste HTML into Editor\n       */\n      tags: [\n        {\n          img: { src: !0 }\n        }\n      ],\n      /**\n       * Paste URL of image into the Editor\n       */\n      patterns: {\n        image: /https?:\\/\\/\\S+\\.(gif|jpe?g|tiff|png|svg|webp)(\\?[a-z0-9=]*)?$/i\n      },\n      /**\n       * Drag n drop file from into the Editor\n       */\n      files: {\n        mimeTypes: [\"image/*\"]\n      }\n    };\n  }\n  /**\n   * Specify paste handlers\n   * @see {@link https://github.com/codex-team/editor.js/blob/master/docs/tools.md#paste-handling}\n   * @param event - editor.js custom paste event\n   *                              {@link https://github.com/codex-team/editor.js/blob/master/types/tools/paste-events.d.ts}\n   */\n  async onPaste(a) {\n    switch (a.type) {\n      case \"tag\": {\n        const i = a.detail.data;\n        if (/^blob:/.test(i.src)) {\n          const r = await (await fetch(i.src)).blob();\n          this.uploadFile(r);\n          break;\n        }\n        this.uploadUrl(i.src);\n        break;\n      }\n      case \"pattern\": {\n        const i = a.detail.data;\n        this.uploadUrl(i);\n        break;\n      }\n      case \"file\": {\n        const i = a.detail.file;\n        this.uploadFile(i);\n        break;\n      }\n    }\n  }\n  /**\n   * Private methods\n   * ̿̿ ̿̿ ̿̿ ̿'̿'\\̵͇̿̿\\з= ( ▀ ͜͞ʖ▀) =ε/̵͇̿̿/’̿’̿ ̿ ̿̿ ̿̿ ̿̿\n   */\n  /**\n   * Stores all Tool's data\n   * @param data - data in Image Tool format\n   */\n  set data(a) {\n    this.image = a.file, this._data.caption = a.caption || \"\", this.ui.fillCaption(this._data.caption), P.tunes.forEach(({ name: i }) => {\n      const s = typeof a[i] < \"u\" ? a[i] === !0 || a[i] === \"true\" : !1;\n      this.setTune(i, s);\n    }), a.caption && this.setTune(\"caption\", !0);\n  }\n  /**\n   * Return Tool data\n   */\n  get data() {\n    return this._data;\n  }\n  /**\n   * Set new image file\n   * @param file - uploaded file data\n   */\n  set image(a) {\n    this._data.file = a || { url: \"\" }, a && a.url && this.ui.fillImage(a.url);\n  }\n  /**\n   * File uploading callback\n   * @param response - uploading server response\n   */\n  onUpload(a) {\n    a.success && a.file ? this.image = a.file : this.uploadingFailed(\"incorrect response: \" + JSON.stringify(a));\n  }\n  /**\n   * Handle uploader errors\n   * @param errorText - uploading error info\n   */\n  uploadingFailed(a) {\n    console.log(\"Image Tool: uploading failed because of\", a), this.api.notifier.show({\n      message: this.api.i18n.t(\"Couldn’t upload image. Please try another.\"),\n      style: \"error\"\n    }), this.ui.hidePreloader();\n  }\n  /**\n   * Callback fired when Block Tune is activated\n   * @param tuneName - tune that has been clicked\n   * @param state - new state\n   */\n  tuneToggled(a, i) {\n    a === \"caption\" ? (this.ui.applyTune(a, i), i == !1 && (this._data.caption = \"\", this.ui.fillCaption(\"\"))) : this.setTune(a, i);\n  }\n  /**\n   * Set one tune\n   * @param tuneName - {@link Tunes.tunes}\n   * @param value - tune state\n   */\n  setTune(a, i) {\n    this._data[a] = i, this.ui.applyTune(a, i), a === \"stretched\" && Promise.resolve().then(() => {\n      this.block.stretched = i;\n    }).catch((s) => {\n      console.error(s);\n    });\n  }\n  /**\n   * Show preloader and upload image file\n   * @param file - file that is currently uploading (from paste)\n   */\n  uploadFile(a) {\n    this.uploader.uploadByFile(a, {\n      onPreview: (i) => {\n        this.ui.showPreloader(i);\n      }\n    });\n  }\n  /**\n   * Show preloader and upload image by target url\n   * @param url - url pasted\n   */\n  uploadUrl(a) {\n    this.ui.showPreloader(a), this.uploader.uploadByUrl(a);\n  }\n}\nexport {\n  P as default\n};\n"], "mappings": ";;;;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,mzEAAmzE,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AACnhF,IAAM,IAAI;AAAV,IAAinC,IAAI;AAArnC,IAA8yE,IAAI;AAAlzE,IAAs9F,IAAI;AAA19F,IAA2/G,IAAI;AAC//G,SAAS,EAAE,GAAG,IAAI,MAAM,IAAI,CAAC,GAAG;AAC9B,QAAM,IAAI,SAAS,cAAc,CAAC;AAClC,QAAM,QAAQ,CAAC,IAAI,EAAE,UAAU,IAAI,GAAG,CAAC,IAAI,MAAM,QAAQ,EAAE,UAAU,IAAI,CAAC;AAC1E,aAAW,KAAK;AACd,MAAE,eAAe,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AACpC,SAAO;AACT;AACA,IAAI,KAAqB,CAAC,OAAO,EAAE,QAAQ,SAAS,EAAE,YAAY,aAAa,EAAE,SAAS,UAAU,IAAI,KAAK,CAAC,CAAC;AAC/G,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQN,YAAY,EAAE,KAAK,GAAG,QAAQ,GAAG,cAAc,GAAG,UAAU,EAAE,GAAG;AAC/D,SAAK,MAAM,GAAG,KAAK,SAAS,GAAG,KAAK,eAAe,GAAG,KAAK,WAAW,GAAG,KAAK,QAAQ;AAAA,MACpF,SAAS,EAAE,OAAO,CAAC,KAAK,IAAI,WAAW,KAAK,IAAI,OAAO,CAAC;AAAA,MACxD,gBAAgB,EAAE,OAAO,CAAC,KAAK,IAAI,cAAc,CAAC;AAAA,MAClD,YAAY,KAAK,iBAAiB;AAAA,MAClC,SAAS;AAAA,MACT,gBAAgB,EAAE,OAAO,KAAK,IAAI,cAAc;AAAA,MAChD,SAAS,EAAE,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAAA,QACpD,iBAAiB,CAAC,KAAK;AAAA,MACzB,CAAC;AAAA,IACH,GAAG,KAAK,MAAM,QAAQ,QAAQ,cAAc,KAAK,OAAO,oBAAoB,KAAK,MAAM,eAAe,YAAY,KAAK,MAAM,cAAc,GAAG,KAAK,MAAM,QAAQ,YAAY,KAAK,MAAM,cAAc,GAAG,KAAK,MAAM,QAAQ,YAAY,KAAK,MAAM,OAAO,GAAG,KAAK,MAAM,QAAQ,YAAY,KAAK,MAAM,UAAU;AAAA,EACnT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG,GAAG;AACd,SAAK,MAAM,QAAQ,UAAU,OAAO,GAAG,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,WAAO,KAAK;AAAA,MACV;AAAA;AAAA,IAEF,GAAG,KAAK,MAAM;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,GAAG;AACf,SAAK,MAAM,eAAe,MAAM,kBAAkB,OAAO,CAAC,KAAK,KAAK;AAAA,MAClE;AAAA;AAAA,IAEF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,SAAK,MAAM,eAAe,MAAM,kBAAkB,IAAI,KAAK;AAAA,MACzD;AAAA;AAAA,IAEF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,GAAG;AACX,UAAM,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU,OAAO,IAAI;AAAA,MAChD,KAAK;AAAA,IACP;AACA,QAAI,IAAI;AACR,UAAM,YAAY,EAAE,WAAW,MAAI,EAAE,OAAO,MAAI,EAAE,QAAQ,MAAI,EAAE,cAAc,MAAI,IAAI,eAAe,KAAK,MAAM,UAAU,EAAE,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,KAAK,MAAM,QAAQ,iBAAiB,GAAG,MAAM;AAChM,WAAK;AAAA,QACH;AAAA;AAAA,MAEF,GAAG,KAAK,MAAM,mBAAmB,WAAW,KAAK,MAAM,eAAe,MAAM,kBAAkB;AAAA,IAChG,CAAC,GAAG,KAAK,MAAM,eAAe,YAAY,KAAK,MAAM,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,GAAG;AACb,SAAK,MAAM,YAAY,WAAW,KAAK,MAAM,QAAQ,YAAY;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,GAAG;AACd,eAAW,KAAK;AACd,UAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,GAAG;AAC9C,cAAM,IAAI,EAAE,CAAC;AACb,aAAK,MAAM,QAAQ,UAAU,OAAO,GAAG,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,MAAM,CAAC;AAAA,MAC1E;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM;AACR,WAAO;AAAA,MACL,WAAW,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS,KAAK,IAAI,OAAO;AAAA,MACzB,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,QAAQ,KAAK,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA,MAIxB,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACjB,UAAM,IAAI,EAAE,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC;AACpC,WAAO,EAAE,YAAY,KAAK,OAAO,iBAAiB,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,EAAE,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,SAAS,MAAM;AAChI,WAAK,aAAa;AAAA,IACpB,CAAC,GAAG;AAAA,EACN;AACF;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,UAAU;AAC/F;AACA,IAAI,IAAI,EAAE,SAAS,CAAC,EAAE;AAAA,CACrB,SAAS,GAAG,GAAG;AACd,GAAC,SAAS,GAAG,GAAG;AACd,MAAE,UAAU,EAAE;AAAA,EAChB,GAAG,QAAQ,WAAW;AACpB,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,CAAC;AACT,eAAS,EAAE,GAAG;AACZ,YAAI,EAAE,CAAC,EAAG,QAAO,EAAE,CAAC,EAAE;AACtB,YAAI,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,OAAI,SAAS,CAAC,EAAE;AAC1C,eAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,MAAI,EAAE;AAAA,MAC5D;AACA,aAAO,EAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE,IAAI,SAAS,GAAG,GAAG,GAAG;AAC/C,UAAE,EAAE,GAAG,CAAC,KAAK,OAAO,eAAe,GAAG,GAAG,EAAE,YAAY,MAAI,KAAK,EAAE,CAAC;AAAA,MACrE,GAAG,EAAE,IAAI,SAAS,GAAG;AACnB,eAAO,SAAS,OAAO,OAAO,eAAe,OAAO,eAAe,GAAG,OAAO,aAAa,EAAE,OAAO,SAAS,CAAC,GAAG,OAAO,eAAe,GAAG,cAAc,EAAE,OAAO,KAAG,CAAC;AAAA,MACtK,GAAG,EAAE,IAAI,SAAS,GAAG,GAAG;AACtB,YAAI,IAAI,MAAM,IAAI,EAAE,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,YAAY,KAAK,EAAE,WAAY,QAAO;AAC7F,YAAI,IAAoB,uBAAO,OAAO,IAAI;AAC1C,YAAI,EAAE,EAAE,CAAC,GAAG,OAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAI,OAAO,EAAE,CAAC,GAAG,IAAI,KAAK,OAAO,KAAK,SAAU,UAAS,KAAK,EAAG,GAAE,EAAE,GAAG,GAAI,SAAS,GAAG;AACpJ,iBAAO,EAAE,CAAC;AAAA,QACZ,EAAG,KAAK,MAAM,CAAC,CAAC;AAChB,eAAO;AAAA,MACT,GAAG,EAAE,IAAI,SAAS,GAAG;AACnB,YAAI,IAAI,KAAK,EAAE,aAAa,WAAW;AACrC,iBAAO,EAAE;AAAA,QACX,IAAI,WAAW;AACb,iBAAO;AAAA,QACT;AACA,eAAO,EAAE,EAAE,GAAG,KAAK,CAAC,GAAG;AAAA,MACzB,GAAG,EAAE,IAAI,SAAS,GAAG,GAAG;AACtB,eAAO,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,MAClD,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC;AAAA,IACxB,EAAE,CAAC,SAAS,GAAG,GAAG;AAChB,UAAI;AACJ,UAAoB,2BAAW;AAC7B,eAAO;AAAA,MACT,EAAE;AACF,UAAI;AACF,YAAI,KAAK,IAAI,SAAS,aAAa,EAAE;AAAA,MACvC,QAAQ;AACN,eAAO,UAAU,aAAa,IAAI;AAAA,MACpC;AACA,QAAE,UAAU;AAAA,IACd,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,OAAC,SAAS,GAAG;AACX,YAAI,IAAI,EAAE,CAAC,GAAG,IAAI;AAClB,iBAAS,IAAI;AAAA,QACb;AACA,iBAAS,EAAE,GAAG;AACZ,cAAI,EAAE,gBAAgB,GAAI,OAAM,IAAI,UAAU,sCAAsC;AACpF,cAAI,OAAO,KAAK,WAAY,OAAM,IAAI,UAAU,gBAAgB;AAChE,eAAK,SAAS,GAAG,KAAK,WAAW,OAAI,KAAK,SAAS,QAAQ,KAAK,aAAa,CAAC,GAAG,EAAE,GAAG,IAAI;AAAA,QAC5F;AACA,iBAAS,EAAE,GAAG,GAAG;AACf,iBAAO,EAAE,WAAW,IAAK,KAAI,EAAE;AAC/B,YAAE,WAAW,KAAK,EAAE,WAAW,MAAI,EAAE,aAAa,WAAW;AAC3D,gBAAI,IAAI,EAAE,WAAW,IAAI,EAAE,cAAc,EAAE;AAC3C,gBAAI,MAAM,MAAM;AACd,kBAAI;AACJ,kBAAI;AACF,oBAAI,EAAE,EAAE,MAAM;AAAA,cAChB,SAAS,GAAG;AACV,uBAAO,KAAK,EAAE,EAAE,SAAS,CAAC;AAAA,cAC5B;AACA,gBAAE,EAAE,SAAS,CAAC;AAAA,YAChB,MAAO,EAAC,EAAE,WAAW,IAAI,IAAI,GAAG,EAAE,SAAS,EAAE,MAAM;AAAA,UACrD,CAAC,KAAK,EAAE,WAAW,KAAK,CAAC;AAAA,QAC3B;AACA,iBAAS,EAAE,GAAG,GAAG;AACf,cAAI;AACF,gBAAI,MAAM,EAAG,OAAM,IAAI,UAAU,2CAA2C;AAC5E,gBAAI,MAAM,OAAO,KAAK,YAAY,OAAO,KAAK,aAAa;AACzD,kBAAI,IAAI,EAAE;AACV,kBAAI,aAAa,EAAG,QAAO,EAAE,SAAS,GAAG,EAAE,SAAS,GAAG,KAAK,EAAE,CAAC;AAC/D,kBAAI,OAAO,KAAK,WAAY,QAAO,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,WAAW;AAClE,kBAAE,MAAM,GAAG,SAAS;AAAA,cACtB,IAAI,CAAC;AAAA,YACP;AACA,cAAE,SAAS,GAAG,EAAE,SAAS,GAAG,EAAE,CAAC;AAAA,UACjC,SAAS,GAAG;AACV,cAAE,GAAG,CAAC;AAAA,UACR;AACA,cAAI,GAAG;AAAA,QACT;AACA,iBAAS,EAAE,GAAG,GAAG;AACf,YAAE,SAAS,GAAG,EAAE,SAAS,GAAG,EAAE,CAAC;AAAA,QACjC;AACA,iBAAS,EAAE,GAAG;AACZ,YAAE,WAAW,KAAK,EAAE,WAAW,WAAW,KAAK,EAAE,aAAa,WAAW;AACvE,cAAE,YAAY,EAAE,sBAAsB,EAAE,MAAM;AAAA,UAChD,CAAC;AACD,mBAAS,IAAI,GAAG,IAAI,EAAE,WAAW,QAAQ,IAAI,GAAG,IAAK,GAAE,GAAG,EAAE,WAAW,CAAC,CAAC;AACzE,YAAE,aAAa;AAAA,QACjB;AACA,iBAAS,EAAE,GAAG,GAAG,GAAG;AAClB,eAAK,cAAc,OAAO,KAAK,aAAa,IAAI,MAAM,KAAK,aAAa,OAAO,KAAK,aAAa,IAAI,MAAM,KAAK,UAAU;AAAA,QAC5H;AACA,iBAAS,EAAE,GAAG,GAAG;AACf,cAAI,IAAI;AACR,cAAI;AACF,cAAE,SAAS,GAAG;AACZ,oBAAM,IAAI,MAAI,EAAE,GAAG,CAAC;AAAA,YACtB,GAAG,SAAS,GAAG;AACb,oBAAM,IAAI,MAAI,EAAE,GAAG,CAAC;AAAA,YACtB,CAAC;AAAA,UACH,SAAS,GAAG;AACV,gBAAI,EAAG;AACP,gBAAI,MAAI,EAAE,GAAG,CAAC;AAAA,UAChB;AAAA,QACF;AACA,UAAE,UAAU,QAAQ,SAAS,GAAG;AAC9B,iBAAO,KAAK,KAAK,MAAM,CAAC;AAAA,QAC1B,GAAG,EAAE,UAAU,OAAO,SAAS,GAAG,GAAG;AACnC,cAAI,IAAI,IAAI,KAAK,YAAY,CAAC;AAC9B,iBAAO,EAAE,MAAM,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG;AAAA,QAClC,GAAG,EAAE,UAAU,UAAU,EAAE,GAAG,EAAE,MAAM,SAAS,GAAG;AAChD,iBAAO,IAAI,EAAE,SAAS,GAAG,GAAG;AAC1B,gBAAI,CAAC,KAAK,EAAE,WAAW,OAAQ,OAAM,IAAI,UAAU,8BAA8B;AACjF,gBAAI,IAAI,MAAM,UAAU,MAAM,KAAK,CAAC;AACpC,gBAAI,EAAE,WAAW,EAAG,QAAO,EAAE,CAAC,CAAC;AAC/B,gBAAI,IAAI,EAAE;AACV,qBAAS,EAAE,GAAG,GAAG;AACf,kBAAI;AACF,oBAAI,MAAM,OAAO,KAAK,YAAY,OAAO,KAAK,aAAa;AACzD,sBAAI,IAAI,EAAE;AACV,sBAAI,OAAO,KAAK,WAAY,QAAO,KAAK,EAAE,KAAK,GAAG,SAAS,GAAG;AAC5D,sBAAE,GAAG,CAAC;AAAA,kBACR,GAAG,CAAC;AAAA,gBACN;AACA,kBAAE,CAAC,IAAI,GAAG,EAAE,KAAK,KAAK,EAAE,CAAC;AAAA,cAC3B,SAAS,GAAG;AACV,kBAAE,CAAC;AAAA,cACL;AAAA,YACF;AACA,qBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,GAAG,EAAE,CAAC,CAAC;AAAA,UAC9C,CAAC;AAAA,QACH,GAAG,EAAE,UAAU,SAAS,GAAG;AACzB,iBAAO,KAAK,OAAO,KAAK,YAAY,EAAE,gBAAgB,IAAI,IAAI,IAAI,EAAE,SAAS,GAAG;AAC9E,cAAE,CAAC;AAAA,UACL,CAAC;AAAA,QACH,GAAG,EAAE,SAAS,SAAS,GAAG;AACxB,iBAAO,IAAI,EAAE,SAAS,GAAG,GAAG;AAC1B,cAAE,CAAC;AAAA,UACL,CAAC;AAAA,QACH,GAAG,EAAE,OAAO,SAAS,GAAG;AACtB,iBAAO,IAAI,EAAE,SAAS,GAAG,GAAG;AAC1B,qBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,IAAK,GAAE,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,UAC1D,CAAC;AAAA,QACH,GAAG,EAAE,eAAe,OAAO,KAAK,cAAc,SAAS,GAAG;AACxD,YAAE,CAAC;AAAA,QACL,KAAK,SAAS,GAAG;AACf,YAAE,GAAG,CAAC;AAAA,QACR,GAAG,EAAE,wBAAwB,SAAS,GAAG;AACvC,iBAAO,UAAU,OAAO,WAAW,QAAQ,KAAK,yCAAyC,CAAC;AAAA,QAC5F,GAAG,EAAE,IAAI;AAAA,MACX,GAAG,KAAK,MAAM,EAAE,CAAC,EAAE,YAAY;AAAA,IACjC,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,QAAE,IAAI,SAAS,GAAG;AAChB,YAAI,IAAI,KAAK;AACb,eAAO,KAAK,KAAK,SAAS,GAAG;AAC3B,iBAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,WAAW;AACpC,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,GAAG,SAAS,GAAG;AACb,iBAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,WAAW;AACpC,mBAAO,EAAE,OAAO,CAAC;AAAA,UACnB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,eAAS,EAAE,GAAG;AACZ,gBAAQ,IAAI,OAAO,UAAU,cAAc,OAAO,OAAO,YAAY,WAAW,SAAS,GAAG;AAC1F,iBAAO,OAAO;AAAA,QAChB,IAAI,SAAS,GAAG;AACd,iBAAO,KAAK,OAAO,UAAU,cAAc,EAAE,gBAAgB,UAAU,MAAM,OAAO,YAAY,WAAW,OAAO;AAAA,QACpH,GAAG,CAAC;AAAA,MACN;AACA,QAAE,CAAC;AACH,UAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,SAAS,GAAG;AACtD,eAAO,IAAI,QAAQ,SAAS,GAAG,GAAG;AAChC,cAAI,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,cAAc,EAAE,WAAW;AAChD,cAAI,IAAI,OAAO,iBAAiB,IAAI,OAAO,eAAe,IAAI,IAAI,OAAO,cAAc,mBAAmB;AAC1G,YAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,GAAG,EAAE,iBAAiB,oBAAoB,gBAAgB,GAAG,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,SAAS,GAAG;AAC5H,gBAAI,IAAI,EAAE,QAAQ,CAAC;AACnB,cAAE,iBAAiB,GAAG,CAAC;AAAA,UACzB,CAAC;AACD,cAAI,IAAI,EAAE;AACV,YAAE,OAAO,iBAAiB,YAAY,SAAS,GAAG;AAChD,gBAAI,IAAI,KAAK,MAAM,EAAE,SAAS,EAAE,QAAQ,GAAG,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG;AACvE,cAAE,SAAS,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,UAC7B,GAAG,KAAE,GAAG,EAAE,iBAAiB,YAAY,SAAS,GAAG;AACjD,gBAAI,IAAI,KAAK,MAAM,EAAE,SAAS,EAAE,QAAQ,GAAG,GAAG,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG,IAAI;AACnF,cAAE,SAAS,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,UAC7B,GAAG,KAAE,GAAG,EAAE,qBAAqB,WAAW;AACxC,gBAAI,EAAE,eAAe,GAAG;AACtB,kBAAI,IAAI,EAAE;AACV,kBAAI;AACF,oBAAI,KAAK,MAAM,CAAC;AAAA,cAClB,QAAQ;AAAA,cACR;AACA,kBAAI,IAAI,EAAE,aAAa,EAAE,sBAAsB,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,SAAS,EAAE;AAC7F,gBAAE,EAAE,MAAM,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,YAC1B;AAAA,UACF,GAAG,EAAE,KAAK,EAAE,IAAI;AAAA,QAClB,CAAC;AAAA,MACH,GAAG,IAAI,SAAS,GAAG;AACjB,eAAO,EAAE,SAAS,QAAQ,EAAE,CAAC;AAAA,MAC/B,GAAG,IAAI,WAAW;AAChB,YAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC;AAC1E,YAAI,EAAE,OAAO,OAAO,EAAE,OAAO,SAAU,OAAM,IAAI,MAAM,sBAAsB;AAC7E,YAAI,EAAE,MAAM,EAAE,OAAO,IAAI,EAAE,UAAU,OAAO,EAAE,UAAU,SAAU,OAAM,IAAI,MAAM,mCAAmC;AACrH,YAAI,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,YAAY,IAAI,OAAO,EAAE,WAAW,EAAE,EAAE,OAAO,MAAM,SAAU,OAAM,IAAI,MAAM,qCAAqC;AACvJ,YAAI,EAAE,UAAU,EAAE,WAAW,CAAC,GAAG,EAAE,SAAS,OAAO,EAAE,QAAQ,YAAY,CAAC,OAAO,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,GAAI,OAAM,IAAI,MAAM,0DAA0D;AACxL,YAAI,EAAE,YAAY,OAAO,EAAE,YAAY,WAAY,OAAM,IAAI,MAAM,uCAAuC;AAC1G,YAAI,EAAE,WAAW,EAAE,YAAY,SAAS,GAAG;AAAA,QAC3C,GAAG,EAAE,aAAa,EAAE,cAAc,SAAS,GAAG;AAAA,QAC9C,GAAG,EAAE,SAAS,OAAO,EAAE,SAAS,SAAU,OAAM,IAAI,MAAM,0BAA0B;AACpF,YAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,IAAK,OAAM,IAAI,MAAM,qCAAqC;AACvF,YAAI,EAAE,QAAQ,EAAE,SAAS,IAAI,EAAE,UAAU,OAAO,EAAE,UAAU,SAAU,OAAM,IAAI,MAAM,6DAA6D;AACnJ,YAAI,EAAE,SAAS,EAAE,UAAU,OAAO,EAAE,YAAY,OAAO,EAAE,YAAY,UAAW,OAAM,IAAI,MAAM,oCAAoC;AACpI,YAAI,EAAE,WAAW,EAAE,YAAY,OAAI,EAAE,aAAa,OAAO,EAAE,aAAa,SAAU,OAAM,IAAI,MAAM,8BAA8B;AAChI,eAAO,EAAE,YAAY,EAAE,aAAa,SAAS;AAAA,MAC/C,GAAG,IAAI,SAAS,GAAG;AACjB,gBAAQ,EAAE,QAAQ;AAAA,UAChB,KAAK;AACH,gBAAI,IAAI,EAAE,EAAE,MAAM,EAAE,UAAU;AAC9B,mBAAO,EAAE,MAAM,EAAE,MAAM,KAAK,KAAK,EAAE,GAAG,IAAI,EAAE,MAAM,MAAM,IAAI,EAAE,MAAM,MAAM;AAC1E;AAAA,UACF,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,IAAI,WAAW;AACjB,sBAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE;AAAA,YACzF,EAAE,CAAC;AACH,aAAC,EAAE,WAAW,EAAE,IAAI,KAAK,EAAE,cAAc,EAAE,IAAI,OAAO,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,GAAG,MAAM,EAAE,YAAY,SAAS,EAAE,QAAQ,cAAc,IAAI;AAAA,QACvJ;AACA,eAAO;AAAA,MACT,GAAG,IAAI,WAAW;AAChB,YAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC;AAC1E,gBAAQ,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI,QAAQ;AAAA,UACpD,KAAK,EAAE;AACL,mBAAO,EAAE,UAAU,CAAC;AAAA,UACtB,KAAK,EAAE;AACL,mBAAO,EAAE,WAAW,CAAC;AAAA,UACvB,KAAK,EAAE;AACL,mBAAO,EAAE,WAAW,CAAC;AAAA,UACvB;AACE,mBAAO;AAAA,QACX;AAAA,MACF,GAAG,IAAI,SAAS,GAAG;AACjB,eAAO,KAAK,OAAO,IAAI;AAAA,MACzB,GAAG,EAAE,aAAa,IAAI,EAAE,YAAY,oDAAoD,MAAM,uBAAuB,MAAM,kCAAkC,GAAG,SAAS,GAAG,KAAK,SAAS,GAAG;AAC3L,eAAO,EAAE,SAAS,OAAO,EAAE,CAAC;AAAA,MAC9B,GAAG,MAAM,GAAG,WAAW,SAAS,GAAG;AACjC,eAAO,IAAI,EAAE,CAAC,GAAG,EAAE,YAAY,CAAC,EAAE,KAAK,SAAS,GAAG;AACjD,mBAAS,IAAI,IAAI,SAAS,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,OAAO,EAAE,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI;AAC5F,YAAE,SAAS,EAAE,IAAI,KAAK,OAAO,KAAK,EAAE,IAAI,EAAE,QAAQ,SAAS,GAAG;AAC5D,gBAAI,IAAI,EAAE,KAAK,CAAC;AAChB,cAAE,OAAO,GAAG,CAAC;AAAA,UACf,CAAC;AACD,cAAI,IAAI,EAAE;AACV,iBAAO,EAAE,aAAa,WAAW;AAC/B,mBAAO,EAAE,CAAC;AAAA,UACZ,GAAG,EAAE,OAAO,GAAG,EAAE,CAAC;AAAA,QACpB,CAAC;AAAA,MACH,GAAG,aAAa,SAAS,GAAG;AAC1B,eAAO,QAAQ,IAAI,EAAE,CAAC,GAAG,YAAY,EAAE,YAAY,CAAC;AAAA,MACtD,EAAE;AACF,QAAE,UAAU;AAAA,IACd,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,QAAE,EAAE,CAAC;AACL,UAAI,IAAI,EAAE,CAAC;AACX,aAAO,UAAU,OAAO,WAAW,EAAE;AAAA,IACvC,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,OAAC,SAAS,GAAG;AACX,YAAI,IAAI,MAAM,UAAU,KAAK,OAAO,OAAO,OAAO,QAAQ,QAAQ,IAAI,SAAS,UAAU;AACzF,iBAAS,EAAE,GAAG,GAAG;AACf,eAAK,MAAM,GAAG,KAAK,WAAW;AAAA,QAChC;AACA,UAAE,aAAa,WAAW;AACxB,iBAAO,IAAI,EAAE,EAAE,KAAK,YAAY,GAAG,SAAS,GAAG,YAAY;AAAA,QAC7D,GAAG,EAAE,cAAc,WAAW;AAC5B,iBAAO,IAAI,EAAE,EAAE,KAAK,aAAa,GAAG,SAAS,GAAG,aAAa;AAAA,QAC/D,GAAG,EAAE,eAAe,EAAE,gBAAgB,SAAS,GAAG;AAChD,eAAK,EAAE,MAAM;AAAA,QACf,GAAG,EAAE,UAAU,QAAQ,EAAE,UAAU,MAAM,WAAW;AAAA,QACpD,GAAG,EAAE,UAAU,QAAQ,WAAW;AAChC,eAAK,SAAS,KAAK,GAAG,KAAK,GAAG;AAAA,QAChC,GAAG,EAAE,SAAS,SAAS,GAAG,GAAG;AAC3B,uBAAa,EAAE,cAAc,GAAG,EAAE,eAAe;AAAA,QACnD,GAAG,EAAE,WAAW,SAAS,GAAG;AAC1B,uBAAa,EAAE,cAAc,GAAG,EAAE,eAAe;AAAA,QACnD,GAAG,EAAE,eAAe,EAAE,SAAS,SAAS,GAAG;AACzC,uBAAa,EAAE,cAAc;AAC7B,cAAI,IAAI,EAAE;AACV,eAAK,MAAM,EAAE,iBAAiB,WAAW,WAAW;AAClD,cAAE,cAAc,EAAE,WAAW;AAAA,UAC/B,GAAG,CAAC;AAAA,QACN,GAAG,EAAE,CAAC,GAAG,EAAE,eAAe,OAAO,OAAO,OAAO,KAAK,gBAAgB,MAAM,UAAU,EAAE,gBAAgB,QAAQ,KAAK,cAAc,EAAE,iBAAiB,OAAO,OAAO,OAAO,KAAK,kBAAkB,MAAM,UAAU,EAAE,kBAAkB,QAAQ,KAAK;AAAA,MACnP,GAAG,KAAK,MAAM,EAAE,CAAC,CAAC;AAAA,IACpB,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,OAAC,SAAS,GAAG,GAAG;AACd,SAAC,SAAS,GAAG,GAAG;AACd,cAAI,CAAC,EAAE,cAAc;AACnB,gBAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,OAAI,IAAI,EAAE,UAAU,IAAI,OAAO,kBAAkB,OAAO,eAAe,CAAC;AAC9G,gBAAI,KAAK,EAAE,aAAa,IAAI,GAAG,CAAC,EAAE,SAAS,KAAK,EAAE,OAAO,MAAM,qBAAqB,IAAI,SAAS,GAAG;AAClG,gBAAE,SAAS,WAAW;AACpB,kBAAE,CAAC;AAAA,cACL,CAAC;AAAA,YACH,IAAI,WAAW;AACb,kBAAI,EAAE,eAAe,CAAC,EAAE,eAAe;AACrC,oBAAI,IAAI,MAAI,IAAI,EAAE;AAClB,uBAAO,EAAE,YAAY,WAAW;AAC9B,sBAAI;AAAA,gBACN,GAAG,EAAE,YAAY,IAAI,GAAG,GAAG,EAAE,YAAY,GAAG;AAAA,cAC9C;AAAA,YACF,EAAE,KAAK,IAAI,kBAAkB,KAAK,OAAO,IAAI,KAAK,IAAI,SAAS,GAAG;AAChE,gBAAE,WAAW,KAAK,OAAO,EAAE,QAAQ,YAAY,EAAE,KAAK,QAAQ,CAAC,MAAM,KAAK,EAAE,CAAC,EAAE,KAAK,MAAM,EAAE,MAAM,CAAC;AAAA,YACrG,GAAG,EAAE,mBAAmB,EAAE,iBAAiB,WAAW,GAAG,KAAE,IAAI,EAAE,YAAY,aAAa,CAAC,GAAG,IAAI,SAAS,GAAG;AAC5G,gBAAE,YAAY,IAAI,GAAG,GAAG;AAAA,YAC1B,KAAK,EAAE,mBAAmB,IAAI,IAAI,eAAe,GAAG,MAAM,YAAY,SAAS,GAAG;AAChF,gBAAE,EAAE,IAAI;AAAA,YACV,GAAG,IAAI,SAAS,GAAG;AACjB,gBAAE,MAAM,YAAY,CAAC;AAAA,YACvB,KAAK,KAAK,wBAAwB,EAAE,cAAc,QAAQ,KAAK,IAAI,EAAE,iBAAiB,IAAI,SAAS,GAAG;AACpG,kBAAI,IAAI,EAAE,cAAc,QAAQ;AAChC,gBAAE,qBAAqB,WAAW;AAChC,kBAAE,CAAC,GAAG,EAAE,qBAAqB,MAAM,EAAE,YAAY,CAAC,GAAG,IAAI;AAAA,cAC3D,GAAG,EAAE,YAAY,CAAC;AAAA,YACpB,KAAK,IAAI,SAAS,GAAG;AACnB,yBAAW,GAAG,GAAG,CAAC;AAAA,YACpB,GAAG,EAAE,eAAe,SAAS,GAAG;AAC9B,qBAAO,KAAK,eAAe,IAAI,IAAI,SAAS,KAAK,CAAC;AAClD,uBAAS,IAAI,IAAI,MAAM,UAAU,SAAS,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,CAAC,IAAI,UAAU,IAAI,CAAC;AAC9F,kBAAI,IAAI,EAAE,UAAU,GAAG,MAAM,EAAE;AAC/B,qBAAO,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG;AAAA,YACzB,GAAG,EAAE,iBAAiB;AAAA,UACxB;AACA,mBAAS,EAAE,GAAG;AACZ,mBAAO,EAAE,CAAC;AAAA,UACZ;AACA,mBAAS,EAAE,GAAG;AACZ,gBAAI,EAAG,YAAW,GAAG,GAAG,CAAC;AAAA,iBACpB;AACH,kBAAI,IAAI,EAAE,CAAC;AACX,kBAAI,GAAG;AACL,oBAAI;AACJ,oBAAI;AACF,mBAAC,SAAS,GAAG;AACX,wBAAI,IAAI,EAAE,UAAU,IAAI,EAAE;AAC1B,4BAAQ,EAAE,QAAQ;AAAA,sBAChB,KAAK;AACH,0BAAE;AACF;AAAA,sBACF,KAAK;AACH,0BAAE,EAAE,CAAC,CAAC;AACN;AAAA,sBACF,KAAK;AACH,0BAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACZ;AAAA,sBACF,KAAK;AACH,0BAAE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAClB;AAAA,sBACF;AACE,0BAAE,MAAM,GAAG,CAAC;AAAA,oBAChB;AAAA,kBACF,GAAG,CAAC;AAAA,gBACN,UAAE;AACA,oBAAE,CAAC,GAAG,IAAI;AAAA,gBACZ;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,GAAG,OAAO,OAAO,MAAM,MAAM,SAAS,OAAO,IAAI,IAAI;AAAA,MACvD,GAAG,KAAK,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC1B,GAAG,SAAS,GAAG,GAAG;AAChB,UAAI,GAAG,GAAG,IAAI,EAAE,UAAU,CAAC;AAC3B,eAAS,IAAI;AACX,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,eAAS,IAAI;AACX,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACrD;AACA,eAAS,EAAE,GAAG;AACZ,YAAI,MAAM,WAAY,QAAO,WAAW,GAAG,CAAC;AAC5C,aAAK,MAAM,KAAK,CAAC,MAAM,WAAY,QAAO,IAAI,YAAY,WAAW,GAAG,CAAC;AACzE,YAAI;AACF,iBAAO,EAAE,GAAG,CAAC;AAAA,QACf,QAAQ;AACN,cAAI;AACF,mBAAO,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,UAC1B,QAAQ;AACN,mBAAO,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AACA,OAAC,WAAW;AACV,YAAI;AACF,cAAI,OAAO,cAAc,aAAa,aAAa;AAAA,QACrD,QAAQ;AACN,cAAI;AAAA,QACN;AACA,YAAI;AACF,cAAI,OAAO,gBAAgB,aAAa,eAAe;AAAA,QACzD,QAAQ;AACN,cAAI;AAAA,QACN;AAAA,MACF,GAAG;AACH,UAAI,GAAG,IAAI,CAAC,GAAG,IAAI,OAAI,IAAI;AAC3B,eAAS,IAAI;AACX,aAAK,MAAM,IAAI,OAAI,EAAE,SAAS,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,UAAU,EAAE;AAAA,MACxE;AACA,eAAS,IAAI;AACX,YAAI,CAAC,GAAG;AACN,cAAI,IAAI,EAAE,CAAC;AACX,cAAI;AACJ,mBAAS,IAAI,EAAE,QAAQ,KAAK;AAC1B,iBAAK,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAK,MAAK,EAAE,CAAC,EAAE,IAAI;AAC7C,gBAAI,IAAI,IAAI,EAAE;AAAA,UAChB;AACA,cAAI,MAAM,IAAI,OAAI,SAAS,GAAG;AAC5B,gBAAI,MAAM,aAAc,QAAO,aAAa,CAAC;AAC7C,iBAAK,MAAM,KAAK,CAAC,MAAM,aAAc,QAAO,IAAI,cAAc,aAAa,CAAC;AAC5E,gBAAI;AACF,gBAAE,CAAC;AAAA,YACL,QAAQ;AACN,kBAAI;AACF,uBAAO,EAAE,KAAK,MAAM,CAAC;AAAA,cACvB,QAAQ;AACN,uBAAO,EAAE,KAAK,MAAM,CAAC;AAAA,cACvB;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AACA,eAAS,EAAE,GAAG,GAAG;AACf,aAAK,MAAM,GAAG,KAAK,QAAQ;AAAA,MAC7B;AACA,eAAS,IAAI;AAAA,MACb;AACA,QAAE,WAAW,SAAS,GAAG;AACvB,YAAI,IAAI,IAAI,MAAM,UAAU,SAAS,CAAC;AACtC,YAAI,UAAU,SAAS,EAAG,UAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,GAAE,IAAI,CAAC,IAAI,UAAU,CAAC;AAC3F,UAAE,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,WAAW,KAAK,KAAK,EAAE,CAAC;AAAA,MACjD,GAAG,EAAE,UAAU,MAAM,WAAW;AAC9B,aAAK,IAAI,MAAM,MAAM,KAAK,KAAK;AAAA,MACjC,GAAG,EAAE,QAAQ,WAAW,EAAE,UAAU,MAAI,EAAE,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,UAAU,IAAI,EAAE,WAAW,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,cAAc,GAAG,EAAE,OAAO,GAAG,EAAE,MAAM,GAAG,EAAE,iBAAiB,GAAG,EAAE,qBAAqB,GAAG,EAAE,OAAO,GAAG,EAAE,kBAAkB,GAAG,EAAE,sBAAsB,GAAG,EAAE,YAAY,SAAS,GAAG;AAC5R,eAAO,CAAC;AAAA,MACV,GAAG,EAAE,UAAU,SAAS,GAAG;AACzB,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACpD,GAAG,EAAE,MAAM,WAAW;AACpB,eAAO;AAAA,MACT,GAAG,EAAE,QAAQ,SAAS,GAAG;AACvB,cAAM,IAAI,MAAM,gCAAgC;AAAA,MAClD,GAAG,EAAE,QAAQ,WAAW;AACtB,eAAO;AAAA,MACT;AAAA,IACF,GAAG,SAAS,GAAG,GAAG,GAAG;AACnB,eAAS,EAAE,GAAG,GAAG;AACf,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAI,IAAI,EAAE,CAAC;AACX,YAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,EAAE,KAAK,CAAC;AAAA,QAC9H;AAAA,MACF;AACA,UAAI,IAAI,EAAE,CAAC;AACX,QAAE,UAAU,WAAW;AACrB,iBAAS,IAAI;AACX,WAAC,SAAS,GAAG,GAAG;AACd,gBAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAAA,UAChF,GAAG,MAAM,CAAC;AAAA,QACZ;AACA,YAAI,GAAG,GAAG;AACV,eAAO,IAAI,GAAG,IAAI,CAAC,EAAE,KAAK,aAAa,OAAO,SAAS,GAAG;AACxD,iBAAO,EAAE,CAAC;AAAA,QACZ,EAAE,GAAG,EAAE,KAAK,cAAc,OAAO,SAAS,GAAG;AAC3C,iBAAO,KAAK,UAAU,CAAC;AAAA,QACzB,EAAE,GAAG,EAAE,KAAK,cAAc,OAAO,SAAS,GAAG;AAC3C,cAAI,KAAK,WAAW,CAAC,EAAG,QAAO;AAC/B,cAAI,KAAK,cAAc,CAAC,EAAG,QAAO,IAAI,SAAS,CAAC;AAChD,cAAI,KAAK,SAAS,CAAC,GAAG;AACpB,gBAAI,IAAI,IAAI,SAAS;AACrB,mBAAO,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAG;AACxC,kBAAI,IAAI,EAAE,CAAC;AACX,gBAAE,OAAO,GAAG,CAAC;AAAA,YACf,CAAC,GAAG;AAAA,UACN;AACA,gBAAM,IAAI,MAAM,sEAAsE;AAAA,QACxF,EAAE,GAAG,EAAE,KAAK,YAAY,OAAO,SAAS,GAAG;AACzC,iBAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAAA,QAC/C,EAAE,GAAG,EAAE,KAAK,cAAc,OAAO,SAAS,GAAG;AAC3C,iBAAO,aAAa;AAAA,QACtB,EAAE,GAAG,EAAE,KAAK,iBAAiB,OAAO,SAAS,GAAG;AAC9C,iBAAO,aAAa;AAAA,QACtB,EAAE,GAAG,EAAE,KAAK,eAAe,OAAO,WAAW;AAC3C,cAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC;AAC1E,iBAAO,IAAI,QAAQ,SAAS,GAAG,GAAG;AAChC,gBAAI,IAAI,SAAS,cAAc,OAAO;AACtC,cAAE,OAAO,QAAQ,EAAE,YAAY,EAAE,aAAa,YAAY,UAAU,GAAG,EAAE,UAAU,EAAE,aAAa,UAAU,EAAE,MAAM,GAAG,EAAE,MAAM,UAAU,QAAQ,SAAS,KAAK,YAAY,CAAC,GAAG,EAAE,iBAAiB,UAAU,SAAS,GAAG;AACtN,kBAAI,IAAI,EAAE,OAAO;AACjB,gBAAE,CAAC,GAAG,SAAS,KAAK,YAAY,CAAC;AAAA,YACnC,GAAG,KAAE,GAAG,EAAE,MAAM;AAAA,UAClB,CAAC;AAAA,QACH,EAAE,GAAG,EAAE,KAAK,gBAAgB,OAAO,SAAS,GAAG;AAC7C,cAAI,IAAI,EAAE,KAAK,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC;AACxC,iBAAO,EAAE,QAAQ,SAAS,GAAG;AAC3B,gBAAI,IAAI,EAAE,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,KAAK,IAAI;AACrD,kBAAM,EAAE,CAAC,IAAI;AAAA,UACf,CAAC,GAAG;AAAA,QACN,EAAE,CAAC,IAAI,IAAI,SAAS,EAAE,EAAE,WAAW,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AAAA,MACvD,EAAE;AAAA,IACJ,GAAG,SAAS,GAAG,GAAG;AAChB,UAAI,IAAI,SAAS,GAAG;AAClB,eAAO,mBAAmB,CAAC,EAAE,QAAQ,YAAY,MAAM,EAAE,QAAQ,QAAQ,GAAG;AAAA,MAC9E,GAAG,IAAI,SAAS,GAAG,GAAG,GAAG,GAAG;AAC1B,eAAO,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,SAAS,GAAG;AACjE,mBAAS,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,GAAE,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACvE,iBAAO;AAAA,QACT,EAAE,OAAO,KAAK,CAAC,EAAE,IAAI,SAAS,GAAG;AAC/B,cAAI,GAAG,GAAG,IAAI;AACd,cAAI,MAAM,IAAI,IAAI,MAAM,IAAI,MAAM,OAAO,EAAE,CAAC,KAAK,YAAY,EAAE,CAAC,MAAM,KAAM,KAAI,EAAE,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC;AAAA,eAC7F;AACH,kBAAM,IAAI,GAAG,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;AACxE,gBAAI,IAAI,EAAE,CAAC;AACX,iBAAK,KAAK,KAAK,IAAI,MAAM,OAAK,MAAM,OAAO,QAAK,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC;AAAA,UACvG;AACA,iBAAO;AAAA,QACT,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,YAAY,EAAE,IAAI;AAAA,MACxC;AACA,QAAE,UAAU;AAAA,IACd,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,GAAG,CAAC;AACJ,IAAI,IAAI,EAAE;AACV,IAAM,IAAoB,EAAE,CAAC;AAC7B,SAAS,EAAE,GAAG;AACZ,SAAO,MAAM,UAAU,OAAO,EAAE,QAAQ;AAC1C;AACA,IAAM,IAAN,MAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAON,YAAY,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,EAAE,GAAG;AAClD,SAAK,SAAS,GAAG,KAAK,WAAW,GAAG,KAAK,UAAU;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,EAAE,WAAW,EAAE,GAAG;AACnC,UAAM,IAAI,SAAS,GAAG;AACpB,YAAM,IAAI,IAAI,WAAW;AACzB,QAAE,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,MAAM;AACpC,UAAE,EAAE,OAAO,MAAM;AAAA,MACnB;AAAA,IACF;AACA,QAAI;AACJ,QAAI,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,SAAS,gBAAgB,YAAY;AAClF,YAAM,IAAI,KAAK,OAAO,SAAS;AAC/B,UAAI,EAAE,YAAY,EAAE,QAAQ,KAAK,OAAO,SAAS,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM;AACxE,UAAE,EAAE,CAAC,CAAC;AACN,cAAM,IAAI,EAAE,EAAE,CAAC,CAAC;AAChB,eAAO,EAAE,CAAC,KAAK,QAAQ,KAAK,6DAA6D,GAAG;AAAA,MAC9F,CAAC;AAAA,IACH;AACE,UAAI,EAAE,UAAU;AAAA,QACd,KAAK,KAAK,OAAO,UAAU;AAAA,QAC3B,MAAM,KAAK,OAAO;AAAA,QAClB,QAAQ,KAAK,OAAO,SAAS;AAAA,QAC7B,SAAS,KAAK,OAAO;AAAA,QACrB,YAAY,CAAC,MAAM;AACjB,YAAE,EAAE,CAAC,CAAC;AAAA,QACR;AAAA,QACA,WAAW,KAAK,OAAO,SAAS;AAAA,MAClC,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI;AACvB,MAAE,KAAK,CAAC,MAAM;AACZ,WAAK,SAAS,CAAC;AAAA,IACjB,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,WAAK,QAAQ,CAAC;AAAA,IAChB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG;AACb,QAAI;AACJ,SAAK,OAAO,YAAY,OAAO,KAAK,OAAO,SAAS,eAAe,cAAc,IAAI,KAAK,OAAO,SAAS,YAAY,CAAC,GAAG,EAAE,CAAC,KAAK,QAAQ,KAAK,4DAA4D,KAAK,IAAI,EAAE,KAAK;AAAA,MACzN,KAAK,KAAK,OAAO,UAAU;AAAA,MAC3B,MAAM,OAAO,OAAO;AAAA,QAClB,KAAK;AAAA,MACP,GAAG,KAAK,OAAO,qBAAqB;AAAA,MACpC,MAAM,EAAE,YAAY;AAAA,MACpB,SAAS,KAAK,OAAO;AAAA,IACvB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,KAAK,CAAC,MAAM;AACpC,WAAK,SAAS,CAAC;AAAA,IACjB,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,WAAK,QAAQ,CAAC;AAAA,IAChB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG,EAAE,WAAW,EAAE,GAAG;AAChC,UAAM,IAAI,IAAI,WAAW;AACzB,MAAE,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,MAAM;AACpC,QAAE,EAAE,OAAO,MAAM;AAAA,IACnB;AACA,QAAI;AACJ,QAAI,KAAK,OAAO,YAAY,OAAO,KAAK,OAAO,SAAS,gBAAgB;AACtE,UAAI,KAAK,OAAO,SAAS,aAAa,CAAC,GAAG,EAAE,CAAC,KAAK,QAAQ,KAAK,6DAA6D;AAAA,SACzH;AACH,YAAM,IAAI,IAAI,SAAS;AACvB,QAAE,OAAO,KAAK,OAAO,SAAS,SAAS,CAAC,GAAG,KAAK,OAAO,yBAAyB,OAAO,KAAK,KAAK,OAAO,qBAAqB,EAAE,UAAU,OAAO,QAAQ,KAAK,OAAO,qBAAqB,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AAC7M,UAAE,OAAO,GAAG,CAAC;AAAA,MACf,CAAC,GAAG,IAAI,EAAE,KAAK;AAAA,QACb,KAAK,KAAK,OAAO,UAAU;AAAA,QAC3B,MAAM;AAAA,QACN,MAAM,EAAE,YAAY;AAAA,QACpB,SAAS,KAAK,OAAO;AAAA,MACvB,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI;AAAA,IACvB;AACA,MAAE,KAAK,CAAC,MAAM;AACZ,WAAK,SAAS,CAAC;AAAA,IACjB,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,WAAK,QAAQ,CAAC;AAAA,IAChB,CAAC;AAAA,EACH;AACF;AA8BA,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASN,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,GAAG,OAAO,EAAE,GAAG;AACjE,SAAK,mBAAmB,MAAM,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS;AAAA,MACxE,WAAW,EAAE;AAAA,MACb,uBAAuB,EAAE;AAAA,MACzB,0BAA0B,EAAE;AAAA,MAC5B,OAAO,EAAE;AAAA,MACT,OAAO,EAAE;AAAA,MACT,oBAAoB,KAAK,IAAI,KAAK,EAAE,EAAE,sBAAsB,SAAS;AAAA,MACrE,eAAe,EAAE;AAAA,MACjB,UAAU,EAAE;AAAA,MACZ,SAAS,EAAE;AAAA,MACX,UAAU,EAAE,YAAY,CAAC;AAAA,IAC3B,GAAG,KAAK,WAAW,IAAI,EAAE;AAAA,MACvB,QAAQ,KAAK;AAAA,MACb,UAAU,CAAC,MAAM,KAAK,SAAS,CAAC;AAAA,MAChC,SAAS,CAAC,MAAM,KAAK,gBAAgB,CAAC;AAAA,IACxC,CAAC,GAAG,KAAK,KAAK,IAAI,EAAE;AAAA,MAClB,KAAK;AAAA,MACL,QAAQ,KAAK;AAAA,MACb,cAAc,MAAM;AAClB,aAAK,SAAS,mBAAmB;AAAA,UAC/B,WAAW,CAAC,MAAM;AAChB,iBAAK,GAAG,cAAc,CAAC;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,UAAU;AAAA,IACZ,CAAC,GAAG,KAAK,QAAQ;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,MAAM;AAAA,QACJ,KAAK;AAAA,MACP;AAAA,IACF,GAAG,KAAK,OAAO;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,QAAQ;AACjB,WAAO;AAAA,MACL;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,GAAG,GAAG;AACV,cAAU,IAAI,KAAK,OAAO,aAAa,OAAO,SAAS,EAAE,aAAa,UAAQ,IAAI,KAAK,OAAO,aAAa,OAAO,SAAS,EAAE,aAAa,YAAY,IAAI,KAAK,OAAO,aAAa,OAAO,SAAS,EAAE,aAAa,cAAc,KAAK,KAAK,aAAa,KAAK,mBAAmB,OAAK,KAAK,GAAG,OAAO;AAAA,EACrS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG;AACV,WAAO,CAAC,CAAC,EAAE,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,UAAM,IAAI,KAAK,GAAG,MAAM;AACxB,WAAO,KAAK,MAAM,UAAU,EAAE,WAAW,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,QAAI;AACJ,UAAM,IAAI,GAAE,MAAM,OAAO,KAAK,OAAO,WAAW,CAAC,CAAC,GAAG,IAAI;AAAA,MACvD,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,MAAE,IAAI,KAAK,OAAO,aAAa,OAAO,SAAS,EAAE,aAAa,cAAc,EAAE,KAAK;AAAA,MACjF,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,IAAI,EAAE,OAAO,CAAC,MAAM;AACxB,UAAI,GAAG;AACP,YAAM,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI;AACpD,aAAO,MAAM,cAAc,IAAI,KAAK,OAAO,aAAa,OAAO,SAAS,EAAE,aAAa,QAAK,KAAK,UAAU,IAAI,KAAK,OAAO,aAAa,OAAO,SAAS,EAAE,CAAC,OAAO;AAAA,IACpK,CAAC,GAAG,IAAI,CAAC,MAAM;AACb,UAAI,IAAI,KAAK,KAAK,EAAE,IAAI;AACxB,aAAO,EAAE,SAAS,cAAc,IAAI,KAAK,oBAAoB,IAAI;AAAA,IACnE;AACA,WAAO,EAAE,IAAI,CAAC,OAAO;AAAA,MACnB,MAAM,EAAE;AAAA,MACR,OAAO,KAAK,IAAI,KAAK,EAAE,EAAE,KAAK;AAAA,MAC9B,MAAM,EAAE;AAAA,MACR,QAAQ,EAAE;AAAA,MACV,UAAU,EAAE,CAAC;AAAA,MACb,YAAY,MAAM;AAChB,YAAI,OAAO,EAAE,UAAU,YAAY;AACjC,YAAE,OAAO,EAAE,IAAI;AACf;AAAA,QACF;AACA,YAAI,IAAI,CAAC,EAAE,CAAC;AACZ,UAAE,SAAS,cAAc,KAAK,mBAAmB,EAAE,KAAK,oBAAoB,QAAK,IAAI,KAAK,mBAAmB,KAAK,YAAY,EAAE,MAAM,CAAC;AAAA,MACzI;AAAA,IACF,EAAE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,SAAK,GAAG,MAAM,WAAW,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,cAAc;AACvB,WAAO;AAAA;AAAA;AAAA;AAAA,MAIL,MAAM;AAAA,QACJ;AAAA,UACE,KAAK,EAAE,KAAK,KAAG;AAAA,QACjB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAIA,OAAO;AAAA,QACL,WAAW,CAAC,SAAS;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,QAAQ,GAAG;AAAA;AACf,cAAQ,EAAE,MAAM;AAAA,QACd,KAAK,OAAO;AACV,gBAAM,IAAI,EAAE,OAAO;AACnB,cAAI,SAAS,KAAK,EAAE,GAAG,GAAG;AACxB,kBAAM,IAAI,OAAO,MAAM,MAAM,EAAE,GAAG,GAAG,KAAK;AAC1C,iBAAK,WAAW,CAAC;AACjB;AAAA,UACF;AACA,eAAK,UAAU,EAAE,GAAG;AACpB;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AACd,gBAAM,IAAI,EAAE,OAAO;AACnB,eAAK,UAAU,CAAC;AAChB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,gBAAM,IAAI,EAAE,OAAO;AACnB,eAAK,WAAW,CAAC;AACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,KAAK,GAAG;AACV,SAAK,QAAQ,EAAE,MAAM,KAAK,MAAM,UAAU,EAAE,WAAW,IAAI,KAAK,GAAG,YAAY,KAAK,MAAM,OAAO,GAAG,GAAE,MAAM,QAAQ,CAAC,EAAE,MAAM,EAAE,MAAM;AACnI,YAAM,IAAI,OAAO,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,MAAM,QAAM,EAAE,CAAC,MAAM,SAAS;AAC/D,WAAK,QAAQ,GAAG,CAAC;AAAA,IACnB,CAAC,GAAG,EAAE,WAAW,KAAK,QAAQ,WAAW,IAAE;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,MAAM,GAAG;AACX,SAAK,MAAM,OAAO,KAAK,EAAE,KAAK,GAAG,GAAG,KAAK,EAAE,OAAO,KAAK,GAAG,UAAU,EAAE,GAAG;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,GAAG;AACV,MAAE,WAAW,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,KAAK,gBAAgB,yBAAyB,KAAK,UAAU,CAAC,CAAC;AAAA,EAC7G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,GAAG;AACjB,YAAQ,IAAI,2CAA2C,CAAC,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,MAChF,SAAS,KAAK,IAAI,KAAK,EAAE,4CAA4C;AAAA,MACrE,OAAO;AAAA,IACT,CAAC,GAAG,KAAK,GAAG,cAAc;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG,GAAG;AAChB,UAAM,aAAa,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,KAAK,UAAO,KAAK,MAAM,UAAU,IAAI,KAAK,GAAG,YAAY,EAAE,MAAM,KAAK,QAAQ,GAAG,CAAC;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG,GAAG;AACZ,SAAK,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,MAAM,eAAe,QAAQ,QAAQ,EAAE,KAAK,MAAM;AAC5F,WAAK,MAAM,YAAY;AAAA,IACzB,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,cAAQ,MAAM,CAAC;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,GAAG;AACZ,SAAK,SAAS,aAAa,GAAG;AAAA,MAC5B,WAAW,CAAC,MAAM;AAChB,aAAK,GAAG,cAAc,CAAC;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,GAAG;AACX,SAAK,GAAG,cAAc,CAAC,GAAG,KAAK,SAAS,YAAY,CAAC;AAAA,EACvD;AACF;", "names": []}
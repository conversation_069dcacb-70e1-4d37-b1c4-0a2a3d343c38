/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Animation for scale in effect */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-in-out;
}

/* Card styling and hover effects */
/* .group {
  transition: all 0.3s ease;
  border: 1px solid var(--hover-blue-gray);
}

.group:hover {
  transform: translateX(4px);
  border-color: var(--primary-purple);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
} */

/* Text truncation for card content */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Sticky header for better UX */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-light-gray);
  backdrop-filter: blur(5px);
}

/* Action buttons animation */
.action-button {
  opacity: 0.9;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.action-button:hover {
  opacity: 1;
  transform: translateY(-2px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Card hover button animations */
.agent-card .opacity-0 {
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.agent-card:hover .opacity-0 {
  opacity: 1 !important;
  transform: translateY(0);
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.02em;
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.status-badge i {
  color: white;
}

.status-badge.model {
  background-color: #5D3FD3;
  border: none;
}

.status-badge.tool {
  background-color: #10B981;
  border: none;
}

.status-badge.plugin {
  background-color: #5D3FD3;
  border: none;
}

/* Dark mode support for status badges */
:host-context(.dark-theme) .status-badge.model {
  background-color: #5D3FD3;
  border: none;
}

:host-context(.dark-theme) .status-badge.tool {
  background-color: #10B981;
  border: none;
}

:host-context(.dark-theme) .status-badge.plugin {
  background-color: #5D3FD3;
  border: none;
}

/* File card specific styling */
.agent-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 0.375rem;
  overflow: hidden;
}

/* .agent-card:hover {
  transform: translateY(-4px);
  border-color: var(--primary-purple);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
} */

/* Button styling */
.primary-button {
  background-color: var(--primary-purple);
  color: white;
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.primary-button:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.secondary-button {
  background-color: var(--hover-blue-gray);
  color: var(--text-dark);
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.secondary-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .agent-card .opacity-0 {
    opacity: 1 !important;
    transform: translateY(0);
  }
}

/* Footer Pagination styles */
.pagination-controls {
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--hover-blue-gray-rgb, 230, 230, 230), 0.3);
  overflow: hidden;
  padding: 0.5rem;
}

.pagination-controls button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  font-size: 1rem;
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.pagination-controls button:hover:not(:disabled):not(.bg-\[var\(--primary-purple\)\]) {
  background-color: var(--hover-blue-gray);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pagination-controls button:active:not(:disabled) {
  transform: translateY(0);
}

.pagination-controls button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.pagination-controls button:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

/* Current page button styles */
.pagination-controls .bg-\[var\(--primary-purple\)\] {
  box-shadow: 0 2px 8px rgba(var(--primary-purple-rgb, 100, 50, 255), 0.5);
  position: relative;
  background: linear-gradient(45deg, var(--primary-purple), var(--secondary-purple));
  font-size: 1rem;
  font-weight: bold;
}

/* Fixed pagination container */
.fixed.bottom-0 {
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.3);
}

/* Dark mode specific styles */
.bg-\[\#2a2a2a\] {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.bg-\[\#2a2a2a\] button {
  font-weight: bold;
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.bg-\[\#2a2a2a\] button:hover {
  background-color: #444444 !important;
}

/* Ensure text is always visible */
.pagination-controls span,
.pagination-controls div {
  color: white !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Make disabled buttons visible */
.pagination-controls button:disabled {
  opacity: 0.7 !important;
  color: #aaaaaa !important;
}

/* Page size selector styles */
.pagination-controls select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1em;
  padding-right: 2rem;
}

/* Pagination animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pagination-fade-in {
  animation: fadeInUp 0.3s ease-out;
}

/* Pagination border styles */
.pagination-controls .border-l,
.pagination-controls .border-r {
  border-color: rgba(var(--hover-blue-gray-rgb, 230, 230, 230), 0.5);
}

/* Fixed pagination styles */
.fixed.bottom-0 {
  backdrop-filter: blur(8px);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.fixed.bottom-0 .pagination-controls {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive pagination */
@media (max-width: 640px) {
  .pagination-controls {
    padding: 0.25rem;
  }

  .pagination-controls .border-l,
  .pagination-controls .border-r {
    border: none;
  }

  .pagination-controls button {
    padding: 0.375rem;
  }

  .pagination-controls .w-9 {
    width: 2rem;
  }

  .pagination-controls .h-9 {
    height: 2rem;
  }

  .fixed.bottom-0 {
    padding: 0.5rem 0.25rem;
  }

  .fixed.bottom-0 .pagination-controls {
    width: 100%;
  }
}

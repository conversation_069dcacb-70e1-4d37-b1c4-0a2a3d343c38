import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-source-references',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink
  ],
  templateUrl: './source-references.component.html',
  styleUrls: ['./source-references.component.css']
})
export class SourceReferencesComponent {
  // Inject ThemeService
  themeService = inject(ThemeService);

  // Input properties
  @Input() showSidebar: boolean = false;
  @Input() searchResults: any[] = [];
  @Input() currentSourceName: string = '';

  // Output events
  @Output() closeSidebar = new EventEmitter<void>();

  /**
   * Closes the sidebar
   */
  onCloseSidebar(): void {
    this.closeSidebar.emit();
  }

  /**
   * Opens a search result URL in a new tab
   * @param url The URL to open
   */
  openSearchResult(url: string): void {
    if (url && url !== '#') {
      window.open(url, '_blank');
    }
  }
}

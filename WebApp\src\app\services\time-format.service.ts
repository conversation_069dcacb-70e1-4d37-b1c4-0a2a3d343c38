import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, interval } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TimeFormatService {
  private currentTimeSubject = new BehaviorSubject<number>(Date.now());
  public currentTime$ = this.currentTimeSubject.asObservable();

  constructor() {
    // Update the time every minute (60000 ms)
    interval(60000).subscribe(() => {
      this.currentTimeSubject.next(Date.now());
    });
  }

  /**
   * Formats a timestamp into a relative time string (e.g., "2 minutes ago")
   * @param timestamp Timestamp in milliseconds
   * @returns Formatted time string
   */  getFormattedTime(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const absDiff = Math.abs(diff);

    // Calculate time units without seconds
    const minutes = Math.floor(absDiff / 60000); // 60000 ms in a minute
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    // More precise month/year calculation using Date objects
    const currentDate = new Date();
    const pastDate = new Date(timestamp);
    let months =
      (currentDate.getFullYear() - pastDate.getFullYear()) * 12 +
      (currentDate.getMonth() - pastDate.getMonth());
    const years = Math.floor(months / 12);

    // Handle negative differences (future timestamps)
    const isFuture = diff < 0;
    const suffix = isFuture ? ' from now' : ' ago';

    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''}${suffix}`;
    } else if (months > 0) {
      return `${months} month${months > 1 ? 's' : ''}${suffix}`;
    } else if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}${suffix}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}${suffix}`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''}${suffix}`;
    } else {
      // If less than a minute has passed, show "just now"
      return 'just now';
    }
  }

  /**
   * Returns an observable that emits the formatted time and updates every minute
   * @param timestamp Timestamp in milliseconds
   * @returns Observable that emits formatted time string
   */
  getFormattedTimeObservable(timestamp: number): Observable<string> {
    return this.currentTime$.pipe(
      map(() => this.getFormattedTime(timestamp))
    );
  }
}

import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { UserAccountServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [FormsModule, CommonModule, NzDropDownModule, SpinnerComponent],
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css'],
})
export class UsersManagementComponent {
  showAddUserDialog = false;
  searchTerm = '';
  selectedRole = '';
  isLoading = false; // Loading state for spinner
  newUser: any = { name: '', email: '', role: '', skills: '', password: '' };

  users: any = [];

  constructor(
    private userAccountService: UserAccountServiceProxy,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers() {
    this.isLoading = true; // Show spinner
    this.userAccountService.getAll().subscribe({
      next: (users: any) => {
        this.users = users;
        console.log(this.users);
        this.isLoading = false; // Hide spinner
      },
      error: (error: any) => {
        console.error('Error loading users:', error);
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  get filteredUsers() {
    let result = [...this.users];

    // Search filter
    if (this.searchTerm) {
      result = result.filter(
        (user) =>
          user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (this.selectedRole) {
      result = result.filter((user) => user.roles.includes(this.selectedRole));
    }

    return result;
  }

  toggleRole(user: any) {
    let role = user.role === 'User' ? 'Admin' : 'User';
    // Update the user in the backend
    this.userAccountService.assignRole(user.email, role).subscribe(() => {
      console.log('Role toggled for:', user.name, 'to:', user.role);
    });
  }

  // Method to trigger filtering (used by UI elements)
  filterUsers() {
    // The actual filtering logic is in the filteredUsers getter
    // This method exists to be called from the template
    return this.filteredUsers;
  }

  addUser() {
    if (this.newUser.name && this.newUser.email && this.newUser.role) {
      this.userAccountService.register(this.newUser).subscribe((res: any) => {
        this.message.success('User added successfully!');
        this.loadUsers();
        this.showAddUserDialog = false;
        console.log(res);
        this.resetFields();
      });
    } else {
      console.log('Please fill all required fields');
    }
  }

  resetFields() {
    this.newUser = {
      name: '',
      email: '',
      role: '',
      skills: '',
      password: '',
    };
  }

  deleteUser(user: any) {
    this.userAccountService.deleteUser(user.email).subscribe((res) => {
      this.loadUsers();
      console.log('User deleted:', res);
    });
  }

  generatePassword() {
    let pass = Math.random().toString(36).slice(-8);
    this.newUser.password = pass;
  }
  assignRole(user: any, role: string) {
    this.userAccountService
      .assignRole(user.email, role)
      .subscribe((res: any) => {
        this.message.success('Role assigned successfully!');
        this.loadUsers();

        console.log(res);
      });
  }
  removeRole(user: any, role: string) {
    this.userAccountService
      .removeRole(user.email, role)
      .subscribe((res: any) => {
        this.message.success('Role removed successfully!');
        this.loadUsers();
        console.log(res);
      });
  }
  showPassword = false;
}

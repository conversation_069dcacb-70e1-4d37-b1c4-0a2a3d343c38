/* Theme variables */
:root {
  /* Sidebar theme colors */
  --sidebar-bg: #2b2b33;
  --sidebar-text: #ffffff;
  --sidebar-border: #3a3a45;
  --sidebar-hover: #3a3a45;
  --sidebar-active: #1e1e24;
  --sidebar-active-text: #ffffff;
  --sidebar-button-text: #8b8b8b;
  --sidebar-active-indicator: #00c39a;
}

/* Dark theme variables */
.dark-theme {
  --sidebar-bg: #1e1e24;
  --sidebar-text: #ffffff;
  --sidebar-border: #2b2b33;
  --sidebar-hover: #2b2b33;
  --sidebar-active: #1a1a1f;
  --sidebar-active-text: #ffffff;
  --sidebar-button-text: #a0a0a0;
  --sidebar-active-indicator: #00c39a;
}

.hideSidebar {
  translate: -100%;
}
.showSidebar {
  translate: 0%;
}

/* Styles for collapsed sidebar */
.sidebar-collapsed .expanded-sidebar {
  display: none !important;
}

/* Show only the icon bar when collapsed */
.sidebar-collapsed .flex-1 {
  display: none;
}

/* Microsoft Teams-like styling */
.sidebar-collapsed {
  width: 6rem !important;
}

/* Adjust the right content area when sidebar is collapsed */
.sidebar-collapsed + .main-content {
  margin-left: 6rem;
}

/* Theme-aware sidebar styling */
.sidebar-main {
  background-color: var(--hover-blue-gray);
  border-right: 2px solid var(--sidebar-border);
  color: var(--sidebar-text);
}

/* Teams-style sidebar navigation */
.sidebar-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  width: 100%;
  transition: all 0.2s ease;
  padding: 8px 0;
  margin-bottom: 12px;
}

/* Full-width button styling with vertical layout */
.sidebar-nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 8px 0;
  border-radius: 4px;
  margin: 2px 0;
  background-color: transparent;
  border: none;
  outline: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

/* Active tab styling for both dark and light modes */
:host-context(.dark-mode) .active-tab {
  background-color: var(--secondary-purple);
}

:host-context(.dark-mode) .active-tab i,
:host-context(.dark-mode) .active-tab span {
  color: white !important;
}

.active-tab {
  background-color: var(--secondary-purple) !important;
  font-weight: 500;
  position: relative;
}

.active-tab i,
.active-tab span {
  color: black !important;
  font-weight: 600;
}

/* Active button icon styling */
button[class*="!bg-[var(--secondary-purple)]"] i {
  color: black !important;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Active button text styling */
.sidebar-nav-item button[class*="!bg-[var(--secondary-purple)]"] + span {
  color: #6264A7 !important;
  font-weight: 600;
  transform: translateY(1px);
}

/* Teams-style active indicator */
button[class*="!bg-[var(--secondary-purple)]"]::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: var(--secondary-purple);
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 8px rgba(98, 100, 167, 0.5);
}

/* Enhanced active button styling */
button[class*="!bg-[var(--secondary-purple)]"] {
  background: linear-gradient(135deg, var(--secondary-purple) 0%, #7476c9 100%) !important;
  box-shadow: 0 2px 8px rgba(98, 100, 167, 0.3);
  transform: translateY(-1px);
  border: 1px solid #7476c9 !important;
}

/* Active full-width button styling */
.sidebar-nav-button.active {
  background: linear-gradient(135deg, var(--secondary-purple) 0%, #7476c9 100%) !important;
  box-shadow: 0 2px 8px rgba(98, 100, 167, 0.3);
  border-left: 4px solid var(--secondary-purple);
}

.sidebar-nav-button.active i {
  color: black !important;
  font-weight: bold;
}

.sidebar-nav-button.active .button-text {
  color: #6264A7 !important;
  font-weight: 600;
  margin-top: 4px;
}

/* Button text styling */
.button-text {
  font-size: 12px;
  margin-top: 6px;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
  color: var(--sidebar-button-text);
  transition: color 0.2s ease;
}

/* Icon container styling */
.icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border-radius: 50%;
  margin-bottom: 2px;
  transition: all 0.2s ease;
}

/* Active icon container */
.sidebar-nav-item.active .icon-container {
  background-color: var(--sidebar-active);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Icon styling */
.sidebar-nav-item .icon-container i {
  color: var(--sidebar-text);
}

/* Active icon styling */
.sidebar-nav-item.active .icon-container i {
  color: var(--sidebar-active-text);
}

/* Theme-specific active icon colors */
.dark-theme .sidebar-nav-item.active .icon-container i {
  color: var(--secondary-purple) !important;
}

.light-theme .sidebar-nav-item.active .icon-container i {
  color: var(--primary-purple) !important;
}

/* Active button text */
.sidebar-nav-item.active .button-text {
  color: var(--sidebar-active-indicator);
  font-weight: 600;
}

/* Theme-specific active button text colors */
.dark-theme .sidebar-nav-item.active .button-text {
  color: var(--secondary-purple) !important;
}

.light-theme .sidebar-nav-item.active .button-text {
  color: var(--primary-purple) !important;
}

/* Active sidebar item styling */
.sidebar-nav-item.active {
  background-color: var(--sidebar-active);
  position: relative;
}

/* Active sidebar item left border for both themes */
.sidebar-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--secondary-purple);
}

/* Specific styling for the left border to match the image */
.dark-theme .sidebar-nav-item.active::before {
  background-color: var(--secondary-purple);
  box-shadow: 0 0 8px rgba(25, 197, 154, 0.5);
}

.light-theme .sidebar-nav-item.active::before {
  background-color: var(--primary-purple);
  box-shadow: 0 0 8px rgba(107, 70, 193, 0.5);
}

/* Base tooltip styles */
.custom-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2000; /* Increased z-index to ensure visibility */
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
  text-align: center;
  min-width: 60px;
}

/* Right side tooltip (for sidebar buttons) */
.sidebar-tooltip {
  left: calc(100% + 10px); /* Position to the right of the icon */
  top: 50%;
  transform: translateY(-50%);
}

/* Right side tooltip arrow */
.sidebar-tooltip::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  border-width: 5px 5px 5px 0;
  border-style: solid;
  border-color: transparent rgba(0, 0, 0, 0.75) transparent transparent;
}

/* Bottom tooltip (for section buttons) */
.bottom-tooltip {
  left: 50%; /* Center horizontally */
  top: calc(100% + 5px); /* Position below the button */
  transform: translateX(-50%); /* Center the tooltip */
}

/* Bottom tooltip arrow */
.bottom-tooltip::before {
  content: '';
  position: absolute;
  bottom: 100%; /* Position at the top of the tooltip */
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 5px 5px 5px;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.75) transparent;
}

/* Button with tooltip container */
.tooltip-container {
  position: relative;
  display: inline-block;
  width: 36px;
  z-index: 25; /* Ensure it's above other elements */
}

/* Special positioning for specific button tooltips */
#archive-tab-btn + .custom-tooltip {
  z-index: 2100; /* Higher z-index for archive button */
}

/* Add specific class for archive button tooltip container */
.archive-tooltip-container .custom-tooltip {
  z-index: 2100; /* Higher z-index for archive button */
}

/* Ensure archive tooltip is properly positioned */
.archive-tooltip-container .bottom-tooltip {
  z-index: 2100;
}

/* Show tooltip on hover */
.tooltip-container:hover .custom-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Dark mode support */
:host-context(.dark-mode) .custom-tooltip {
  background-color: rgba(50, 50, 50, 0.9);
}

/* Dark mode for right side tooltip */
:host-context(.dark-mode) .sidebar-tooltip::before {
  border-color: transparent rgba(50, 50, 50, 0.9) transparent transparent;
}

/* Dark mode for bottom tooltip */
:host-context(.dark-mode) .bottom-tooltip::before {
  border-color: transparent transparent rgba(50, 50, 50, 0.9) transparent;
}

/* Hover expanded sidebar styles */
.sidebar-container {
  position: relative;
}

.mini-sidebar {
  position: relative;
  z-index: 20;
  transition: all 0.3s ease;
}

.expanded-sidebar {
  position: absolute;
  left: 6rem; /* Position right after the mini sidebar */
  top: 0;
  height: 100%;
  width: 250px;
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--sidebar-border);
  box-shadow: 4px 0 10px rgba(0, 0, 0, 0.2);
  z-index: 15;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-20px);
  transition: all 0.3s ease;
  overflow-y: auto;
}

/* Show expanded sidebar on hover */
.mini-sidebar:hover + .expanded-sidebar,
.expanded-sidebar:hover {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.3);
}

/* Expanded sidebar content */
.sidebar-expanded-content {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
}

/* Expanded sidebar headings */
.sidebar-expanded-content h3 {
  color: var(--sidebar-text);
  border-bottom-color: var(--sidebar-border);
}

/* Right content area */
.sidebar-content {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
}

/* Button hover effects */
.sidebar-nav-item:hover .icon-container {
  background-color: var(--sidebar-hover);
  transform: translateY(-2px);
  transition: all 0.2s ease;
}

.sidebar-nav-item:hover i {
  transform: scale(1.1);
  transition: transform 0.2s ease;
  color: var(--sidebar-active-indicator) !important;
}

.sidebar-nav-item:hover .button-text {
  color: var(--sidebar-active-indicator) !important;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Full-width button hover effects */
.sidebar-nav-button:hover {
  background-color: var(--sidebar-hover) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.sidebar-nav-button:hover i {
  transform: scale(1.1);
  transition: transform 0.2s ease;
  color: var(--sidebar-text) !important;
}

.sidebar-nav-button:hover .button-text {
  color: var(--sidebar-active-indicator) !important;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Ensure the button container takes full width */
.sidebar-nav-item {
  width: 100%;
}

/* Add hover effects for menu items */
.expanded-sidebar div[class*="flex items-center"]:hover {
  transform: translateX(5px);
  transition: transform 0.2s ease;
}

/* Hide tooltips when expanded sidebar is visible */
.mini-sidebar:hover .custom-tooltip {
  opacity: 0;
  visibility: hidden;
}

/* Ensure tooltips are visible in collapsed sidebar */
.tooltip-container .custom-tooltip {
  z-index: 2000;
}

/* Specific positioning for the refresh button tooltip */
.tooltip-container button[class*="refresh"] + .custom-tooltip {
  z-index: 2100;
}

/* Profile menu styling */
#userMenu {
  transition: opacity 0.2s ease, visibility 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

/* User menu theme styling */
.user-menu {
  border: 1px solid var(--sidebar-border);
  z-index: 1050;
}

/* Dark theme user menu */
.dark-theme .user-menu {
  background-color: var(--background-light-gray);
  color: var(--sidebar-text);
}

/* Light theme user menu */
.light-theme .user-menu {
  background-color: white;
  color: black;
  border: 1px solid #e0e0e0;
}

#userMenu:not(.hidden) {
  animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

<!-- Header and Table Section -->
<div class="flex flex-col shadow-default transition-margin duration-300 ease-in-out relative overflow-hidden">
  <!-- Header -->
  <header class="flex justify-between items-start mb-medium">
    <!-- Currently Active Embedding Card -->
    <div class="bg-background-white rounded-large p-3 shadow-default min-w-[400px] m-2">
      <div class="flex justify-between items-center mb-2">
        <button
          class="bg-primary-purple text-background-white px-2 py-1 rounded-small hover:bg-secondary-purple transition-default outline-none border-none hover:text-black cursor-pointer text-sm"
          (click)="changeActiveModel()">Change Active Model</button>
        <span class="px-2 py-0.5 bg-green-100 text-green-600 rounded-small text-xs">
          {{ isActiveModel ? 'Active' : 'Inactive' }}
        </span>
      </div>
      <div class="space-y-2">
        <div>
          <label class="text-xs text-text-medium-gray block mb-0.5">Model ID</label>
          <p class="text-sm text-text-dark font-medium break-all max-w-[300px]">{{ currentActiveModelData?.embeddingModelId || 'No active model' }}</p>
        </div>
        <div>
          <label class="text-xs text-text-medium-gray block mb-0.5">API Key</label>
          <p class="text-sm text-text-dark font-medium break-all max-w-[300px] font-mono">{{ currentActiveModelData.apiKey | slice:0:4 }}****</p>
        </div>
      </div>
    </div>

    <!-- Add Button -->
    <div class="flex justify-end items-center m-2">
      <button (click)="openAddEditModal()"
        class="bg-primary-purple text-background-white p-2 px-3 rounded-small hover:bg-secondary-purple transition-default outline-none border-none hover:text-black cursor-pointer">
        <i class="ri-add-line text-2xl"></i>
      </button>
    </div>
  </header>

  <!-- Table Display -->
  <div class="mt-medium bg-background-white rounded-large p-medium shadow-default mx-2">
    <div class="overflow-x-auto">
      <table class="w-full text-body text-text-dark">
        <thead>
          <tr class="bg-background-light-gray">
            <th class="p-small text-left">Model ID</th>
            <th class="p-small text-left">Provider</th>
            <th class="p-small text-left">API Key</th>
            <th class="p-small text-left">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let config of apiConfigs; let i = index" class="border-b border-hover-blue-gray">
            <td class="p-small">{{ config.modelId }}</td>
            <td class="p-small">{{ config.provider }}</td>
            <td class="p-small">{{ config.apiKey | slice:0:4 }}****</td>
            <td class="p-small flex justify-start space-x-2">
              <button (click)="openAddEditModal(config)"
                class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
                <i
                  class="ri-edit-line text-[var(--primary-purple)] hover:text-[var(--text-dark)] transition-colors text-lg"></i>
              </button>
              <button (click)="deleteConfig(config)"
                class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
                <i class="ri-delete-bin-line text-red-500 hover:text-red-600 transition-colors text-lg"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="apiConfigs.length === 0">
            <td colspan="5" class="p-small text-center text-text-medium-gray">
              No API configurations available.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

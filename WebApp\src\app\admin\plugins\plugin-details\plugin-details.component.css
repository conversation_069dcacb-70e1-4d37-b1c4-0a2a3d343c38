.plugin-details-container {
  min-height: calc(100vh - 64px);
  background-color: var(--background-default);
}

.functions-list {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-purple) var(--hover-blue-gray);
}

.functions-list::-webkit-scrollbar {
  width: 6px;
}

.functions-list::-webkit-scrollbar-track {
  background: var(--hover-blue-gray);
  border-radius: 3px;
}

.functions-list::-webkit-scrollbar-thumb {
  background: var(--primary-purple);
  border-radius: 3px;
}

.function-item {
  transition: background-color 0.2s ease;
}

.function-item:hover {
  background-color: var(--secondary-purple);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.info-grid {
  display: grid;
  gap: 1rem;
}

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'selector',
  content: ["./src/**/*.{html,ts}"],
  theme: {
    extend: {
      colors: {
        'primary-purple': 'var(--primary-purple, #6B46C1)',
        'secondary-purple': 'var(--secondary-purple, #D6BCFA)',
        'background-light-gray': 'var(--background-light-gray, #F7FAFC)',
        'background-white': 'var(--background-white, #FFFFFF)',
        'text-dark': 'var(--text-dark, #333333)',
        'text-medium-gray': 'var(--text-medium-gray, #555555)',
        'hover-blue-gray': 'var(--hover-blue-gray, #D8DCE6)',
      },
      fontFamily: {
        roboto: ['var(--font-family, "Roboto", sans-serif)'],
      },
      fontSize: {
        'header': 'var(--font-size-header, 24px)',
        'body': 'var(--font-size-body, 14px)',
      },
      fontWeight: {
        'bold': 'var(--font-weight-bold, 700)',
        'regular': 'var(--font-weight-regular, 400)',
      },
      lineHeight: {
        'normal': 'var(--line-height, 1.6)',
      },
      borderRadius: {
        'small': 'var(--border-radius-small, 5px)',
        'large': 'var(--border-radius-large, 10px)',
      },
      padding: {
        'small': 'var(--padding-small, 10px)',
        'medium': 'var(--padding-medium, 15px)',
        'large': 'var(--padding-large, 20px)',
      },
      margin: {
        'small': 'var(--margin-small, 10px)',
      },
      boxShadow: {
        'custom': 'var(--box-shadow, 0 2px 5px rgba(0, 0, 0, 0.1))',
      },
      transitionProperty: {
        'default': 'var(--transition-default, all 0.3s ease)',
      },
    },
  },
  plugins: [],
};

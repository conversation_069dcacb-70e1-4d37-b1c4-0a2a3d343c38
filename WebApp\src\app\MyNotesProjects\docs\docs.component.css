.docs-layout {
  display: grid;
  gap: 1rem;
  height: 100vh;
  padding: 1rem;
  background-color: #f8f9fa;
}

/* Original three-column layout */
.docs-layout:not(.toggled) {
  grid-template-columns: 250px minmax(0, 1fr) 300px;
}

/* When toggled, expand chat to middle section */
.docs-layout.toggled {
  grid-template-columns: 250px 1fr;
}

.docs-list {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 2rem);
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.docs-list-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
}

.docs-list-header i {
  color: #4285f4;
  font-size: 18px;
}

.docs-list-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
}

.add-doc-btn {
  color: white !important;
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-doc-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(66, 133, 244, 0.2);
}

.doc-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
  overflow: scroll-y;
  cursor: pointer;
}

.doc-item:hover {
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.doc-item:hover .action-buttons {
  opacity: 1;
}

.action-buttons {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 2rem);
  /* Full viewport height minus padding */
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 300px;
  transition: all 0.3s ease;
}

/* Make chat container take full width of middle + right sections */
.chat-container.expanded {
  width: 100%;
  grid-column: -3 / span 2;
  margin-right: -1rem;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  background: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 10;
}

.chat-header .ai-icon {
  width: 35px;
  height: 35px;
  flex-shrink: 0;
}

.chat-header span {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-header svg {
  width: 24px;
  height: 24px;
}

.chat-messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
  max-height: calc(100vh - 215px);
  /* Adjust based on header and input heights */
}

.chat-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #5f6368;
  text-align: center;
  padding: 2rem;
}

.chat-icon {
  font-size: 48px;
  margin-bottom: 1rem;
  color: #4285f4;
}

.main-text {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.sub-text {
  font-size: 14px;
  color: #5f6368;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-input-container {
  padding: 16px;
  border-top: 1px solid #eee;
  background: white;
  position: sticky;
  bottom: 0;
  margin-top: auto;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  z-index: 10;
}

.chat-input {
  width: 100%;
  padding: 12px;
  padding-right: 44px;
  border: 1px solid #e0e0e0;
  border-radius: 24px;
  font-size: 14px;
}

.send-button {
  position: absolute;
  right: 24px;
  bottom: 24px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.close-button {
  margin-left: auto;
  padding: 4px;
  cursor: pointer;
  color: #5f6368;
}

.editor-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 2rem);
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.editor-header {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.title-input-container {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.title-input {
  width: 100%;
  padding: 12px 16px;
  font-size: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  outline: none;
  transition: all 0.2s ease;
  background: #f8f9fa;
}

.title-input:hover {
  background: #fff;
  border-color: #d0d0d0;
}

.title-input:focus {
  background: #fff;
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.title-input::placeholder {
  color: #9aa0a6;
  font-weight: 400;
}

/* Quill editor custom styling */
.ql-toolbar.ql-snow {
  border: none !important;
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 12px 24px !important;
  background: #fff;
}

.ql-container.ql-snow {
  border: none !important;
  font-size: 16px !important;
}

.ql-editor {
  padding: 24px !important;
  min-height: 300px !important;
}

/* Toolbar button styling */
.ql-toolbar button {
  width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px !important;
  margin: 0 2px;
  transition: all 0.2s ease;
}

.ql-toolbar button:hover {
  background-color: #f0f2f5;
}

.ql-toolbar button.ql-active {
  background-color: #e3e9ff;
  color: #4285f4;
}

/* Format dropdown styling */
.ql-toolbar .ql-picker {
  height: 32px !important;
}

.ql-toolbar .ql-picker-label {
  padding: 0 12px !important;
  border-radius: 6px !important;
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0 !important;
}

.ql-toolbar .ql-picker-label:hover {
  background-color: #f0f2f5;
}

/* Editor actions */
.editor-actions {
  padding: 1rem;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-shrink: 0;
}

.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
}

.btn-secondary {
  background: #f0f2f5;
  color: #5f6368;
}

.btn-secondary:hover {
  background: #e3e6ea;
}

.btn-primary {
  background: #4285f4;
  color: white;
}

.btn-primary:hover {
  background: #3367d6;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(66, 133, 244, 0.2);
}

.btn-success {
  background: #34a853;
  color: white;
}

.btn-success:hover {
  background: #2d8745;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(52, 168, 83, 0.2);
}

/* Word count badge */
.word-count {
  background: #f0f2f5;
  color: #5f6368;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
}

#editor {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.message {
  max-width: 80%;
  margin-bottom: 8px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  margin-left: auto;
}

.bot-message {
  margin-right: auto;
}

.message-content {
  padding: 12px 16px;
  border-radius: 12px;
  background: #f8f9fa;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message .message-content {
  background: #4285f4;
  color: white;
}

.bot-message .message-content {
  background: #f8f9fa;
  color: #333;
}

.message-text {
  margin-bottom: 8px;
}

.code-block {
  margin-top: 8px;
  background: #2d2d2d;
  border-radius: 8px;
  overflow: hidden;
}

.code-header {
  background: #1e1e1e;
  padding: 8px 12px;
  color: #fff;
  font-size: 12px;
}

.code-block pre {
  margin: 0;
  padding: 12px;
  overflow-x: auto;
}

.code-block code {
  color: #fff;
  font-family: 'Consolas', 'Monaco', monospace;
}

.message-time {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.7;
}

/* Add a scroll anchor element */
.scroll-anchor {
  overflow-anchor: auto;
  height: 1px;
}

/* Custom scrollbar styling */
.chat-messages-area::-webkit-scrollbar {
  width: 6px;
}

.chat-messages-area::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages-area::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages-area::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.editor-container.hidden {
  display: none;
}

/* Ensure the editor container is visible by default */
.editor-container {
  display: flex;
  flex-direction: column;
}

.close-editor-btn {
  padding: 8px;
  border-radius: 6px;
  border: none;
  background: transparent;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-editor-btn:hover {
  background: #f0f2f5;
  color: #333;
}

.docs-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
}

.docs-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Custom scrollbar for docs-content */
.docs-content::-webkit-scrollbar,
#editor::-webkit-scrollbar {
  width: 6px;
}

.docs-content::-webkit-scrollbar-track,
#editor::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.docs-content::-webkit-scrollbar-thumb,
#editor::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.docs-content::-webkit-scrollbar-thumb:hover,
#editor::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* For Firefox */
.docs-content,
#editor {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.modal-message {
  color: #5f6368;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

[title] {
  position: relative;
}

[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 4px;
  z-index: 1000;
  pointer-events: none;
  animation: fadeIn 0.2s ease-in-out;
}

[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  pointer-events: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 5px);
  }

  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.toolbar-button {
  position: relative;
  /* padding: 6px; */
  border-radius: 4px;
  border: none;
  background: transparent;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar-button:hover {
  background: #f0f2f5;
  color: #333;
}

::ng-deep markdown {
  line-height: 1.6;
}

::ng-deep markdown p {
  margin: 0.5em 0;
}

::ng-deep markdown code {
  /* background: #7c7c7c; */
  padding: 0.2em 0.4em;
  border-radius: 3px;
}

::ng-deep markdown pre {
  background: #f4f4f4;
  padding: 1em;
  border-radius: 4px;
  margin: 0.5em 0;
}

/* Add these styles for EditorJS */
.editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* Update editor container styles */
.editor-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 2rem);
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Remove Quill-specific styles and keep the rest */

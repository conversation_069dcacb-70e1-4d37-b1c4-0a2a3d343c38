{"version": 3, "sources": ["../../../../../node_modules/@editorjs/warning/dist/warning.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(`.cdx-warning{position:relative}@media all and (min-width: 736px){.cdx-warning{padding-left:36px}}.cdx-warning [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-warning [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-warning [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-warning:before{content:\"\";background-image:url(\"data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='5' y='5' width='14' height='14' rx='4' stroke='black' stroke-width='2'/%3E%3Cline x1='12' y1='9' x2='12' y2='12' stroke='black' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M12 15.02V15.01' stroke='black' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E\");width:24px;height:24px;background-size:24px 24px;position:absolute;margin-top:8px;left:0}@media all and (max-width: 735px){.cdx-warning:before{display:none}}.cdx-warning__message{min-height:85px}.cdx-warning__title{margin-bottom:6px}`)),document.head.appendChild(e)}}catch(t){console.error(\"vite-plugin-css-injected-by-js\",t)}})();\nconst l = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/><line x1=\"12\" x2=\"12\" y1=\"9\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 15.02V15.01\"/></svg>';\nclass i {\n  /**\n   * Notify core that read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Toolbox settings\n   *\n   * @public\n   * @returns {ToolboxConfig} An object containing Tool's icon and title.\n   */\n  static get toolbox() {\n    return {\n      icon: l,\n      title: \"Warning\"\n    };\n  }\n  /**\n   * Allow to press Enter inside the Warning\n   *\n   * @public\n   * @returns {boolean}\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Default placeholder for warning title\n   *\n   * @public\n   * @returns {string}\n   */\n  static get DEFAULT_TITLE_PLACEHOLDER() {\n    return \"Title\";\n  }\n  /**\n   * Default placeholder for warning message\n   *\n   * @public\n   * @returns {string}\n   */\n  static get DEFAULT_MESSAGE_PLACEHOLDER() {\n    return \"Message\";\n  }\n  /**\n   * Warning Tool`s styles\n   *\n   * @returns {WarningCSS} An object containing Tool`s CSS classnames.\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-warning\",\n      title: \"cdx-warning__title\",\n      input: this.api.styles.input,\n      message: \"cdx-warning__message\"\n    };\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} params — constructor params\n   * @param {WarningData} params.data — previously saved data\n   * @param {WarningConfig} params.config — user config for Tool\n   * @param {API} params.api - Editor.js API\n   * @param {boolean} params.readOnly - read-only mode flag\n   */\n  constructor({ data: e, config: t, api: s, readOnly: r }) {\n    this.api = s, this.readOnly = r, this.titlePlaceholder = (t == null ? void 0 : t.titlePlaceholder) || i.DEFAULT_TITLE_PLACEHOLDER, this.messagePlaceholder = (t == null ? void 0 : t.messagePlaceholder) || i.DEFAULT_MESSAGE_PLACEHOLDER, this.data = {\n      title: e.title || \"\",\n      message: e.message || \"\"\n    };\n  }\n  /**\n   * Create Warning Tool container with inputs\n   *\n   * @returns {Element} Html element of Warning Tool.\n   */\n  render() {\n    const e = this._make(\"div\", [this.CSS.baseClass, this.CSS.wrapper]), t = this._make(\"div\", [this.CSS.input, this.CSS.title], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.title\n    }), s = this._make(\"div\", [this.CSS.input, this.CSS.message], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.message\n    });\n    return t.dataset.placeholder = this.titlePlaceholder, s.dataset.placeholder = this.messagePlaceholder, e.appendChild(t), e.appendChild(s), e;\n  }\n  /**\n   * Extract Warning data from Warning Tool element\n   *\n   * @param {HTMLDivElement} warningElement - element to save\n   * @returns {WarningData} Warning Tool`s data.\n   */\n  save(e) {\n    const t = e.querySelector(`.${this.CSS.title}`), s = e.querySelector(`.${this.CSS.message}`);\n    return Object.assign(this.data, {\n      title: (t == null ? void 0 : t.innerHTML) ?? \"\",\n      message: (s == null ? void 0 : s.innerHTML) ?? \"\"\n    });\n  }\n  /**\n   * Helper for making Elements with attributes\n   *\n   * @param  {string} tagName           - new Element tag name\n   * @param  {Array|string} classNames  - list or name of CSS classname(s)\n   * @param  {object} attributes        - any attributes\n   * @returns {Element} Html element of {tagName}.\n   */\n  _make(e, t = null, s = {}) {\n    const r = document.createElement(e);\n    Array.isArray(t) ? r.classList.add(...t) : t && r.classList.add(t);\n    for (const a in s)\n      r[a] = s[a];\n    return r;\n  }\n  /**\n   * Sanitizer config for Warning Tool saved data\n   *\n   */\n  static get sanitize() {\n    return {\n      title: {},\n      message: {}\n    };\n  }\n}\nexport {\n  i as default\n};\n"], "mappings": ";;;CAAC,WAAU;AAAC;AAAa,MAAG;AAAC,QAAG,OAAO,WAAS,KAAI;AAAC,UAAI,IAAE,SAAS,cAAc,OAAO;AAAE,QAAE,YAAY,SAAS,eAAe,skCAAskC,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,YAAQ,MAAM,kCAAiC,CAAC;AAAA,EAAC;AAAC,GAAG;AACtyC,IAAM,IAAI;AACV,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA,EAIN,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,4BAA4B;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,8BAA8B;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM;AACR,WAAO;AAAA,MACL,WAAW,KAAK,IAAI,OAAO;AAAA,MAC3B,SAAS;AAAA,MACT,OAAO;AAAA,MACP,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,EAAE,GAAG;AACvD,SAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,oBAAoB,KAAK,OAAO,SAAS,EAAE,qBAAqB,GAAE,2BAA2B,KAAK,sBAAsB,KAAK,OAAO,SAAS,EAAE,uBAAuB,GAAE,6BAA6B,KAAK,OAAO;AAAA,MACrP,OAAO,EAAE,SAAS;AAAA,MAClB,SAAS,EAAE,WAAW;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,UAAM,IAAI,KAAK,MAAM,OAAO,CAAC,KAAK,IAAI,WAAW,KAAK,IAAI,OAAO,CAAC,GAAG,IAAI,KAAK,MAAM,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,GAAG;AAAA,MAC3H,iBAAiB,CAAC,KAAK;AAAA,MACvB,WAAW,KAAK,KAAK;AAAA,IACvB,CAAC,GAAG,IAAI,KAAK,MAAM,OAAO,CAAC,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAAA,MAC5D,iBAAiB,CAAC,KAAK;AAAA,MACvB,WAAW,KAAK,KAAK;AAAA,IACvB,CAAC;AACD,WAAO,EAAE,QAAQ,cAAc,KAAK,kBAAkB,EAAE,QAAQ,cAAc,KAAK,oBAAoB,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG;AAAA,EAC7I;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,GAAG;AACN,UAAM,IAAI,EAAE,cAAc,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG,IAAI,EAAE,cAAc,IAAI,KAAK,IAAI,OAAO,EAAE;AAC3F,WAAO,OAAO,OAAO,KAAK,MAAM;AAAA,MAC9B,QAAQ,KAAK,OAAO,SAAS,EAAE,cAAc;AAAA,MAC7C,UAAU,KAAK,OAAO,SAAS,EAAE,cAAc;AAAA,IACjD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,GAAG;AACzB,UAAM,IAAI,SAAS,cAAc,CAAC;AAClC,UAAM,QAAQ,CAAC,IAAI,EAAE,UAAU,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,UAAU,IAAI,CAAC;AACjE,eAAW,KAAK;AACd,QAAE,CAAC,IAAI,EAAE,CAAC;AACZ,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,OAAO,CAAC;AAAA,MACR,SAAS,CAAC;AAAA,IACZ;AAAA,EACF;AACF;", "names": []}
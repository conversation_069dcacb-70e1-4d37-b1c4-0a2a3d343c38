import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
  FileParameter,
  FileServiceProxy,
} from '../../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-file-add',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink],
  templateUrl: './file-add.component.html',
  styleUrl: './file-add.component.css',
})
export class FileAddComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef;

  selectedFile: File | null = null;
  isUploading: boolean = false;

  constructor(private router: Router, private fileService: FileServiceProxy) {}

  ngOnInit(): void {
    // Initialize component
  }

  onFileSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.selectedFile = event.target.files[0];
    }
  }

  removeFile(): void {
    this.selectedFile = null;
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  uploadFile(): void {
    if (!this.selectedFile) {
      return;
    }

    this.isUploading = true;

    // Create FileParameter object for the API
    const fileParam: FileParameter = {
      data: this.selectedFile,
      fileName: this.selectedFile.name,
    };

    // Call the file service with proper parameters
    this.fileService.upload('File', [fileParam]).subscribe({
      next: (res) => {
        console.log('File uploaded:', res);
        // Navigate back to the files list
        this.goBack();
      },
      error: (err) => {
        console.error('Error uploading file:', err);
        this.isUploading = false;
      },
      complete: () => {
        this.isUploading = false;
      },
    });
  }

  goBack(): void {
    this.router.navigate(['/settings/files']);
  }
}

import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { SqlConnectionServiceProxy, SqlConnectionInfo, SqlQueryRequest } from '../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-sql-connection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzModalModule,
    NzInputModule,
    NzButtonModule,
    NzSelectModule,
    NzRadioModule
  ],
  templateUrl: './sql-connection-dialog.component.html',
  styleUrls: ['./sql-connection-dialog.component.css']
})
export class SqlConnectionDialogComponent implements OnInit {
  connectionString: string = '';
  sql: string = '';
  connectionType: string = 'existing'; // 'existing' or 'custom'
  savedConnections: string[] = [];
  selectedConnection: string = '';
  connectionName: string = '';
  isLoading: boolean = false;
  isTesting: boolean = false;
  isSaving: boolean = false;

  constructor(
    private modalRef: NzModalRef,
    private messageService: NzMessageService,
    private sqlConnectionService: SqlConnectionServiceProxy,
    @Inject(NZ_MODAL_DATA) public data: { sql: string }
  ) { }
  ngOnInit(): void {
    if (this.data && this.data.sql) {
      this.sql = this.data.sql;
    }

    // Fetch saved connections from the service
    this.loadSavedConnections();
  }
  loadSavedConnections(): void {
    this.isLoading = true;

    // Then, load connections from the API
    this.sqlConnectionService.getAll().subscribe({
      next: (result) => {
        if (result && result.connections) {
          // Extract connection strings from the connection info objects
          const apiConnections = result.connections.map(conn => conn.connectionString || '');

          // Merge with local connections, avoiding duplicates
          const allConnections = [...this.savedConnections];

          apiConnections.forEach(conn => {
            if (!allConnections.includes(conn)) {
              allConnections.push(conn);
            }
          });

          this.savedConnections = allConnections;

          if (this.savedConnections.length > 0) {
            this.selectedConnection = this.savedConnections[0];
            this.connectionString = this.selectedConnection;
          }
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching SQL connections:', error);
        this.messageService.warning('Failed to load API connections, using local connections only');

        // Still set a connection if we have local ones
        if (this.savedConnections.length > 0) {
          this.selectedConnection = this.savedConnections[0];
          this.connectionString = this.selectedConnection;
        }

        this.isLoading = false;
      }
    });
  }

  onConnectionTypeChange(): void {
    if (this.connectionType === 'existing' && this.savedConnections.length > 0) {
      this.connectionString = this.selectedConnection;
    } else if (this.connectionType === 'custom') {
      this.connectionString = ''; // Clear when switching to custom
    }
  }

  onConnectionSelect(): void {
    this.connectionString = this.selectedConnection;
  } testConnection(): void {
    if (!this.connectionString.trim()) {
      this.messageService.error('Please select or enter a connection string');
      return;
    }

    this.isTesting = true;
    const connectionInfo = new SqlConnectionInfo({
      connectionString: this.connectionString,
      name: 'Test Connection'
    });

    const loadingMsgId = this.messageService.loading('Testing connection...', { nzDuration: 0 }).messageId;

    this.sqlConnectionService.testConnection(connectionInfo).subscribe({
      next: (result) => {
        this.isTesting = false;
        this.messageService.remove(loadingMsgId);

        if (!result.isError) {
          this.messageService.success('Connection test successful!');
        } else {
          this.messageService.error(result.message || 'Connection test failed');
        }
      },
      error: (error) => {
        this.isTesting = false;
        this.messageService.remove(loadingMsgId);
        console.error('Error testing connection:', error);
        this.messageService.error('Connection test failed: ' + (error.message || 'Unknown error'));
      }
    });
  }

  onCancel(): void {
    this.modalRef.close(null);
  }
  onSubmit(): void {
    if (!this.connectionString.trim()) {
      this.messageService.error('Please enter a connection string');
      return;
    }

    if (!this.sql.trim()) {
      this.messageService.error('Please enter an SQL query');
      return;
    }

    const sqlQueryRequest = new SqlQueryRequest({
      connectionString: this.connectionString,
      sqlQuery: this.sql
    });

    const loadingMsgId = this.messageService.loading('Executing SQL query...', { nzDuration: 0 }).messageId;

    this.sqlConnectionService.execute(sqlQueryRequest).subscribe({
      next: (result) => {
        this.messageService.remove(loadingMsgId);

        if (result.isSuccess) {
          this.messageService.success(`Query executed successfully. ${result.rowsAffected} row(s) affected.`);
          this.modalRef.close({
            connectionString: this.connectionString,
            sql: this.sql,
            result: result
          });
        } else {
          this.messageService.error(result.message || 'Failed to execute SQL query');
        }
      },
      error: (error) => {
        this.messageService.remove(loadingMsgId);
        console.error('Error executing SQL query:', error);
        this.messageService.error('Failed to execute SQL query: ' + (error.message || 'Unknown error'));
      }
    });
  }
}

<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-database-2-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">API Settings</h1>
        <div class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filteredApis.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input
            type="text"
            placeholder="Search APIs..."
            [(ngModel)]="searchQuery"
            (ngModelChange)="filterApis()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm text-[var(--text-dark)]  border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200"
          />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchQuery">
            <button
              (click)="clearSearch()"
              class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] transition-colors focus:outline-none"
            >
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Resync Button - Teams-style -->
        <button
          *ngIf="apiLists.length > 0"
          (click)="resyncAllModels()"
          [disabled]="isResyncingAll"
          class="h-8 px-3 py-1 bg-[var(--hover-blue-gray)] text-sm font-medium text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all duration-200 flex items-center justify-center gap-1"
          [ngClass]="{'opacity-70 cursor-not-allowed': isResyncingAll}"
          title="Resync all models (custom models will be skipped)"
        >
          <i *ngIf="isResyncingAll" class="ri-loader-4-line animate-spin text-sm"></i>
          <i *ngIf="!isResyncingAll" class="ri-refresh-line text-sm"></i>
          <span>Resync</span>
        </button>

        <!-- Add Button - Teams-style -->
        <button
          (click)="onAddApi()"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
        >
          <i class="ri-add-line"></i>
          <span>Add API</span>
        </button>
      </div>
    </div>

    <!-- API List - Card View -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="relative min-h-[300px]">
        <app-spinner message="Loading API settings..." [overlay]="false"></app-spinner>
      </div>

      <!-- Grid Layout for Cards -->
      <div *ngIf="!isLoading" class="bg-[var(--background-light-gray)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        @for (api of filteredApis; track $index) {
        <div
          class="api-card bg-[var(--background-white)] rounded-md overflow-hidden group animate-fadeIn relative"
          [ngStyle]="{'animation-delay': ($index * 0.05) + 's'}">
          <!-- Card Header -->
          <div class="p-4 border-b border-[var(--hover-blue-gray)] relative">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
                <i class="ri-database-2-line text-white text-lg"></i>
              </div>
              <div>
                <h3
                  class="text-base font-medium text-[var(--text-dark)] capitalize group-hover:text-[var(--primary-purple)] transition-colors duration-300">
                  {{searchName(api.tokenUrl)}} API
                </h3>
                <p class="text-xs text-[var(--text-medium-gray)]">API Provider</p>
              </div>
            </div>
          </div>

          <!-- Card Body -->
          <div class="p-4">
            <div class="mb-3">
              <p class="text-xs text-[var(--text-medium-gray)] mb-1">Token URL</p>
              <p class="text-sm text-[var(--text-dark)] break-all">
                {{api.tokenUrl}}
              </p>
            </div>

            <!-- Status Indicators -->
            <div class="flex items-center gap-4">
              <!-- Active Status -->
              <div class="flex items-center gap-2">
                <div class="flex items-center justify-center">
                  <span class="w-2 h-2 rounded-full bg-green-500"></span>
                </div>
                <span class="text-xs text-[var(--text-medium-gray)] leading-none">Active</span>
              </div>

              <!-- Custom Models Indicator (if applicable) -->
              <div *ngIf="api.hasCustomModels" class="flex items-center gap-2">
                <div class="flex items-center justify-center">
                  <span class="w-2 h-2 rounded-full bg-blue-500"></span>
                </div>
                <span class="text-xs text-[var(--text-medium-gray)] leading-none">Custom Models</span>
              </div>
            </div>
          </div>

          <!-- Action Buttons (Hidden by Default, Visible on Hover) - Positioned in top-right corner -->
          <div class="absolute top-3 right-3 flex gap-2 card-actions">
            <!-- Resync Models Button (disabled for custom models) -->
            <button (click)="resyncModels(api)" [disabled]="isResyncing[api.id] || api.hasCustomModels"
              class="action-button w-8 h-8 rounded-md bg-[#E6F7FF] hover:bg-[#BAE7FF] transition-all duration-200 flex items-center justify-center border-none"
              [ngClass]="{'opacity-70 cursor-not-allowed': isResyncing[api.id] || api.hasCustomModels}"
              [title]="api.hasCustomModels ? 'Cannot resync custom models' : 'Resync models'">
              <div class="flex items-center justify-center w-full h-full">
                <i *ngIf="!isResyncing[api.id]" class="ri-refresh-line text-blue-500 text-base"></i>
                <i *ngIf="isResyncing[api.id]" class="ri-loader-4-line text-blue-500 animate-spin text-base"></i>
              </div>
            </button>

            <!-- Edit Custom Models Button (only for custom models) -->
            <button *ngIf="api.hasCustomModels" (click)="onEditCustomModels(api)"
              class="action-button w-8 h-8 rounded-md bg-[#E6F7FF] hover:bg-[#BAE7FF] transition-all duration-200 flex items-center justify-center border-none"
              title="Edit custom models">
              <div class="flex items-center justify-center w-full h-full">
                <i class="ri-edit-line text-blue-500 text-base"></i>
              </div>
            </button>

            <!-- Delete API Button -->
            <button (click)="deleteApi(api)"
              class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none"
              title="Delete API">
              <div class="flex items-center justify-center w-full h-full">
                <i class="ri-delete-bin-6-line text-red-500 text-base"></i>
              </div>
            </button>
          </div>
        </div>
        }

        <!-- Add API Card -->
        <div (click)="onAddApi()"
          class="bg-[var(--background-white)] rounded-md border-dashed overflow-hidden flex flex-col items-center justify-center p-6 cursor-pointer h-full min-h-[220px] group animate-fadeIn"
          style="animation-delay: 0.3s">
          <div
            class="w-12 h-12 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
            <i class="ri-add-line text-xl text-white"></i>
          </div>
          <h3
            class="text-base font-medium text-[var(--text-dark)] mb-2 text-center group-hover:text-[var(--primary-purple)] transition-colors duration-300">
            Add New API</h3>
          <p class="text-xs text-[var(--text-medium-gray)] text-center mb-4">
            Connect a new API provider
          </p>

          <!-- Button -->
          <button
            class="px-3 py-1.5 bg-[var(--primary-purple)] text-white text-xs font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-1.5 border-none">
            <i class="ri-add-line"></i>
            <span>Add Provider</span>
          </button>
        </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isLoading && apiLists.length === 0 && !showForm"
          class="flex flex-col items-center justify-center py-12 px-4 bg-[var(--background-white)] rounded-md border border-dashed border-[var(--hover-blue-gray)] shadow-sm animate-fadeIn">
          <div
            class="w-16 h-16 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
            <i class="ri-database-2-line text-3xl text-[var(--primary-purple)]"></i>
          </div>

          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No API Providers</h3>
          <p class="text-sm text-[var(--text-medium-gray)] text-center max-w-md mb-6">
            No API providers have been added yet. Add your first API to connect with AI models.
          </p>

          <button (click)="onAddApi()"
            class="px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 border-none">
            <i class="ri-add-line"></i>
            <span>Add Your First API</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add API Form Overlay -->
<div *ngIf="showForm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
  <div
    class="bg-[var(--background-white)] rounded-md p-5 w-full max-w-md border border-[var(--hover-blue-gray)] shadow-md">
    <div class="flex items-center justify-between mb-5">
      <div class="flex items-center gap-2">
        <div
          class="w-8 h-8 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
          <i class="ri-database-2-line text-[var(--primary-purple)] text-lg"></i>
        </div>
        <h3 class="text-base font-medium text-[var(--text-dark)]">Add API Provider</h3>
      </div>
      <button (click)="resetForm()"
        class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-[var(--hover-blue-gray)] transition-all duration-200 border-none bg-transparent">
        <i class="ri-close-line text-[var(--text-medium-gray)] text-lg"></i>
      </button>
    </div>

    <div class="space-y-4">
      <div>
        <label class="text-sm font-medium text-[var(--text-dark)] mb-1">
          Token URL <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="ri-link text-[var(--text-medium-gray)]"></i>
          </div>
          <input type="text" [(ngModel)]="apiData.tokenUrl"
            (input)="isCredentialsValid = false; isTokenUrlValid = false;"
            [ngClass]="{'border-red-500 focus:ring-red-500': apiData.tokenUrl && !isTokenUrlValid,
                      'border-green-500 focus:ring-green-500': apiData.tokenUrl && isTokenUrlValid}"
            class="w-full h-10 pl-10 pr-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
            placeholder="Enter token URL">
          <div *ngIf="apiData.tokenUrl && isTokenUrlValid" class="absolute inset-y-0 right-3 flex items-center">
            <i class="ri-check-line text-green-500"></i>
          </div>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1">Example: https://api.openai.com</p>
      </div>

      <div>
        <div class="flex items-center justify-between mb-1">
          <label class="text-sm font-medium text-[var(--text-dark)]">
            API Key <span class="text-red-500" *ngIf="!noApiMode">*</span>
          </label>
          <div class="flex items-center">
            <button
              type="button"
              (click)="noApiMode = !noApiMode; toggleNoApiMode()"
              class="flex items-center px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 border"
              [ngClass]="noApiMode ?
                'bg-[var(--primary-purple)] text-white border-[var(--primary-purple)]' :
                'bg-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] border-[var(--hover-blue-gray)]'"
            >
              <i class="ri-toggle-line mr-1.5 text-base" [ngClass]="noApiMode ? 'text-white' : 'text-[var(--text-medium-gray)]'"></i>
              No API Mode
            </button>
          </div>
        </div>
        <div class="relative" *ngIf="!noApiMode">
          <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <i class="ri-key-2-line text-[var(--text-medium-gray)]"></i>
          </div>
          <input type="text" [(ngModel)]="apiData.apiKey" (input)="isCredentialsValid = false"
            class="w-full h-10 pl-10 pr-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
            placeholder="Enter API key">
        </div>
        <div *ngIf="noApiMode" class="flex items-center h-10 px-4 bg-[var(--primary-purple)] bg-opacity-10 rounded-md border border-[var(--primary-purple)] border-opacity-20 animate-fadeIn">
          <div class="flex items-center justify-center">
            <span class="relative flex h-2.5 w-2.5 mr-3">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-[var(--primary-purple)] opacity-75"></span>
              <span class="relative inline-flex rounded-full h-2.5 w-2.5 bg-[var(--primary-purple)]"></span>
            </span>
          </div>
          <span class="text-sm font-medium text-white">Using "no api" mode</span>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1" *ngIf="!noApiMode">Your API key will be securely stored</p>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1" *ngIf="noApiMode">
          <i class="ri-information-line text-[var(--primary-purple)] mr-1"></i>
          No API key required - system will use the default "no api" value
        </p>
      </div>

      <!-- Validation Message -->
      <div *ngIf="validationMessage"
        [ngClass]="{'bg-green-100 border-green-300 text-green-800': isCredentialsValid,
                   'bg-red-100 border-red-300 text-red-800': !isCredentialsValid}"
        class="p-3 rounded-md border animate-fadeIn">
        <div class="flex items-center">
          <i *ngIf="isCredentialsValid" class="ri-checkbox-circle-line text-green-600 mr-2"></i>
          <i *ngIf="!isCredentialsValid" class="ri-error-warning-line text-red-600 mr-2"></i>
          <span>{{ validationMessage }}</span>
        </div>
      </div>

      <div class="mt-4">
        <div class="flex items-center mb-2">
          <input type="checkbox" id="useCustomModels" [(ngModel)]="apiData.hasCustomModels"
            class="w-4 h-4 text-[var(--primary-purple)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded focus:ring-[var(--primary-purple)]">
          <label for="useCustomModels" class="ml-2 text-sm font-medium text-[var(--text-dark)]">
            Use Custom Models
          </label>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mb-2">
          Instead of automatically extracting models from the API, you can specify custom model names
        </p>

        <div *ngIf="apiData.hasCustomModels" class="mt-2">
          <label class="text-sm font-medium text-[var(--text-dark)] mb-1">
            Custom Model Names
          </label>
          <div class="relative">
            <textarea [(ngModel)]="apiData.customModelsText" (input)="updateCustomModels()"
              class="w-full min-h-[80px] p-3 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
              placeholder="Enter model names, one per line"></textarea>
          </div>
          <p class="text-xs text-[var(--text-medium-gray)] mt-1">Enter each model name on a new line</p>
        </div>
      </div>

      <div class="pt-2">
        <button (click)="validateCredentials()" [disabled]="isValidating"
          class="w-full h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 border-none"
          [ngClass]="{'opacity-70 cursor-not-allowed': isValidating}">
          <i *ngIf="isValidating" class="ri-loader-4-line animate-spin"></i>
          <i *ngIf="!isValidating" class="ri-check-line"></i>
          <span>{{ isValidating ? 'Validating...' : 'Validate Credentials' }}</span>
        </button>
      </div>
    </div>

    <div class="flex justify-end gap-3 mt-8 pt-4 border-t border-[var(--hover-blue-gray)] relative z-10">
      <button (click)="resetForm()"
        class="h-10 px-5 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] text-sm font-medium rounded-lg hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center gap-2 border-none shadow-sm hover:shadow-md">
        <i class="ri-close-line"></i>
        <span>Cancel</span>
      </button>

      <button (click)="saveApi()"
        [ngClass]="{'bg-[var(--primary-purple)] hover:bg-opacity-90 hover:scale-[1.02] transform': isCredentialsValid, 'bg-gray-400 cursor-not-allowed': !isCredentialsValid}"
        class="h-10 px-5 py-2 text-white text-sm font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md border-none"
        [disabled]="!isCredentialsValid">
        <i class="ri-save-line"></i>
        <span>Save</span>
      </button>
    </div>

    <!-- Status Indicator (Conditional) -->
    <div *ngIf="isCredentialsValid"
      class="absolute top-6 right-6 flex items-center gap-2 bg-green-100 dark:bg-green-900 bg-opacity-80 px-3 py-1 rounded-full z-20 animate-fadeIn">
      <span class="relative flex h-2 w-2">
        <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
        <span class="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
      </span>
      <span class="text-xs font-medium text-green-800 dark:text-green-200">Validated</span>
    </div>
  </div>
</div>

<!-- Edit Custom Models Form Overlay -->
<div *ngIf="showEditCustomModelsForm"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
  <div
    class="bg-[var(--background-white)] rounded-md p-5 w-full max-w-md border border-[var(--hover-blue-gray)] shadow-md">
    <div class="flex items-center justify-between mb-5">
      <div class="flex items-center gap-2">
        <div
          class="w-8 h-8 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
          <i class="ri-edit-line text-[var(--primary-purple)] text-lg"></i>
        </div>
        <h3 class="text-base font-medium text-[var(--text-dark)]">Edit Custom Models</h3>
      </div>
      <button (click)="resetEditCustomModelsForm()"
        class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-[var(--hover-blue-gray)] transition-all duration-200 border-none bg-transparent">
        <i class="ri-close-line text-[var(--text-medium-gray)] text-lg"></i>
      </button>
    </div>

    <div class="space-y-4">
      <div>
        <label class="text-sm font-medium text-[var(--text-dark)] mb-1">
          Custom Model Names
        </label>
        <div class="relative">
          <textarea [(ngModel)]="editCustomModelsData.customModelsText" (input)="updateEditCustomModels()"
            class="w-full min-h-[150px] p-3 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
            placeholder="Enter model names, one per line"></textarea>
        </div>
        <p class="text-xs text-[var(--text-medium-gray)] mt-1">Enter each model name on a new line</p>
      </div>
    </div>

    <div class="flex justify-end gap-3 mt-8 pt-4 border-t border-[var(--hover-blue-gray)] relative z-10">
      <button (click)="resetEditCustomModelsForm()"
        class="h-10 px-5 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] text-sm font-medium rounded-lg hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center gap-2 border-none shadow-sm hover:shadow-md">
        <i class="ri-close-line"></i>
        <span>Cancel</span>
      </button>

      <button (click)="saveEditCustomModels()"
        [ngClass]="{'bg-[var(--primary-purple)] hover:bg-opacity-90 hover:scale-[1.02] transform': editCustomModelsData.customModels.length > 0, 'bg-gray-400 cursor-not-allowed': editCustomModelsData.customModels.length === 0}"
        class="h-10 px-5 py-2 text-white text-sm font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md border-none"
        [disabled]="editCustomModelsData.customModels.length === 0">
        <i class="ri-save-line"></i>
        <span>Save</span>
      </button>
    </div>
  </div>
</div>

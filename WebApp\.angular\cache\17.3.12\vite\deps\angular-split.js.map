{"version": 3, "sources": ["../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../node_modules/angular-split/fesm2022/angular-split.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable(observer => {\n        const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    constructor(source) {\n        this.source = source;\n        this.destroyed = false;\n        this.destroyRef = inject(DestroyRef);\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: value => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n    const destroyRef = ɵgetOutputDestroyRef(ref);\n    return new Observable(observer => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe(v => observer.next(v));\n        return () => subscription.unsubscribe();\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n    !options?.injector && assertInInjectionContext(toObservable);\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n    const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue });\n    }\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: value => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: error => {\n            if (options?.rejectErrors) {\n                // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n                // the error to end up as an uncaught exception.\n                throw error;\n            }\n            state.set({ kind: 2 /* StateKind.Error */, error });\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n                throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };\n\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ElementRef, computed, signal, untracked, NgZone, numberAttribute, input, output, ViewContainerRef, effect, Injector, Renderer2, contentChildren, contentChild, booleanAttribute, isDevMode, Component, ChangeDetectionStrategy, HostBinding, NgModule } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { merge, fromEvent, filter, Observable, switchMap, take, map, takeUntil, tap, timeInterval, scan, mergeMap, of, delay, repeat, Subject, startWith, pairwise, skipWhile } from 'rxjs';\nimport { DOCUMENT, NgStyle, NgTemplateOutlet } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = (a0, a1, a2, a3, a4, a5) => ({\n  areaBefore: a0,\n  areaAfter: a1,\n  gutterNum: a2,\n  first: a3,\n  last: a4,\n  isDragged: a5\n});\nfunction SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const injector_r5 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    const area_r7 = ctx_r5.$implicit;\n    const $index_r2 = ctx_r5.$index;\n    const ɵ$index_2_r8 = ctx_r5.$index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.customGutter().template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(3, _c1, area_r7, ctx_r2._areas()[$index_r2 + 1], $index_r2 + 1, ɵ$index_2_r8 === 0, $index_r2 === ctx_r2._areas().length - 2, ctx_r2.draggedGutterIndex() === $index_r2))(\"ngTemplateOutletInjector\", injector_r5);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_Template, 2, 10, \"ng-container\", 3);\n  }\n  if (rf & 2) {\n    const $index_r2 = i0.ɵɵnextContext(2).$index;\n    i0.ɵɵproperty(\"asSplitGutterDynamicInjector\", $index_r2 + 1);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 5);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2, 0);\n    i0.ɵɵlistener(\"asSplitCustomClick\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterClicked($index_r2));\n    })(\"asSplitCustomDblClick\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomDblClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterDoubleClicked($index_r2));\n    })(\"asSplitCustomMouseDown\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomMouseDown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const gutter_r4 = i0.ɵɵreference(1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterMouseDown($event, gutter_r4, $index_r2, $index_r2, $index_r2 + 1));\n    })(\"asSplitCustomKeyDown\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomKeyDown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterKeyDown($event, $index_r2, $index_r2, $index_r2 + 1));\n    });\n    i0.ɵɵtemplate(2, SplitComponent_For_2_Conditional_0_Conditional_2_Template, 1, 1, \"ng-container\")(3, SplitComponent_For_2_Conditional_0_Conditional_3_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_22_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    const area_r7 = ctx_r5.$implicit;\n    const $index_r2 = ctx_r5.$index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"as-dragged\", ctx_r2.draggedGutterIndex() === $index_r2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getGutterGridStyle($index_r2 + 1))(\"asSplitCustomMultiClickThreshold\", ctx_r2.gutterDblClickDuration())(\"asSplitCustomClickDeltaInPx\", ctx_r2.gutterClickDeltaPx());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.gutterAriaLabel())(\"aria-orientation\", ctx_r2.direction())(\"aria-valuemin\", ctx_r2.getAriaValue(area_r7.minSize()))(\"aria-valuemax\", ctx_r2.getAriaValue(area_r7.maxSize()))(\"aria-valuenow\", ctx_r2.getAriaValue(area_r7._internalSize()))(\"aria-valuetext\", ctx_r2.getAriaAreaSizeText(area_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ((tmp_22_0 = ctx_r2.customGutter()) == null ? null : tmp_22_0.template) ? 2 : 3);\n  }\n}\nfunction SplitComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SplitComponent_For_2_Conditional_0_Template, 4, 12, \"div\", 1);\n  }\n  if (rf & 2) {\n    const ɵ$index_2_r8 = ctx.$index;\n    const ɵ$count_2_r9 = ctx.$count;\n    i0.ɵɵconditional(0, !(ɵ$index_2_r8 === ɵ$count_2_r9 - 1) ? 0 : -1);\n  }\n}\nfunction SplitAreaComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 0);\n  }\n}\nconst defaultOptions = {\n  dir: 'ltr',\n  direction: 'horizontal',\n  disabled: false,\n  gutterDblClickDuration: 0,\n  gutterSize: 11,\n  gutterStep: 1,\n  gutterClickDeltaPx: 2,\n  restrictMove: false,\n  unit: 'percent',\n  useTransition: false\n};\nconst ANGULAR_SPLIT_DEFAULT_OPTIONS = new InjectionToken('angular-split-global-config', {\n  providedIn: 'root',\n  factory: () => defaultOptions\n});\n/**\n * Provides default options for angular split. The options object has hierarchical inheritance\n * which means only the declared properties will be overridden\n */\nfunction provideAngularSplitOptions(options) {\n  return {\n    provide: ANGULAR_SPLIT_DEFAULT_OPTIONS,\n    useFactory: () => ({\n      ...inject(ANGULAR_SPLIT_DEFAULT_OPTIONS, {\n        skipSelf: true\n      }),\n      ...options\n    })\n  };\n}\nclass SplitGutterDirective {\n  constructor() {\n    this.template = inject(TemplateRef);\n    /**\n     * The map holds reference to the drag handle elements inside instances\n     * of the provided template.\n     *\n     * @internal\n     */\n    this._gutterToHandleElementMap = new Map();\n    /**\n     * The map holds reference to the excluded drag elements inside instances\n     * of the provided template.\n     *\n     * @internal\n     */\n    this._gutterToExcludeDragElementMap = new Map();\n  }\n  /**\n   * @internal\n   */\n  _canStartDragging(originElement, gutterNum) {\n    if (this._gutterToExcludeDragElementMap.has(gutterNum)) {\n      const isInsideExclude = this._gutterToExcludeDragElementMap.get(gutterNum).some(gutterExcludeElement => gutterExcludeElement.nativeElement.contains(originElement));\n      if (isInsideExclude) {\n        return false;\n      }\n    }\n    if (this._gutterToHandleElementMap.has(gutterNum)) {\n      return this._gutterToHandleElementMap.get(gutterNum).some(gutterHandleElement => gutterHandleElement.nativeElement.contains(originElement));\n    }\n    return true;\n  }\n  /**\n   * @internal\n   */\n  _addToMap(map, gutterNum, elementRef) {\n    if (map.has(gutterNum)) {\n      map.get(gutterNum).push(elementRef);\n    } else {\n      map.set(gutterNum, [elementRef]);\n    }\n  }\n  /**\n   * @internal\n   */\n  _removedFromMap(map, gutterNum, elementRef) {\n    const elements = map.get(gutterNum);\n    elements.splice(elements.indexOf(elementRef), 1);\n    if (elements.length === 0) {\n      map.delete(gutterNum);\n    }\n  }\n  static ngTemplateContextGuard(_dir, ctx) {\n    return true;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDirective_Factory(t) {\n      return new (t || SplitGutterDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDirective,\n      selectors: [[\"\", \"asSplitGutter\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutter]',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Identifies the gutter by number through DI\n * to allow SplitGutterDragHandleDirective and SplitGutterExcludeFromDragDirective to know\n * the gutter template context without inputs\n */\nconst GUTTER_NUM_TOKEN = new InjectionToken('Gutter num');\nclass SplitGutterDragHandleDirective {\n  constructor() {\n    this.gutterNum = inject(GUTTER_NUM_TOKEN);\n    this.elementRef = inject(ElementRef);\n    this.gutterDir = inject(SplitGutterDirective);\n    this.gutterDir._addToMap(this.gutterDir._gutterToHandleElementMap, this.gutterNum, this.elementRef);\n  }\n  ngOnDestroy() {\n    this.gutterDir._removedFromMap(this.gutterDir._gutterToHandleElementMap, this.gutterNum, this.elementRef);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDragHandleDirective_Factory(t) {\n      return new (t || SplitGutterDragHandleDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDragHandleDirective,\n      selectors: [[\"\", \"asSplitGutterDragHandle\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDragHandleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterDragHandle]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nclass SplitGutterExcludeFromDragDirective {\n  constructor() {\n    this.gutterNum = inject(GUTTER_NUM_TOKEN);\n    this.elementRef = inject(ElementRef);\n    this.gutterDir = inject(SplitGutterDirective);\n    this.gutterDir._addToMap(this.gutterDir._gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n  }\n  ngOnDestroy() {\n    this.gutterDir._removedFromMap(this.gutterDir._gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterExcludeFromDragDirective_Factory(t) {\n      return new (t || SplitGutterExcludeFromDragDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterExcludeFromDragDirective,\n      selectors: [[\"\", \"asSplitGutterExcludeFromDrag\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterExcludeFromDragDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterExcludeFromDrag]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Only supporting a single {@link TouchEvent} point\n */\nfunction getPointFromEvent(event) {\n  // NOTE: In firefox TouchEvent is only defined for touch capable devices\n  const isTouchEvent = e => window.TouchEvent && event instanceof TouchEvent;\n  if (isTouchEvent(event)) {\n    if (event.changedTouches.length === 0) {\n      return undefined;\n    }\n    const {\n      clientX,\n      clientY\n    } = event.changedTouches[0];\n    return {\n      x: clientX,\n      y: clientY\n    };\n  }\n  if (event instanceof KeyboardEvent) {\n    const target = event.target;\n    // Calculate element midpoint\n    return {\n      x: target.offsetLeft + target.offsetWidth / 2,\n      y: target.offsetTop + target.offsetHeight / 2\n    };\n  }\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nfunction gutterEventsEqualWithDelta(startEvent, endEvent, deltaInPx, gutterElement) {\n  if (!gutterElement.contains(startEvent.target) || !gutterElement.contains(endEvent.target)) {\n    return false;\n  }\n  const startPoint = getPointFromEvent(startEvent);\n  const endPoint = getPointFromEvent(endEvent);\n  return Math.abs(endPoint.x - startPoint.x) <= deltaInPx && Math.abs(endPoint.y - startPoint.y) <= deltaInPx;\n}\nfunction fromMouseDownEvent(target) {\n  return merge(fromEvent(target, 'mousedown').pipe(filter(e => e.button === 0)),\n  // We must prevent default here so we declare it as non passive explicitly\n  fromEvent(target, 'touchstart', {\n    passive: false\n  }));\n}\nfunction fromMouseMoveEvent(target) {\n  return merge(fromEvent(target, 'mousemove'), fromEvent(target, 'touchmove'));\n}\nfunction fromMouseUpEvent(target, includeTouchCancel = false) {\n  const withoutTouchCancel = merge(fromEvent(target, 'mouseup'), fromEvent(target, 'touchend'));\n  return includeTouchCancel ? merge(withoutTouchCancel, fromEvent(target, 'touchcancel')) : withoutTouchCancel;\n}\nfunction sum(array, fn) {\n  return array.reduce((sum, item) => sum + fn(item), 0);\n}\nfunction toRecord(array, fn) {\n  return array.reduce((record, item, index) => {\n    const [key, value] = fn(item, index);\n    record[key] = value;\n    return record;\n  }, {});\n}\nfunction createClassesString(classesRecord) {\n  return Object.entries(classesRecord).filter(([, value]) => value).map(([key]) => key).join(' ');\n}\n/**\n * Creates a semi signal which allows writes but is based on an existing signal\n * Whenever the original signal changes the mirror signal gets aligned\n * overriding the current value inside.\n */\nfunction mirrorSignal(outer) {\n  const inner = computed(() => signal(outer()));\n  const mirror = () => inner()();\n  mirror.set = value => untracked(inner).set(value);\n  mirror.reset = () => untracked(() => inner().set(outer()));\n  return mirror;\n}\nfunction leaveNgZone() {\n  return source => new Observable(observer => inject(NgZone).runOutsideAngular(() => source.subscribe(observer)));\n}\nconst numberAttributeWithFallback = fallback => value => numberAttribute(value, fallback);\nconst assertUnreachable = (value, name) => {\n  throw new Error(`as-split: unknown value \"${value}\" for \"${name}\"`);\n};\n\n/* eslint-disable @angular-eslint/no-output-native */\n/* eslint-disable @angular-eslint/no-output-rename */\n/* eslint-disable @angular-eslint/no-input-rename */\n/**\n * Emits mousedown, click, double click and keydown out of zone\n *\n * Emulates browser behavior of click and double click with new features:\n * 1. Supports touch events (tap and double tap)\n * 2. Ignores the first click in a double click with the side effect of a bit slower emission of the click event\n * 3. Allow customizing the delay after mouse down to count another mouse down as a double click\n */\nclass SplitCustomEventsBehaviorDirective {\n  constructor() {\n    this.elementRef = inject(ElementRef);\n    this.document = inject(DOCUMENT);\n    this.multiClickThreshold = input.required({\n      alias: 'asSplitCustomMultiClickThreshold'\n    });\n    this.deltaInPx = input.required({\n      alias: 'asSplitCustomClickDeltaInPx'\n    });\n    this.mouseDown = output({\n      alias: 'asSplitCustomMouseDown'\n    });\n    this.click = output({\n      alias: 'asSplitCustomClick'\n    });\n    this.dblClick = output({\n      alias: 'asSplitCustomDblClick'\n    });\n    this.keyDown = output({\n      alias: 'asSplitCustomKeyDown'\n    });\n    fromEvent(this.elementRef.nativeElement, 'keydown').pipe(leaveNgZone(), takeUntilDestroyed()).subscribe(e => this.keyDown.emit(e));\n    // We just need to know when drag start to cancel all click related interactions\n    const dragStarted$ = fromMouseDownEvent(this.elementRef.nativeElement).pipe(switchMap(mouseDownEvent => fromMouseMoveEvent(this.document).pipe(filter(e => !gutterEventsEqualWithDelta(mouseDownEvent, e, this.deltaInPx(), this.elementRef.nativeElement)), take(1), map(() => true), takeUntil(fromMouseUpEvent(this.document)))));\n    fromMouseDownEvent(this.elementRef.nativeElement).pipe(tap(e => this.mouseDown.emit(e)),\n    // Gather mousedown events intervals to identify whether it is a single double or more click\n    timeInterval(),\n    // We only count a click as part of a multi click if the multiClickThreshold wasn't reached\n    scan((sum, {\n      interval\n    }) => interval >= this.multiClickThreshold() ? 1 : sum + 1, 0),\n    // As mouseup always comes after mousedown if the delayed mouseup has yet to come\n    // but a new mousedown arrived we can discard the older mouseup as we are part of a multi click\n    switchMap(numOfConsecutiveClicks =>\n    // In case of a double click we directly emit as we don't care about more than two consecutive clicks\n    // so we don't have to wait compared to a single click that might be followed by another for a double.\n    // In case of a mouse up that was too long after the mouse down\n    // we don't have to wait as we know it won't be a multi click but a single click\n    fromMouseUpEvent(this.elementRef.nativeElement).pipe(timeInterval(), take(1), numOfConsecutiveClicks === 2 ? map(() => numOfConsecutiveClicks) : mergeMap(({\n      interval\n    }) => interval >= this.multiClickThreshold() ? of(numOfConsecutiveClicks) : of(numOfConsecutiveClicks).pipe(delay(this.multiClickThreshold() - interval))))),\n    // Discard everything once drag started and listen again (repeat) to mouse down\n    takeUntil(dragStarted$), repeat(), leaveNgZone(), takeUntilDestroyed()).subscribe(amount => {\n      if (amount === 1) {\n        this.click.emit();\n      } else if (amount === 2) {\n        this.dblClick.emit();\n      }\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitCustomEventsBehaviorDirective_Factory(t) {\n      return new (t || SplitCustomEventsBehaviorDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitCustomEventsBehaviorDirective,\n      selectors: [[\"\", \"asSplitCustomEventsBehavior\", \"\"]],\n      inputs: {\n        multiClickThreshold: [i0.ɵɵInputFlags.SignalBased, \"asSplitCustomMultiClickThreshold\", \"multiClickThreshold\"],\n        deltaInPx: [i0.ɵɵInputFlags.SignalBased, \"asSplitCustomClickDeltaInPx\", \"deltaInPx\"]\n      },\n      outputs: {\n        mouseDown: \"asSplitCustomMouseDown\",\n        click: \"asSplitCustomClick\",\n        dblClick: \"asSplitCustomDblClick\",\n        keyDown: \"asSplitCustomKeyDown\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitCustomEventsBehaviorDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitCustomEventsBehavior]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nfunction areAreasValid(areas, unit, logWarnings) {\n  if (areas.length === 0) {\n    return true;\n  }\n  const areaSizes = areas.map(area => {\n    const size = area.size();\n    return size === 'auto' ? '*' : size;\n  });\n  const wildcardAreas = areaSizes.filter(areaSize => areaSize === '*');\n  if (wildcardAreas.length > 1) {\n    if (logWarnings) {\n      console.warn('as-split: Maximum one * area is allowed');\n    }\n    return false;\n  }\n  if (unit === 'pixel') {\n    if (wildcardAreas.length === 1) {\n      return true;\n    }\n    if (logWarnings) {\n      console.warn('as-split: Pixel mode must have exactly one * area');\n    }\n    return false;\n  }\n  const sumPercent = sum(areaSizes, areaSize => areaSize === '*' ? 0 : areaSize);\n  // As percent calculation isn't perfect we allow for a small margin of error\n  if (wildcardAreas.length === 1) {\n    if (sumPercent <= 100.1) {\n      return true;\n    }\n    if (logWarnings) {\n      console.warn(`as-split: Percent areas must total 100%`);\n    }\n    return false;\n  }\n  if (sumPercent < 99.9 || sumPercent > 100.1) {\n    if (logWarnings) {\n      console.warn('as-split: Percent areas must total 100%');\n    }\n    return false;\n  }\n  return true;\n}\n\n/**\n * This directive allows creating a dynamic injector inside ngFor\n * with dynamic gutter num and expose the injector for ngTemplateOutlet usage\n */\nclass SplitGutterDynamicInjectorDirective {\n  constructor() {\n    this.vcr = inject(ViewContainerRef);\n    this.templateRef = inject(TemplateRef);\n    this.gutterNum = input.required({\n      alias: 'asSplitGutterDynamicInjector'\n    });\n    effect(() => {\n      this.vcr.clear();\n      const injector = Injector.create({\n        providers: [{\n          provide: GUTTER_NUM_TOKEN,\n          useValue: this.gutterNum()\n        }],\n        parent: this.vcr.injector\n      });\n      this.vcr.createEmbeddedView(this.templateRef, {\n        $implicit: injector\n      });\n    });\n  }\n  static ngTemplateContextGuard(_dir, ctx) {\n    return true;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDynamicInjectorDirective_Factory(t) {\n      return new (t || SplitGutterDynamicInjectorDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDynamicInjectorDirective,\n      selectors: [[\"\", \"asSplitGutterDynamicInjector\", \"\"]],\n      inputs: {\n        gutterNum: [i0.ɵɵInputFlags.SignalBased, \"asSplitGutterDynamicInjector\", \"gutterNum\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDynamicInjectorDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterDynamicInjector]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nconst SPLIT_AREA_CONTRACT = new InjectionToken('Split Area Contract');\nclass SplitComponent {\n  get hostClassesBinding() {\n    return this.hostClasses();\n  }\n  get hostDirBinding() {\n    return this.dir();\n  }\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.renderer = inject(Renderer2);\n    this.elementRef = inject(ElementRef);\n    this.ngZone = inject(NgZone);\n    this.defaultOptions = inject(ANGULAR_SPLIT_DEFAULT_OPTIONS);\n    this.gutterMouseDownSubject = new Subject();\n    this.dragProgressSubject = new Subject();\n    /**\n     * @internal\n     */\n    this._areas = contentChildren(SPLIT_AREA_CONTRACT);\n    this.customGutter = contentChild(SplitGutterDirective);\n    this.gutterSize = input(this.defaultOptions.gutterSize, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterSize)\n    });\n    this.gutterStep = input(this.defaultOptions.gutterStep, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterStep)\n    });\n    this.disabled = input(this.defaultOptions.disabled, {\n      transform: booleanAttribute\n    });\n    this.gutterClickDeltaPx = input(this.defaultOptions.gutterClickDeltaPx, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterClickDeltaPx)\n    });\n    this.direction = input(this.defaultOptions.direction);\n    this.dir = input(this.defaultOptions.dir);\n    this.unit = input(this.defaultOptions.unit);\n    this.gutterAriaLabel = input();\n    this.restrictMove = input(this.defaultOptions.restrictMove, {\n      transform: booleanAttribute\n    });\n    this.useTransition = input(this.defaultOptions.useTransition, {\n      transform: booleanAttribute\n    });\n    this.gutterDblClickDuration = input(this.defaultOptions.gutterDblClickDuration, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterDblClickDuration)\n    });\n    this.gutterClick = output();\n    this.gutterDblClick = output();\n    this.dragStart = output();\n    this.dragEnd = output();\n    this.transitionEnd = output();\n    this.dragProgress$ = this.dragProgressSubject.asObservable();\n    /**\n     * @internal\n     */\n    this._visibleAreas = computed(() => this._areas().filter(area => area.visible()));\n    this.gridTemplateColumnsStyle = computed(() => this.createGridTemplateColumnsStyle());\n    this.hostClasses = computed(() => createClassesString({\n      [`as-${this.direction()}`]: true,\n      [`as-${this.unit()}`]: true,\n      ['as-disabled']: this.disabled(),\n      ['as-dragging']: this._isDragging(),\n      ['as-transition']: this.useTransition() && !this._isDragging()\n    }));\n    this.draggedGutterIndex = signal(undefined);\n    /**\n     * @internal\n     */\n    this._isDragging = computed(() => this.draggedGutterIndex() !== undefined);\n    /**\n     * @internal\n     * Should only be used by {@link SplitAreaComponent._internalSize}\n     */\n    this._alignedVisibleAreasSizes = computed(() => this.createAlignedVisibleAreasSize());\n    if (isDevMode()) {\n      // Logs warnings to console when the provided areas sizes are invalid\n      effect(() => {\n        // Special mode when no size input was declared which is a valid mode\n        if (this.unit() === 'percent' && this._visibleAreas().every(area => area.size() === 'auto')) {\n          return;\n        }\n        areAreasValid(this._visibleAreas(), this.unit(), true);\n      });\n    }\n    // Responsible for updating grid template style. Must be this way and not based on HostBinding\n    // as change detection for host binding is bound to the parent component and this style\n    // is updated on every mouse move. Doing it this way will prevent change detection cycles in parent.\n    effect(() => {\n      const gridTemplateColumnsStyle = this.gridTemplateColumnsStyle();\n      this.renderer.setStyle(this.elementRef.nativeElement, 'grid-template', gridTemplateColumnsStyle);\n    });\n    this.gutterMouseDownSubject.pipe(filter(context => !this.customGutter() || this.customGutter()._canStartDragging(context.mouseDownEvent.target, context.gutterIndex + 1)), switchMap(mouseDownContext =>\n    // As we have gutterClickDeltaPx we can't just start the drag but need to make sure\n    // we are out of the delta pixels. As the delta can be any number we make sure\n    // we always start the drag if we go out of the gutter (delta based on mouse position is larger than gutter).\n    // As moving can start inside the drag and end outside of it we always keep track of the previous event\n    // so once the current is out of the delta size we use the previous one as the drag start baseline.\n    fromMouseMoveEvent(this.document).pipe(startWith(mouseDownContext.mouseDownEvent), pairwise(), skipWhile(([, currMoveEvent]) => gutterEventsEqualWithDelta(mouseDownContext.mouseDownEvent, currMoveEvent, this.gutterClickDeltaPx(), mouseDownContext.gutterElement)), take(1), takeUntil(fromMouseUpEvent(this.document, true)), tap(() => {\n      this.ngZone.run(() => {\n        this.dragStart.emit(this.createDragInteractionEvent(mouseDownContext.gutterIndex));\n        this.draggedGutterIndex.set(mouseDownContext.gutterIndex);\n      });\n    }), map(([prevMouseEvent]) => this.createDragStartContext(prevMouseEvent, mouseDownContext.areaBeforeGutterIndex, mouseDownContext.areaAfterGutterIndex)), switchMap(dragStartContext => fromMouseMoveEvent(this.document).pipe(tap(moveEvent => this.mouseDragMove(moveEvent, dragStartContext)), takeUntil(fromMouseUpEvent(this.document, true)), tap({\n      complete: () => this.ngZone.run(() => {\n        this.dragEnd.emit(this.createDragInteractionEvent(this.draggedGutterIndex()));\n        this.draggedGutterIndex.set(undefined);\n      })\n    }))))), takeUntilDestroyed()).subscribe();\n    fromEvent(this.elementRef.nativeElement, 'transitionend').pipe(filter(e => e.propertyName.startsWith('grid-template')), leaveNgZone(), takeUntilDestroyed()).subscribe(() => this.ngZone.run(() => this.transitionEnd.emit(this.createAreaSizes())));\n  }\n  gutterClicked(gutterIndex) {\n    this.ngZone.run(() => this.gutterClick.emit(this.createDragInteractionEvent(gutterIndex)));\n  }\n  gutterDoubleClicked(gutterIndex) {\n    this.ngZone.run(() => this.gutterDblClick.emit(this.createDragInteractionEvent(gutterIndex)));\n  }\n  gutterMouseDown(e, gutterElement, gutterIndex, areaBeforeGutterIndex, areaAfterGutterIndex) {\n    if (this.disabled()) {\n      return;\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    this.gutterMouseDownSubject.next({\n      mouseDownEvent: e,\n      gutterElement,\n      gutterIndex,\n      areaBeforeGutterIndex,\n      areaAfterGutterIndex\n    });\n  }\n  gutterKeyDown(e, gutterIndex, areaBeforeGutterIndex, areaAfterGutterIndex) {\n    if (this.disabled()) {\n      return;\n    }\n    const pixelsToMove = 50;\n    const pageMoveMultiplier = 10;\n    let xPointOffset = 0;\n    let yPointOffset = 0;\n    if (this.direction() === 'horizontal') {\n      // Even though we are going in the x axis we support page up and down\n      switch (e.key) {\n        case 'ArrowLeft':\n          xPointOffset -= pixelsToMove;\n          break;\n        case 'ArrowRight':\n          xPointOffset += pixelsToMove;\n          break;\n        case 'PageUp':\n          if (this.dir() === 'rtl') {\n            xPointOffset -= pixelsToMove * pageMoveMultiplier;\n          } else {\n            xPointOffset += pixelsToMove * pageMoveMultiplier;\n          }\n          break;\n        case 'PageDown':\n          if (this.dir() === 'rtl') {\n            xPointOffset += pixelsToMove * pageMoveMultiplier;\n          } else {\n            xPointOffset -= pixelsToMove * pageMoveMultiplier;\n          }\n          break;\n        default:\n          return;\n      }\n    } else {\n      switch (e.key) {\n        case 'ArrowUp':\n          yPointOffset -= pixelsToMove;\n          break;\n        case 'ArrowDown':\n          yPointOffset += pixelsToMove;\n          break;\n        case 'PageUp':\n          yPointOffset -= pixelsToMove * pageMoveMultiplier;\n          break;\n        case 'PageDown':\n          yPointOffset += pixelsToMove * pageMoveMultiplier;\n          break;\n        default:\n          return;\n      }\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    const gutterMidPoint = getPointFromEvent(e);\n    const dragStartContext = this.createDragStartContext(e, areaBeforeGutterIndex, areaAfterGutterIndex);\n    this.ngZone.run(() => {\n      this.dragStart.emit(this.createDragInteractionEvent(gutterIndex));\n      this.draggedGutterIndex.set(gutterIndex);\n    });\n    this.dragMoveToPoint({\n      x: gutterMidPoint.x + xPointOffset,\n      y: gutterMidPoint.y + yPointOffset\n    }, dragStartContext);\n    this.ngZone.run(() => {\n      this.dragEnd.emit(this.createDragInteractionEvent(gutterIndex));\n      this.draggedGutterIndex.set(undefined);\n    });\n  }\n  getGutterGridStyle(nextAreaIndex) {\n    const gutterNum = nextAreaIndex * 2;\n    const style = `${gutterNum} / ${gutterNum}`;\n    return {\n      ['grid-column']: this.direction() === 'horizontal' ? style : '1',\n      ['grid-row']: this.direction() === 'vertical' ? style : '1'\n    };\n  }\n  getAriaAreaSizeText(area) {\n    const size = area._internalSize();\n    if (size === '*') {\n      return undefined;\n    }\n    return `${size.toFixed(0)} ${this.unit()}`;\n  }\n  getAriaValue(size) {\n    return size === '*' ? undefined : size;\n  }\n  createDragInteractionEvent(gutterIndex) {\n    return {\n      gutterNum: gutterIndex + 1,\n      sizes: this.createAreaSizes()\n    };\n  }\n  createAreaSizes() {\n    return this._visibleAreas().map(area => area._internalSize());\n  }\n  createDragStartContext(startEvent, areaBeforeGutterIndex, areaAfterGutterIndex) {\n    const splitBoundingRect = this.elementRef.nativeElement.getBoundingClientRect();\n    const splitSize = this.direction() === 'horizontal' ? splitBoundingRect.width : splitBoundingRect.height;\n    const totalAreasPixelSize = splitSize - (this._visibleAreas().length - 1) * this.gutterSize();\n    // Use the internal size and split size to calculate the pixel size from wildcard and percent areas\n    const areaPixelSizesWithWildcard = this._areas().map(area => {\n      if (this.unit() === 'pixel') {\n        return area._internalSize();\n      } else {\n        const size = area._internalSize();\n        if (size === '*') {\n          return size;\n        }\n        return size / 100 * totalAreasPixelSize;\n      }\n    });\n    const remainingSize = Math.max(0, totalAreasPixelSize - sum(areaPixelSizesWithWildcard, size => size === '*' ? 0 : size));\n    const areasPixelSizes = areaPixelSizesWithWildcard.map(size => size === '*' ? remainingSize : size);\n    return {\n      startEvent,\n      areaBeforeGutterIndex,\n      areaAfterGutterIndex,\n      areasPixelSizes,\n      totalAreasPixelSize,\n      areaIndexToBoundaries: toRecord(this._areas(), (area, index) => {\n        const percentToPixels = percent => percent / 100 * totalAreasPixelSize;\n        const value = this.unit() === 'pixel' ? {\n          min: area._normalizedMinSize(),\n          max: area._normalizedMaxSize()\n        } : {\n          min: percentToPixels(area._normalizedMinSize()),\n          max: percentToPixels(area._normalizedMaxSize())\n        };\n        return [index.toString(), value];\n      })\n    };\n  }\n  mouseDragMove(moveEvent, dragStartContext) {\n    moveEvent.preventDefault();\n    moveEvent.stopPropagation();\n    const endPoint = getPointFromEvent(moveEvent);\n    this.dragMoveToPoint(endPoint, dragStartContext);\n  }\n  dragMoveToPoint(endPoint, dragStartContext) {\n    const startPoint = getPointFromEvent(dragStartContext.startEvent);\n    const preDirOffset = this.direction() === 'horizontal' ? endPoint.x - startPoint.x : endPoint.y - startPoint.y;\n    const offset = this.direction() === 'horizontal' && this.dir() === 'rtl' ? -preDirOffset : preDirOffset;\n    const isDraggingForward = offset > 0;\n    // Align offset with gutter step and abs it as we need absolute pixels movement\n    const absSteppedOffset = Math.abs(Math.round(offset / this.gutterStep()) * this.gutterStep());\n    // Copy as we don't want to edit the original array\n    const tempAreasPixelSizes = [...dragStartContext.areasPixelSizes];\n    // As we are going to shuffle the areas order for easier iterations we should work with area indices array\n    // instead of actual area sizes array.\n    const areasIndices = tempAreasPixelSizes.map((_, index) => index);\n    // The two variables below are ordered for iterations with real area indices inside.\n    // We must also remove the invisible ones as we can't expand or shrink them.\n    const areasIndicesBeforeGutter = this.restrictMove() ? [dragStartContext.areaBeforeGutterIndex] : areasIndices.slice(0, dragStartContext.areaBeforeGutterIndex + 1).filter(index => this._areas()[index].visible()).reverse();\n    const areasIndicesAfterGutter = this.restrictMove() ? [dragStartContext.areaAfterGutterIndex] : areasIndices.slice(dragStartContext.areaAfterGutterIndex).filter(index => this._areas()[index].visible());\n    // Based on direction we need to decide which areas are expanding and which are shrinking\n    const potentialAreasIndicesArrToShrink = isDraggingForward ? areasIndicesAfterGutter : areasIndicesBeforeGutter;\n    const potentialAreasIndicesArrToExpand = isDraggingForward ? areasIndicesBeforeGutter : areasIndicesAfterGutter;\n    let remainingPixels = absSteppedOffset;\n    let potentialShrinkArrIndex = 0;\n    let potentialExpandArrIndex = 0;\n    // We gradually run in both expand and shrink direction transferring pixels from the offset.\n    // We stop once no pixels are left or we reached the end of either the expanding areas or the shrinking areas\n    while (remainingPixels !== 0 && potentialShrinkArrIndex < potentialAreasIndicesArrToShrink.length && potentialExpandArrIndex < potentialAreasIndicesArrToExpand.length) {\n      const areaIndexToShrink = potentialAreasIndicesArrToShrink[potentialShrinkArrIndex];\n      const areaIndexToExpand = potentialAreasIndicesArrToExpand[potentialExpandArrIndex];\n      const areaToShrinkSize = tempAreasPixelSizes[areaIndexToShrink];\n      const areaToExpandSize = tempAreasPixelSizes[areaIndexToExpand];\n      const areaToShrinkMinSize = dragStartContext.areaIndexToBoundaries[areaIndexToShrink].min;\n      const areaToExpandMaxSize = dragStartContext.areaIndexToBoundaries[areaIndexToExpand].max;\n      // We can only transfer pixels based on the shrinking area min size and the expanding area max size\n      // to avoid overflow. If any pixels left they will be handled by the next area in the next `while` iteration\n      const maxPixelsToShrink = areaToShrinkSize - areaToShrinkMinSize;\n      const maxPixelsToExpand = areaToExpandMaxSize - areaToExpandSize;\n      const pixelsToTransfer = Math.min(maxPixelsToShrink, maxPixelsToExpand, remainingPixels);\n      // Actual pixels transfer\n      tempAreasPixelSizes[areaIndexToShrink] -= pixelsToTransfer;\n      tempAreasPixelSizes[areaIndexToExpand] += pixelsToTransfer;\n      remainingPixels -= pixelsToTransfer;\n      // Once min threshold reached we need to move to the next area in turn\n      if (tempAreasPixelSizes[areaIndexToShrink] === areaToShrinkMinSize) {\n        potentialShrinkArrIndex++;\n      }\n      // Once max threshold reached we need to move to the next area in turn\n      if (tempAreasPixelSizes[areaIndexToExpand] === areaToExpandMaxSize) {\n        potentialExpandArrIndex++;\n      }\n    }\n    this._areas().forEach((area, index) => {\n      // No need to update wildcard size\n      if (area._internalSize() === '*') {\n        return;\n      }\n      if (this.unit() === 'pixel') {\n        area._internalSize.set(tempAreasPixelSizes[index]);\n      } else {\n        const percentSize = tempAreasPixelSizes[index] / dragStartContext.totalAreasPixelSize * 100;\n        // Fix javascript only working with float numbers which are inaccurate compared to decimals\n        area._internalSize.set(parseFloat(percentSize.toFixed(10)));\n      }\n    });\n    this.dragProgressSubject.next(this.createDragInteractionEvent(this.draggedGutterIndex()));\n  }\n  createGridTemplateColumnsStyle() {\n    const columns = [];\n    const sumNonWildcardSizes = sum(this._visibleAreas(), area => {\n      const size = area._internalSize();\n      return size === '*' ? 0 : size;\n    });\n    const visibleAreasCount = this._visibleAreas().length;\n    let visitedVisibleAreas = 0;\n    this._areas().forEach((area, index, areas) => {\n      const unit = this.unit();\n      const areaSize = area._internalSize();\n      // Add area size column\n      if (!area.visible()) {\n        columns.push(unit === 'percent' || areaSize === '*' ? '0fr' : '0px');\n      } else {\n        if (unit === 'pixel') {\n          const columnValue = areaSize === '*' ? '1fr' : `${areaSize}px`;\n          columns.push(columnValue);\n        } else {\n          const percentSize = areaSize === '*' ? 100 - sumNonWildcardSizes : areaSize;\n          const columnValue = `${percentSize}fr`;\n          columns.push(columnValue);\n        }\n        visitedVisibleAreas++;\n      }\n      const isLastArea = index === areas.length - 1;\n      if (isLastArea) {\n        return;\n      }\n      const remainingVisibleAreas = visibleAreasCount - visitedVisibleAreas;\n      // Only add gutter with size if this area is visible and there are more visible areas after this one\n      // to avoid ghost gutters\n      if (area.visible() && remainingVisibleAreas > 0) {\n        columns.push(`${this.gutterSize()}px`);\n      } else {\n        columns.push('0px');\n      }\n    });\n    return this.direction() === 'horizontal' ? `1fr / ${columns.join(' ')}` : `${columns.join(' ')} / 1fr`;\n  }\n  createAlignedVisibleAreasSize() {\n    const visibleAreasSizes = this._visibleAreas().map(area => {\n      const size = area.size();\n      return size === 'auto' ? '*' : size;\n    });\n    const isValid = areAreasValid(this._visibleAreas(), this.unit(), false);\n    if (isValid) {\n      return visibleAreasSizes;\n    }\n    const unit = this.unit();\n    if (unit === 'percent') {\n      // Distribute sizes equally\n      const defaultPercentSize = 100 / visibleAreasSizes.length;\n      return visibleAreasSizes.map(() => defaultPercentSize);\n    }\n    if (unit === 'pixel') {\n      // Make sure only one wildcard area\n      const wildcardAreas = visibleAreasSizes.filter(areaSize => areaSize === '*');\n      if (wildcardAreas.length === 0) {\n        return ['*', ...visibleAreasSizes.slice(1)];\n      } else {\n        const firstWildcardIndex = visibleAreasSizes.findIndex(areaSize => areaSize === '*');\n        const defaultPxSize = 100;\n        return visibleAreasSizes.map((areaSize, index) => index === firstWildcardIndex || areaSize !== '*' ? areaSize : defaultPxSize);\n      }\n    }\n    return assertUnreachable(unit, 'SplitUnit');\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitComponent_Factory(t) {\n      return new (t || SplitComponent)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SplitComponent,\n      selectors: [[\"as-split\"]],\n      contentQueries: function SplitComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx._areas, SPLIT_AREA_CONTRACT, 4);\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx.customGutter, SplitGutterDirective, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance(2);\n        }\n      },\n      hostVars: 3,\n      hostBindings: function SplitComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"dir\", ctx.hostDirBinding);\n          i0.ɵɵclassMap(ctx.hostClassesBinding);\n        }\n      },\n      inputs: {\n        gutterSize: [i0.ɵɵInputFlags.SignalBased, \"gutterSize\"],\n        gutterStep: [i0.ɵɵInputFlags.SignalBased, \"gutterStep\"],\n        disabled: [i0.ɵɵInputFlags.SignalBased, \"disabled\"],\n        gutterClickDeltaPx: [i0.ɵɵInputFlags.SignalBased, \"gutterClickDeltaPx\"],\n        direction: [i0.ɵɵInputFlags.SignalBased, \"direction\"],\n        dir: [i0.ɵɵInputFlags.SignalBased, \"dir\"],\n        unit: [i0.ɵɵInputFlags.SignalBased, \"unit\"],\n        gutterAriaLabel: [i0.ɵɵInputFlags.SignalBased, \"gutterAriaLabel\"],\n        restrictMove: [i0.ɵɵInputFlags.SignalBased, \"restrictMove\"],\n        useTransition: [i0.ɵɵInputFlags.SignalBased, \"useTransition\"],\n        gutterDblClickDuration: [i0.ɵɵInputFlags.SignalBased, \"gutterDblClickDuration\"]\n      },\n      outputs: {\n        gutterClick: \"gutterClick\",\n        gutterDblClick: \"gutterDblClick\",\n        dragStart: \"dragStart\",\n        dragEnd: \"dragEnd\",\n        transitionEnd: \"transitionEnd\"\n      },\n      exportAs: [\"asSplit\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"gutter\", \"\"], [\"role\", \"separator\", \"tabindex\", \"0\", \"asSplitCustomEventsBehavior\", \"\", 1, \"as-split-gutter\", 3, \"ngStyle\", \"as-dragged\", \"asSplitCustomMultiClickThreshold\", \"asSplitCustomClickDeltaInPx\"], [\"role\", \"separator\", \"tabindex\", \"0\", \"asSplitCustomEventsBehavior\", \"\", 1, \"as-split-gutter\", 3, \"asSplitCustomClick\", \"asSplitCustomDblClick\", \"asSplitCustomMouseDown\", \"asSplitCustomKeyDown\", \"ngStyle\", \"asSplitCustomMultiClickThreshold\", \"asSplitCustomClickDeltaInPx\"], [4, \"asSplitGutterDynamicInjector\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", \"ngTemplateOutletInjector\"], [1, \"as-split-gutter-icon\"]],\n      template: function SplitComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵrepeaterCreate(1, SplitComponent_For_2_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx._areas());\n        }\n      },\n      dependencies: [NgStyle, SplitCustomEventsBehaviorDirective, SplitGutterDynamicInjectorDirective, NgTemplateOutlet],\n      styles: [\"@property --as-gutter-background-color{syntax: \\\"<color>\\\"; inherits: true; initial-value: #eeeeee;}@property --as-gutter-icon-horizontal{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-gutter-icon-vertical{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC);}@property --as-gutter-icon-disabled{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-transition-duration{syntax: \\\"<time>\\\"; inherits: true; initial-value: .3s;}@property --as-gutter-disabled-cursor{syntax: \\\"*\\\"; inherits: true; initial-value: default;}[_nghost-%COMP%]{--_as-gutter-background-color: var(--as-gutter-background-color, #eeeeee);--_as-gutter-icon-horizontal: var( --as-gutter-icon-horizontal, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-gutter-icon-vertical: var( --as-gutter-icon-vertical, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC) );--_as-gutter-icon-disabled: var( --as-gutter-icon-disabled, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-transition-duration: var(--as-transition-duration, .3s);--_as-gutter-disabled-cursor: var(--as-gutter-disabled-cursor, default)}[_nghost-%COMP%]{display:grid;overflow:hidden;height:100%;width:100%}.as-transition[_nghost-%COMP%]{transition:grid-template var(--_as-transition-duration)}.as-split-gutter[_ngcontent-%COMP%]{background-color:var(--_as-gutter-background-color);display:flex;align-items:center;justify-content:center;touch-action:none}.as-horizontal[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:col-resize;height:100%}.as-vertical[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:row-resize;width:100%}.as-disabled[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:var(--_as-gutter-disabled-cursor)}.as-split-gutter-icon[_ngcontent-%COMP%]{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}.as-horizontal[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:var(--_as-gutter-icon-horizontal)}.as-vertical[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:var(--_as-gutter-icon-vertical)}.as-disabled[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:var(--_as-gutter-icon-disabled)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitComponent, [{\n    type: Component,\n    args: [{\n      selector: 'as-split',\n      imports: [NgStyle, SplitCustomEventsBehaviorDirective, SplitGutterDynamicInjectorDirective, NgTemplateOutlet],\n      exportAs: 'asSplit',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n@for (area of _areas(); track area) {\\n  @if (!$last) {\\n    <div\\n      #gutter\\n      class=\\\"as-split-gutter\\\"\\n      role=\\\"separator\\\"\\n      tabindex=\\\"0\\\"\\n      [attr.aria-label]=\\\"gutterAriaLabel()\\\"\\n      [attr.aria-orientation]=\\\"direction()\\\"\\n      [attr.aria-valuemin]=\\\"getAriaValue(area.minSize())\\\"\\n      [attr.aria-valuemax]=\\\"getAriaValue(area.maxSize())\\\"\\n      [attr.aria-valuenow]=\\\"getAriaValue(area._internalSize())\\\"\\n      [attr.aria-valuetext]=\\\"getAriaAreaSizeText(area)\\\"\\n      [ngStyle]=\\\"getGutterGridStyle($index + 1)\\\"\\n      [class.as-dragged]=\\\"draggedGutterIndex() === $index\\\"\\n      asSplitCustomEventsBehavior\\n      [asSplitCustomMultiClickThreshold]=\\\"gutterDblClickDuration()\\\"\\n      [asSplitCustomClickDeltaInPx]=\\\"gutterClickDeltaPx()\\\"\\n      (asSplitCustomClick)=\\\"gutterClicked($index)\\\"\\n      (asSplitCustomDblClick)=\\\"gutterDoubleClicked($index)\\\"\\n      (asSplitCustomMouseDown)=\\\"gutterMouseDown($event, gutter, $index, $index, $index + 1)\\\"\\n      (asSplitCustomKeyDown)=\\\"gutterKeyDown($event, $index, $index, $index + 1)\\\"\\n    >\\n      @if (customGutter()?.template) {\\n        <ng-container *asSplitGutterDynamicInjector=\\\"$index + 1; let injector\\\">\\n          <ng-container\\n            *ngTemplateOutlet=\\\"\\n              customGutter().template;\\n              context: {\\n                areaBefore: area,\\n                areaAfter: _areas()[$index + 1],\\n                gutterNum: $index + 1,\\n                first: $first,\\n                last: $index === _areas().length - 2,\\n                isDragged: draggedGutterIndex() === $index\\n              };\\n              injector: injector\\n            \\\"\\n          ></ng-container>\\n        </ng-container>\\n      } @else {\\n        <div class=\\\"as-split-gutter-icon\\\"></div>\\n      }\\n    </div>\\n  }\\n}\\n\",\n      styles: [\"@property --as-gutter-background-color{syntax: \\\"<color>\\\"; inherits: true; initial-value: #eeeeee;}@property --as-gutter-icon-horizontal{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-gutter-icon-vertical{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC);}@property --as-gutter-icon-disabled{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-transition-duration{syntax: \\\"<time>\\\"; inherits: true; initial-value: .3s;}@property --as-gutter-disabled-cursor{syntax: \\\"*\\\"; inherits: true; initial-value: default;}:host{--_as-gutter-background-color: var(--as-gutter-background-color, #eeeeee);--_as-gutter-icon-horizontal: var( --as-gutter-icon-horizontal, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-gutter-icon-vertical: var( --as-gutter-icon-vertical, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC) );--_as-gutter-icon-disabled: var( --as-gutter-icon-disabled, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-transition-duration: var(--as-transition-duration, .3s);--_as-gutter-disabled-cursor: var(--as-gutter-disabled-cursor, default)}:host{display:grid;overflow:hidden;height:100%;width:100%}:host(.as-transition){transition:grid-template var(--_as-transition-duration)}.as-split-gutter{background-color:var(--_as-gutter-background-color);display:flex;align-items:center;justify-content:center;touch-action:none}:host(.as-horizontal)>.as-split-gutter{cursor:col-resize;height:100%}:host(.as-vertical)>.as-split-gutter{cursor:row-resize;width:100%}:host(.as-disabled)>.as-split-gutter{cursor:var(--_as-gutter-disabled-cursor)}.as-split-gutter-icon{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}:host(.as-horizontal)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-horizontal)}:host(.as-vertical)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-vertical)}:host(.as-disabled)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-disabled)}\\n\"]\n    }]\n  }], () => [], {\n    hostClassesBinding: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    hostDirBinding: [{\n      type: HostBinding,\n      args: ['dir']\n    }]\n  });\n})();\nconst internalAreaSizeTransform = areaSize => areaSize === undefined || areaSize === null || areaSize === '*' ? '*' : +areaSize;\nconst areaSizeTransform = areaSize => internalAreaSizeTransform(areaSize);\nconst boundaryAreaSizeTransform = areaSize => internalAreaSizeTransform(areaSize);\nclass SplitAreaComponent {\n  constructor() {\n    this.split = inject(SplitComponent);\n    this.size = input('auto', {\n      transform: areaSizeTransform\n    });\n    this.minSize = input('*', {\n      transform: boundaryAreaSizeTransform\n    });\n    this.maxSize = input('*', {\n      transform: boundaryAreaSizeTransform\n    });\n    this.lockSize = input(false, {\n      transform: booleanAttribute\n    });\n    this.visible = input(true, {\n      transform: booleanAttribute\n    });\n    /**\n     * @internal\n     */\n    this._internalSize = mirrorSignal(\n    // As size is an input and we can change the size without the outside\n    // listening to the change we need an intermediate writeable signal\n    computed(() => {\n      if (!this.visible()) {\n        return 0;\n      }\n      const visibleIndex = this.split._visibleAreas().findIndex(area => area === this);\n      return this.split._alignedVisibleAreasSizes()[visibleIndex];\n    }));\n    /**\n     * @internal\n     */\n    this._normalizedMinSize = computed(() => this.normalizeMinSize());\n    /**\n     * @internal\n     */\n    this._normalizedMaxSize = computed(() => this.normalizeMaxSize());\n    this.index = computed(() => this.split._areas().findIndex(area => area === this));\n    this.gridAreaNum = computed(() => this.index() * 2 + 1);\n    this.hostClasses = computed(() => createClassesString({\n      ['as-split-area']: true,\n      ['as-min']: this.visible() && this._internalSize() === this._normalizedMinSize(),\n      ['as-max']: this.visible() && this._internalSize() === this._normalizedMaxSize(),\n      ['as-hidden']: !this.visible()\n    }));\n  }\n  get hostClassesBinding() {\n    return this.hostClasses();\n  }\n  get hostGridColumnStyleBinding() {\n    return this.split.direction() === 'horizontal' ? `${this.gridAreaNum()} / ${this.gridAreaNum()}` : undefined;\n  }\n  get hostGridRowStyleBinding() {\n    return this.split.direction() === 'vertical' ? `${this.gridAreaNum()} / ${this.gridAreaNum()}` : undefined;\n  }\n  get hostPositionStyleBinding() {\n    return this.split._isDragging() ? 'relative' : undefined;\n  }\n  normalizeMinSize() {\n    const defaultMinSize = 0;\n    if (!this.visible()) {\n      return defaultMinSize;\n    }\n    const minSize = this.normalizeSizeBoundary(this.minSize, defaultMinSize);\n    const size = this.size();\n    if (size !== '*' && size !== 'auto' && size < minSize) {\n      if (isDevMode()) {\n        console.warn('as-split: size cannot be smaller than minSize');\n      }\n      return defaultMinSize;\n    }\n    return minSize;\n  }\n  normalizeMaxSize() {\n    const defaultMaxSize = Infinity;\n    if (!this.visible()) {\n      return defaultMaxSize;\n    }\n    const maxSize = this.normalizeSizeBoundary(this.maxSize, defaultMaxSize);\n    const size = this.size();\n    if (size !== '*' && size !== 'auto' && size > maxSize) {\n      if (isDevMode()) {\n        console.warn('as-split: size cannot be larger than maxSize');\n      }\n      return defaultMaxSize;\n    }\n    return maxSize;\n  }\n  normalizeSizeBoundary(sizeBoundarySignal, defaultBoundarySize) {\n    const size = this.size();\n    const lockSize = this.lockSize();\n    const boundarySize = sizeBoundarySignal();\n    if (lockSize) {\n      if (isDevMode() && boundarySize !== '*') {\n        console.warn('as-split: lockSize overwrites maxSize/minSize');\n      }\n      if (size === '*' || size === 'auto') {\n        if (isDevMode()) {\n          console.warn(`as-split: lockSize isn't supported on area with * size or without size`);\n        }\n        return defaultBoundarySize;\n      }\n      return size;\n    }\n    if (boundarySize === '*') {\n      return defaultBoundarySize;\n    }\n    if (size === '*' || size === 'auto') {\n      if (isDevMode()) {\n        console.warn('as-split: maxSize/minSize not allowed on * or without size');\n      }\n      return defaultBoundarySize;\n    }\n    return boundarySize;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitAreaComponent_Factory(t) {\n      return new (t || SplitAreaComponent)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SplitAreaComponent,\n      selectors: [[\"as-split-area\"]],\n      hostVars: 8,\n      hostBindings: function SplitAreaComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.hostClassesBinding);\n          i0.ɵɵstyleProp(\"grid-column\", ctx.hostGridColumnStyleBinding)(\"grid-row\", ctx.hostGridRowStyleBinding)(\"position\", ctx.hostPositionStyleBinding);\n        }\n      },\n      inputs: {\n        size: [i0.ɵɵInputFlags.SignalBased, \"size\"],\n        minSize: [i0.ɵɵInputFlags.SignalBased, \"minSize\"],\n        maxSize: [i0.ɵɵInputFlags.SignalBased, \"maxSize\"],\n        lockSize: [i0.ɵɵInputFlags.SignalBased, \"lockSize\"],\n        visible: [i0.ɵɵInputFlags.SignalBased, \"visible\"]\n      },\n      exportAs: [\"asSplitArea\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: SPLIT_AREA_CONTRACT,\n        useExisting: SplitAreaComponent\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"as-iframe-fix\"]],\n      template: function SplitAreaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, SplitAreaComponent_Conditional_1_Template, 1, 0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.split._isDragging() ? 1 : -1);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{overflow-x:hidden;overflow-y:auto}.as-horizontal > [_nghost-%COMP%]{height:100%}.as-vertical > [_nghost-%COMP%]{width:100%}.as-iframe-fix[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitAreaComponent, [{\n    type: Component,\n    args: [{\n      selector: 'as-split-area',\n      standalone: true,\n      exportAs: 'asSplitArea',\n      providers: [{\n        provide: SPLIT_AREA_CONTRACT,\n        useExisting: SplitAreaComponent\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n@if (split._isDragging()) {\\n  <div class=\\\"as-iframe-fix\\\"></div>\\n}\\n\",\n      styles: [\":host{overflow-x:hidden;overflow-y:auto}.as-horizontal>:host{height:100%}.as-vertical>:host{width:100%}.as-iframe-fix{position:absolute;top:0;left:0;width:100%;height:100%}\\n\"]\n    }]\n  }], null, {\n    hostClassesBinding: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    hostGridColumnStyleBinding: [{\n      type: HostBinding,\n      args: ['style.grid-column']\n    }],\n    hostGridRowStyleBinding: [{\n      type: HostBinding,\n      args: ['style.grid-row']\n    }],\n    hostPositionStyleBinding: [{\n      type: HostBinding,\n      args: ['style.position']\n    }]\n  });\n})();\nclass AngularSplitModule {\n  /** @nocollapse */static {\n    this.ɵfac = function AngularSplitModule_Factory(t) {\n      return new (t || AngularSplitModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AngularSplitModule,\n      imports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective],\n      exports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective]\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularSplitModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective],\n      exports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-split\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularSplitModule, SplitAreaComponent, SplitComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective, provideAngularSplitOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,SAAS,mBAAmB,YAAY;AACpC,MAAI,CAAC,YAAY;AACb,6BAAyB,kBAAkB;AAC3C,iBAAa,OAAO,UAAU;AAAA,EAClC;AACA,QAAM,aAAa,IAAI,WAAW,cAAY;AAC1C,UAAM,eAAe,WAAW,UAAU,SAAS,KAAK,KAAK,QAAQ,CAAC;AACtE,WAAO;AAAA,EACX,CAAC;AACD,SAAO,CAAC,WAAW;AACf,WAAO,OAAO,KAAK,UAAU,UAAU,CAAC;AAAA,EAC5C;AACJ;;;AC3BA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,EACvC,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AACb;AACA,SAAS,wFAAwF,IAAI,KAAK;AACxG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AACzF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yFAAyF,GAAG,GAAG,gBAAgB,CAAC;AACjI,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,UAAU,OAAO;AACvB,UAAM,YAAY,OAAO;AACzB,UAAM,eAAe,OAAO;AAC5B,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa,EAAE,QAAQ,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,SAAS,OAAO,OAAO,EAAE,YAAY,CAAC,GAAG,YAAY,GAAG,iBAAiB,GAAG,cAAc,OAAO,OAAO,EAAE,SAAS,GAAG,OAAO,mBAAmB,MAAM,SAAS,CAAC,EAAE,4BAA4B,WAAW;AAAA,EACnU;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,IAAI,gBAAgB,CAAC;AAAA,EACrH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAe,cAAc,CAAC,EAAE;AACtC,IAAG,WAAW,gCAAgC,YAAY,CAAC;AAAA,EAC7D;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,WAAW,sBAAsB,SAAS,gFAAgF;AAC3H,MAAG,cAAc,GAAG;AACpB,YAAM,YAAe,cAAc,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,SAAS,CAAC;AAAA,IACvD,CAAC,EAAE,yBAAyB,SAAS,mFAAmF;AACtH,MAAG,cAAc,GAAG;AACpB,YAAM,YAAe,cAAc,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,SAAS,CAAC;AAAA,IAC7D,CAAC,EAAE,0BAA0B,SAAS,kFAAkF,QAAQ;AAC9H,MAAG,cAAc,GAAG;AACpB,YAAM,YAAe,YAAY,CAAC;AAClC,YAAM,YAAe,cAAc,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,QAAQ,WAAW,WAAW,WAAW,YAAY,CAAC,CAAC;AAAA,IACtG,CAAC,EAAE,wBAAwB,SAAS,gFAAgF,QAAQ;AAC1H,MAAG,cAAc,GAAG;AACpB,YAAM,YAAe,cAAc,EAAE;AACrC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,QAAQ,WAAW,WAAW,YAAY,CAAC,CAAC;AAAA,IACzF,CAAC;AACD,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,cAAc,EAAE,GAAG,2DAA2D,GAAG,CAAC;AACpK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,UAAM,UAAU,OAAO;AACvB,UAAM,YAAY,OAAO;AACzB,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,cAAc,OAAO,mBAAmB,MAAM,SAAS;AACtE,IAAG,WAAW,WAAW,OAAO,mBAAmB,YAAY,CAAC,CAAC,EAAE,oCAAoC,OAAO,uBAAuB,CAAC,EAAE,+BAA+B,OAAO,mBAAmB,CAAC;AAClM,IAAG,YAAY,cAAc,OAAO,gBAAgB,CAAC,EAAE,oBAAoB,OAAO,UAAU,CAAC,EAAE,iBAAiB,OAAO,aAAa,QAAQ,QAAQ,CAAC,CAAC,EAAE,iBAAiB,OAAO,aAAa,QAAQ,QAAQ,CAAC,CAAC,EAAE,iBAAiB,OAAO,aAAa,QAAQ,cAAc,CAAC,CAAC,EAAE,kBAAkB,OAAO,oBAAoB,OAAO,CAAC;AACrU,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,KAAK,WAAW,OAAO,aAAa,MAAM,OAAO,OAAO,SAAS,YAAY,IAAI,CAAC;AAAA,EACrG;AACF;AACA,SAAS,8BAA8B,IAAI,KAAK;AAC9C,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,6CAA6C,GAAG,IAAI,OAAO,CAAC;AAAA,EAC/E;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,UAAM,eAAe,IAAI;AACzB,IAAG,cAAc,GAAG,EAAE,iBAAiB,eAAe,KAAK,IAAI,EAAE;AAAA,EACnE;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB,KAAK;AAAA,EACL,WAAW;AAAA,EACX,UAAU;AAAA,EACV,wBAAwB;AAAA,EACxB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,MAAM;AAAA,EACN,eAAe;AACjB;AACA,IAAM,gCAAgC,IAAI,eAAe,+BAA+B;AAAA,EACtF,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAKD,SAAS,2BAA2B,SAAS;AAC3C,SAAO;AAAA,IACL,SAAS;AAAA,IACT,YAAY,MAAO,kCACd,OAAO,+BAA+B;AAAA,MACvC,UAAU;AAAA,IACZ,CAAC,IACE;AAAA,EAEP;AACF;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,WAAW,OAAO,WAAW;AAOlC,SAAK,4BAA4B,oBAAI,IAAI;AAOzC,SAAK,iCAAiC,oBAAI,IAAI;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,eAAe,WAAW;AAC1C,QAAI,KAAK,+BAA+B,IAAI,SAAS,GAAG;AACtD,YAAM,kBAAkB,KAAK,+BAA+B,IAAI,SAAS,EAAE,KAAK,0BAAwB,qBAAqB,cAAc,SAAS,aAAa,CAAC;AAClK,UAAI,iBAAiB;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,KAAK,0BAA0B,IAAI,SAAS,GAAG;AACjD,aAAO,KAAK,0BAA0B,IAAI,SAAS,EAAE,KAAK,yBAAuB,oBAAoB,cAAc,SAAS,aAAa,CAAC;AAAA,IAC5I;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,UAAUA,MAAK,WAAW,YAAY;AACpC,QAAIA,KAAI,IAAI,SAAS,GAAG;AACtB,MAAAA,KAAI,IAAI,SAAS,EAAE,KAAK,UAAU;AAAA,IACpC,OAAO;AACL,MAAAA,KAAI,IAAI,WAAW,CAAC,UAAU,CAAC;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgBA,MAAK,WAAW,YAAY;AAC1C,UAAM,WAAWA,KAAI,IAAI,SAAS;AAClC,aAAS,OAAO,SAAS,QAAQ,UAAU,GAAG,CAAC;AAC/C,QAAI,SAAS,WAAW,GAAG;AACzB,MAAAA,KAAI,OAAO,SAAS;AAAA,IACtB;AAAA,EACF;AAAA,EACA,OAAO,uBAAuB,MAAM,KAAK;AACvC,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAsB;AAAA,IACzC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,mBAAmB,IAAI,eAAe,YAAY;AACxD,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,cAAc;AACZ,SAAK,YAAY,OAAO,gBAAgB;AACxC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,YAAY,OAAO,oBAAoB;AAC5C,SAAK,UAAU,UAAU,KAAK,UAAU,2BAA2B,KAAK,WAAW,KAAK,UAAU;AAAA,EACpG;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,gBAAgB,KAAK,UAAU,2BAA2B,KAAK,WAAW,KAAK,UAAU;AAAA,EAC1G;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,GAAG;AAC7D,aAAO,KAAK,KAAK,iCAAgC;AAAA,IACnD;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,MAC/C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,sCAAN,MAAM,qCAAoC;AAAA,EACxC,cAAc;AACZ,SAAK,YAAY,OAAO,gBAAgB;AACxC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,YAAY,OAAO,oBAAoB;AAC5C,SAAK,UAAU,UAAU,KAAK,UAAU,gCAAgC,KAAK,WAAW,KAAK,UAAU;AAAA,EACzG;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,gBAAgB,KAAK,UAAU,gCAAgC,KAAK,WAAW,KAAK,UAAU;AAAA,EAC/G;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,4CAA4C,GAAG;AAClE,aAAO,KAAK,KAAK,sCAAqC;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gCAAgC,EAAE,CAAC;AAAA,MACpD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,kBAAkB,OAAO;AAEhC,QAAM,eAAe,OAAK,OAAO,cAAc,iBAAiB;AAChE,MAAI,aAAa,KAAK,GAAG;AACvB,QAAI,MAAM,eAAe,WAAW,GAAG;AACrC,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,MAAM,eAAe,CAAC;AAC1B,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,MAAI,iBAAiB,eAAe;AAClC,UAAM,SAAS,MAAM;AAErB,WAAO;AAAA,MACL,GAAG,OAAO,aAAa,OAAO,cAAc;AAAA,MAC5C,GAAG,OAAO,YAAY,OAAO,eAAe;AAAA,IAC9C;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG,MAAM;AAAA,IACT,GAAG,MAAM;AAAA,EACX;AACF;AACA,SAAS,2BAA2B,YAAY,UAAU,WAAW,eAAe;AAClF,MAAI,CAAC,cAAc,SAAS,WAAW,MAAM,KAAK,CAAC,cAAc,SAAS,SAAS,MAAM,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,QAAM,aAAa,kBAAkB,UAAU;AAC/C,QAAM,WAAW,kBAAkB,QAAQ;AAC3C,SAAO,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC,KAAK,aAAa,KAAK,IAAI,SAAS,IAAI,WAAW,CAAC,KAAK;AACpG;AACA,SAAS,mBAAmB,QAAQ;AAClC,SAAO;AAAA,IAAM,UAAU,QAAQ,WAAW,EAAE,KAAK,OAAO,OAAK,EAAE,WAAW,CAAC,CAAC;AAAA;AAAA,IAE5E,UAAU,QAAQ,cAAc;AAAA,MAC9B,SAAS;AAAA,IACX,CAAC;AAAA,EAAC;AACJ;AACA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,MAAM,UAAU,QAAQ,WAAW,GAAG,UAAU,QAAQ,WAAW,CAAC;AAC7E;AACA,SAAS,iBAAiB,QAAQ,qBAAqB,OAAO;AAC5D,QAAM,qBAAqB,MAAM,UAAU,QAAQ,SAAS,GAAG,UAAU,QAAQ,UAAU,CAAC;AAC5F,SAAO,qBAAqB,MAAM,oBAAoB,UAAU,QAAQ,aAAa,CAAC,IAAI;AAC5F;AACA,SAAS,IAAI,OAAO,IAAI;AACtB,SAAO,MAAM,OAAO,CAACC,MAAK,SAASA,OAAM,GAAG,IAAI,GAAG,CAAC;AACtD;AACA,SAAS,SAAS,OAAO,IAAI;AAC3B,SAAO,MAAM,OAAO,CAAC,QAAQ,MAAM,UAAU;AAC3C,UAAM,CAAC,KAAK,KAAK,IAAI,GAAG,MAAM,KAAK;AACnC,WAAO,GAAG,IAAI;AACd,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,oBAAoB,eAAe;AAC1C,SAAO,OAAO,QAAQ,aAAa,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE,KAAK,GAAG;AAChG;AAMA,SAAS,aAAa,OAAO;AAC3B,QAAM,QAAQ,SAAS,MAAM,OAAO,MAAM,CAAC,CAAC;AAC5C,QAAM,SAAS,MAAM,MAAM,EAAE;AAC7B,SAAO,MAAM,WAAS,UAAU,KAAK,EAAE,IAAI,KAAK;AAChD,SAAO,QAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,MAAM,CAAC,CAAC;AACzD,SAAO;AACT;AACA,SAAS,cAAc;AACrB,SAAO,YAAU,IAAI,WAAW,cAAY,OAAO,MAAM,EAAE,kBAAkB,MAAM,OAAO,UAAU,QAAQ,CAAC,CAAC;AAChH;AACA,IAAM,8BAA8B,cAAY,WAAS,gBAAgB,OAAO,QAAQ;AACxF,IAAM,oBAAoB,CAAC,OAAO,SAAS;AACzC,QAAM,IAAI,MAAM,4BAA4B,KAAK,UAAU,IAAI,GAAG;AACpE;AAaA,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,sBAAsB,MAAM,SAAS;AAAA,MACxC,OAAO;AAAA,IACT,CAAC;AACD,SAAK,YAAY,MAAM,SAAS;AAAA,MAC9B,OAAO;AAAA,IACT,CAAC;AACD,SAAK,YAAY,OAAO;AAAA,MACtB,OAAO;AAAA,IACT,CAAC;AACD,SAAK,QAAQ,OAAO;AAAA,MAClB,OAAO;AAAA,IACT,CAAC;AACD,SAAK,WAAW,OAAO;AAAA,MACrB,OAAO;AAAA,IACT,CAAC;AACD,SAAK,UAAU,OAAO;AAAA,MACpB,OAAO;AAAA,IACT,CAAC;AACD,cAAU,KAAK,WAAW,eAAe,SAAS,EAAE,KAAK,YAAY,GAAG,mBAAmB,CAAC,EAAE,UAAU,OAAK,KAAK,QAAQ,KAAK,CAAC,CAAC;AAEjI,UAAM,eAAe,mBAAmB,KAAK,WAAW,aAAa,EAAE,KAAK,UAAU,oBAAkB,mBAAmB,KAAK,QAAQ,EAAE,KAAK,OAAO,OAAK,CAAC,2BAA2B,gBAAgB,GAAG,KAAK,UAAU,GAAG,KAAK,WAAW,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,MAAM,IAAI,GAAG,UAAU,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC;AACnU,uBAAmB,KAAK,WAAW,aAAa,EAAE;AAAA,MAAK,IAAI,OAAK,KAAK,UAAU,KAAK,CAAC,CAAC;AAAA;AAAA,MAEtF,aAAa;AAAA;AAAA,MAEb,KAAK,CAACA,MAAK;AAAA,QACT;AAAA,MACF,MAAM,YAAY,KAAK,oBAAoB,IAAI,IAAIA,OAAM,GAAG,CAAC;AAAA;AAAA;AAAA,MAG7D,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,QAKV,iBAAiB,KAAK,WAAW,aAAa,EAAE,KAAK,aAAa,GAAG,KAAK,CAAC,GAAG,2BAA2B,IAAI,IAAI,MAAM,sBAAsB,IAAI,SAAS,CAAC;AAAA,UACzJ;AAAA,QACF,MAAM,YAAY,KAAK,oBAAoB,IAAI,GAAG,sBAAsB,IAAI,GAAG,sBAAsB,EAAE,KAAK,MAAM,KAAK,oBAAoB,IAAI,QAAQ,CAAC,CAAC,CAAC;AAAA,OAAC;AAAA;AAAA,MAE3J,UAAU,YAAY;AAAA,MAAG,OAAO;AAAA,MAAG,YAAY;AAAA,MAAG,mBAAmB;AAAA,IAAC,EAAE,UAAU,YAAU;AAC1F,UAAI,WAAW,GAAG;AAChB,aAAK,MAAM,KAAK;AAAA,MAClB,WAAW,WAAW,GAAG;AACvB,aAAK,SAAS,KAAK;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,GAAG;AACjE,aAAO,KAAK,KAAK,qCAAoC;AAAA,IACvD;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,+BAA+B,EAAE,CAAC;AAAA,MACnD,QAAQ;AAAA,QACN,qBAAqB,CAAI,WAAa,aAAa,oCAAoC,qBAAqB;AAAA,QAC5G,WAAW,CAAI,WAAa,aAAa,+BAA+B,WAAW;AAAA,MACrF;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,cAAc,OAAO,MAAM,aAAa;AAC/C,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,MAAM,IAAI,UAAQ;AAClC,UAAM,OAAO,KAAK,KAAK;AACvB,WAAO,SAAS,SAAS,MAAM;AAAA,EACjC,CAAC;AACD,QAAM,gBAAgB,UAAU,OAAO,cAAY,aAAa,GAAG;AACnE,MAAI,cAAc,SAAS,GAAG;AAC5B,QAAI,aAAa;AACf,cAAQ,KAAK,yCAAyC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AACA,MAAI,SAAS,SAAS;AACpB,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,QAAI,aAAa;AACf,cAAQ,KAAK,mDAAmD;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAa,IAAI,WAAW,cAAY,aAAa,MAAM,IAAI,QAAQ;AAE7E,MAAI,cAAc,WAAW,GAAG;AAC9B,QAAI,cAAc,OAAO;AACvB,aAAO;AAAA,IACT;AACA,QAAI,aAAa;AACf,cAAQ,KAAK,yCAAyC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AACA,MAAI,aAAa,QAAQ,aAAa,OAAO;AAC3C,QAAI,aAAa;AACf,cAAQ,KAAK,yCAAyC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAMA,IAAM,sCAAN,MAAM,qCAAoC;AAAA,EACxC,cAAc;AACZ,SAAK,MAAM,OAAO,gBAAgB;AAClC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,YAAY,MAAM,SAAS;AAAA,MAC9B,OAAO;AAAA,IACT,CAAC;AACD,WAAO,MAAM;AACX,WAAK,IAAI,MAAM;AACf,YAAM,WAAW,SAAS,OAAO;AAAA,QAC/B,WAAW,CAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU,KAAK,UAAU;AAAA,QAC3B,CAAC;AAAA,QACD,QAAQ,KAAK,IAAI;AAAA,MACnB,CAAC;AACD,WAAK,IAAI,mBAAmB,KAAK,aAAa;AAAA,QAC5C,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO,uBAAuB,MAAM,KAAK;AACvC,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,4CAA4C,GAAG;AAClE,aAAO,KAAK,KAAK,sCAAqC;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gCAAgC,EAAE,CAAC;AAAA,MACpD,QAAQ;AAAA,QACN,WAAW,CAAI,WAAa,aAAa,gCAAgC,WAAW;AAAA,MACtF;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qCAAqC,CAAC;AAAA,IAC5G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,sBAAsB,IAAI,eAAe,qBAAqB;AACpE,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,IAAI,qBAAqB;AACvB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,iBAAiB,OAAO,6BAA6B;AAC1D,SAAK,yBAAyB,IAAI,QAAQ;AAC1C,SAAK,sBAAsB,IAAI,QAAQ;AAIvC,SAAK,SAAS,gBAAgB,mBAAmB;AACjD,SAAK,eAAe,aAAa,oBAAoB;AACrD,SAAK,aAAa,MAAM,KAAK,eAAe,YAAY;AAAA,MACtD,WAAW,4BAA4B,KAAK,eAAe,UAAU;AAAA,IACvE,CAAC;AACD,SAAK,aAAa,MAAM,KAAK,eAAe,YAAY;AAAA,MACtD,WAAW,4BAA4B,KAAK,eAAe,UAAU;AAAA,IACvE,CAAC;AACD,SAAK,WAAW,MAAM,KAAK,eAAe,UAAU;AAAA,MAClD,WAAW;AAAA,IACb,CAAC;AACD,SAAK,qBAAqB,MAAM,KAAK,eAAe,oBAAoB;AAAA,MACtE,WAAW,4BAA4B,KAAK,eAAe,kBAAkB;AAAA,IAC/E,CAAC;AACD,SAAK,YAAY,MAAM,KAAK,eAAe,SAAS;AACpD,SAAK,MAAM,MAAM,KAAK,eAAe,GAAG;AACxC,SAAK,OAAO,MAAM,KAAK,eAAe,IAAI;AAC1C,SAAK,kBAAkB,MAAM;AAC7B,SAAK,eAAe,MAAM,KAAK,eAAe,cAAc;AAAA,MAC1D,WAAW;AAAA,IACb,CAAC;AACD,SAAK,gBAAgB,MAAM,KAAK,eAAe,eAAe;AAAA,MAC5D,WAAW;AAAA,IACb,CAAC;AACD,SAAK,yBAAyB,MAAM,KAAK,eAAe,wBAAwB;AAAA,MAC9E,WAAW,4BAA4B,KAAK,eAAe,sBAAsB;AAAA,IACnF,CAAC;AACD,SAAK,cAAc,OAAO;AAC1B,SAAK,iBAAiB,OAAO;AAC7B,SAAK,YAAY,OAAO;AACxB,SAAK,UAAU,OAAO;AACtB,SAAK,gBAAgB,OAAO;AAC5B,SAAK,gBAAgB,KAAK,oBAAoB,aAAa;AAI3D,SAAK,gBAAgB,SAAS,MAAM,KAAK,OAAO,EAAE,OAAO,UAAQ,KAAK,QAAQ,CAAC,CAAC;AAChF,SAAK,2BAA2B,SAAS,MAAM,KAAK,+BAA+B,CAAC;AACpF,SAAK,cAAc,SAAS,MAAM,oBAAoB;AAAA,MACpD,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,GAAG;AAAA,MAC5B,CAAC,MAAM,KAAK,KAAK,CAAC,EAAE,GAAG;AAAA,MACvB,CAAC,aAAa,GAAG,KAAK,SAAS;AAAA,MAC/B,CAAC,aAAa,GAAG,KAAK,YAAY;AAAA,MAClC,CAAC,eAAe,GAAG,KAAK,cAAc,KAAK,CAAC,KAAK,YAAY;AAAA,IAC/D,CAAC,CAAC;AACF,SAAK,qBAAqB,OAAO,MAAS;AAI1C,SAAK,cAAc,SAAS,MAAM,KAAK,mBAAmB,MAAM,MAAS;AAKzE,SAAK,4BAA4B,SAAS,MAAM,KAAK,8BAA8B,CAAC;AACpF,QAAI,UAAU,GAAG;AAEf,aAAO,MAAM;AAEX,YAAI,KAAK,KAAK,MAAM,aAAa,KAAK,cAAc,EAAE,MAAM,UAAQ,KAAK,KAAK,MAAM,MAAM,GAAG;AAC3F;AAAA,QACF;AACA,sBAAc,KAAK,cAAc,GAAG,KAAK,KAAK,GAAG,IAAI;AAAA,MACvD,CAAC;AAAA,IACH;AAIA,WAAO,MAAM;AACX,YAAM,2BAA2B,KAAK,yBAAyB;AAC/D,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,iBAAiB,wBAAwB;AAAA,IACjG,CAAC;AACD,SAAK,uBAAuB,KAAK,OAAO,aAAW,CAAC,KAAK,aAAa,KAAK,KAAK,aAAa,EAAE,kBAAkB,QAAQ,eAAe,QAAQ,QAAQ,cAAc,CAAC,CAAC,GAAG,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMrL,mBAAmB,KAAK,QAAQ,EAAE,KAAK,UAAU,iBAAiB,cAAc,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC,EAAE,aAAa,MAAM,2BAA2B,iBAAiB,gBAAgB,eAAe,KAAK,mBAAmB,GAAG,iBAAiB,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,UAAU,iBAAiB,KAAK,UAAU,IAAI,CAAC,GAAG,IAAI,MAAM;AAC3U,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,UAAU,KAAK,KAAK,2BAA2B,iBAAiB,WAAW,CAAC;AACjF,eAAK,mBAAmB,IAAI,iBAAiB,WAAW;AAAA,QAC1D,CAAC;AAAA,MACH,CAAC,GAAG,IAAI,CAAC,CAAC,cAAc,MAAM,KAAK,uBAAuB,gBAAgB,iBAAiB,uBAAuB,iBAAiB,oBAAoB,CAAC,GAAG,UAAU,sBAAoB,mBAAmB,KAAK,QAAQ,EAAE,KAAK,IAAI,eAAa,KAAK,cAAc,WAAW,gBAAgB,CAAC,GAAG,UAAU,iBAAiB,KAAK,UAAU,IAAI,CAAC,GAAG,IAAI;AAAA,QACvV,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM;AACpC,eAAK,QAAQ,KAAK,KAAK,2BAA2B,KAAK,mBAAmB,CAAC,CAAC;AAC5E,eAAK,mBAAmB,IAAI,MAAS;AAAA,QACvC,CAAC;AAAA,MACH,CAAC,CAAC,CAAC,CAAC;AAAA,KAAC,GAAG,mBAAmB,CAAC,EAAE,UAAU;AACxC,cAAU,KAAK,WAAW,eAAe,eAAe,EAAE,KAAK,OAAO,OAAK,EAAE,aAAa,WAAW,eAAe,CAAC,GAAG,YAAY,GAAG,mBAAmB,CAAC,EAAE,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,cAAc,KAAK,KAAK,gBAAgB,CAAC,CAAC,CAAC;AAAA,EACrP;AAAA,EACA,cAAc,aAAa;AACzB,SAAK,OAAO,IAAI,MAAM,KAAK,YAAY,KAAK,KAAK,2BAA2B,WAAW,CAAC,CAAC;AAAA,EAC3F;AAAA,EACA,oBAAoB,aAAa;AAC/B,SAAK,OAAO,IAAI,MAAM,KAAK,eAAe,KAAK,KAAK,2BAA2B,WAAW,CAAC,CAAC;AAAA,EAC9F;AAAA,EACA,gBAAgB,GAAG,eAAe,aAAa,uBAAuB,sBAAsB;AAC1F,QAAI,KAAK,SAAS,GAAG;AACnB;AAAA,IACF;AACA,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,SAAK,uBAAuB,KAAK;AAAA,MAC/B,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc,GAAG,aAAa,uBAAuB,sBAAsB;AACzE,QAAI,KAAK,SAAS,GAAG;AACnB;AAAA,IACF;AACA,UAAM,eAAe;AACrB,UAAM,qBAAqB;AAC3B,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,KAAK,UAAU,MAAM,cAAc;AAErC,cAAQ,EAAE,KAAK;AAAA,QACb,KAAK;AACH,0BAAgB;AAChB;AAAA,QACF,KAAK;AACH,0BAAgB;AAChB;AAAA,QACF,KAAK;AACH,cAAI,KAAK,IAAI,MAAM,OAAO;AACxB,4BAAgB,eAAe;AAAA,UACjC,OAAO;AACL,4BAAgB,eAAe;AAAA,UACjC;AACA;AAAA,QACF,KAAK;AACH,cAAI,KAAK,IAAI,MAAM,OAAO;AACxB,4BAAgB,eAAe;AAAA,UACjC,OAAO;AACL,4BAAgB,eAAe;AAAA,UACjC;AACA;AAAA,QACF;AACE;AAAA,MACJ;AAAA,IACF,OAAO;AACL,cAAQ,EAAE,KAAK;AAAA,QACb,KAAK;AACH,0BAAgB;AAChB;AAAA,QACF,KAAK;AACH,0BAAgB;AAChB;AAAA,QACF,KAAK;AACH,0BAAgB,eAAe;AAC/B;AAAA,QACF,KAAK;AACH,0BAAgB,eAAe;AAC/B;AAAA,QACF;AACE;AAAA,MACJ;AAAA,IACF;AACA,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAClB,UAAM,iBAAiB,kBAAkB,CAAC;AAC1C,UAAM,mBAAmB,KAAK,uBAAuB,GAAG,uBAAuB,oBAAoB;AACnG,SAAK,OAAO,IAAI,MAAM;AACpB,WAAK,UAAU,KAAK,KAAK,2BAA2B,WAAW,CAAC;AAChE,WAAK,mBAAmB,IAAI,WAAW;AAAA,IACzC,CAAC;AACD,SAAK,gBAAgB;AAAA,MACnB,GAAG,eAAe,IAAI;AAAA,MACtB,GAAG,eAAe,IAAI;AAAA,IACxB,GAAG,gBAAgB;AACnB,SAAK,OAAO,IAAI,MAAM;AACpB,WAAK,QAAQ,KAAK,KAAK,2BAA2B,WAAW,CAAC;AAC9D,WAAK,mBAAmB,IAAI,MAAS;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,eAAe;AAChC,UAAM,YAAY,gBAAgB;AAClC,UAAM,QAAQ,GAAG,SAAS,MAAM,SAAS;AACzC,WAAO;AAAA,MACL,CAAC,aAAa,GAAG,KAAK,UAAU,MAAM,eAAe,QAAQ;AAAA,MAC7D,CAAC,UAAU,GAAG,KAAK,UAAU,MAAM,aAAa,QAAQ;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,oBAAoB,MAAM;AACxB,UAAM,OAAO,KAAK,cAAc;AAChC,QAAI,SAAS,KAAK;AAChB,aAAO;AAAA,IACT;AACA,WAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,SAAS,MAAM,SAAY;AAAA,EACpC;AAAA,EACA,2BAA2B,aAAa;AACtC,WAAO;AAAA,MACL,WAAW,cAAc;AAAA,MACzB,OAAO,KAAK,gBAAgB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,cAAc,EAAE,IAAI,UAAQ,KAAK,cAAc,CAAC;AAAA,EAC9D;AAAA,EACA,uBAAuB,YAAY,uBAAuB,sBAAsB;AAC9E,UAAM,oBAAoB,KAAK,WAAW,cAAc,sBAAsB;AAC9E,UAAM,YAAY,KAAK,UAAU,MAAM,eAAe,kBAAkB,QAAQ,kBAAkB;AAClG,UAAM,sBAAsB,aAAa,KAAK,cAAc,EAAE,SAAS,KAAK,KAAK,WAAW;AAE5F,UAAM,6BAA6B,KAAK,OAAO,EAAE,IAAI,UAAQ;AAC3D,UAAI,KAAK,KAAK,MAAM,SAAS;AAC3B,eAAO,KAAK,cAAc;AAAA,MAC5B,OAAO;AACL,cAAM,OAAO,KAAK,cAAc;AAChC,YAAI,SAAS,KAAK;AAChB,iBAAO;AAAA,QACT;AACA,eAAO,OAAO,MAAM;AAAA,MACtB;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,KAAK,IAAI,GAAG,sBAAsB,IAAI,4BAA4B,UAAQ,SAAS,MAAM,IAAI,IAAI,CAAC;AACxH,UAAM,kBAAkB,2BAA2B,IAAI,UAAQ,SAAS,MAAM,gBAAgB,IAAI;AAClG,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,uBAAuB,SAAS,KAAK,OAAO,GAAG,CAAC,MAAM,UAAU;AAC9D,cAAM,kBAAkB,aAAW,UAAU,MAAM;AACnD,cAAM,QAAQ,KAAK,KAAK,MAAM,UAAU;AAAA,UACtC,KAAK,KAAK,mBAAmB;AAAA,UAC7B,KAAK,KAAK,mBAAmB;AAAA,QAC/B,IAAI;AAAA,UACF,KAAK,gBAAgB,KAAK,mBAAmB,CAAC;AAAA,UAC9C,KAAK,gBAAgB,KAAK,mBAAmB,CAAC;AAAA,QAChD;AACA,eAAO,CAAC,MAAM,SAAS,GAAG,KAAK;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc,WAAW,kBAAkB;AACzC,cAAU,eAAe;AACzB,cAAU,gBAAgB;AAC1B,UAAM,WAAW,kBAAkB,SAAS;AAC5C,SAAK,gBAAgB,UAAU,gBAAgB;AAAA,EACjD;AAAA,EACA,gBAAgB,UAAU,kBAAkB;AAC1C,UAAM,aAAa,kBAAkB,iBAAiB,UAAU;AAChE,UAAM,eAAe,KAAK,UAAU,MAAM,eAAe,SAAS,IAAI,WAAW,IAAI,SAAS,IAAI,WAAW;AAC7G,UAAM,SAAS,KAAK,UAAU,MAAM,gBAAgB,KAAK,IAAI,MAAM,QAAQ,CAAC,eAAe;AAC3F,UAAM,oBAAoB,SAAS;AAEnC,UAAM,mBAAmB,KAAK,IAAI,KAAK,MAAM,SAAS,KAAK,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC;AAE5F,UAAM,sBAAsB,CAAC,GAAG,iBAAiB,eAAe;AAGhE,UAAM,eAAe,oBAAoB,IAAI,CAAC,GAAG,UAAU,KAAK;AAGhE,UAAM,2BAA2B,KAAK,aAAa,IAAI,CAAC,iBAAiB,qBAAqB,IAAI,aAAa,MAAM,GAAG,iBAAiB,wBAAwB,CAAC,EAAE,OAAO,WAAS,KAAK,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ;AAC5N,UAAM,0BAA0B,KAAK,aAAa,IAAI,CAAC,iBAAiB,oBAAoB,IAAI,aAAa,MAAM,iBAAiB,oBAAoB,EAAE,OAAO,WAAS,KAAK,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;AAExM,UAAM,mCAAmC,oBAAoB,0BAA0B;AACvF,UAAM,mCAAmC,oBAAoB,2BAA2B;AACxF,QAAI,kBAAkB;AACtB,QAAI,0BAA0B;AAC9B,QAAI,0BAA0B;AAG9B,WAAO,oBAAoB,KAAK,0BAA0B,iCAAiC,UAAU,0BAA0B,iCAAiC,QAAQ;AACtK,YAAM,oBAAoB,iCAAiC,uBAAuB;AAClF,YAAM,oBAAoB,iCAAiC,uBAAuB;AAClF,YAAM,mBAAmB,oBAAoB,iBAAiB;AAC9D,YAAM,mBAAmB,oBAAoB,iBAAiB;AAC9D,YAAM,sBAAsB,iBAAiB,sBAAsB,iBAAiB,EAAE;AACtF,YAAM,sBAAsB,iBAAiB,sBAAsB,iBAAiB,EAAE;AAGtF,YAAM,oBAAoB,mBAAmB;AAC7C,YAAM,oBAAoB,sBAAsB;AAChD,YAAM,mBAAmB,KAAK,IAAI,mBAAmB,mBAAmB,eAAe;AAEvF,0BAAoB,iBAAiB,KAAK;AAC1C,0BAAoB,iBAAiB,KAAK;AAC1C,yBAAmB;AAEnB,UAAI,oBAAoB,iBAAiB,MAAM,qBAAqB;AAClE;AAAA,MACF;AAEA,UAAI,oBAAoB,iBAAiB,MAAM,qBAAqB;AAClE;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO,EAAE,QAAQ,CAAC,MAAM,UAAU;AAErC,UAAI,KAAK,cAAc,MAAM,KAAK;AAChC;AAAA,MACF;AACA,UAAI,KAAK,KAAK,MAAM,SAAS;AAC3B,aAAK,cAAc,IAAI,oBAAoB,KAAK,CAAC;AAAA,MACnD,OAAO;AACL,cAAM,cAAc,oBAAoB,KAAK,IAAI,iBAAiB,sBAAsB;AAExF,aAAK,cAAc,IAAI,WAAW,YAAY,QAAQ,EAAE,CAAC,CAAC;AAAA,MAC5D;AAAA,IACF,CAAC;AACD,SAAK,oBAAoB,KAAK,KAAK,2BAA2B,KAAK,mBAAmB,CAAC,CAAC;AAAA,EAC1F;AAAA,EACA,iCAAiC;AAC/B,UAAM,UAAU,CAAC;AACjB,UAAM,sBAAsB,IAAI,KAAK,cAAc,GAAG,UAAQ;AAC5D,YAAM,OAAO,KAAK,cAAc;AAChC,aAAO,SAAS,MAAM,IAAI;AAAA,IAC5B,CAAC;AACD,UAAM,oBAAoB,KAAK,cAAc,EAAE;AAC/C,QAAI,sBAAsB;AAC1B,SAAK,OAAO,EAAE,QAAQ,CAAC,MAAM,OAAO,UAAU;AAC5C,YAAM,OAAO,KAAK,KAAK;AACvB,YAAM,WAAW,KAAK,cAAc;AAEpC,UAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,gBAAQ,KAAK,SAAS,aAAa,aAAa,MAAM,QAAQ,KAAK;AAAA,MACrE,OAAO;AACL,YAAI,SAAS,SAAS;AACpB,gBAAM,cAAc,aAAa,MAAM,QAAQ,GAAG,QAAQ;AAC1D,kBAAQ,KAAK,WAAW;AAAA,QAC1B,OAAO;AACL,gBAAM,cAAc,aAAa,MAAM,MAAM,sBAAsB;AACnE,gBAAM,cAAc,GAAG,WAAW;AAClC,kBAAQ,KAAK,WAAW;AAAA,QAC1B;AACA;AAAA,MACF;AACA,YAAM,aAAa,UAAU,MAAM,SAAS;AAC5C,UAAI,YAAY;AACd;AAAA,MACF;AACA,YAAM,wBAAwB,oBAAoB;AAGlD,UAAI,KAAK,QAAQ,KAAK,wBAAwB,GAAG;AAC/C,gBAAQ,KAAK,GAAG,KAAK,WAAW,CAAC,IAAI;AAAA,MACvC,OAAO;AACL,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AACD,WAAO,KAAK,UAAU,MAAM,eAAe,SAAS,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,QAAQ,KAAK,GAAG,CAAC;AAAA,EAChG;AAAA,EACA,gCAAgC;AAC9B,UAAM,oBAAoB,KAAK,cAAc,EAAE,IAAI,UAAQ;AACzD,YAAM,OAAO,KAAK,KAAK;AACvB,aAAO,SAAS,SAAS,MAAM;AAAA,IACjC,CAAC;AACD,UAAM,UAAU,cAAc,KAAK,cAAc,GAAG,KAAK,KAAK,GAAG,KAAK;AACtE,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,SAAS,WAAW;AAEtB,YAAM,qBAAqB,MAAM,kBAAkB;AACnD,aAAO,kBAAkB,IAAI,MAAM,kBAAkB;AAAA,IACvD;AACA,QAAI,SAAS,SAAS;AAEpB,YAAM,gBAAgB,kBAAkB,OAAO,cAAY,aAAa,GAAG;AAC3E,UAAI,cAAc,WAAW,GAAG;AAC9B,eAAO,CAAC,KAAK,GAAG,kBAAkB,MAAM,CAAC,CAAC;AAAA,MAC5C,OAAO;AACL,cAAM,qBAAqB,kBAAkB,UAAU,cAAY,aAAa,GAAG;AACnF,cAAM,gBAAgB;AACtB,eAAO,kBAAkB,IAAI,CAAC,UAAU,UAAU,UAAU,sBAAsB,aAAa,MAAM,WAAW,aAAa;AAAA,MAC/H;AAAA,IACF;AACA,WAAO,kBAAkB,MAAM,WAAW;AAAA,EAC5C;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,8BAA8B,IAAI,KAAK,UAAU;AACxE,YAAI,KAAK,GAAG;AACV,UAAG,qBAAqB,UAAU,IAAI,QAAQ,qBAAqB,CAAC;AACpE,UAAG,qBAAqB,UAAU,IAAI,cAAc,sBAAsB,CAAC;AAAA,QAC7E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,CAAC;AAAA,QACrB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,OAAO,IAAI,cAAc;AAC3C,UAAG,WAAW,IAAI,kBAAkB;AAAA,QACtC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY,CAAI,WAAa,aAAa,YAAY;AAAA,QACtD,YAAY,CAAI,WAAa,aAAa,YAAY;AAAA,QACtD,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,QAClD,oBAAoB,CAAI,WAAa,aAAa,oBAAoB;AAAA,QACtE,WAAW,CAAI,WAAa,aAAa,WAAW;AAAA,QACpD,KAAK,CAAI,WAAa,aAAa,KAAK;AAAA,QACxC,MAAM,CAAI,WAAa,aAAa,MAAM;AAAA,QAC1C,iBAAiB,CAAI,WAAa,aAAa,iBAAiB;AAAA,QAChE,cAAc,CAAI,WAAa,aAAa,cAAc;AAAA,QAC1D,eAAe,CAAI,WAAa,aAAa,eAAe;AAAA,QAC5D,wBAAwB,CAAI,WAAa,aAAa,wBAAwB;AAAA,MAChF;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,aAAa,YAAY,KAAK,+BAA+B,IAAI,GAAG,mBAAmB,GAAG,WAAW,cAAc,oCAAoC,6BAA6B,GAAG,CAAC,QAAQ,aAAa,YAAY,KAAK,+BAA+B,IAAI,GAAG,mBAAmB,GAAG,sBAAsB,yBAAyB,0BAA0B,wBAAwB,WAAW,oCAAoC,6BAA6B,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,oBAAoB,2BAA2B,0BAA0B,GAAG,CAAC,GAAG,sBAAsB,CAAC;AAAA,MAC5nB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,iBAAiB,GAAG,+BAA+B,GAAG,GAAG,MAAM,MAAS,yBAAyB;AAAA,QACtG;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,IAAI,OAAO,CAAC;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,oCAAoC,qCAAqC,gBAAgB;AAAA,MACjH,QAAQ,CAAC,2jGAAukG;AAAA,MAChlG,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,SAAS,CAAC,SAAS,oCAAoC,qCAAqC,gBAAgB;AAAA,MAC5G,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,QAAQ,CAAC,qwFAAixF;AAAA,IAC5xF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,KAAK;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,4BAA4B,cAAY,aAAa,UAAa,aAAa,QAAQ,aAAa,MAAM,MAAM,CAAC;AACvH,IAAM,oBAAoB,cAAY,0BAA0B,QAAQ;AACxE,IAAM,4BAA4B,cAAY,0BAA0B,QAAQ;AAChF,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,QAAQ,OAAO,cAAc;AAClC,SAAK,OAAO,MAAM,QAAQ;AAAA,MACxB,WAAW;AAAA,IACb,CAAC;AACD,SAAK,UAAU,MAAM,KAAK;AAAA,MACxB,WAAW;AAAA,IACb,CAAC;AACD,SAAK,UAAU,MAAM,KAAK;AAAA,MACxB,WAAW;AAAA,IACb,CAAC;AACD,SAAK,WAAW,MAAM,OAAO;AAAA,MAC3B,WAAW;AAAA,IACb,CAAC;AACD,SAAK,UAAU,MAAM,MAAM;AAAA,MACzB,WAAW;AAAA,IACb,CAAC;AAID,SAAK,gBAAgB;AAAA;AAAA;AAAA,MAGrB,SAAS,MAAM;AACb,YAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,iBAAO;AAAA,QACT;AACA,cAAM,eAAe,KAAK,MAAM,cAAc,EAAE,UAAU,UAAQ,SAAS,IAAI;AAC/E,eAAO,KAAK,MAAM,0BAA0B,EAAE,YAAY;AAAA,MAC5D,CAAC;AAAA,IAAC;AAIF,SAAK,qBAAqB,SAAS,MAAM,KAAK,iBAAiB,CAAC;AAIhE,SAAK,qBAAqB,SAAS,MAAM,KAAK,iBAAiB,CAAC;AAChE,SAAK,QAAQ,SAAS,MAAM,KAAK,MAAM,OAAO,EAAE,UAAU,UAAQ,SAAS,IAAI,CAAC;AAChF,SAAK,cAAc,SAAS,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC;AACtD,SAAK,cAAc,SAAS,MAAM,oBAAoB;AAAA,MACpD,CAAC,eAAe,GAAG;AAAA,MACnB,CAAC,QAAQ,GAAG,KAAK,QAAQ,KAAK,KAAK,cAAc,MAAM,KAAK,mBAAmB;AAAA,MAC/E,CAAC,QAAQ,GAAG,KAAK,QAAQ,KAAK,KAAK,cAAc,MAAM,KAAK,mBAAmB;AAAA,MAC/E,CAAC,WAAW,GAAG,CAAC,KAAK,QAAQ;AAAA,IAC/B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,6BAA6B;AAC/B,WAAO,KAAK,MAAM,UAAU,MAAM,eAAe,GAAG,KAAK,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,KAAK;AAAA,EACrG;AAAA,EACA,IAAI,0BAA0B;AAC5B,WAAO,KAAK,MAAM,UAAU,MAAM,aAAa,GAAG,KAAK,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,KAAK;AAAA,EACnG;AAAA,EACA,IAAI,2BAA2B;AAC7B,WAAO,KAAK,MAAM,YAAY,IAAI,aAAa;AAAA,EACjD;AAAA,EACA,mBAAmB;AACjB,UAAM,iBAAiB;AACvB,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,sBAAsB,KAAK,SAAS,cAAc;AACvE,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,SAAS,OAAO,SAAS,UAAU,OAAO,SAAS;AACrD,UAAI,UAAU,GAAG;AACf,gBAAQ,KAAK,+CAA+C;AAAA,MAC9D;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AACjB,UAAM,iBAAiB;AACvB,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,sBAAsB,KAAK,SAAS,cAAc;AACvE,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,SAAS,OAAO,SAAS,UAAU,OAAO,SAAS;AACrD,UAAI,UAAU,GAAG;AACf,gBAAQ,KAAK,8CAA8C;AAAA,MAC7D;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,oBAAoB,qBAAqB;AAC7D,UAAM,OAAO,KAAK,KAAK;AACvB,UAAM,WAAW,KAAK,SAAS;AAC/B,UAAM,eAAe,mBAAmB;AACxC,QAAI,UAAU;AACZ,UAAI,UAAU,KAAK,iBAAiB,KAAK;AACvC,gBAAQ,KAAK,+CAA+C;AAAA,MAC9D;AACA,UAAI,SAAS,OAAO,SAAS,QAAQ;AACnC,YAAI,UAAU,GAAG;AACf,kBAAQ,KAAK,wEAAwE;AAAA,QACvF;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,KAAK;AACxB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,OAAO,SAAS,QAAQ;AACnC,UAAI,UAAU,GAAG;AACf,gBAAQ,KAAK,4DAA4D;AAAA,MAC3E;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,kBAAkB;AACpC,UAAG,YAAY,eAAe,IAAI,0BAA0B,EAAE,YAAY,IAAI,uBAAuB,EAAE,YAAY,IAAI,wBAAwB;AAAA,QACjJ;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM,CAAI,WAAa,aAAa,MAAM;AAAA,QAC1C,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,QAChD,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,QAChD,UAAU,CAAI,WAAa,aAAa,UAAU;AAAA,QAClD,SAAS,CAAI,WAAa,aAAa,SAAS;AAAA,MAClD;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC;AAAA,MAC7B,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC5E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,MAAM,YAAY,IAAI,IAAI,EAAE;AAAA,QACtD;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,sOAAsO;AAAA,MAC/O,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,QAAQ,CAAC,gLAAgL;AAAA,IAC3L,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACL,OAAO;AACvB,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,mCAAmC;AAAA,MACvI,SAAS,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,mCAAmC;AAAA,IACzI,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,mCAAmC;AAAA,MACvI,SAAS,CAAC,gBAAgB,oBAAoB,sBAAsB,gCAAgC,mCAAmC;AAAA,IACzI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["map", "sum"]}
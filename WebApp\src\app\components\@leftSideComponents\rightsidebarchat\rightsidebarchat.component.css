/* Chat Sidebar Styles */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

/* Active tab styling */
.active-tab {
  background-color: var(--secondary-purple) !important;
  color: black !important;
}

.active-tab i {
  color: black !important;
}

/* Tooltip styles */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.custom-tooltip {
  visibility: hidden;
  position: absolute;
  background-color: var(--background-light-gray);
  color: var(--text-dark);
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--hover-blue-gray);
}

.bottom-tooltip {
  top: 125%;
  left: 50%;
  transform: translateX(-50%);
}

.bottom-tooltip::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent var(--hover-blue-gray) transparent;
}

.tooltip-container:hover .custom-tooltip {
  visibility: visible;
  opacity: 1;
}

/* Archive tooltip positioning */
.archive-tooltip-container .custom-tooltip {
  right: -10px;
  left: auto;
  transform: none;
}

/* Ensure proper height calculation */
.max-h-\[78vh\] {
  max-height: 78vh;
}

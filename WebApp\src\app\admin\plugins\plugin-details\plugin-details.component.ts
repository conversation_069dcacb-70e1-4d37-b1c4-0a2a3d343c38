import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzMessageService } from 'ng-zorro-antd/message';
import { PluginServiceProxy, PluginResponseDto } from '../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-plugin-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzTagModule,
    NzIconModule,
    NzButtonModule,
    NzToolTipModule
  ],
  templateUrl: './plugin-details.component.html',
  styleUrl: './plugin-details.component.css'
})
export class PluginDetailsComponent implements OnInit {
  plugin?: PluginResponseDto;
  loading = true;
  pluginName: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private pluginService: PluginServiceProxy,
    private message: NzMessageService
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.pluginName = params['pluginName'];
      if (this.pluginName) {
        this.loadPluginDetails();
      }
    });
  }

  loadPluginDetails() {
    this.loading = true;
    this.pluginService.getByName(this.pluginName).subscribe({
      next: (plugin) => {
        this.plugin = plugin;
        this.loading = false;
      },
      error: (error) => {
        this.message.error(`Failed to load plugin details for ${this.pluginName}`);
        this.loading = false;
        console.error('Error loading plugin details:', error);
      }
    });
  }

  resyncPlugin() {
    if (!this.plugin?.pluginName) return;

    this.loading = true;
    if (this.plugin.type?.toLowerCase() === 'openapi') {
      this.pluginService.resyncOpenApiPlugin(this.plugin.pluginName).subscribe({
        next: (plugin) => {
          this.plugin = plugin;
          this.loading = false;
          this.message.success(`Plugin ${this.plugin.pluginName} resynced successfully`);
        },
        error: (error) => {
          this.message.error(`Failed to resync plugin ${this.plugin?.pluginName}`);
          this.loading = false;
          console.error('Error resyncing plugin:', error);
        }
      });
    }
  }

  getPluginTypeColor(type: string | undefined): string {
    if (!type) return 'default';
    switch (type.toLowerCase()) {
      case 'openapi':
        return 'blue';
      case 'customplugin':
        return 'green';
      default:
        return 'default';
    }
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    return new Date(date.toString()).toLocaleDateString();
  }

  getFunctionList(functions: string | undefined): string[] {
    if (!functions) return [];
    return functions.split('\r\n');
  }

  goBack() {
    this.router.navigate(['/settings/plugins']);
  }
}

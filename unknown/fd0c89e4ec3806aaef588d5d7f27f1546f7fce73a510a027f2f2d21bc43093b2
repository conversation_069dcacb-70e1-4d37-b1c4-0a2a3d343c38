<div  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div
    class="bg-[var(--background-white)] rounded-[var(--border-radius-large)] p-6 w-full max-w-md border border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)]">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
      <h2 class="text-xl font-[var(--font-weight-bold)] text-[var(--primary-purple)]">{{ isEditing ? 'Edit' : 'Add'
        }}
        AI Agent</h2>
      <div class="flex justify-end gap-3 w-full sm:w-auto">
        <button (click)="closeDialog()"
          class="px-4 py-2 bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none w-full sm:w-auto text-[var(--text-dark)]">
          Cancel
        </button>
        <button (click)="saveAgent()" *ngIf="!isEditing"
          class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none w-full sm:w-auto text-[var(--background-white)]">
          Save
        </button>
        <button (click)="saveAgent()" *ngIf="isEditing"
          class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none w-full sm:w-auto text-[var(--background-white)]">
          Update
        </button>
      </div>
    </div>

    <div class="space-y-4">
      <div>
        <label for="agentName"
          class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Agent Name</label>
        <input type="text" id="agentName" [(ngModel)]="currentAgent.agentName"
          class="w-full p-2 bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] text-[var(--text-dark)]  "
          placeholder="e.g. AnswerAgent">
      </div>

      <div>
        <label for="instructions"
          class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Instructions</label>
        <textarea id="instructions" [(ngModel)]="currentAgent.instructions" rows="4"
          class="w-full p-2 bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] focus:outline-none resize-y focus:ring-2 focus:ring-[var(--primary-purple)] text-[var(--text-dark)] min-h-[100px] max-h-[200px]  "
          placeholder="Enter agent instructions"></textarea>
      </div>

      <div>
        <label for="model"
          class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Model</label>
        <nz-input-group nzSearch nzSize="large" [nzAddOnAfter]="suffixIconButton">
          <input placeholder="Search model name..."
            class="w-full p-2 !bg-[var(--background-light-gray)] border !text-[var(--text-dark)] !border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] "
            nz-input [(ngModel)]="modelSearchQuery" (input)="onChange($event)" [nzAutocomplete]="auto" />
        </nz-input-group>
        <ng-template #suffixIconButton>
          <button nz-button nzType="primary" nzSize="large" nzSearch
            class="p-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)]">
            <!-- <nz-icon nzType="search" nzTheme="outline" class="text-[var(--background-white)]" /> -->
          </button>
        </ng-template>
        <nz-autocomplete #auto class="!bg-[var(--background-white)] w-full">
          @for (option of filteredModels; track option.modelName) {
          <nz-auto-option
            class="search-item !bg-[var(--background-white)] !border-[var(--hover-blue-gray)] hover:!bg-[var(--secondary-purple)]"
            [nzValue]="option.modelName">
            <div class="search-item-desc !text-[var(--text-dark)]  p-2" (click)="updateModel(option.modelName)">
              {{ option.modelName }}
            </div>
          </nz-auto-option>
          }
        </nz-autocomplete>
      </div>
      <div>
        <label for="model"
          class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Select Plugin</label>
          <nz-select [(ngModel)]="selectedPlugins" nzSize="default"  nzMode="tags"
          [nzMaxTagCount]="2" nzMode="multiple" nzPlaceHolder="Please Select Plugins">
            @for (option of plugins; track option) {
              <nz-option [nzLabel]="option" [nzValue]="option" ></nz-option>
            }
          </nz-select>

      </div>
    </div>
  </div>
</div>

<!-- Notes Sidebar Content -->
<div
  class="h-full flex flex-col bg-[var(--background-white)] text-[var(--text-dark)]"
  [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-white text-[var(--text-dark)]': !themeService.isDarkMode()
  }"
>
  <!-- Header Section -->
  <div
    class="px-[var(--padding-small)] py-[var(--padding-small)] border-b border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2"
    [ngClass]="{
      'border-[#3a3a45]': themeService.isDarkMode(),
      'border-[var(--border-light)]': !themeService.isDarkMode()
    }"
  >
    <div class="flex items-center justify-between w-full">
      <div class="flex flex-col">
        <div class="flex items-center gap-1.5">
          <span
            class="font-bold text-[var(--text-dark)] text-lg"
            [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }"
          >
            {{ isPublicRoute ? "Notes" : "Documents" }}
          </span>
        </div>
      </div>

      <!-- Filter buttons - Icon Only -->
      <div class="flex items-center ms-6 gap-2">
        <div class="tooltip-container">
          <button
            id="all-notes-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeFilter === 'all'
            }"
            (click)="toggleFilter('all')"
          >
            <i class="ri-list-check text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">All</span>
        </div>
        <div class="tooltip-container">
          <button
            id="favorites-notes-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeFilter === 'favorites'
            }"
            (click)="toggleFilter('favorites')"
          >
            <i class="ri-star-line text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">Favorites</span>
        </div>
        <div class="tooltip-container">
          <button
            id="recent-notes-btn"
            class="w-6 h-6 flex justify-center items-center rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] border border-transparent bg-transparent outline-none"
            [ngClass]="{
              'active-tab': activeFilter === 'recent'
            }"
            (click)="toggleFilter('recent')"
          >
            <i class="ri-time-line text-lg"></i>
          </button>
          <span class="custom-tooltip bottom-tooltip">Recent</span>
        </div>

        <div class="flex items-center gap-2">
          <button
            (click)="addDocument($event)"
            class="w-6 h-6 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-[var(--transition-default)] flex justify-center items-center outline-none border-none bg-transparent text-lg font-bold cursor-pointer"
          >
            <i
              class="ri-add-line text-[var(--text-dark)]"
              [ngClass]="{
                'text-white': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode()
              }"
            ></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Content Section -->
  <div class="flex-1 overflow-y-auto">
    <!-- Filter Title -->
    <div class="flex items-center px-[var(--padding-small)] mb-1">
      <span class="text-[var(--text-medium-gray)]">{{
        getCurrentFilterTitle()
      }}</span>
    </div>

    <!-- Filtered Documents List -->
    <div class="flex-1 px-[var(--padding-small)] overflow-y-auto">
      <div class="py-[var(--padding-small)]">
        <div class="space-y-1">
          <div
            *ngFor="let doc of getFilteredDocuments()"
            class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group"
            [ngClass]="{
              'bg-[var(--secondary-purple)]': isDocumentActive(doc) && !themeService.isDarkMode(),
              'bg-[var(--active-tab-bg)]': isDocumentActive(doc) && themeService.isDarkMode(),
              'hover:bg-[var(--primary-purple)]': true,
              'hover:text-white': true
            }"
            (click)="selectDocument(doc)"
          >
            <!-- Active indicator bar -->
            <div
              class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all"
              [ngClass]="{
                'opacity-100': isDocumentActive(doc),
                'opacity-0': !isDocumentActive(doc),
                'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
                'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
              }"
            ></div>

            <i
              class="ri-file-text-line text-lg transition-colors duration-200"
              [ngClass]="{
                'text-black': isDocumentActive(doc) && !themeService.isDarkMode(),
                'text-white': isDocumentActive(doc) && themeService.isDarkMode(),
                'text-[#ACACBE]': !isDocumentActive(doc) && themeService.isDarkMode(),
                'text-[var(--primary-purple)]': !isDocumentActive(doc) && !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-black': !themeService.isDarkMode()
              }"
            ></i>
            <span
              class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0"
              [ngClass]="{
                'text-black': isDocumentActive(doc) && !themeService.isDarkMode(),
                'text-white': isDocumentActive(doc) && themeService.isDarkMode(),
                'text-[#ACACBE]': !isDocumentActive(doc) && themeService.isDarkMode(),
                'text-[var(--text-dark)]': !isDocumentActive(doc) && !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-black': !themeService.isDarkMode()
              }"
            >
              {{ doc.title }}
            </span>
            <div class="flex items-center gap-2">
              <!-- Show star icon for all notes in "All Notes" filter -->
              <i
                *ngIf="activeFilter === 'all'"
                (click)="toggleFavorite(doc); $event.stopPropagation()"
                class="text-yellow-400 text-sm cursor-pointer hover:scale-110 transition-transform"
                [ngClass]="{
                  'ri-star-fill': doc.isFavorite,
                  'ri-star-line': !doc.isFavorite
                }"
                [title]="
                  doc.isFavorite ? 'Remove from favorites' : 'Add to favorites'
                "
              ></i>
              <!-- Show star icon only for favorites filter -->
              <i
                *ngIf="activeFilter === 'favorites'"
                (click)="toggleFavorite(doc); $event.stopPropagation()"
                class="text-yellow-400 text-sm cursor-pointer hover:scale-110 transition-transform"
                [ngClass]="{
                  'ri-star-fill': doc.isFavorite,
                  'ri-star-line': !doc.isFavorite
                }"
                [title]="
                  doc.isFavorite ? 'Remove from favorites' : 'Add to favorites'
                "
              ></i>
              <!-- Show time icon for recent filter -->
              <i
                *ngIf="activeFilter === 'recent'"
                class="ri-time-line text-sm"
                [ngClass]="{
                  'text-gray-400': themeService.isDarkMode(),
                  'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
                }"
              ></i>
            </div>
          </div>

          <!-- Empty state -->
          <div
            *ngIf="getFilteredDocuments().length === 0"
            class="flex items-center justify-center px-2 py-4 flex-col gap-2 h-full"
          >
            <span class="text-[var(--text-medium-gray)]"
              >No {{ getCurrentFilterTitle().toLowerCase() }} for now.</span
            >
            <i
              class="ri-emotion-sad-line text-[var(--text-medium-gray)] text-4xl"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  class="flex flex-col px-4 sm:px-6 py-5   bg-[var(--background-light-gray)] text-[var(--text-dark)] font-[var(--font-family)]">
  <!-- Header -->
  <div class="flex flex-row justify-between items-start sm:items-center gap-4">
    <h1 class="font-[var(--font-weight-bold)] text-[var(--text-dark)]">AI Agents</h1>
    <button (click)="addNewAgent()"
      class="bg-[var(--primary-purple)] text-[var(--background-white)] px-3 py-1 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-[var(--transition-default)] cursor-pointer border-none outline-none sm:w-auto">
      Add Agent
    </button>
  </div>

  <!-- Agent List -->
  <div class="grid gap-2 mb-8 " style="
  height: 78vh;
  overflow-y: scroll;" *ngIf="agents.length > 0">
    <div *ngFor="let agent of agents"
      class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)]">
      <div class="flex flex-col w-full gap-2">
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
          <div class="flex items-center gap-2">
            <i class="ri-user-2-line text-[var(--primary-purple)] text-lg"></i>
            <h3 class="text-lg sm:text-xl font-[var(--font-weight-regular)] text-[var(--text-dark)]">{{ agent.agentName
              }}</h3>
          </div>
          <h4 class=" m-0 text-[var(--text-medium-gray)]">{{agent.modelName}}</h4>
        </div>
        <p class="text-[var(--text-medium-gray)]  break-words">Instructions: {{ agent.instructions }}</p>
      </div>
      <div class="flex justify-end gap-2 mt-2 sm:mt-0 w-full sm:w-auto">
        <button (click)="editAgent(agent)"
          class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
          <i
            class="ri-edit-2-line text-[var(--primary-purple)] hover:text-[var(--text-dark)] transition-colors text-lg"></i>
        </button>
        <button (click)="deleteAgent(agent.agentName)"
          class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
          <i class="ri-delete-bin-6-line text-red-500 hover:text-red-600 transition-colors text-lg"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="agents.length === 0 && !showForm"
    class="flex flex-col items-center justify-center p-6 sm:p-10 bg-[var(--background-white)] rounded-[var(--border-radius-large)] border border-dashed border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)]">
    <i class="ri-emotion-sad-line text-4xl sm:text-6xl text-[var(--text-medium-gray)] mb-4"></i>
    <p class="text-[var(--text-medium-gray)] text-center mb-4  ">No AI agents have been added yet.</p>
    <button (click)="addNewAgent()"
      class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] flex items-center gap-2 transition-[var(--transition-default)] outline-none border-none cursor-pointer text-[var(--background-white)]  ">
      Add Your First Agent
    </button>
  </div>

  <!-- Form Overlay -->
  <div *ngIf="showForm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div
      class="bg-[var(--background-white)] rounded-[var(--border-radius-large)] p-6 w-full max-w-md border border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)]">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <h2 class="text-xl font-[var(--font-weight-bold)] text-[var(--primary-purple)]">{{ isEditing ? 'Edit' : 'Add' }}
          AI Agent</h2>
        <div class="flex justify-end gap-3 w-full sm:w-auto">
          <button (click)="resetForm()"
            class="px-4 py-2 bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none w-full sm:w-auto text-[var(--text-dark)]">
            Cancel
          </button>
          <button (click)="saveAgent()" *ngIf="!isEditing"
            class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none w-full sm:w-auto text-[var(--background-white)]">
            Save
          </button>
          <button (click)="saveEditedAgent()" *ngIf="isEditing"
            class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] cursor-pointer outline-none border-none w-full sm:w-auto text-[var(--background-white)]">
            Update
          </button>
        </div>
      </div>

      <div class="space-y-4">
        <div>
          <label for="agentName"
            class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Agent Name</label>
          <input type="text" id="agentName" [(ngModel)]="currentAgent.agentName"
            class="w-full p-2 bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] text-[var(--text-dark)]  "
            placeholder="e.g. AnswerAgent">
        </div>

        <div>
          <label for="instructions"
            class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Instructions</label>
          <textarea id="instructions" [(ngModel)]="currentAgent.instructions" rows="4"
            class="w-full p-2 bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] focus:outline-none resize-y focus:ring-2 focus:ring-[var(--primary-purple)] text-[var(--text-dark)] min-h-[100px] max-h-[200px]  "
            placeholder="Enter agent instructions"></textarea>
        </div>

        <div>
          <label for="model"
            class="block  font-[var(--font-weight-regular)] text-[var(--text-medium-gray)] mb-1">Model</label>
          <nz-input-group nzSearch nzSize="large" [nzAddOnAfter]="suffixIconButton">
            <input placeholder="Search model name..."
              class="w-full p-2 !bg-[var(--background-light-gray)] border !text-[var(--text-dark)] !border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] "
              nz-input [(ngModel)]="modelSearchQuery" (input)="onChange($event)" [nzAutocomplete]="auto" />
          </nz-input-group>
          <ng-template #suffixIconButton>
            <button nz-button nzType="primary" nzSize="large" nzSearch
              class="p-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)]">
              <nz-icon nzType="search" nzTheme="outline" class="text-[var(--background-white)]" />
            </button>
          </ng-template>
          <nz-autocomplete #auto class="!bg-[var(--background-white)] w-full">
            @for (option of filteredModels; track option.modelName) {
            <nz-auto-option
              class="search-item !bg-[var(--background-white)] !border-[var(--hover-blue-gray)] hover:!bg-[var(--secondary-purple)]"
              [nzValue]="option.modelName">
              <div class="search-item-desc !text-[var(--text-dark)]  p-2" (click)="updateModel(option.modelName)">
                {{ option.modelName }}
              </div>
            </nz-auto-option>
            }
          </nz-autocomplete>
        </div>
      </div>
    </div>
  </div>
</div>

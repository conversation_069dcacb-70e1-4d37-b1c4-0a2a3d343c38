<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title -->
      <div class="flex items-center gap-2">
        <i class="ri-upload-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">Upload File</h1>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <button
          (click)="goBack()"
          class="h-8 px-3 py-1 text-sm font-medium flex items-center justify-center gap-1 secondary-button"
        >
          <i class="ri-arrow-left-line"></i>
          <span>Back to Files</span>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <div class="bg-[var(--background-white)] rounded-lg shadow-sm p-6">
        <!-- File Upload Area -->
        <div class="mb-6 file-upload-container">
          <label class="block text-[var(--text-dark)] text-sm font-medium mb-2">Select File</label>
          <div
            class="file-upload-area h-40 flex flex-col items-center justify-center cursor-pointer group"
            (click)="fileInput.click()"
          >
            <input
              type="file"
              #fileInput
              class="hidden"
              (change)="onFileSelected($event)"
            >
            <i class="ri-upload-cloud-line text-4xl text-[var(--text-medium-gray)] group-hover:text-[var(--primary-purple)] transition-colors"></i>
            <p class="text-[var(--text-medium-gray)] group-hover:text-[var(--primary-purple)] transition-colors mt-3 text-center max-w-md px-4">
              Click to select a file or drag and drop
            </p>
            <p class="text-xs text-[var(--text-medium-gray)] mt-2">
              Supported formats: PDF, DOCX, TXT, JPG, PNG
            </p>
          </div>
        </div>

        <!-- Selected File Preview -->
        <div *ngIf="selectedFile" class="mb-6 p-4 file-preview flex justify-between items-center animate-fadeIn">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center">
              <i class="ri-file-text-line text-xl text-[var(--primary-purple)]"></i>
            </div>
            <div>
              <p class="text-[var(--text-dark)] font-medium">{{ selectedFile.name }}</p>
              <p class="text-xs text-[var(--text-medium-gray)]">{{ (selectedFile.size / 1024).toFixed(2) }} KB</p>
            </div>
          </div>
          <button
            (click)="removeFile()"
            class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none shadow-sm"
          >
            <i class="ri-close-line text-red-500"></i>
          </button>
        </div>


        <!-- Submit Button -->
        <div class="flex justify-end">
          <button
            (click)="uploadFile()"
            [disabled]="!selectedFile || isUploading"
            class="px-4 py-2 flex items-center gap-2 primary-button"
            [ngClass]="{'opacity-50 cursor-not-allowed': !selectedFile || isUploading}"
          >
            <i *ngIf="isUploading" class="ri-loader-4-line animate-spin"></i>
            <i *ngIf="!isUploading" class="ri-upload-line"></i>
            <span>{{ isUploading ? 'Uploading...' : 'Upload File' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

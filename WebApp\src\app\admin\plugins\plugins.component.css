.plugins-container {
  min-height: calc(100vh - 64px);
  background-color: var(--background-default);
  overflow-y: auto;
}
/* 
.plugin-item {
  transition: all 0.3s ease;
  border: 1px solid var(--hover-blue-gray);
}

.plugin-item:hover {
  transform: translateX(4px);
  border-color: var(--primary-purple);
} */

.functions-list {
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-purple) var(--hover-blue-gray);
}

.functions-list::-webkit-scrollbar {
  width: 6px;
}

.functions-list::-webkit-scrollbar-track {
  background: var(--hover-blue-gray);
  border-radius: 3px;
}

.functions-list::-webkit-scrollbar-thumb {
  background: var(--primary-purple);
  border-radius: 3px;
}

.function-item {
  transition: background-color 0.2s ease;
}

.function-item:hover {
  background-color: var(--secondary-purple);
}

.loading-container {
  min-height: 200px;
}

.empty-state {
  background-color: var(--background-white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-default);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject, takeUntil } from 'rxjs';
// import { HubConnection, HubConnectionBuilder } from '@microsoft/signalr';
import { tap } from 'rxjs/operators';
import { BehaviorSubject } from 'rxjs';

export interface Note {
  id: number;
  title: string;
  content: any;
  createdAt: Date;
  isFavourite: boolean;
  isJournal: boolean;

}

// export interface ChatResponseDto {
//   answer: string;
//   sources: SourceDocumentDto[];
// }

// export interface SourceDocumentDto {
//   id: number;
//   title: string;
// }
@Injectable({
  providedIn: 'root'
})

export class NotesService {
  private apiUrl = 'https://localhost:44350/api';
  favoriteNotesChanged = new Subject<void>();
  recentNotesChanged = new Subject<void>();
  journalUpdated = new Subject<void>();
  // private hubConnection: HubConnection;
  private currentMessageSubject?: Subject<string>;
  // private sourcesSubject = new Subject<SourceDocumentDto[]>();
  private cancelSubject = new Subject<void>();

  constructor(private http: HttpClient) {
    // this.hubConnection = new HubConnectionBuilder()
    //   .withUrl('https://localhost:44350/chatHub')  // Update with your API URL
    //   .build();

    this.startConnection();
    this.addListeners();
  }

  private async startConnection() {
    try {
      // await this.hubConnection.start();
      console.log('SignalR Connected!');
    } catch (err) {
      console.error('SignalR Connection Error: ', err);
      setTimeout(() => this.startConnection(), 5000);
    }
  }

  private addListeners() {
    // Remove existing listeners before adding new ones
    //   this.hubConnection.off('ReceiveMessage');
    //   this.hubConnection.off('ReceiveSources');
    //   this.hubConnection.off('CompleteChatResponse');

    //   this.hubConnection.on('ReceiveMessage', (message: string) => {
    //     console.log('SignalR received message:', message); // Debug log
    //     if (this.currentMessageSubject) {
    //       this.currentMessageSubject.next(message);
    //     } else {
    //       console.warn('No message subject available'); // Debug log
    //     }
    //   });

    //   this.hubConnection.on('ReceiveSources', (sources: SourceDocumentDto[]) => {
    //     console.log('SignalR received sources:', sources); // Debug log
    //     this.sourcesSubject.next(sources);
    //   });

    //   this.hubConnection.on('CompleteChatResponse', () => {
    //     console.log('SignalR chat response completed'); // Debug log
    //     if (this.currentMessageSubject) {
    //       this.currentMessageSubject.complete();
    //     }
    //     this.sourcesSubject.complete();
    //   });
  }

  // Get all notes
  getAllNotes(): Observable<Note[]> {
    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/GetAll`);
  }

  // Get note by ID
  getNoteById(id: number): Observable<Note> {
    return this.http.get<Note>(`${this.apiUrl}/MyNotes/GetById?id=${id}`);
  }

  // Create or update note
  createOrUpdateNote(note: Partial<Note>): Observable<Note> {
    return this.http.post<Note>(`${this.apiUrl}/MyNotes/CreateOrUpdate`, note).pipe(
      tap(() => {
        this.recentNotesChanged.next();
      })
    );
  }

  // Delete note
  deleteNote(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/MyNotes/Delete?id=${id}`);
  }

  // Get favorite notes
  getFavoriteNotes(): Observable<Note[]> {
    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/GetFavouriteNotes`);
  }

  // Toggle favorite status
  toggleFavorite(id: number): Observable<Note> {
    return this.http.post<Note>(`${this.apiUrl}/MyNotes/ToggleFavourite`, null, {
      params: { id: id.toString() }
    }).pipe(
      tap(() => {
        this.favoriteNotesChanged.next();
      })
    );
  }

  // Get chat response
  getChatResponse(message: string, sessionId: string) {
    // const connectionId = this.hubConnection.connectionId;
    // if (!connectionId) {
    //   throw new Error('SignalR connection not established');
    // }

    // console.log('Starting chat response with connectionId:', connectionId); // Debug log

    // // Create new subjects for this conversation
    // this.currentMessageSubject = new Subject<string>();
    // this.sourcesSubject = new Subject<SourceDocumentDto[]>();
    // this.cancelSubject = new Subject<void>();

    // // Re-add listeners for this conversation
    // this.addListeners();

    // // Make the HTTP request
    // this.http.get(`${this.apiUrl}/Ai/chat-response`, {
    //   params: { message, sessionId, connectionId }
    // })
    // .pipe(
    //   takeUntil(this.cancelSubject)
    // )
    // .subscribe({
    //   error: (error) => {
    //     console.error('HTTP request error:', error);
    //     this.currentMessageSubject?.error(error);
    //     this.sourcesSubject.error(error);
    //   },
    //   complete: () => {
    //     console.log('HTTP request completed');
    //   }
    // });

    // return {
    //   messages: this.currentMessageSubject.asObservable(),
    //   sources: this.sourcesSubject.asObservable(),
    //   stop: () => {
    //     console.log('Stopping chat response');
    //     this.cancelSubject.next();
    //     this.cancelSubject.complete();
    //   }
    // };
  }

  // Get recent notes
  getRecentNotes(limit: number): Observable<Note[]> {
    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/recent`, {
      params: { limit: limit.toString() }
    });
  }

  // Update recently opened
  updateRecentlyOpened(id: number): Observable<Note> {
    return this.http.post<Note>(`${this.apiUrl}/MyNotes/UpdateRecentlyOpened`, null, {
      params: { id: id.toString() }
    });
  }

  // Search notes
  searchNotes(query: string): Observable<Note[]> {
    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/SearchNotes`, {
      params: { searchTerm: query }
    });
  }

  // Get all journal documents
  getAllJournalDocs(): Observable<Note[]> {
    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/GetAllJournalDocs`);
  }
}

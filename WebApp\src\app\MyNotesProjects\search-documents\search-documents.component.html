<div class="container h-100 p-4 bg-light rounded shadow">
  <div class="d-flex align-items-center gap-2 mb-4">
    <input [(ngModel)]="searchQuery" (keyup.enter)="onSearch()" placeholder="Search documents..."
      class="form-control w-100 px-3 py-2 border rounded focus:outline-none" />
    <button (click)="onSearch()"
      class="btn btn-primary px-4 py-2">Search</button>
  </div>

  <div *ngIf="isLoading" class="text-center text-muted">Loading...</div>

  <div *ngIf="!isLoading && searchResults.length === 0" class="text-center text-muted">No results found.</div>

  <div *ngIf="!isLoading && searchResults.length > 0">
    <ul class="list-unstyled gap-3">
      <li *ngFor="let note of searchResults" class="p-4 bg-white rounded shadow mb-3 cursor-pointer" (click)="goToNote(note.id)">
        <div class="d-flex justify-content-between align-items-center">
          <h3 class="h5 font-weight-bold text-dark">{{ note.title }}</h3>

          <button class="btn p-2 rounded-circle"
            [class.text-warning]="note?.isFavourite"
            [class.text-secondary]="!note?.isFavourite"
            (click)="toggleFavorite(note)"
            [title]="note?.isFavourite ? 'Remove from favorites' : 'Add to favorites'">
            <i class="fas fa-star fs-4"></i>
          </button>
        </div>

        <div class="mt-2">
          <div [innerHTML]="getSlicedContent(note)" class="text-dark"></div>
        </div>
      </li>
    </ul>
  </div>
</div>

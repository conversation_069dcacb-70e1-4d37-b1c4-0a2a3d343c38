declare module '@editorjs/simple-image' {
  import { BlockTool } from '@editorjs/editorjs';
  export default class SimpleImage implements BlockTool {
    constructor({ data, api, config }: any);
  }
}

declare module '@editorjs/link' {
  import { BlockTool, BlockToolData } from '@editorjs/editorjs';

  export interface LinkData extends BlockToolData {
    link: string;
    meta?: {
      title?: string;
      description?: string;
      image?: {
        url?: string;
      };
    };
  }

  export default class Link implements BlockTool {
    constructor(config?: {
      endpoint?: string;
    });
    render(): HTMLElement;
    save(blockContent: HTMLElement): LinkData;
    validate(data: LinkData): boolean;
  }
}

declare module '@editorjs/checklist' {
  import { BlockTool } from '@editorjs/editorjs';
  export default class Checklist implements BlockTool {
    constructor({ data, api, config }: any);
  }
}

declare module '@editorjs/marker' {
  import { BlockTool } from '@editorjs/editorjs';
  export default class Marker implements BlockTool {
    constructor({ data, api, config }: any);
  }
}

declare module '@editorjs/embed' {
  import { BlockTool } from '@editorjs/editorjs';
  export default class Embed implements BlockTool {
    constructor({ data, api, config }: any);
    static get toolbox(): {
      icon: string;
      title: string;
    };
  }
}

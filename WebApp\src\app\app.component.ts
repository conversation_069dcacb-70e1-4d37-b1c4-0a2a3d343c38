import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { HeaderComponent } from './components/header/header.component';
import { HeroComponent } from './pages/hero/hero.component';
import { TogglingService } from './toggling.service';
import { AuthService } from '../shared/services/auth.service';
import { Subscription } from 'rxjs';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { ThemeService } from '../shared/services/theam.service';
import { AngularSplitModule } from 'angular-split';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    SidebarComponent,
    RouterOutlet,
    HeaderComponent,
    NzButtonModule,
    AngularSplitModule
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'chatapp';
  togglingService = inject(TogglingService);
  authService = inject(AuthService);
  themeService = inject(ThemeService);
  router = inject(Router);
  cdr = inject(ChangeDetectorRef);

  isWorkspaceUrl: boolean;
  isDailyInsightMode: boolean = false;
  isNavigating: boolean = false;
  private routerSubscription: Subscription | undefined;
  private navigationTimeout: any;

  // Sidebar states
  sidebarState: 'collapsed' | 'narrow' | 'expanded' = 'narrow';

  // Split area sizes for different states
  splitSizes = {
    sidebarArea: 20,
    contentArea: 80
  };

  // Size configurations for different states
  sizeConfigs = {
    collapsed: {
      sidebarArea: 4,
      contentArea: 96
    },
    narrow: {
      sidebarArea: 25,
      contentArea: 75
    },
    expanded: {
      sidebarArea: 40,
      contentArea: 60
    }
  };

  // Minimum sizes for split areas
  minSizes = {
    sidebarArea: 4, // Changed from 5 to 4 to allow collapsed state
    contentArea: 50
  };

  // Storage keys for localStorage
  readonly STORAGE_KEYS = {
    sidebarState: 'sidebarState',
    splitSizes: 'splitSizes',
    previousSidebarState: 'previousSidebarState'
  };

  // Previous split sizes to restore when toggling sidebar
  previousSplitSizes = {
    sidebarArea: 25,
    contentArea: 75
  };

  // Flag to indicate if the user is currently dragging the splitter
  isDragging = false;

  // Reference to Math for use in the template
  Math = Math;

  constructor() {
    const url = window.location.href;
    this.isWorkspaceUrl = url.includes('workspaces');
    console.log(this.isWorkspaceUrl);
  }

  ngOnInit() {
    // Handle navbar state from localStorage
    const storedValue = localStorage.getItem('isNavbarOpen');
    if (storedValue === 'true') {
      this.togglingService.isNavbarOpen = true;
    } else if (storedValue === 'false') {
      this.togglingService.isNavbarOpen = false;
    } else {
      this.togglingService.isNavbarOpen = true; // Default value if not set in localStorage
    }
    console.log('isNavbarOpen', this.togglingService.isNavbarOpen);

    // Load saved sidebar state from localStorage
    const savedSidebarState = localStorage.getItem(this.STORAGE_KEYS.sidebarState);
    if (savedSidebarState && ['collapsed', 'narrow', 'expanded'].includes(savedSidebarState)) {
      this.sidebarState = savedSidebarState as 'collapsed' | 'narrow' | 'expanded';
    }

    // Load saved split sizes from localStorage if available
    const savedSplitSizes = localStorage.getItem(this.STORAGE_KEYS.splitSizes);
    if (savedSplitSizes) {
      try {
        const parsedSizes = JSON.parse(savedSplitSizes);

        // Validate the parsed sizes to ensure they're valid
        if (this.isValidSplitSizes(parsedSizes)) {
          this.splitSizes = parsedSizes;

          // Ensure the sidebar state matches the loaded split sizes
          this.updateSidebarStateFromSizes(this.splitSizes.sidebarArea);

          // Also store as previous split sizes if not in collapsed state
          if (this.sidebarState !== 'collapsed') {
            this.previousSplitSizes = { ...this.splitSizes };
          }
        } else {
          console.warn('Invalid split sizes in localStorage, using defaults');
          this.resetToDefaultSizes();
        }
      } catch (e) {
        console.error('Error parsing saved split sizes:', e);
        this.resetToDefaultSizes();
      }
    } else {
      // If no saved split sizes, use the default for the current state
      this.resetToDefaultSizes();
    }

    // Sync the toggling service state with our sidebar state
    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';

    // Listen for sidebar toggle events
    window.addEventListener('sidebar-toggle', this.handleSidebarToggle as EventListener);

    // Subscribe to router events to handle transitions
    this.routerSubscription = this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        // Check if this navigation should have animation disabled
        const currentNav = this.router.getCurrentNavigation();
        const noAnimation = currentNav?.extras?.state?.['noAnimation'];

        if (!noAnimation) {
          // Only show animation if not explicitly disabled
          this.isNavigating = true;
        }

        // Clear any existing timeout
        if (this.navigationTimeout) {
          clearTimeout(this.navigationTimeout);
        }
      } else if (
        event instanceof NavigationEnd ||
        event instanceof NavigationCancel ||
        event instanceof NavigationError
      ) {
        // Get the current URL to check if we're navigating to a chat route
        const currentUrl = this.router.url;
        const isChatRoute = currentUrl.includes('/chat/');

        // Check if we have a chat ID in the router state
        const navigation = this.router.getCurrentNavigation();
        const hasChatId = navigation?.extras?.state?.['chatId'];

        // If we're navigating to a chat route with a specific ID or have a chat ID in state,
        // use a shorter delay to make the transition smoother
        const transitionDelay = (isChatRoute || hasChatId) ? 0 : 100;

        // Navigation complete - fade in new view after a short delay
        // This delay ensures the fade-out completes before fade-in starts
        this.navigationTimeout = setTimeout(() => {
          this.isNavigating = false;
        }, transitionDelay);
      }
    });
  }

  ngDoCheck(): void {
    // Check for workspace URL
    const url = window.location.href;
    this.isWorkspaceUrl = url.includes('workspaces');
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

    // Clear any pending timeouts
    if (this.navigationTimeout) {
      clearTimeout(this.navigationTimeout);
    }

    // Remove event listeners
    window.removeEventListener('sidebar-toggle', this.handleSidebarToggle as EventListener);
  }

  /**
   * Handler for sidebar toggle events
   */
  private handleSidebarToggle = (event: CustomEvent) => {
    const toggleAction = event.detail.action;
    const currentState = this.sidebarState;

    console.log('Sidebar toggle event received:', toggleAction, 'Current state:', currentState);

    // Save current split sizes before changing state
    if (this.sidebarState !== 'collapsed') {
      this.previousSplitSizes = { ...this.splitSizes };
    }

    // Handle different toggle actions
    switch (toggleAction) {
      case 'toggle':
        // Cycle through states: collapsed -> narrow -> expanded -> collapsed
        if (currentState === 'collapsed') {
          this.sidebarState = 'narrow';
          this.splitSizes = { ...this.sizeConfigs.narrow };
        } else if (currentState === 'narrow') {
          this.sidebarState = 'expanded';
          this.splitSizes = { ...this.sizeConfigs.expanded };
        } else {
          this.sidebarState = 'collapsed';
          this.splitSizes = { ...this.sizeConfigs.collapsed };
        }
        break;

      case 'collapse':
        this.sidebarState = 'collapsed';
        this.splitSizes = { ...this.sizeConfigs.collapsed };
        break;

      case 'expand':
        this.sidebarState = 'expanded';
        this.splitSizes = { ...this.sizeConfigs.expanded };
        break;

      case 'narrow':
        this.sidebarState = 'narrow';
        this.splitSizes = { ...this.sizeConfigs.narrow };
        break;

      default:
        // Default toggle behavior (between collapsed and narrow)
        if (currentState === 'collapsed') {
          this.sidebarState = 'narrow';
          this.splitSizes = { ...this.sizeConfigs.narrow };
        } else {
          this.sidebarState = 'collapsed';
          this.splitSizes = { ...this.sizeConfigs.collapsed };
        }
    }

    // Save the current state to localStorage
    this.saveSplitterState();
  }

  // Track drag start position
  private dragStartX: number = 0;
  private isDragDirectionValid: boolean = false;

  // Variables for custom splitter drag
  private isSplitterDragging: boolean = false;
  private splitterDragStartX: number = 0;

  /**
   * Handles the drag progress event from the split component
   * @param sizes The current sizes during dragging
   */
  onSplitDragProgress(sizes: any) {
    // If this is the first drag event, record the starting position
    if (!this.isDragging) {
      this.dragStartX = this.splitSizes.sidebarArea;
      // We'll determine if the drag direction is valid after some movement
    }

    // Set dragging flag to true
    this.isDragging = true;

    // Check drag direction based on the sidebar size change
    // Only consider drag valid if moving from left to right (expanding)
    // or if we're already in a valid drag operation
    const isMovingRightward = sizes[0] > this.dragStartX;
    if (isMovingRightward) {
      this.isDragDirectionValid = true;
    }

    // Only update sizes if the drag direction is valid
    if (this.isDragDirectionValid) {
      // Update sizes during drag without saving to localStorage
      this.splitSizes = {
        sidebarArea: sizes[0],
        contentArea: sizes[1]
      };

      // Update sidebar state during drag for immediate visual feedback
      if (sizes[0] <= this.sizeConfigs.collapsed.sidebarArea + 2) {
        this.sidebarState = 'collapsed';
      } else if (sizes[0] <= this.sizeConfigs.narrow.sidebarArea + 5) {
        this.sidebarState = 'narrow';
      } else {
        this.sidebarState = 'expanded';
      }
    }
  }

  /**
   * Handles the drag end event from the split component
   * @param sizes The new sizes of the split areas
   */
  onSplitDragEnd(sizes: any) {
    console.log('Split sizes updated:', sizes);

    // Reset dragging flags
    this.isDragging = false;
    const wasDragValid = this.isDragDirectionValid;
    this.isDragDirectionValid = false;
    this.dragStartX = 0;

    // If the drag was not in a valid direction, don't update anything
    if (!wasDragValid) {
      return;
    }

    // Determine the sidebar state based on the new size
    let newState: 'collapsed' | 'narrow' | 'expanded';
    let snapToSize: number;

    if (sizes[0] <= this.sizeConfigs.collapsed.sidebarArea + 2) {
      newState = 'collapsed';
      snapToSize = this.sizeConfigs.collapsed.sidebarArea;
    } else if (sizes[0] <= this.sizeConfigs.narrow.sidebarArea + 5) {
      newState = 'narrow';
      snapToSize = this.sizeConfigs.narrow.sidebarArea;
    } else {
      newState = 'expanded';
      snapToSize = this.sizeConfigs.expanded.sidebarArea;
    }

    // Snap to preset sizes for better UX
    const snapThreshold = 4; // Threshold in percentage points

    // Check if we're close to a preset size
    if (Math.abs(sizes[0] - this.sizeConfigs.collapsed.sidebarArea) <= snapThreshold) {
      snapToSize = this.sizeConfigs.collapsed.sidebarArea;
      newState = 'collapsed';
    } else if (Math.abs(sizes[0] - this.sizeConfigs.narrow.sidebarArea) <= snapThreshold) {
      snapToSize = this.sizeConfigs.narrow.sidebarArea;
      newState = 'narrow';
    } else if (Math.abs(sizes[0] - this.sizeConfigs.expanded.sidebarArea) <= snapThreshold) {
      snapToSize = this.sizeConfigs.expanded.sidebarArea;
      newState = 'expanded';
    }

    // Update sizes with snap-to behavior
    this.splitSizes = {
      sidebarArea: snapToSize,
      contentArea: 100 - snapToSize
    };

    this.sidebarState = newState;

    // Save the split sizes and state to localStorage
    this.saveSplitterState();

    // Update the toggling service state
    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';
  }

  /**
   * Handles double-click on the gutter
   * Cycles through sidebar states: collapsed -> narrow -> expanded -> collapsed
   */
  onGutterDoubleClick(event: any) {
    console.log('Gutter double-clicked:', event);

    // Cycle through states
    if (this.sidebarState === 'collapsed') {
      this.sidebarState = 'narrow';
      this.splitSizes = { ...this.sizeConfigs.narrow };
    } else if (this.sidebarState === 'narrow') {
      this.sidebarState = 'expanded';
      this.splitSizes = { ...this.sizeConfigs.expanded };
    } else {
      this.sidebarState = 'collapsed';
      this.splitSizes = { ...this.sizeConfigs.collapsed };
    }

    // Save the split sizes and state to localStorage
    this.saveSplitterState();

    // Update the toggling service state
    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';
  }

  // We'll use the gutterClick event from the Angular Split component

  /**
   * Handles click on the gutter
   * Toggles between collapsed and previous state
   */
  onGutterClick(event: any) {
    console.log('Gutter clicked:', event);

    // If we're currently dragging, ignore the click
    if (this.isDragging || this.isSplitterDragging) {
      return;
    }

    // For the Angular Split component's gutterClick event, we don't need to check
    // for genuine clicks as the component already handles this internally
    // We just need to implement the toggle behavior

    // Toggle between collapsed and previous state
    if (this.sidebarState === 'collapsed') {
      // If collapsed, expand to the previous state (or default to narrow)
      const previousState = localStorage.getItem(this.STORAGE_KEYS.previousSidebarState) || 'narrow';
      this.sidebarState = previousState as 'narrow' | 'expanded';
      this.splitSizes = { ...this.sizeConfigs[this.sidebarState] };
    } else {
      // If expanded or narrow, save current state and collapse
      localStorage.setItem(this.STORAGE_KEYS.previousSidebarState, this.sidebarState);
      this.sidebarState = 'collapsed';
      this.splitSizes = { ...this.sizeConfigs.collapsed };
    }

    // Save the split sizes and state to localStorage
    this.saveSplitterState();

    // Update the toggling service state
    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';
  }

  /**
   * Handles mouse down on the splitter hover area
   * Initiates dragging
   */
  onSplitterMouseDown(event: MouseEvent) {
    // Only handle left mouse button
    if (event.button !== 0) return;

    // Record starting position
    this.splitterDragStartX = event.clientX;
    this.isSplitterDragging = true;

    // Store the current sidebar size as the starting point for calculations
    this.dragStartX = this.splitSizes.sidebarArea;

    // Add a class to the body to indicate dragging state
    document.body.classList.add('splitter-dragging');

    // Prevent default to avoid text selection
    event.preventDefault();
    event.stopPropagation();

    // Add document-level event listeners for move and up events
    document.addEventListener('mousemove', this.onDocumentMouseMove, { capture: true });
    document.addEventListener('mouseup', this.onDocumentMouseUp, { capture: true });
  }

  /**
   * Handles mouse move on the splitter hover area
   * Updates the sidebar size during drag
   */
  onSplitterMouseMove(event: MouseEvent) {
    // Only process if we're dragging
    if (!this.isSplitterDragging) return;

    // Prevent default behavior and stop propagation
    event.preventDefault();
    event.stopPropagation();

    // Calculate the drag distance
    const deltaX = event.clientX - this.splitterDragStartX;

    // Set dragging flag for visual feedback
    this.isDragging = true;

    // Calculate new sidebar size based on the drag distance
    // Convert pixels to percentage of window width
    const containerWidth = window.innerWidth;
    const pixelChange = deltaX;
    const percentageChange = (pixelChange / containerWidth) * 100;

    // Calculate new sizes - allow both expanding and collapsing
    let newSidebarSize = this.dragStartX + percentageChange;

    // Enforce minimum and maximum constraints
    newSidebarSize = Math.max(this.minSizes.sidebarArea, newSidebarSize);
    newSidebarSize = Math.min(50, newSidebarSize); // Maximum 50% of screen width

    const newContentSize = 100 - newSidebarSize;

    // Update sizes
    this.splitSizes = {
      sidebarArea: newSidebarSize,
      contentArea: newContentSize
    };

    // Update sidebar state based on new size
    if (newSidebarSize <= this.sizeConfigs.collapsed.sidebarArea + 2) {
      this.sidebarState = 'collapsed';
    } else if (newSidebarSize <= this.sizeConfigs.narrow.sidebarArea + 5) {
      this.sidebarState = 'narrow';
    } else {
      this.sidebarState = 'expanded';
    }

    // Force a layout update
    this.cdr.detectChanges();
  }

  /**
   * Handles mouse up on the splitter hover area
   * Finalizes dragging
   */
  onSplitterMouseUp(_event: MouseEvent) {
    // Only process if we're dragging
    if (!this.isSplitterDragging) return;

    // Prevent default behavior
    if (_event) {
      _event.preventDefault();
      _event.stopPropagation();
    }

    // End the drag operation
    this.isSplitterDragging = false;
    this.isDragging = false;

    // Remove dragging class from body
    document.body.classList.remove('splitter-dragging');

    // Determine the sidebar state based on the new size
    let newState: 'collapsed' | 'narrow' | 'expanded';
    let snapToSize: number;

    if (this.splitSizes.sidebarArea <= this.sizeConfigs.collapsed.sidebarArea + 2) {
      newState = 'collapsed';
      snapToSize = this.sizeConfigs.collapsed.sidebarArea;
    } else if (this.splitSizes.sidebarArea <= this.sizeConfigs.narrow.sidebarArea + 5) {
      newState = 'narrow';
      snapToSize = this.sizeConfigs.narrow.sidebarArea;
    } else {
      newState = 'expanded';
      snapToSize = this.sizeConfigs.expanded.sidebarArea;
    }

    // Snap to preset sizes for better UX
    const snapThreshold = 5; // Threshold in percentage points

    // Check if we're close to a preset size
    if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.collapsed.sidebarArea) <= snapThreshold) {
      snapToSize = this.sizeConfigs.collapsed.sidebarArea;
      newState = 'collapsed';
    } else if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.narrow.sidebarArea) <= snapThreshold) {
      snapToSize = this.sizeConfigs.narrow.sidebarArea;
      newState = 'narrow';
    } else if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.expanded.sidebarArea) <= snapThreshold) {
      snapToSize = this.sizeConfigs.expanded.sidebarArea;
      newState = 'expanded';
    }

    // Update sizes with snap-to behavior
    this.splitSizes = {
      sidebarArea: snapToSize,
      contentArea: 100 - snapToSize
    };

    this.sidebarState = newState;

    // Save the split sizes and state to localStorage
    this.saveSplitterState();

    // Update the toggling service state
    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';

    // Remove document-level event listeners
    document.removeEventListener('mousemove', this.onDocumentMouseMove, { capture: true });
    document.removeEventListener('mouseup', this.onDocumentMouseUp, { capture: true });

    // Force a layout update
    this.cdr.detectChanges();
  }

  /**
   * Document-level mouse move handler
   * Bound to this instance to maintain context
   */
  private onDocumentMouseMove = (event: MouseEvent) => {
    this.onSplitterMouseMove(event);
  };

  /**
   * Document-level mouse up handler
   * Bound to this instance to maintain context
   */
  private onDocumentMouseUp = (_event: MouseEvent) => {
    this.onSplitterMouseUp(_event);
  };

  /**
   * Validates that the split sizes object has the correct structure and values
   * @param sizes The split sizes object to validate
   * @returns True if the sizes are valid, false otherwise
   */
  private isValidSplitSizes(sizes: any): boolean {
    // Check if the object has the required properties
    if (!sizes || typeof sizes !== 'object' || !('sidebarArea' in sizes) || !('contentArea' in sizes)) {
      return false;
    }

    // Check if the values are numbers and within valid ranges
    const sidebarSize = sizes.sidebarArea;
    const contentSize = sizes.contentArea;

    if (typeof sidebarSize !== 'number' || typeof contentSize !== 'number') {
      return false;
    }

    // Check if the values are within valid ranges
    if (sidebarSize < this.minSizes.sidebarArea || sidebarSize > 50) {
      return false;
    }

    if (contentSize < this.minSizes.contentArea || contentSize > 96) {
      return false;
    }

    // Check if the values sum to approximately 100
    const sum = sidebarSize + contentSize;
    if (sum < 99.5 || sum > 100.5) {
      return false;
    }

    return true;
  }

  /**
   * Updates the sidebar state based on the current sidebar width
   * @param sidebarWidth The current width of the sidebar area
   */
  private updateSidebarStateFromSizes(sidebarWidth: number): void {
    // Determine the sidebar state based on the width
    if (sidebarWidth <= this.sizeConfigs.collapsed.sidebarArea + 2) {
      this.sidebarState = 'collapsed';
    } else if (sidebarWidth <= this.sizeConfigs.narrow.sidebarArea + 5) {
      this.sidebarState = 'narrow';
    } else {
      this.sidebarState = 'expanded';
    }
  }

  /**
   * Resets the split sizes to the default for the current sidebar state
   */
  private resetToDefaultSizes(): void {
    // Use the default sizes for the current state
    this.splitSizes = { ...this.sizeConfigs[this.sidebarState] };

    // If we're not in collapsed state, also update the previous split sizes
    if (this.sidebarState !== 'collapsed') {
      this.previousSplitSizes = { ...this.splitSizes };
    }
  }

  /**
   * Saves the current split sizes and state to localStorage
   */
  private saveSplitterState(): void {
    // Only save valid split sizes
    if (this.isValidSplitSizes(this.splitSizes)) {
      localStorage.setItem(this.STORAGE_KEYS.splitSizes, JSON.stringify(this.splitSizes));
      localStorage.setItem(this.STORAGE_KEYS.sidebarState, this.sidebarState);
    } else {
      console.warn('Attempted to save invalid split sizes, using defaults instead');
      this.resetToDefaultSizes();
      localStorage.setItem(this.STORAGE_KEYS.splitSizes, JSON.stringify(this.splitSizes));
      localStorage.setItem(this.STORAGE_KEYS.sidebarState, this.sidebarState);
    }
  }
}

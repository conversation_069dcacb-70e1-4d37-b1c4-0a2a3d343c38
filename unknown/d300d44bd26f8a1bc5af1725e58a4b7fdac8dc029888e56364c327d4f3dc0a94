<div class="docs-layout">
  <!-- Left Sidebar -->
  <div class="docs-list">
    <div class="docs-list-header">
      <i class="fas fa-folder"></i>
      <h2>Documents</h2>
      <button class="add-doc-btn" (click)="createNewDocument()">
        <i class="fas fa-plus text-white"></i>
      </button>
    </div>

    <div *ngIf="isLoading" class="loading-spinner">
      Loading...
    </div>

    <div class="doc-item" *ngFor="let doc of documents">
      <div class="flex items-center gap-2">
        <i class="fas fa-file-alt"></i>
        <span>{{doc.title}}</span>
      </div>
      <div class="action-buttons opacity-0 hover:opacity-100 transition-opacity">
        <button class="toolbar-button" title="View" (click)="editDocument(doc)">
          <i class="fas fa-eye"></i>
        </button>
        <button class="toolbar-button" title="Edit" (click)="editDocument(doc)">
          <i class="fas fa-edit"></i>
        </button>
        <button class="toolbar-button" title="Delete" (click)="deleteDocument(doc.id)">
          <i class="fas fa-trash"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Middle Editor -->
  <div class="editor-container" [class.hidden]="isToogled">
    <div class="editor-header">
      <div class="flex items-center gap-3">
        <i class="fas fa-book text-blue-500 text-xl"></i>
        <h2 class="text-xl font-semibold text-gray-800">Note Editor</h2>
        <!-- <span class="word-count">{{ wordCount }} words</span> -->
      </div>
    </div>

    <div class="title-input-container">
      <input type="text" [(ngModel)]="noteTitle" class="title-input" placeholder="Enter note title...">
    </div>

    <div id="editor"></div>

    <div class="editor-actions">
      <button (click)="clearContent()" class="btn btn-secondary">
        <i class="fas fa-trash-alt"></i>
        Clear
      </button>
      <button (click)="saveContent()" class="btn btn-primary">
        <i class="fas fa-save"></i>
        Save
      </button>
    </div>
  </div>

  <!-- Right Chat -->
  <div class="chat-container" [class.expanded]="isToogled">
    <!-- Chat Header -->
    <div class="chat-header">
      <i class="fas fa-robot ai-icon zoom-effect"></i>
      <span>AI Assistant</span>
      <button class="close-button" (click)="isToogled = !isToogled">
        <i class="fas" [class.fa-expand]="!isToogled" [class.fa-compress]="isToogled"></i>
      </button>
    </div>

    <!-- Chat Messages Area -->
    <div class="chat-messages-area">
      <!-- Empty State -->
      <div class="chat-body" *ngIf="!chatMessages.length">
        <i class="fas fa-comments chat-icon"></i>
        <div class="main-text">Start a conversation with the AI Assistant</div>
        <div class="sub-text">Ask questions about the subject content</div>
      </div>

      <!-- Messages -->
      <div class="chat-messages" #chatContainer>
        <div *ngFor="let message of chatMessages"
          [class]="message.isUser ? 'message user-message' : 'message bot-message'">
          <div class="message-content">
            <div class="message-text">{{ message.text }}</div>
            <div *ngIf="message.hasCodeBlock" class="code-block">
              <div class="code-header">
                <span class="language">{{ message.codeLanguage }}</span>
              </div>
              <pre><code>{{ message.code }}</code></pre>
            </div>
            <div class="message-time">
              {{ message.timestamp | date:'shortTime' }}
            </div>
          </div>
        </div>
        <!-- Add scroll anchor -->
        <div class="scroll-anchor"></div>
      </div>
    </div>

    <!-- Chat Input - Always at bottom -->
    <div class="chat-input-container">
      <input type="text" #chatInput class="chat-input" placeholder="Ask about this subject..."
        (keyup.enter)="sendMessage(chatInput.value)">
      <button class="send-button" (click)="sendMessage(chatInput.value)" [disabled]="isLoading">
        <i class="fas" [class.fa-paper-plane]="!isLoading" [class.fa-spinner]="isLoading"
          [class.fa-spin]="isLoading"></i>
      </button>
    </div>
  </div>

</div>

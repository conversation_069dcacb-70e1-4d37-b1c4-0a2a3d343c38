import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import Quill from 'quill';
import QuillMarkdown from 'quilljs-markdown';
import 'quill/dist/quill.snow.css';
import 'quilljs-markdown/dist/quilljs-markdown-common-style.css';
import { NotesService, Note } from '../services/notes.service';
import Delta from 'quill-delta';

interface ChatMessage {
  text: string;
  isUser: boolean;
  timestamp: Date;
  hasCodeBlock?: boolean;
  codeLanguage?: string;
  code?: string;
}

interface Document {
  id: string;
  title: string;
  content: string;
  createdAt: Date;
}

@Component({
  selector: 'app-docs',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './docs.component.html',
  styleUrl: './docs.component.css'
})
export class DocsComponent implements OnInit {
  @ViewChild('chatContainer') private chatContainer!: ElementRef;
  @ViewChild('chatInput') private chatInput!: ElementRef;
  @ViewChild('editor') private editorElement!: ElementRef;

  private editor: Quill | null = null;
  private quillMarkdown: QuillMarkdown | null = null;
  isToogled = false;
  wordCount = 0;
  markdownContent = '';
  isSaving = false;
  isLoading = false;
  isGenerating = false;
  generateModalVisible = false;
  aiPrompt = '';
  contentType = 'article';
  useCurrentContent = false;
  chatMessages: ChatMessage[] = [
    {
      text: "Hello! How can I help you today?",
      isUser: false,
      timestamp: new Date(),
    },
    {
      text: "Can you explain JavaScript promises?",
      isUser: true,
      timestamp: new Date(),
    },
    {
      text: "Sure! Promises in JavaScript are objects that represent the eventual completion (or failure) of an asynchronous operation. Here's a simple example:",
      isUser: false,
      timestamp: new Date(),
      hasCodeBlock: true,
      codeLanguage: 'javascript',
      code: `const promise = new Promise((resolve, reject) => {
  // Async operation
  setTimeout(() => {
    resolve('Success!');
  }, 1000);
});

promise.then(result => {
  console.log(result); // 'Success!'
}).catch(error => {
  console.error(error);
});`
    },
    {
      text: "That's helpful! Can you show me how to handle multiple promises?",
      isUser: true,
      timestamp: new Date(),
    },
    {
      text: "Here's how you can handle multiple promises using Promise.all():",
      isUser: false,
      timestamp: new Date(),
      hasCodeBlock: true,
      codeLanguage: 'javascript',
      code: `const promise1 = Promise.resolve(1);
const promise2 = Promise.resolve(2);
const promise3 = Promise.resolve(3);

Promise.all([promise1, promise2, promise3])
  .then(values => {
    console.log(values); // [1, 2, 3]
  });`
    }
  ];
  selectedOption = 'article';


  documents: Note[] = [];

  // Add title property
  noteTitle: string = '';
  currentNoteId: number | null = null;

  isDocsOpen = false;  // Set default to false to hide docs section
  showDeleteModal = false;
  noteToDelete: number | null = null;

  constructor(private notesService: NotesService) { }

  ngOnInit() {
    // this.loadNotes();
    // this.initializeEditor();
  }

  createNewDocument() {
    this.isToogled = !this.isToogled;
  }

  private initializeEditor() {
    setTimeout(() => {
      const options = {
        theme: 'snow',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['blockquote', 'code-block'],
            [{ header: 1 }, { header: 2 }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            [{ script: 'sub' }, { script: 'super' }],
            ['link', 'image'],
            ['clean'],
          ],
        },
        placeholder: 'Write your content here...',
      };

      this.editor = new Quill('#editor', options);
      this.quillMarkdown = new QuillMarkdown(this.editor);

      // Listen for text changes to update word count
      this.editor.on('text-change', () => {
        this.updateWordCount();
      });
    });
  }

  // Convert Quill content to Markdown
  private getMarkdownContent(): string {
    if (!this.editor) return '';

    const delta = this.editor.getContents();
    return this.deltaToMarkdown(delta);
  }

  // Helper function to convert Delta to Markdown
  private deltaToMarkdown(delta: any): string {
    let markdown = '';
    delta.ops.forEach((op: any) => {
      if (typeof op.insert === 'string') {
        let text = op.insert;
        if (op.attributes) {
          if (op.attributes.bold) text = `***${text}***`;
          if (op.attributes.italic) text = `*${text}*`;
          if (op.attributes.code) text = `\`${text}\``;
          if (op.attributes.header === 1) text = `# ${text}`;
          if (op.attributes.blockquote) text = `> ${text}`;
          if (op.attributes.list === 'bullet') text = `- ${text}`;
          if (op.attributes.list === 'ordered') text = `1. ${text}`;
        }
        markdown += text;
      }
    });
    return markdown;
  }

  generateContent() {
    this.generateModalVisible = true;
    const currentContent = this.getMarkdownContent();
    if (currentContent) {
      this.aiPrompt = `Based on this context:\n${currentContent}\n\nPlease generate a ${this.selectedOption}`;
      this.useCurrentContent = true;
    }
  }

  async confirmGeneration() {
    if (!this.editor) return;
    this.isGenerating = true;

    try {
      const currentContent = this.useCurrentContent ? this.getMarkdownContent() : '';
      const response = await this.callAIService(
        this.aiPrompt,
        this.selectedOption,
        currentContent
      );

      if (this.useCurrentContent) {
        this.editor.insertText(this.editor.getLength(), '\n\n' + response);
      } else {
        this.editor.setText(response);
      }
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      this.isGenerating = false;
      this.generateModalVisible = false;
    }
  }

  private scrollToBottom(): void {
    try {
      setTimeout(() => {
        if (this.chatContainer) {
          const element = this.chatContainer.nativeElement;
          element.scrollTop = element.scrollHeight;
        }
      }, 100); // Small delay to ensure content is rendered
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  async sendMessage(message: string) {
    if (!message.trim()) return;

    this.isLoading = true;
    try {
      // Add user message
      this.chatMessages.push({
        text: message,
        isUser: true,
        timestamp: new Date()
      });

      // Scroll after user message
      this.scrollToBottom();

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Add bot response
      this.chatMessages.push({
        text: `This is a response to: ${message}`,
        isUser: false,
        timestamp: new Date(),
        hasCodeBlock: message.toLowerCase().includes('code'),
        codeLanguage: 'typescript',
        code: message.toLowerCase().includes('code') ?
          'console.log("Hello World!");' : undefined
      });

      // Scroll after bot response
      this.scrollToBottom();
    } finally {
      this.isLoading = false;
      if (this.chatInput?.nativeElement) {
        this.chatInput.nativeElement.value = '';
        this.chatInput.nativeElement.focus();
      }
    }
  }

  ngAfterViewInit() {
    // Initial scroll to bottom
    this.scrollToBottom();
  }

  private async callAIService(prompt: string, type: string, context: string = ''): Promise<string> {
    // Implement your AI service call here
    const payload = {
      prompt,
      type,
      context,
      useContext: this.useCurrentContent
    };
    // Replace with actual API call
    return `AI generated content based on ${context ? 'existing context' : 'new prompt'}`;
  }



  async saveContent() {
    if (!this.editor) return;

    this.isSaving = true;
    try {
      const markdownContent = this.getMarkdownContent();
      const note: Partial<Note> = {
        id: this.currentNoteId || undefined,
        title: this.noteTitle || 'Untitled Note',
        content: markdownContent, // Send as markdown
      };

      await this.notesService.createOrUpdateNote(note).toPromise();

      // Refresh the notes list
      this.loadNotes();

      // Clear the editor and title
      this.clearContent();
    } catch (error) {
      console.error('Error saving note:', error);
    } finally {
      this.isSaving = false;
    }
  }

  ngOnDestroy() {
    if (this.quillMarkdown) {
      this.quillMarkdown.destroy();
    }
  }

  private updateWordCount() {
    if (this.editor) {
      const text = this.editor.getText();
      this.wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
    }
  }

  clearContent() {
    if (this.editor) {
      this.editor.setText('');
      this.noteTitle = '';
      this.currentNoteId = null;
      this.wordCount = 0;
    }
  }

  private loadNotes() {
    this.isLoading = true;
    this.notesService.getAllNotes().subscribe({
      next: (notes) => {
        this.documents = notes;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading notes:', error);
        this.isLoading = false;
      }
    });
  }

  deleteDocument(id: number) {
    this.noteToDelete = id;
    this.showDeleteModal = true;
  }

  confirmDelete() {
    if (this.noteToDelete !== null) {
      this.notesService.deleteNote(this.noteToDelete).subscribe({
        next: () => {
          this.loadNotes(); // Refresh the list after deletion
          this.showDeleteModal = false;
          this.noteToDelete = null;
        },
        error: (error) => {
          console.error('Error deleting note:', error);
          this.showDeleteModal = false;
          this.noteToDelete = null;
        }
      });
    }
  }

  cancelDelete() {
    this.showDeleteModal = false;
    this.noteToDelete = null;
  }

  editDocument(note: Note) {
    if (this.editor) {
      this.noteTitle = note.title;
      this.currentNoteId = note.id;

      // Parse markdown and apply formatting
       this.editor.root.innerHTML = note.content;
      this.updateWordCount();
    }
  }


  toggleDocs() {
    this.isDocsOpen = !this.isDocsOpen;
    if (this.editor) {
      this.initializeEditor();
    }
  }

}

<div class="container px-[var(--padding-small)] py-[var(--padding-small)] bg-[var(--background-white)]">
  <div class="rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] p-[var(--padding-small)] bg-[var(--background-white)]">
    <!-- Title section with favorite icon -->
    <div class="flex justify-between items-center mb-3 pb-3 border-b border-[var(--hover-blue-gray)]">
      <h1 class="text-2xl font-[var(--font-weight-medium)] text-[var(--text-dark)]">
        {{ note?.title }}
      </h1>
      <div class="flex gap-2">
        <button class="p-2 rounded-full border-none transition-[var(--transition-default)]" [ngClass]="{
            'text-yellow-400 bg-yellow-50': note?.isFavourite,
            'text-[var(--text-medium-gray)] bg-[var(--hover-blue-gray)]': !note?.isFavourite,
            'hover:bg-yellow-100': note?.isFavourite,
            'hover:bg-[var(--hover-blue-gray)]': !note?.isFavourite
          }" (click)="toggleFavorite()" [title]="note?.isFavourite ? 'Remove from favorites' : 'Add to favorites'">
          <i [ngClass]="note?.isFavourite ? 'ri-star-fill' : 'ri-star-line'" class="text-current text-lg"></i>
        </button>
        <button class="p-2 rounded-full border-none text-[var(--text-medium-gray)] bg-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)]"
          (click)="note && editDocument(note.id)">
          <i class="ri-edit-line text-lg"></i>
        </button>
      </div>
    </div>

    <!-- Content section -->
    <div class="overflow-auto" style="height: calc(100vh - 165px);">
      <div [innerHTML]="formattedContent" class="text-[var(--text-dark)]"></div>
    </div>
  </div>
</div>

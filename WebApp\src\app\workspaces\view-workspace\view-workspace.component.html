<div class="bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">
  <div class="flex w-full h-full">
    <!-- Main Content Area -->
    <div class="flex flex-col w-full relative overflow-x-hidden">
      <div class="overflow-auto p-3" #mainContent>
        <router-outlet></router-outlet>
      </div>
    </div>

    <!-- Navigation Dots -->
    <div
      class="fixed right-0 top-1/2 transform -translate-y-1/2 flex flex-col items-center z-50 bg-[var(--background-white)] rounded-l-xl py-6 px-2 shadow-lg border-l border-[var(--hover-blue-gray)]">
      <!-- Navigation Dots -->
      <div class="flex flex-col items-center gap-6">
        <a class="w-8 h-8 rounded-full bg-[var(--hover-blue-gray)] hover:bg-[var(--primary-purple)] transition-all duration-300 relative group flex items-center justify-center"
          *ngIf="isAdmin" [routerLink]="['project-memory']" routerLinkActive="!bg-[var(--primary-purple)] !scale-110">
          <i class="ri-brain-line text-[var(--text-dark)] text-sm"></i>
          <span class="absolute right-full mr-3 py-1 px-2 bg-[var(--background-white)] rounded text-[var(--text-dark)] text-xs opacity-0
                     group-hover:opacity-100 transition-all duration-300 shadow-tooltip -translate-x-2 group-hover:translate-x-0
                     whitespace-nowrap border border-[var(--hover-blue-gray)]">
            Project Memory
          </span>
        </a>
        <a class="w-8 h-8 rounded-full bg-[var(--hover-blue-gray)] hover:bg-[var(--primary-purple)] transition-all duration-300 relative group flex items-center justify-center"
          *ngIf="isAdmin" [routerLink]="['documents']" routerLinkActive="!bg-[var(--primary-purple)] !scale-110">
          <i class="ri-file-text-line text-[var(--text-dark)] text-sm"></i>
          <span class="absolute right-full mr-3 py-1 px-2 bg-[var(--background-white)] rounded text-[var(--text-dark)] text-xs opacity-0
                     group-hover:opacity-100 transition-all duration-300 shadow-tooltip -translate-x-2 group-hover:translate-x-0
                     whitespace-nowrap border border-[var(--hover-blue-gray)]">
            Documents
          </span>
        </a>
        <a class="w-8 h-8 rounded-full bg-[var(--hover-blue-gray)] hover:bg-[var(--primary-purple)] transition-all duration-300 relative group flex items-center justify-center"
          *ngIf="isAdmin" [routerLink]="['agents']" routerLinkActive="!bg-[var(--primary-purple)] !scale-110">
          <i class="ri-robot-line text-[var(--text-dark)] text-sm"></i>
          <span class="absolute right-full mr-3 py-1 px-2 bg-[var(--background-white)] rounded text-[var(--text-dark)] text-xs opacity-0
                     group-hover:opacity-100 transition-all duration-300 shadow-tooltip -translate-x-2 group-hover:translate-x-0
                     whitespace-nowrap border border-[var(--hover-blue-gray)]">
            Agents
          </span>
        </a>
        <a class="w-8 h-8 rounded-full bg-[var(--hover-blue-gray)] hover:bg-[var(--primary-purple)] transition-all duration-300 relative group flex items-center justify-center"
          [routerLink]="['prompt-library']" routerLinkActive="!bg-[var(--primary-purple)] !scale-110">
          <i class="ri-message-2-line text-[var(--text-dark)] text-sm"></i>
          <span class="absolute right-full mr-3 py-1 px-2 bg-[var(--background-white)] rounded text-[var(--text-dark)] text-xs opacity-0
                     group-hover:opacity-100 transition-all duration-300 shadow-tooltip -translate-x-2 group-hover:translate-x-0
                     whitespace-nowrap border border-[var(--hover-blue-gray)]">
            Prompt Library
          </span>
        </a>
      </div>

    </div>

  </div>
</div>

<style>
  /* Theme-aware styles */
  .shadow-tooltip {
    box-shadow: 0 2px 8px var(--shadow-color);
  }

  /* Animation for hover effects */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateX(-8px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Tooltip refinements */
  .group:hover span {
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  /* Icon hover effect */
  .group:hover i {
    transform: scale(1.1);
  }

  .group i {
    transition: transform 0.3s ease;
  }
</style>
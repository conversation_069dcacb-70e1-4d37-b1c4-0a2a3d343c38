import { Component, OnInit, inject, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TogglingService } from '../../toggling.service';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormsModule } from '@angular/forms';
import { AgentDefinitionServiceProxy, DailyInsightServiceProxy, DailyInsightAgentDto, DailyInsightAgentResponseDto, ChatServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';

// Interface for insights
interface Insight {
  id?: number;
  title: string | undefined;
  content: string;
  lastRunAt?: DateTime | undefined;
  agentName?: string;
  expanded?: boolean; // Track if the content is expanded
  isLoading?: boolean; // Track if this specific insight is loading
}

@Component({
  selector: 'app-daily-insight',
  standalone: true,
  imports: [
    CommonModule,
    NzModalModule,
    NzButtonModule,
    NzIconModule,
    NzSpinModule,
    FormsModule
  ],
  templateUrl: './daily-insight.component.html',
  styleUrls: ['./daily-insight.component.css']
})
export class DailyInsightComponent implements OnInit {
  togglingService = inject(TogglingService);
  router = inject(Router);
  dailyInsightService = inject(DailyInsightServiceProxy);
  modalService = inject(NzModalService);
  messageService = inject(NzMessageService);
  agentService = inject(AgentDefinitionServiceProxy);
  chatService = inject(ChatServiceProxy);

  @ViewChild('addAgentModalTemplate', { static: false }) addAgentModalTemplate!: TemplateRef<any>;

  // Agent insights from the backend
  agentInsights: DailyInsightAgentResponseDto[] = [];

  // Insights to display
  insights: Insight[] = [];
  filteredInsights: Insight[] = [];

  // Search functionality
  searchQuery: string = '';

  // Available agents for adding
  availableAgents: (string | undefined)[] = [];
  filteredAvailableAgents: (string | undefined)[] = [];

  // Agent categories for discovery
  agentCategories: string[] = ['Summarization', 'Research', 'Analytics', 'Creative', 'Productivity'];

  // Track the currently selected category
  selectedCategory: string | null = null;

  // Track agent counts by category
  categoryAgentCounts: { [key: string]: number } = {};

  // Search functionality
  agentSearchQuery: string = '';

  // Loading states
  isLoading = false;
  isAddingAgent = false;
  isSearching = false;

  // New agent form data
  newAgent = {
    agentName: '',
    prompt: '',
    title: ''
  };

  ngOnInit() {
    // Ensure the sidebar is closed (narrow mode) when first viewing Daily Insights
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }

    // Load agent insights
    this.loadAgentInsights();

    // Load available agents
    this.loadAvailableAgents();

    // No longer loading chat statistics
  }

  // Removed loadChatStatistics method as we no longer display stats

  loadAgentInsights() {
    // Only show loading indicator on initial load when there are no insights yet
    if (this.insights.length === 0) {
      this.isLoading = true;
    }

    this.dailyInsightService.getAll().subscribe({
      next: (agents) => {
        this.agentInsights = agents;
        this.updateInsights();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading agent insights:', error);
        this.isLoading = false;
        this.messageService.error('Failed to load insights. Please try again later.');
      }
    });
  }

  loadAvailableAgents() {
    this.agentService.getAllAgentName().subscribe({
      next: (agents) => {
        this.availableAgents = agents.map(a => a.agentName);
        this.filteredAvailableAgents = [...this.availableAgents]; // Initialize filtered list

        // Calculate agent counts by category
        this.calculateAgentCountsByCategory();
      },
      error: (error) => {
        console.error('Error loading available agents:', error);
      }
    });
  }

  /**
   * Calculate how many agents belong to each category
   */
  calculateAgentCountsByCategory() {
    // Reset counts
    this.categoryAgentCounts = {};

    // Initialize counts for all categories
    this.agentCategories.forEach(category => {
      this.categoryAgentCounts[category] = 0;
    });

    // Define keywords for each category
    const categoryKeywords: { [key: string]: string[] } = {
      'Summarization': ['summary', 'summarize', 'summarizer', 'summarization', 'digest', 'brief'],
      'Research': ['research', 'finder', 'search', 'discover', 'explorer', 'knowledge'],
      'Analytics': ['analytics', 'analysis', 'analyzer', 'insight', 'data', 'statistics', 'metric'],
      'Creative': ['creative', 'writer', 'writing', 'story', 'content', 'blog', 'generate'],
      'Productivity': ['task', 'schedule', 'planner', 'organize', 'productivity', 'assistant', 'helper']
    };

    // Count agents in each category
    this.availableAgents.forEach(agent => {
      if (!agent) return;

      const agentLower = agent.toLowerCase();

      // Check each category
      this.agentCategories.forEach(category => {
        const keywords = categoryKeywords[category] || [];

        // If agent name contains any keyword for this category, increment count
        if (keywords.some(keyword => agentLower.includes(keyword.toLowerCase()))) {
          this.categoryAgentCounts[category]++;
        }
      });
    });
  }

  /**
   * Search agents based on the query
   */
  searchAgents() {
    // Clear selected category when searching
    this.selectedCategory = null;

    if (!this.agentSearchQuery || this.agentSearchQuery.trim() === '') {
      this.filteredAvailableAgents = [...this.availableAgents];
      return;
    }

    const query = this.agentSearchQuery.toLowerCase().trim();
    this.filteredAvailableAgents = this.availableAgents.filter(agent =>
      agent?.toLowerCase().includes(query)
    );
  }

  /**
   * Clear category filter and show all agents
   */
  clearCategoryFilter() {
    this.selectedCategory = null;
    this.filteredAvailableAgents = [...this.availableAgents];
  }

  /**
   * Filter agents by category
   */
  filterAgentsByCategory(category: string) {
    // If the same category is clicked again, clear the filter
    if (this.selectedCategory === category) {
      this.selectedCategory = null;
      this.filteredAvailableAgents = [...this.availableAgents];
      return;
    }

    // Set the selected category
    this.selectedCategory = category;

    // Define keywords for each category - more comprehensive than before
    const categoryKeywords: { [key: string]: string[] } = {
      'Summarization': ['summary', 'summarize', 'summarizer', 'summarization', 'digest', 'brief'],
      'Research': ['research', 'finder', 'search', 'discover', 'explorer', 'knowledge'],
      'Analytics': ['analytics', 'analysis', 'analyzer', 'insight', 'data', 'statistics', 'metric'],
      'Creative': ['creative', 'writer', 'writing', 'story', 'content', 'blog', 'generate'],
      'Productivity': ['task', 'schedule', 'planner', 'organize', 'productivity', 'assistant', 'helper']
    };

    // Get keywords for the selected category
    const keywords = categoryKeywords[category] || [];

    if (keywords.length > 0) {
      // Filter agents that match any keyword in the category
      this.filteredAvailableAgents = this.availableAgents.filter(agent => {
        if (!agent) return false;

        const agentLower = agent.toLowerCase();
        return keywords.some(keyword => agentLower.includes(keyword.toLowerCase()));
      });

      // If no matches found, show a subset of agents (for demo purposes)
      // In a real implementation, you might want to show a message instead
      if (this.filteredAvailableAgents.length === 0) {
        // Create some placeholder agents for the category if none exist
        const placeholderAgents: { [key: string]: string[] } = {
          'Summarization': ['SummaryAgent', 'ContentSummarizerAgent'],
          'Research': ['ResearchAgent', 'InformationFinderAgent'],
          'Analytics': ['DataAnalysisAgent', 'InsightGeneratorAgent'],
          'Creative': ['CreativeWritingAgent', 'StorytellerAgent'],
          'Productivity': ['TaskManagerAgent', 'SchedulerAgent']
        };

        // Use placeholders for this category
        this.filteredAvailableAgents = placeholderAgents[category] || [];
      }
    } else {
      // If no keywords defined for this category, show all agents
      this.filteredAvailableAgents = [...this.availableAgents];
    }

    // Clear search query
    this.agentSearchQuery = '';
  }

  /**
   * Select an agent for the new insight
   */
  selectAgent(agent: string | undefined) {
    if (agent) {
      this.newAgent.agentName = agent;
      // Auto-generate a title based on the agent name
      if (!this.newAgent.title) {
        this.newAgent.title = `Daily ${agent.replace('Agent', '')} Insight`;
      }
    }
  }

  updateInsights() {
    // Only show agent insights
    this.insights = this.agentInsights.map(agent => {
      const formattedContent = this.formatAgentResponse(agent.lastResponse) || 'This insight has not been generated yet.';

      // Find existing insight to preserve its state (like expanded and isLoading)
      const existingInsight = this.insights.find(i => i.id === agent.id);

      return {
        id: agent.id,
        title: agent.title,
        content: formattedContent,
        lastRunAt: agent.lastRunAt,
        agentName: agent.agentName,
        expanded: existingInsight ? existingInsight.expanded : false, // Preserve expanded state if it exists
        isLoading: existingInsight ? existingInsight.isLoading : false // Preserve loading state if it exists
      };
    });

    // Initialize filtered insights with all insights
    this.filterInsights();
  }

  /**
   * Filter insights based on search query
   */
  filterInsights() {
    this.isSearching = true;

    if (!this.searchQuery || this.searchQuery.trim() === '') {
      this.filteredInsights = [...this.insights];
      this.isSearching = false;
      return;
    }

    const query = this.searchQuery.toLowerCase().trim();

    this.filteredInsights = this.insights.filter(insight =>
      (insight.title?.toLowerCase().includes(query) ||
       insight.content.toLowerCase().includes(query) ||
       insight.agentName?.toLowerCase().includes(query))
    );

    this.isSearching = false;
  }

  toggleExpand(insight: Insight) {
    // If we're expanding this insight, first collapse any others that might be expanded
    if (!insight.expanded) {
      // Close any other expanded insights
      this.insights.forEach(i => {
        if (i !== insight && i.expanded) {
          i.expanded = false;
        }
      });
    }

    // Toggle the expanded state of the clicked insight
    insight.expanded = !insight.expanded;

    // Prevent body scrolling when an insight is expanded
    if (insight.expanded) {
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }
  }

  formatAgentResponse(response: string | undefined): string {
    if (!response) return '';

    // Check if the response contains specific patterns from the screenshot
    if (response.includes('**Title**:') || response.includes('**Content**:')) {
      // This appears to be a note or raw data format - extract just the content
      const contentMatch = response.match(/\*\*Content\*\*:\s*([\s\S]*?)(?:\*\*|$)/);
      if (contentMatch && contentMatch[1]) {
        return contentMatch[1].trim();
      }
    }

    // Remove any markdown code blocks or formatting markers
    let formatted = response
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers
      .replace(/\*(.*?)\*/g, '$1')     // Remove italic markers
      .replace(/__(.*?)__/g, '$1')     // Remove underline markers
      .replace(/~~(.*?)~~/g, '$1')     // Remove strikethrough markers
      .replace(/\[\[(.*?)\]\]/g, '$1')  // Remove wiki-style links
      .replace(/\[(.*?)\]\((.*?)\)/g, '$1') // Remove markdown links

      // Clean up timestamps and formatting codes that appear in the screenshot
      .replace(/\*\*Title\*\*:\s*[^\n]*\n?/g, '')
      .replace(/\*\*Content\*\*:\s*/g, '')
      .replace(/\*\*Created At\*\*:\s*\d{4}-\d{2}-\d{2}/g, '')
      .replace(/\*\*Is Favourite\*\*:\s*(True|False)\s*\d*/g, '')
      .replace(/Last updated:\s*\d{4}-\d{2}-\d{2}\s*\d{2}:\d{2}:\d{2}/g, '')
      .replace(/--+/g, '') // Remove divider lines
      .replace(/\(continues with [^)]*\)/g, '') // Remove continuation notes
      .replace(/\n+Last updated:\s*\d{4}-\d{2}-\d{2}\s*\d{2}:\d{2}:\d{2}/g, ''); // Remove timestamp at end

    // Special handling for lists - preserve the intro text but clean it up
    if (formatted.includes('Here are all the note titles numbered for your convenience:')) {
      formatted = formatted.replace(/Here are all the note titles numbered for your convenience:\s*\n+/, 'Here are all the note titles:\n\n');
    } else {
      // If it's not a list with that specific intro, remove any generic intro text
      formatted = formatted.replace(/\n+Here are all the notes available:\s*\d*\./g, '');
    }

    // Trim extra whitespace and newlines
    formatted = formatted.trim()
      .replace(/\n{3,}/g, '\n\n') // Replace 3+ consecutive newlines with just 2
      .replace(/\s+$/gm, '');     // Remove trailing whitespace from each line
    // Preserve numbered list markers

    return formatted;
  }

  runAgentNow(id: number) {
    // Find the insight and set its loading state
    const insight = this.insights.find(i => i.id === id);
    if (insight) {
      insight.isLoading = true;
    }

    this.dailyInsightService.runNow(id).subscribe({
      next: (_response) => {
        this.messageService.success('Agent executed successfully. Refreshing...');
        // Reload insights after a short delay to allow the backend to process
        setTimeout(() => {
          this.dailyInsightService.getAll().subscribe({
            next: (agents) => {
              this.agentInsights = agents;

              // Only update the specific insight that was refreshed
              const updatedAgent = agents.find(a => a.id === id);
              if (updatedAgent && insight) {
                insight.content = this.formatAgentResponse(updatedAgent.lastResponse) || 'This insight has not been generated yet.';
                insight.lastRunAt = updatedAgent.lastRunAt;
              }

              // Clear loading state
              if (insight) {
                insight.isLoading = false;
              }
            },
            error: (error: any) => {
              console.error('Error refreshing agent insight:', error);
              if (insight) {
                insight.isLoading = false;
              }
              this.messageService.error('Failed to refresh insight. Please try again later.');
            }
          });
        }, 2000);
      },
      error: (error: any) => {
        console.error('Error running agent:', error);
        if (insight) {
          insight.isLoading = false;
        }
        this.messageService.error('Failed to run agent. Please try again later.');
      }
    });
  }

  deleteAgentInsight(id: number) {
    this.modalService.confirm({
      nzTitle: 'Are you sure you want to delete this insight?',
      nzContent: 'This action cannot be undone.',
      nzOkText: 'Yes',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        // Find the insight and set its loading state
        const insight = this.insights.find(i => i.id === id);
        if (insight) {
          insight.isLoading = true;
        }

        this.dailyInsightService.delete(id).subscribe({
          next: (_response) => {
            this.messageService.success('Insight deleted successfully.');
            // Remove the deleted insight from the list
            this.insights = this.insights.filter(i => i.id !== id);
            // Reload all insights in the background
            this.loadAgentInsights();
          },
          error: (error) => {
            console.error('Error deleting agent insight:', error);
            if (insight) {
              insight.isLoading = false;
            }
            this.messageService.error('Failed to delete insight. Please try again later.');
          }
        });
      },
      nzCancelText: 'No'
    });
  }

  showAddAgentModal() {
    // Reset form data
    this.newAgent = {
      agentName: '',
      prompt: '',
      title: ''
    };

    // Reset search
    this.agentSearchQuery = '';
    this.filteredAvailableAgents = [...this.availableAgents];

    // Create modal with wider width to accommodate the two-column layout
    this.modalService.create({
      nzTitle: 'Discover and Add AI Agents',
      nzContent: this.addAgentModalTemplate,
      nzWidth: '800px', // Wider modal for two columns
      nzClassName: 'agent-discovery-modal', // Custom class for styling
      nzFooter: null // Remove default footer, we'll manage it in the template
    });
  }

  addNewAgent() {
    if (!this.newAgent.agentName || !this.newAgent.title) {
      this.messageService.warning('Please fill in all required fields.');
      return;
    }

    this.isAddingAgent = true;

    const dto = new DailyInsightAgentDto({
      agentName: this.newAgent.agentName,
      prompt: this.newAgent.prompt,
      title: this.newAgent.title
    });

    this.dailyInsightService.create(dto).subscribe({
      next: (_response) => {
        this.messageService.success('New insight agent added successfully.');
        this.modalService.closeAll();
        this.isAddingAgent = false;
        this.loadAgentInsights();
      },
      error: (error) => {
        console.error('Error adding new agent insight:', error);
        this.isAddingAgent = false;
        this.messageService.error('Failed to add new insight agent. Please try again later.');
      }
    });
  }

  // Navigate to chat page
  goToChat() {
    // Ensure the sidebar is open when navigating to chat
    if (!this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
    this.router.navigate(['/chat']);
  }

  // Navigate to workspaces
  goToWorkspace() {
    // Keep sidebar closed when navigating to workspaces
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
    this.router.navigate(['/workspaces']);
  }
}

import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { FormsModule } from '@angular/forms';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { RouterLink } from '@angular/router';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import {
  ModelDetailsServiceProxy,
  WorkspaceServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-addor-edit-worksapce',
  standalone: true,
  imports: [
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzAutocompleteModule,
    FormsModule,
    NzBreadCrumbModule,
    NzSwitchModule,
  ],
  templateUrl: './addor-edit-worksapce.component.html',
  styleUrl: './addor-edit-worksapce.component.css',
})
export class AddorEditWorksapceComponent {
  workspace: any = {
    title: '',
    description: '',
    systemInformation: '',
    modelName: '',
    isDefault: false,
    isProjectManagement: false,
  };

  modelSearchQuery: string = '';
  models: any[] = [];
  filteredModels = [...this.models];
  constructor(
    private modelDetailsService: ModelDetailsServiceProxy,
    @Inject(NZ_MODAL_DATA)
    public data: { isUpdating: boolean; title: any; workspace: any },
    private modelRef: NzModalRef,
    private worksapceService: WorkspaceServiceProxy
  ) {
    if (this.data.isUpdating) {
      this.workspace = this.data.workspace;
      this.modelSearchQuery = this.data.workspace.modelName;
      console.log(this.workspace);
    } else {
      this.workspace = {
        title: '',
        description: '',
        systemInformation: '',
        modelName: '',
        isDefault: false,
        isProjectManagement: false,
      };
    }

    // console.log(this.data.id);
  }
  ngOnInit(): void {
    this.loadModels();
  }
  loadModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((response: any) => {
      this.models = response;
      this.filteredModels = this.models;
    });
  }
  onChange(event: Event) {
    const query = (event.target as HTMLInputElement).value.toLowerCase();
    this.filteredModels = this.models.filter((option: any) =>
      option.modelName.toLowerCase().includes(query)
    );
  }

  updateModel(selectedModel: string) {
    this.modelSearchQuery = selectedModel;
  }
  updateWorkspace() {}
  saveWorkspace() {
    this.workspace.modelName = this.modelSearchQuery;
    this.workspace.title = this.workspace.title
      .trim()
      .replace(/[^a-zA-Z0-9 ]/g, ''); // Trim whitespace from title
    console.log(this.workspace.systemInformation);
    console.log(this.workspace);
    this.worksapceService
      .createOrUpdate(this.workspace)
      .subscribe((response: any) => {
        if (response) {
          this.modelRef.close(this.workspace); // Return the added users to the parent component
          console.log('Workspace saved');
          this.clearWorkspace();
        }
      });
  }

  cancel() {
    this.modelRef.close();
  }
  clearWorkspace() {
    this.workspace = {
      title: '',
      description: '',
      systemInformation: '',
      modelName: '',
      isDefault: false,
      isProjectManagement: false,
    };
  }
}

<div class="flex min-h-screen bg-[#1A1A1A] text-white">
  <!-- Left Sidebar -->
  <div class="w-48 border-r border-[#2A2A2A] p-4">
    <div class="space-y-1 *:no-underline transition-all *:cursor-pointer">
      <div class="flex items-center gap-2 py-1" [class.text-gray-400]="activeTab !== 'general'"
        (click)="activeTab = 'general'">
        <i class="ri-settings-3-line"></i>
        <span class="text-sm">General</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'connections'" (click)="activeTab = 'connections'">
        <i class="ri-link"></i>
        <span class="text-sm">Connections</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'models'" (click)="activeTab = 'models'">
        <i class="ri-brain-line"></i>
        <span class="text-sm">Models</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'evaluations'" (click)="activeTab = 'evaluations'">
        <i class="ri-file-list-line"></i>
        <span class="text-sm">Evaluations</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'documents'" (click)="activeTab = 'documents'">
        <i class="ri-file-text-line"></i>
        <span class="text-sm">Documents</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'web search'" (click)="activeTab = 'web search'">
        <i class="ri-search-line"></i>
        <span class="text-sm">Web Search</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'interface'" (click)="activeTab = 'interface'">
        <i class="ri-window-line"></i>
        <span class="text-sm">Interface</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'audio'" (click)="activeTab = 'audio'">
        <i class="ri-volume-up-line"></i>
        <span class="text-sm">Audio</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'images'" (click)="activeTab = 'images'">
        <i class="ri-image-line"></i>
        <span class="text-sm">Images</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'pipelines'" (click)="activeTab = 'pipelines'">
        <i class="ri-git-branch-line"></i>
        <span class="text-sm">Pipelines</span>
      </div>
      <div class="flex items-center gap-2 text-gray-400 hover:text-white py-1"
        [class.text-white]="activeTab === 'database'" (click)="activeTab = 'database'">
        <i class="ri-database-2-line"></i>
        <span class="text-sm">Database</span>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 p-6" *ngIf="activeTab === 'general'">
    <div class="mb-6  ">
      <h2 class="text-lg mb-4 text-white">General Settings</h2>

      <!-- Settings List -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Enable New Sign Ups</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Default User Role</h3>
          </div>
          <select class="bg-[#2A2A2A] text-sm rounded px-3 py-1.5 outline-none">
            <option>pending</option>
          </select>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Enable API Key</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox" checked
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">API Key Endpoint Restrictions</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Show Admin Details in Account Pending Overlay</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox" checked
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Enable Community Sharing</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox" checked
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Enable Message Rating</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox" checked
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>

        <div class="space-y-2">
          <h3 class="text-sm font-medium">WebUI URL</h3>
          <input type="text" value="https://localchat.3dbotics.com/"
            class="w-full bg-[#2A2A2A] px-1 border-none outline-none text-white py-2 rounded text-sm">
          <p class="text-xs text-gray-400">Enter the public URL of your WebUI. This URL will be used to generate links
            in the notifications.</p>
        </div>

        <div class="space-y-2">
          <h3 class="text-sm font-medium">JWT Expiration</h3>
          <input type="text" value="-1"
            class="w-full bg-[#2A2A2A] px-1 border-none outline-none text-white py-2 rounded text-sm">
          <p class="text-xs text-gray-400">Valid time units: 's', 'm', 'h', 'd' or '-1' for no expiration.</p>
        </div>

        <div class="space-y-2">
          <h3 class="text-sm font-medium">Webhook URL</h3>
          <input type="text" placeholder="https://example.com/webhook"
            class="w-full bg-[#2A2A2A] px-1 border-none outline-none text-white py-2 rounded text-sm">
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Channels (Beta)</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">LDAP</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox"
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div>
      </div>
    </div>

    <!-- Save Button -->
    <div class=" bottom-6 right-6">
      <button class="bg-white text-black px-4 py-2 rounded-full hover:bg-gray-200 border-none outline-none ">
        Save
      </button>
    </div>
  </div>

  <!-- API Settings -->
  <div class="p-6 bg-[#1A1A1A] rounded-lg mb-6 w-full" *ngIf="activeTab === 'connections'">
    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h2 class="text-lg font-medium text-white">API Settings</h2>
        <button (click)="onAddApi()"
          class="bg-[#2A2A2A] text-white px-3 py-1 rounded hover:bg-[#3A3A3A] transition-colors cursor-pointer border-none outline-none">Add
          API</button>
      </div>

      <div class="space-y-6">
        <!-- OpenAI API Toggle -->
        @for (api of apiLists; track $index) {
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">OpenAI Url</h3>
            <span>{{api.tokenUrl}}</span>
          </div>
          <div class="relative  mr-2 align-middle select-none flex items-center gap-4">
            <button (click)="editApi(api)"
              class="p-2 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors cursor-pointer outline-none border-none">
              <i class="ri-edit-2-line text-emerald-400 hover:text-emerald-300 transition-colors text-lg"></i>
            </button>
            <button (click)="deleteApi(api)"
              class="p-2 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors cursor-pointer outline-none border-none">
              <i class="ri-delete-bin-6-line text-red-500 hover:text-red-400 transition-colors text-lg"></i>
            </button>
          </div>
        </div>
        }
        <div *ngIf="apiLists.length === 0 && !showForm"
        class="flex flex-col items-center justify-center p-10 bg-zinc-900 rounded-lg border border-dashed border-zinc-700">
        <i class="ri-brain-line text-6xl text-zinc-600 mb-4"></i>
        <p class="text-zinc-400 text-center mb-4">No chat models have been added yet.</p>
        <button (click)="onAddApi()"
          class="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 rounded-md flex items-center gap-2 transition-colors outline-none border-none cursor-pointer">
          Add Your First Model
        </button>
      </div>
        <!-- Ollama API Toggle -->
        <!-- <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium">Ollama API</h3>
          </div>
          <div class="relative inline-block w-10 mr-2 align-middle select-none">
            <input type="checkbox" checked
              class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
            <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
          </div>
        </div> -->

        <!-- Manage Ollama API Connections -->
        <!-- <div class="space-y-2">
          <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium">Manage Ollama API Connections</h3>
            <button class="text-xs bg-[#2A2A2A] px-2 py-1 rounded hover:bg-[#3A3A3A] transition-colors">
              <i class="ri-add-line"></i>
            </button>
          </div>
          <input type="text" value="https://locala.3dbotics.com"
            class="w-full bg-[#2A2A2A] px-3 border-none outline-none text-white py-2 rounded text-sm">
          <div class="flex items-center gap-2 text-xs text-gray-400">
            <span>Trouble accessing Ollama?</span>
            <a href="#" class="text-blue-400 hover:underline">Click here for help</a>
          </div>
        </div> -->
      </div>
    </div>
  </div>

  <!-- Model Settings -->
  <div class="p-6 bg-[#1A1A1A] rounded-lg mb-6 w-full" *ngIf="activeTab === 'models'">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <h2 class="text-lg font-medium text-white">Models</h2>
          <span class="bg-[#2A2A2A] px-2 py-0.5 rounded text-xs">5</span>
        </div>
        <div class="flex items-center gap-4">
          <div class="relative">
            <input type="text" placeholder="Search Models"
              class="bg-[#2A2A2A] w-64 text-white outline-none border-none p-2 rounded pl-9 text-sm">
            <i class="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
          </div>
        </div>
      </div>

      <!-- Models List -->
      <div class="space-y-3">
        <!-- Model Item -->
        <div class="flex items-center justify-between p-3 bg-[#1F1F1F] rounded-lg" *ngFor="let model of models">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-[#2A2A2A] rounded-full flex items-center justify-center">
              <i class="ri-ai-generate"></i>
            </div>
            <div>
              <h3 class="text-sm font-medium">{{model.modelName}}</h3>
              <p class="text-xs text-gray-400">Model ID: {{model.id}}</p>
            </div>
          </div>
          <div class="flex items-center gap-4">
            <!-- <button class="text-gray-400 hover:text-white transition-colors">
              <i class="ri-pencil-line"></i>
            </button> -->
            <div class="relative inline-block w-10 mr-2 align-middle select-none">
              <input type="checkbox"
                class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                [(ngModel)]="model.isActive" (input)="updateModelIsActive(model)" />
              <label class="toggle-label block overflow-hidden h-6 rounded-full bg-[#2A2A2A] cursor-pointer"></label>
            </div>
          </div>
        </div>
      </div>

      <!-- Import/Export Buttons -->
      <div class="flex justify-end gap-3">
        <button
          class="flex items-center gap-2 bg-[#2A2A2A] px-3 py-1.5 rounded text-sm hover:bg-[#3A3A3A] transition-colors">
          <i class="ri-download-line"></i>
          Import Presets
        </button>
        <button
          class="flex items-center gap-2 bg-[#2A2A2A] px-3 py-1.5 rounded text-sm hover:bg-[#3A3A3A] transition-colors">
          <i class="ri-upload-line"></i>
          Export Presets
        </button>
      </div>
    </div>
  </div>


</div>
<div *ngIf="showForm" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
  <div class="bg-zinc-900 rounded-lg p-6 w-full max-w-md border border-zinc-800 shadow-lg">

    <div class="space-y-4">

      <div>
        <label class="block text-sm font-medium text-white">Token URL</label>
        <input type="text" [(ngModel)]="apiData.tokenUrl"
          class="w-full bg-[#2A2A2A] px-3 py-2 border-none outline-none text-white rounded text-sm">
      </div>
      <div>
        <label class="block text-sm font-medium text-white">API Key</label>
        <input type="text" [(ngModel)]="apiData.apiKey"
          class="w-full bg-[#2A2A2A] px-3 py-2 border-none outline-none text-white rounded text-sm">
      </div>
      <div class="flex justify-end gap-3 mt-6">
        <button (click)="validateCredentials()"
          class="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors cursor-pointer outline-none border-none">
          Validate
        </button>
      </div>
    </div>
    <div class="flex justify-end gap-3 mt-6">
      <button (click)="resetForm()"
        class="px-4 py-2 bg-zinc-700 hover:bg-zinc-700 rounded-md transition-colors cursor-pointer outline-none border-none">
        Cancel
      </button>
      <button (click)="saveApi()"
        [ngClass]="{'bg-emerald-600 hover:bg-emerald-700': isCredentialsValid, 'bg-gray-500 cursor-not-allowed': !isCredentialsValid}"
        class="px-4 py-2 rounded-md transition-colors cursor-pointer outline-none border-none">
        Save
      </button>
    </div>
  </div>

</div>

import { Component, inject } from '@angular/core';
import { ThemeService } from '../../shared/services/theam.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div
      class="relative w-auto h-8 flex items-center rounded-md p-1 pr-3 bg-[#E8ECEF] px-2 overflow-hidden cursor-pointer transition-[var(--transition-default)] shadow-[inset_0_0_5px_rgba(0,0,0,0.2)]"
      (click)="themeService.toggleTheme()"
      aria-label="Toggle theme"
    >
      <span
        class="absolute w-8 h-[85%] bg-[var(--background-white)] shadow-[0_2px_5px_rgba(0,0,0,0.2)] rounded-md flex items-center justify-center"
        [ngClass]="{
          ' trans-0 ': !themeService.isDarkMode(),
          'trans-30': themeService.isDarkMode()
        }"
      ></span>
      <i
        class="ri-sun-line text-[#000000] text-base z-10 ml-1"
        [ngClass]="{
          'translate-x-0': !themeService.isDarkMode(),
          'translate-x-6 ': themeService.isDarkMode()
        }"
      ></i>
      <i
        class="ri-moon-line text-[#000000] text-base z-10 ml-4 transform transition-[var(--transition-default)]"
        [ngClass]="{
          'translate-x-0 !text-white': themeService.isDarkMode(),
        }"
      ></i>
    </div>
  `,
  styles: [
    `
      :host {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .trans-0 {
        transform: translateX(-1px);
        transition: transform 0.3s ease-in-out;
      }
      .trans-30 {
        transform: translateX(29px);
        transition: transform 0.3s ease-in-out;
      }
    `,
  ],
})
export class ThemeToggleComponent {
  themeService = inject(ThemeService);
}

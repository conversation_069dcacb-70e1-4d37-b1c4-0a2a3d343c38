import { Component, inject, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, KeyValue } from '@angular/common';
import { Router } from '@angular/router';
import { TogglingService } from '../../../toggling.service';
import { ChatListService } from '../../../services/chat-list.service';
import { ChatServiceProxy, ChatRequestDto } from '../../../../shared/service-proxies/service-proxies';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-rightsidebarchat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzPopoverModule
  ],
  templateUrl: './rightsidebarchat.component.html',
  styleUrls: ['./rightsidebarchat.component.css']
})
export class RightsidebarchatComponent {
  // Inject services
  router = inject(Router);
  togglingService = inject(TogglingService);
  chatListService = inject(ChatListService);
  nzMessageService = inject(NzMessageService);

  // Input properties
  @Input() workspaceName: string = '';
  @Input() userInput: ChatRequestDto = new ChatRequestDto();
  @Input() activeTab: string = 'all';
  @Input() groupedChats: { [key: string]: any[] } = { History: [] };
  @Input() filteredGroupedChats: { [key: string]: any[] } = {};
  @Input() pinnedChats: any[] = [];
  @Input() favoriteChats: any[] = [];
  @Input() archivedChats: any[] = [];
  @Input() hasMoreMessages: boolean = false;
  @Input() isAllChatsOpen: boolean = true;

  // Output events
  @Output() tabChanged = new EventEmitter<string>();
  @Output() loadMore = new EventEmitter<void>();
  @Output() allChatsToggled = new EventEmitter<boolean>();
  @Output() filteredChatsChanged = new EventEmitter<{ [key: string]: any[] }>();

  // Tab configuration
  tabConfig: {
    [key: string]: {
      title: string;
      isGrouped: boolean;
      hasMore?: boolean;
    };
  } = {
    all: {
      title: 'All Chats',
      isGrouped: true,
      hasMore: true,
    },
    'pinned-history': {
      title: 'Pinned Chats',
      isGrouped: true,
    },
    favorite: {
      title: 'Favorite Chats',
      isGrouped: true,
    },
    archive: {
      title: 'Archive Chats',
      isGrouped: true,
    },
    notes: { title: 'My Notes', isGrouped: false },
    history: { title: 'Chat History', isGrouped: false },
  };

  constructor(private _chatService: ChatServiceProxy) {}

  // Original order for keyvalue pipe
  originalOrder = (
    a: KeyValue<string, any[]>,
    b: KeyValue<string, any[]>
  ): number => {
    const order = ['History'];
    return order.indexOf(a.key) - order.indexOf(b.key);
  };

  // Track by functions
  trackByChatId(index: number, chat: any): string {
    return chat.id;
  }

  // Methods for chat functionality
  toggleTab(tab: string) {
    // Emit event to parent component
    this.activeTab = tab;
    this.isAllChatsOpen = true;
    this.tabChanged.emit(tab);
  }

  filterChats(event: Event) {
    event.stopPropagation();
    const searchTerm = (event.target as HTMLInputElement).value.toLowerCase();

    if (!searchTerm) {
      this.filteredGroupedChats = { ...this.groupedChats };
      this.filteredChatsChanged.emit(this.filteredGroupedChats);
      return;
    }

    this.filteredGroupedChats = {};
    Object.keys(this.groupedChats).forEach((group) => {
      this.filteredGroupedChats[group] = this.groupedChats[group].filter(
        (chat: any) => chat.title.toLowerCase().includes(searchTerm)
      );
    });
    this.filteredChatsChanged.emit(this.filteredGroupedChats);
  }

  toggleAllChats(event: Event) {
    event.stopPropagation();
    this.isAllChatsOpen = !this.isAllChatsOpen;
    this.allChatsToggled.emit(this.isAllChatsOpen);
  }

  addNewChats(event: Event) {
    event.stopPropagation();
    this.isAllChatsOpen = true;
    this.chatListService.chatId = 0; // Reset chatId to 0 for new chat

    if (this.workspaceName) {
      // If workspaceName is available, navigate to the chat within that workspace
      this.router.navigate(['workspaces', this.workspaceName, 'chat']);
    } else {
      // If no workspace, navigate to chat
      this.router.navigate(['/chat']);
    }
  }

  toggleChat(
    event: Event,
    chat: {
      id: number;
      title: string;
      lastMessage: string;
      isToggled: boolean;
      chats: any;
    }
  ) {
    // Set the chat ID and navigate to the chat
    this.chatListService.chatId = chat.id;

    // Navigate based on workspace context
    if (this.workspaceName) {
      this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);
    } else {
      this.router.navigate(['/chat', chat.id]);
    }

    event.stopPropagation();
    chat.isToggled = !chat.isToggled;
  }

  addToPinnedChat(chat: any) {
    this._chatService.pin(chat.id, !chat.isPinned).subscribe((res) => {
      if (res.isPinned) {
        this.nzMessageService.success('Chat pinned successfully!');
        this.pinnedChats = [...this.pinnedChats, res];
      } else {
        this.nzMessageService.success('Chat unpinned successfully!');
        this.pinnedChats = this.pinnedChats.filter((c) => c.id !== res.id);
      }
      const index = this.chatListService.chatList.findIndex(
        (c: any) => c.id === res.id
      );
      if (index !== -1) {
        this.chatListService.chatList[index] = res;
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;
        this.filteredGroupedChats = { ...this.groupedChats };
      }
    });
  }

  addToFavChat(chat: any) {
    this._chatService
      .favorite(chat.id, !chat.isFavorite)
      .subscribe((res) => {
        if (res.isFavorite) {
          this.nzMessageService.success('Chat favorited successfully!');
          this.favoriteChats = [...this.favoriteChats, res];
        } else {
          this.nzMessageService.success('Chat unfavorited successfully!');
          this.favoriteChats = this.favoriteChats.filter(
            (c) => c.id !== res.id
          );
        }
        const index = this.chatListService.chatList.findIndex(
          (c: any) => c.id === res.id
        );
        if (index !== -1) {
          this.chatListService.chatList[index] = res;
          this.chatListService.groupChatsByDate();
          this.groupedChats = this.chatListService.groupedChats;
          this.filteredGroupedChats = { ...this.groupedChats };
        }
      });
  }

  addToArchiveChat(chat: any) {
    this._chatService
      .archive(chat.id, !chat.isArchived)
      .subscribe((res) => {
        if (res.isArchived) {
          this.nzMessageService.success('Chat archived successfully!');
          this.archivedChats = [...this.archivedChats, res];
          this.chatListService.chatList = this.chatListService.chatList.filter(
            (c: any) => c.id !== res.id
          );
        } else {
          this.nzMessageService.success('Chat unarchived successfully!');
          this.archivedChats = this.archivedChats.filter(
            (c) => c.id !== res.id
          );
          this.chatListService.chatList.push(res);
        }
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;
        this.filteredGroupedChats = { ...this.groupedChats };
      });
  }

  getCurrentTabChats() {
    if (this.activeTab === 'all') {
      return this.groupedChats;
    }

    const chats = this.filteredGroupedChats;
    if (this.tabConfig[this.activeTab].isGrouped) {
      return Object.keys(chats).length > 0 ? chats : { 'No Chats': [] };
    }
    return chats;
  }

  getCurrentTabTitle() {
    return this.tabConfig[this.activeTab].title;
  }

  isCurrentTabGrouped() {
    return this.tabConfig[this.activeTab].isGrouped;
  }

  hasMoreForCurrentTab() {
    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;
  }

  loadMoreChatList() {
    // Emit event to parent component to load more chats
    this.loadMore.emit();
  }
}

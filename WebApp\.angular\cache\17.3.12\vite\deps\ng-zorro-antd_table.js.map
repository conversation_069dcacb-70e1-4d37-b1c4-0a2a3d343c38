{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-checkbox.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-cdk-resize-observer.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-pagination.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-table.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, forwardRef, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i5 from '@angular/forms';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i4 from 'ng-zorro-antd/core/form';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"inputElement\"];\nconst _c2 = [\"nz-checkbox\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzCheckboxGroupComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 1);\n    i0.ɵɵlistener(\"nzCheckedChange\", function NzCheckboxGroupComponent_For_1_Template_label_nzCheckedChange_0_listener($event) {\n      const option_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckedChange(option_r2, $event));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", option_r2.disabled || ctx_r2.nzDisabled)(\"nzChecked\", option_r2.checked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r2.label);\n  }\n}\nclass NzCheckboxWrapperComponent {\n  constructor() {\n    this.nzOnChange = new EventEmitter();\n    this.checkboxList = [];\n  }\n  addCheckbox(value) {\n    this.checkboxList.push(value);\n  }\n  removeCheckbox(value) {\n    this.checkboxList.splice(this.checkboxList.indexOf(value), 1);\n  }\n  onChange() {\n    const listOfCheckedValue = this.checkboxList.filter(item => item.nzChecked).map(item => item.nzValue);\n    this.nzOnChange.emit(listOfCheckedValue);\n  }\n  static {\n    this.ɵfac = function NzCheckboxWrapperComponent_Factory(t) {\n      return new (t || NzCheckboxWrapperComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxWrapperComponent,\n      selectors: [[\"nz-checkbox-wrapper\"]],\n      hostAttrs: [1, \"ant-checkbox-group\"],\n      outputs: {\n        nzOnChange: \"nzOnChange\"\n      },\n      exportAs: [\"nzCheckboxWrapper\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzCheckboxWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxWrapperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-wrapper',\n      exportAs: 'nzCheckboxWrapper',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-checkbox-group'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzOnChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzCheckboxComponent {\n  innerCheckedChange(checked) {\n    if (!this.nzDisabled) {\n      this.nzChecked = checked;\n      this.onChange(this.nzChecked);\n      this.nzCheckedChange.emit(this.nzChecked);\n      if (this.nzCheckboxWrapperComponent) {\n        this.nzCheckboxWrapperComponent.onChange();\n      }\n    }\n  }\n  writeValue(value) {\n    this.nzChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  constructor(ngZone, elementRef, nzCheckboxWrapperComponent, cdr, focusMonitor, directionality, nzFormStatusService) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.nzCheckboxWrapperComponent = nzCheckboxWrapperComponent;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this.nzFormStatusService = nzFormStatusService;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzCheckedChange = new EventEmitter();\n    this.nzValue = null;\n    this.nzAutoFocus = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzChecked = false;\n    this.nzId = null;\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    if (this.nzCheckboxWrapperComponent) {\n      this.nzCheckboxWrapperComponent.addCheckbox(this);\n    }\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.preventDefault();\n        this.focus();\n        if (this.nzDisabled) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.innerCheckedChange(!this.nzChecked);\n          this.cdr.markForCheck();\n        });\n      });\n      fromEvent(this.inputElement.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => event.stopPropagation());\n    });\n  }\n  ngAfterViewInit() {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    if (this.nzCheckboxWrapperComponent) {\n      this.nzCheckboxWrapperComponent.removeCheckbox(this);\n    }\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzCheckboxComponent_Factory(t) {\n      return new (t || NzCheckboxComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NzCheckboxWrapperComponent, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NzFormStatusService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxComponent,\n      selectors: [[\"\", \"nz-checkbox\", \"\"]],\n      viewQuery: function NzCheckboxComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-checkbox-wrapper\"],\n      hostVars: 6,\n      hostBindings: function NzCheckboxComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-wrapper-in-form-item\", !!ctx.nzFormStatusService)(\"ant-checkbox-wrapper-checked\", ctx.nzChecked)(\"ant-checkbox-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzValue: \"nzValue\",\n        nzAutoFocus: \"nzAutoFocus\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzChecked: \"nzChecked\",\n        nzId: \"nzId\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\"\n      },\n      exportAs: [\"nzCheckbox\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 11,\n      consts: [[\"inputElement\", \"\"], [1, \"ant-checkbox\"], [\"type\", \"checkbox\", 1, \"ant-checkbox-input\", 3, \"ngModelChange\", \"checked\", \"ngModel\", \"disabled\"], [1, \"ant-checkbox-inner\"]],\n      template: function NzCheckboxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n          i0.ɵɵlistener(\"ngModelChange\", function NzCheckboxComponent_Template_input_ngModelChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.innerCheckedChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"span\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-checked\", ctx.nzChecked && !ctx.nzIndeterminate)(\"ant-checkbox-disabled\", ctx.nzDisabled)(\"ant-checkbox-indeterminate\", ctx.nzIndeterminate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"ngModel\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled);\n          i0.ɵɵattribute(\"autofocus\", ctx.nzAutoFocus ? \"autofocus\" : null)(\"id\", ctx.nzId);\n        }\n      },\n      dependencies: [FormsModule, i5.CheckboxControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzAutoFocus\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzIndeterminate\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzChecked\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-checkbox]',\n      exportAs: 'nzCheckbox',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      class=\"ant-checkbox\"\n      [class.ant-checkbox-checked]=\"nzChecked && !nzIndeterminate\"\n      [class.ant-checkbox-disabled]=\"nzDisabled\"\n      [class.ant-checkbox-indeterminate]=\"nzIndeterminate\"\n    >\n      <input\n        #inputElement\n        type=\"checkbox\"\n        class=\"ant-checkbox-input\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [attr.id]=\"nzId\"\n        [checked]=\"nzChecked\"\n        [ngModel]=\"nzChecked\"\n        [disabled]=\"nzDisabled\"\n        (ngModelChange)=\"innerCheckedChange($event)\"\n      />\n      <span class=\"ant-checkbox-inner\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-wrapper',\n        '[class.ant-checkbox-wrapper-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-checkbox-wrapper-checked]': 'nzChecked',\n        '[class.ant-checkbox-rtl]': `dir === 'rtl'`\n      },\n      imports: [FormsModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: NzCheckboxWrapperComponent,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzAutoFocus: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input\n    }],\n    nzId: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCheckboxGroupComponent {\n  onCheckedChange(option, checked) {\n    option.checked = checked;\n    this.onChange(this.options);\n  }\n  constructor(elementRef, focusMonitor, cdr, directionality) {\n    this.elementRef = elementRef;\n    this.focusMonitor = focusMonitor;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.options = [];\n    this.nzDisabled = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.options = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzCheckboxGroupComponent_Factory(t) {\n      return new (t || NzCheckboxGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxGroupComponent,\n      selectors: [[\"nz-checkbox-group\"]],\n      hostAttrs: [1, \"ant-checkbox-group\"],\n      hostVars: 2,\n      hostBindings: function NzCheckboxGroupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-group-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzDisabled: \"nzDisabled\"\n      },\n      exportAs: [\"nzCheckboxGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"nz-checkbox\", \"\", 1, \"ant-checkbox-group-item\", 3, \"nzDisabled\", \"nzChecked\"], [\"nz-checkbox\", \"\", 1, \"ant-checkbox-group-item\", 3, \"nzCheckedChange\", \"nzDisabled\", \"nzChecked\"]],\n      template: function NzCheckboxGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, NzCheckboxGroupComponent_For_1_Template, 3, 3, \"label\", 0, _forTrack0);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx.options);\n        }\n      },\n      dependencies: [NzCheckboxComponent],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([InputBoolean()], NzCheckboxGroupComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-group',\n      exportAs: 'nzCheckboxGroup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @for (option of options; track option.value) {\n      <label\n        nz-checkbox\n        class=\"ant-checkbox-group-item\"\n        [nzDisabled]=\"option.disabled || nzDisabled\"\n        [nzChecked]=\"option.checked!\"\n        (nzCheckedChange)=\"onCheckedChange(option, $event)\"\n      >\n        <span>{{ option.label }}</span>\n      </label>\n    }\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-group',\n        '[class.ant-checkbox-group-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzCheckboxComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxModule {\n  static {\n    this.ɵfac = function NzCheckboxModule_Factory(t) {\n      return new (t || NzCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzCheckboxModule,\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxModule, NzCheckboxWrapperComponent };\n", "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Factory that creates a new ResizeObserver and allows us to stub it out in unit tests.\n */\nclass NzResizeObserverFactory {\n  create(callback) {\n    return typeof ResizeObserver === 'undefined' ? null : new ResizeObserver(callback);\n  }\n  static {\n    this.ɵfac = function NzResizeObserverFactory_Factory(t) {\n      return new (t || NzResizeObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeObserverFactory,\n      factory: NzResizeObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass NzResizeObserver {\n  constructor(nzResizeObserverFactory) {\n    this.nzResizeObserverFactory = nzResizeObserverFactory;\n    /** Keeps track of the existing ResizeObservers so they can be reused. */\n    this.observedElements = new Map();\n  }\n  ngOnDestroy() {\n    this.observedElements.forEach((_, element) => this.cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this.observeElement(element);\n      const subscription = stream.subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n        this.unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing ResizeObserver if available, or creating a\n   * new one if not.\n   */\n  observeElement(element) {\n    if (!this.observedElements.has(element)) {\n      const stream = new Subject();\n      const observer = this.nzResizeObserverFactory.create(mutations => stream.next(mutations));\n      if (observer) {\n        observer.observe(element);\n      }\n      this.observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this.observedElements.get(element).count++;\n    }\n    return this.observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying ResizeObserver if nobody else is\n   * observing this element.\n   */\n  unobserveElement(element) {\n    if (this.observedElements.has(element)) {\n      this.observedElements.get(element).count--;\n      if (!this.observedElements.get(element).count) {\n        this.cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying ResizeObserver for the specified element. */\n  cleanupObserver(element) {\n    if (this.observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this.observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this.observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeObserver_Factory(t) {\n      return new (t || NzResizeObserver)(i0.ɵɵinject(NzResizeObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeObserver,\n      factory: NzResizeObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: NzResizeObserverFactory\n  }], null);\n})();\nclass NzResizeObserverDirective {\n  subscribe() {\n    this.unsubscribe();\n    this.currentSubscription = this.nzResizeObserver.observe(this.elementRef).subscribe(this.nzResizeObserve);\n  }\n  unsubscribe() {\n    this.currentSubscription?.unsubscribe();\n  }\n  constructor(nzResizeObserver, elementRef) {\n    this.nzResizeObserver = nzResizeObserver;\n    this.elementRef = elementRef;\n    this.nzResizeObserve = new EventEmitter();\n    this.nzResizeObserverDisabled = false;\n    this.currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this.currentSubscription && !this.nzResizeObserverDisabled) {\n      this.subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzResizeObserve\n    } = changes;\n    if (nzResizeObserve) {\n      if (this.nzResizeObserverDisabled) {\n        this.unsubscribe();\n      } else {\n        this.subscribe();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeObserverDirective_Factory(t) {\n      return new (t || NzResizeObserverDirective)(i0.ɵɵdirectiveInject(NzResizeObserver), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzResizeObserverDirective,\n      selectors: [[\"\", \"nzResizeObserver\", \"\"]],\n      inputs: {\n        nzResizeObserverDisabled: \"nzResizeObserverDisabled\"\n      },\n      outputs: {\n        nzResizeObserve: \"nzResizeObserve\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzResizeObserverFactory]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzResizeObserverDirective.prototype, \"nzResizeObserverDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzResizeObserver]',\n      standalone: true,\n      providers: [NzResizeObserverFactory]\n    }]\n  }], () => [{\n    type: NzResizeObserver\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzResizeObserve: [{\n      type: Output\n    }],\n    nzResizeObserverDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzResizeObserverModule {\n  static {\n    this.ɵfac = function NzResizeObserverModule_Factory(t) {\n      return new (t || NzResizeObserverModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzResizeObserverModule,\n      imports: [NzResizeObserverDirective],\n      exports: [NzResizeObserverDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzResizeObserverDirective],\n      exports: [NzResizeObserverDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzResizeObserver, NzResizeObserverDirective, NzResizeObserverFactory, NzResizeObserverModule };\n", "import { __decorate } from 'tslib';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, Optional, ViewChild, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2$1 from 'ng-zorro-antd/core/services';\nimport { gridResponsiveMap, NzBreakpointEnum } from 'ng-zorro-antd/core/services';\nimport { toNumber, InputBoolean, InputNumber } from 'ng-zorro-antd/core/util';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i1$1 from 'ng-zorro-antd/select';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport * as i1$2 from '@angular/cdk/bidi';\nimport * as i1$3 from 'ng-zorro-antd/i18n';\n\n/* eslint-disable */\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-pagination-item\", \"\"];\nconst _c1 = (a0, a1) => ({\n  $implicit: a0,\n  page: a1\n});\nfunction NzPaginationItemComponent_ng_template_0_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r1 = i0.ɵɵnextContext().page;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(page_r1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵtemplate(1, NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template, 1, 0, \"span\", 3)(2, NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.prev_page);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.direction === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵtemplate(1, NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template, 1, 0, \"span\", 4)(2, NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.next_page);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.direction === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template, 1, 0, \"span\", 8)(1, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template, 1, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional(0, ctx_r1.direction === \"rtl\" ? 0 : 1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template, 1, 0, \"span\", 9)(1, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template, 1, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional(0, ctx_r1.direction === \"rtl\" ? 0 : 1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5)(1, \"div\", 6);\n    i0.ɵɵtemplate(2, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template, 2, 1)(3, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template, 2, 1);\n    i0.ɵɵelementStart(4, \"span\", 7);\n    i0.ɵɵtext(5, \"\\u2022\\u2022\\u2022\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const type_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, (tmp_5_0 = type_r3) === \"prev_5\" ? 2 : tmp_5_0 === \"next_5\" ? 3 : -1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_0_Template, 2, 1)(1, NzPaginationItemComponent_ng_template_0_Case_1_Template, 3, 3)(2, NzPaginationItemComponent_ng_template_0_Case_2_Template, 3, 3)(3, NzPaginationItemComponent_ng_template_0_Case_3_Template, 6, 1);\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵconditional(0, (tmp_4_0 = type_r3) === \"page\" ? 0 : tmp_4_0 === \"prev\" ? 1 : tmp_4_0 === \"next\" ? 2 : 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_2_Template(rf, ctx) {}\nconst _c2 = [\"nz-pagination-options\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzPaginationOptionsComponent_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 3);\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r3.label)(\"nzValue\", option_r3.value);\n  }\n}\nfunction NzPaginationOptionsComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-select\", 2);\n    i0.ɵɵlistener(\"ngModelChange\", function NzPaginationOptionsComponent_Conditional_0_Template_nz_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange($event));\n    });\n    i0.ɵɵrepeaterCreate(1, NzPaginationOptionsComponent_Conditional_0_For_2_Template, 1, 2, \"nz-option\", 3, _forTrack0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", ctx_r1.disabled)(\"nzSize\", ctx_r1.nzSize)(\"ngModel\", ctx_r1.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1.listOfPageSizeOption);\n  }\n}\nfunction NzPaginationOptionsComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"input\", 4);\n    i0.ɵɵlistener(\"keydown.enter\", function NzPaginationOptionsComponent_Conditional_1_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.jumpToPageViaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.jump_to, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.page, \" \");\n  }\n}\nconst _c3 = [\"containerTemplate\"];\nfunction _forTrack1($index, $item) {\n  return this.trackByPageItem;\n}\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  range: a1\n});\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template(rf, ctx) {}\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 1);\n    i0.ɵɵtemplate(1, NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template, 0, 0, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.showTotal)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, ctx_r0.total, ctx_r0.ranges));\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 5);\n    i0.ɵɵlistener(\"gotoIndex\", function NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_gotoIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.jumpPage($event));\n    })(\"diffIndex\", function NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_diffIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.jumpDiff($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"type\", page_r3.type)(\"index\", page_r3.index)(\"disabled\", !!page_r3.disabled)(\"itemRender\", ctx_r0.itemRender)(\"active\", ctx_r0.pageIndex === page_r3.index)(\"direction\", ctx_r0.dir);\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 6);\n    i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPageIndexChange($event));\n    })(\"pageSizeChange\", function NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageSizeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPageSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"total\", ctx_r0.total)(\"locale\", ctx_r0.locale)(\"disabled\", ctx_r0.disabled)(\"nzSize\", ctx_r0.nzSize)(\"showSizeChanger\", ctx_r0.showSizeChanger)(\"showQuickJumper\", ctx_r0.showQuickJumper)(\"pageIndex\", ctx_r0.pageIndex)(\"pageSize\", ctx_r0.pageSize)(\"pageSizeOptions\", ctx_r0.pageSizeOptions);\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template, 2, 5, \"li\", 1);\n    i0.ɵɵrepeaterCreate(2, NzPaginationDefaultComponent_ng_template_0_For_3_Template, 1, 7, \"li\", 2, _forTrack1, true);\n    i0.ɵɵtemplate(4, NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template, 1, 9, \"li\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.showTotal ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.listOfPageItem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(4, ctx_r0.showQuickJumper || ctx_r0.showSizeChanger ? 4 : -1);\n  }\n}\nfunction NzPaginationSimpleComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\")(1, \"li\", 1);\n    i0.ɵɵlistener(\"click\", function NzPaginationSimpleComponent_ng_template_0_Template_li_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prePage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"li\", 2)(3, \"input\", 3);\n    i0.ɵɵlistener(\"keydown.enter\", function NzPaginationSimpleComponent_ng_template_0_Template_input_keydown_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.jumpToPageViaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 4);\n    i0.ɵɵtext(5, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"li\", 5);\n    i0.ɵɵlistener(\"click\", function NzPaginationSimpleComponent_ng_template_0_Template_li_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextPage());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r1.locale)(\"disabled\", ctx_r1.isFirstIndex)(\"direction\", ctx_r1.dir)(\"itemRender\", ctx_r1.itemRender);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.prev_page);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"title\", ctx_r1.pageIndex + \"/\" + ctx_r1.lastIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.pageIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.lastIndex, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r1.locale)(\"disabled\", ctx_r1.isLastIndex)(\"direction\", ctx_r1.dir)(\"itemRender\", ctx_r1.itemRender);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale == null ? null : ctx_r1.locale.next_page);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzPaginationComponent_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const simplePagination_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", simplePagination_r2.template);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzPaginationComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const defaultPagination_r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", defaultPagination_r3.template);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_0_Template, 1, 1, null, 4)(1, NzPaginationComponent_Conditional_0_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r3.nzSimple ? 0 : 1);\n  }\n}\nclass NzPaginationItemComponent {\n  constructor() {\n    this.active = false;\n    this.index = null;\n    this.disabled = false;\n    this.direction = 'ltr';\n    this.type = null;\n    this.itemRender = null;\n    this.diffIndex = new EventEmitter();\n    this.gotoIndex = new EventEmitter();\n    this.title = null;\n  }\n  clickItem() {\n    if (!this.disabled) {\n      if (this.type === 'page') {\n        this.gotoIndex.emit(this.index);\n      } else {\n        this.diffIndex.emit({\n          next: 1,\n          prev: -1,\n          prev_5: -5,\n          next_5: 5\n        }[this.type]);\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      locale,\n      index,\n      type\n    } = changes;\n    if (locale || index || type) {\n      this.title = {\n        page: `${this.index}`,\n        next: this.locale?.next_page,\n        prev: this.locale?.prev_page,\n        prev_5: this.locale?.prev_5,\n        next_5: this.locale?.next_5\n      }[this.type];\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationItemComponent_Factory(t) {\n      return new (t || NzPaginationItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationItemComponent,\n      selectors: [[\"li\", \"nz-pagination-item\", \"\"]],\n      hostVars: 19,\n      hostBindings: function NzPaginationItemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzPaginationItemComponent_click_HostBindingHandler() {\n            return ctx.clickItem();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"title\", ctx.title);\n          i0.ɵɵclassProp(\"ant-pagination-prev\", ctx.type === \"prev\")(\"ant-pagination-next\", ctx.type === \"next\")(\"ant-pagination-item\", ctx.type === \"page\")(\"ant-pagination-jump-prev\", ctx.type === \"prev_5\")(\"ant-pagination-jump-prev-custom-icon\", ctx.type === \"prev_5\")(\"ant-pagination-jump-next\", ctx.type === \"next_5\")(\"ant-pagination-jump-next-custom-icon\", ctx.type === \"next_5\")(\"ant-pagination-disabled\", ctx.disabled)(\"ant-pagination-item-active\", ctx.active);\n        }\n      },\n      inputs: {\n        active: \"active\",\n        locale: \"locale\",\n        index: \"index\",\n        disabled: \"disabled\",\n        direction: \"direction\",\n        type: \"type\",\n        itemRender: \"itemRender\"\n      },\n      outputs: {\n        diffIndex: \"diffIndex\",\n        gotoIndex: \"gotoIndex\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 3,\n      vars: 5,\n      consts: [[\"renderItemTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"button\", 1, \"ant-pagination-item-link\", 3, \"disabled\"], [\"nz-icon\", \"\", \"nzType\", \"right\"], [\"nz-icon\", \"\", \"nzType\", \"left\"], [1, \"ant-pagination-item-link\"], [1, \"ant-pagination-item-container\"], [1, \"ant-pagination-item-ellipsis\"], [\"nz-icon\", \"\", \"nzType\", \"double-right\", 1, \"ant-pagination-item-link-icon\"], [\"nz-icon\", \"\", \"nzType\", \"double-left\", 1, \"ant-pagination-item-link-icon\"]],\n      template: function NzPaginationItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Template, 4, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzPaginationItemComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n        }\n        if (rf & 2) {\n          const renderItemTemplate_r4 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.itemRender || renderItemTemplate_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c1, ctx.type, ctx.index));\n        }\n      },\n      dependencies: [NzIconModule, i1.NzIconDirective, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'li[nz-pagination-item]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #renderItemTemplate let-type let-page=\"page\">\n      @switch (type) {\n        @case ('page') {\n          <a>{{ page }}</a>\n        }\n        @case ('prev') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.prev_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"right\"></span>\n            } @else {\n              <span nz-icon nzType=\"left\"></span>\n            }\n          </button>\n        }\n        @case ('next') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.next_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"left\"></span>\n            } @else {\n              <span nz-icon nzType=\"right\"></span>\n            }\n          </button>\n        }\n        @default {\n          <a class=\"ant-pagination-item-link\">\n            <div class=\"ant-pagination-item-container\">\n              @switch (type) {\n                @case ('prev_5') {\n                  @if (direction === 'rtl') {\n                    <span\n                      nz-icon\n                      nzType=\"double-right\"\n                      class=\"ant-pagination-item-link-icon\"\n                    ></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-left\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n                @case ('next_5') {\n                  @if (direction === 'rtl') {\n                    <span nz-icon nzType=\"double-left\"\n                          class=\"ant-pagination-item-link-icon\"></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-right\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n              }\n              <span class=\"ant-pagination-item-ellipsis\">•••</span>\n            </div>\n          </a>\n        }\n      }\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"itemRender || renderItemTemplate\"\n      [ngTemplateOutletContext]=\"{ $implicit: type, page: index }\"\n    />\n  `,\n      host: {\n        '[class.ant-pagination-prev]': `type === 'prev'`,\n        '[class.ant-pagination-next]': `type === 'next'`,\n        '[class.ant-pagination-item]': `type === 'page'`,\n        '[class.ant-pagination-jump-prev]': `type === 'prev_5'`,\n        '[class.ant-pagination-jump-prev-custom-icon]': `type === 'prev_5'`,\n        '[class.ant-pagination-jump-next]': `type === 'next_5'`,\n        '[class.ant-pagination-jump-next-custom-icon]': `type === 'next_5'`,\n        '[class.ant-pagination-disabled]': 'disabled',\n        '[class.ant-pagination-item-active]': 'active',\n        '[attr.title]': 'title',\n        '(click)': 'clickItem()'\n      },\n      imports: [NzIconModule, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], null, {\n    active: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    direction: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    diffIndex: [{\n      type: Output\n    }],\n    gotoIndex: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationOptionsComponent {\n  constructor() {\n    this.nzSize = 'default';\n    this.disabled = false;\n    this.showSizeChanger = false;\n    this.showQuickJumper = false;\n    this.total = 0;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.pageSizeOptions = [];\n    this.pageIndexChange = new EventEmitter();\n    this.pageSizeChange = new EventEmitter();\n    this.listOfPageSizeOption = [];\n  }\n  onPageSizeChange(size) {\n    if (this.pageSize !== size) {\n      this.pageSizeChange.next(size);\n    }\n  }\n  jumpToPageViaInput($event) {\n    const target = $event.target;\n    const index = Math.floor(toNumber(target.value, this.pageIndex));\n    this.pageIndexChange.next(index);\n    target.value = '';\n  }\n  ngOnChanges(changes) {\n    const {\n      pageSize,\n      pageSizeOptions,\n      locale\n    } = changes;\n    if (pageSize || pageSizeOptions || locale) {\n      this.listOfPageSizeOption = [...new Set([...this.pageSizeOptions, this.pageSize])].map(item => ({\n        value: item,\n        label: `${item} ${this.locale.items_per_page}`\n      }));\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationOptionsComponent_Factory(t) {\n      return new (t || NzPaginationOptionsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationOptionsComponent,\n      selectors: [[\"li\", \"nz-pagination-options\", \"\"]],\n      hostAttrs: [1, \"ant-pagination-options\"],\n      inputs: {\n        nzSize: \"nzSize\",\n        disabled: \"disabled\",\n        showSizeChanger: \"showSizeChanger\",\n        showQuickJumper: \"showQuickJumper\",\n        locale: \"locale\",\n        total: \"total\",\n        pageIndex: \"pageIndex\",\n        pageSize: \"pageSize\",\n        pageSizeOptions: \"pageSizeOptions\"\n      },\n      outputs: {\n        pageIndexChange: \"pageIndexChange\",\n        pageSizeChange: \"pageSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"ant-pagination-options-size-changer\", 3, \"nzDisabled\", \"nzSize\", \"ngModel\"], [1, \"ant-pagination-options-quick-jumper\"], [1, \"ant-pagination-options-size-changer\", 3, \"ngModelChange\", \"nzDisabled\", \"nzSize\", \"ngModel\"], [3, \"nzLabel\", \"nzValue\"], [3, \"keydown.enter\", \"disabled\"]],\n      template: function NzPaginationOptionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationOptionsComponent_Conditional_0_Template, 3, 3, \"nz-select\", 0)(1, NzPaginationOptionsComponent_Conditional_1_Template, 4, 3, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.showSizeChanger ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showQuickJumper ? 1 : -1);\n        }\n      },\n      dependencies: [NzSelectModule, i1$1.NzOptionComponent, i1$1.NzSelectComponent, FormsModule, i2.NgControlStatus, i2.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationOptionsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'li[nz-pagination-options]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (showSizeChanger) {\n      <nz-select\n        class=\"ant-pagination-options-size-changer\"\n        [nzDisabled]=\"disabled\"\n        [nzSize]=\"nzSize\"\n        [ngModel]=\"pageSize\"\n        (ngModelChange)=\"onPageSizeChange($event)\"\n      >\n        @for (option of listOfPageSizeOption; track option.value) {\n          <nz-option [nzLabel]=\"option.label\" [nzValue]=\"option.value\" />\n        }\n      </nz-select>\n    }\n\n    @if (showQuickJumper) {\n      <div class=\"ant-pagination-options-quick-jumper\">\n        {{ locale.jump_to }}\n        <input [disabled]=\"disabled\" (keydown.enter)=\"jumpToPageViaInput($event)\" />\n        {{ locale.page }}\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-pagination-options'\n      },\n      imports: [NzSelectModule, FormsModule],\n      standalone: true\n    }]\n  }], () => [], {\n    nzSize: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    showSizeChanger: [{\n      type: Input\n    }],\n    showQuickJumper: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }],\n    pageSizeChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzPaginationDefaultComponent {\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this.nzSize = 'default';\n    this.itemRender = null;\n    this.showTotal = null;\n    this.disabled = false;\n    this.showSizeChanger = false;\n    this.showQuickJumper = false;\n    this.total = 0;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.pageSizeOptions = [10, 20, 30, 40];\n    this.pageIndexChange = new EventEmitter();\n    this.pageSizeChange = new EventEmitter();\n    this.ranges = [0, 0];\n    this.listOfPageItem = [];\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.updateRtlStyle();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateRtlStyle();\n  }\n  updateRtlStyle() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  jumpPage(index) {\n    this.onPageIndexChange(index);\n  }\n  jumpDiff(diff) {\n    this.jumpPage(this.pageIndex + diff);\n  }\n  trackByPageItem(_, value) {\n    return `${value.type}-${value.index}`;\n  }\n  onPageIndexChange(index) {\n    this.pageIndexChange.next(index);\n  }\n  onPageSizeChange(size) {\n    this.pageSizeChange.next(size);\n  }\n  getLastIndex(total, pageSize) {\n    return Math.ceil(total / pageSize);\n  }\n  buildIndexes() {\n    const lastIndex = this.getLastIndex(this.total, this.pageSize);\n    this.listOfPageItem = this.getListOfPageItem(this.pageIndex, lastIndex);\n  }\n  getListOfPageItem(pageIndex, lastIndex) {\n    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n    const concatWithPrevNext = listOfPage => {\n      const prevItem = {\n        type: 'prev',\n        disabled: pageIndex === 1\n      };\n      const nextItem = {\n        type: 'next',\n        disabled: pageIndex === lastIndex\n      };\n      return [prevItem, ...listOfPage, nextItem];\n    };\n    const generatePage = (start, end) => {\n      const list = [];\n      for (let i = start; i <= end; i++) {\n        list.push({\n          index: i,\n          type: 'page'\n        });\n      }\n      return list;\n    };\n    if (lastIndex <= 9) {\n      return concatWithPrevNext(generatePage(1, lastIndex));\n    } else {\n      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n      const generateRangeItem = (selected, last) => {\n        let listOfRange = [];\n        const prevFiveItem = {\n          type: 'prev_5'\n        };\n        const nextFiveItem = {\n          type: 'next_5'\n        };\n        const firstPageItem = generatePage(1, 1);\n        const lastPageItem = generatePage(lastIndex, lastIndex);\n        if (selected < 5) {\n          // If the 4th is selected, one more page will be displayed.\n          const maxLeft = selected === 4 ? 6 : 5;\n          listOfRange = [...generatePage(2, maxLeft), nextFiveItem];\n        } else if (selected < last - 3) {\n          listOfRange = [prevFiveItem, ...generatePage(selected - 2, selected + 2), nextFiveItem];\n        } else {\n          // If the 4th from last is selected, one more page will be displayed.\n          const minRight = selected === last - 3 ? last - 5 : last - 4;\n          listOfRange = [prevFiveItem, ...generatePage(minRight, last - 1)];\n        }\n        return [...firstPageItem, ...listOfRange, ...lastPageItem];\n      };\n      return concatWithPrevNext(generateRangeItem(pageIndex, lastIndex));\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      pageIndex,\n      pageSize,\n      total\n    } = changes;\n    if (pageIndex || pageSize || total) {\n      this.ranges = [(this.pageIndex - 1) * this.pageSize + 1, Math.min(this.pageIndex * this.pageSize, this.total)];\n      this.buildIndexes();\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationDefaultComponent_Factory(t) {\n      return new (t || NzPaginationDefaultComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationDefaultComponent,\n      selectors: [[\"nz-pagination-default\"]],\n      viewQuery: function NzPaginationDefaultComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        }\n      },\n      inputs: {\n        nzSize: \"nzSize\",\n        itemRender: \"itemRender\",\n        showTotal: \"showTotal\",\n        disabled: \"disabled\",\n        locale: \"locale\",\n        showSizeChanger: \"showSizeChanger\",\n        showQuickJumper: \"showQuickJumper\",\n        total: \"total\",\n        pageIndex: \"pageIndex\",\n        pageSize: \"pageSize\",\n        pageSizeOptions: \"pageSizeOptions\"\n      },\n      outputs: {\n        pageIndexChange: \"pageIndexChange\",\n        pageSizeChange: \"pageSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"containerTemplate\", \"\"], [1, \"ant-pagination-total-text\"], [\"nz-pagination-item\", \"\", 3, \"locale\", \"type\", \"index\", \"disabled\", \"itemRender\", \"active\", \"direction\"], [\"nz-pagination-options\", \"\", 3, \"total\", \"locale\", \"disabled\", \"nzSize\", \"showSizeChanger\", \"showQuickJumper\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"nz-pagination-item\", \"\", 3, \"gotoIndex\", \"diffIndex\", \"locale\", \"type\", \"index\", \"disabled\", \"itemRender\", \"active\", \"direction\"], [\"nz-pagination-options\", \"\", 3, \"pageIndexChange\", \"pageSizeChange\", \"total\", \"locale\", \"disabled\", \"nzSize\", \"showSizeChanger\", \"showQuickJumper\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"]],\n      template: function NzPaginationDefaultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationDefaultComponent_ng_template_0_Template, 5, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationDefaultComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination-default',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #containerTemplate>\n      <ul>\n        @if (showTotal) {\n          <li class=\"ant-pagination-total-text\">\n            <ng-template\n              [ngTemplateOutlet]=\"showTotal\"\n              [ngTemplateOutletContext]=\"{ $implicit: total, range: ranges }\"\n            />\n          </li>\n        }\n\n        @for (page of listOfPageItem; track trackByPageItem) {\n          <li\n            nz-pagination-item\n            [locale]=\"locale\"\n            [type]=\"page.type\"\n            [index]=\"page.index\"\n            [disabled]=\"!!page.disabled\"\n            [itemRender]=\"itemRender\"\n            [active]=\"pageIndex === page.index\"\n            (gotoIndex)=\"jumpPage($event)\"\n            (diffIndex)=\"jumpDiff($event)\"\n            [direction]=\"dir\"\n          ></li>\n        }\n\n        @if (showQuickJumper || showSizeChanger) {\n          <li\n            nz-pagination-options\n            [total]=\"total\"\n            [locale]=\"locale\"\n            [disabled]=\"disabled\"\n            [nzSize]=\"nzSize\"\n            [showSizeChanger]=\"showSizeChanger\"\n            [showQuickJumper]=\"showQuickJumper\"\n            [pageIndex]=\"pageIndex\"\n            [pageSize]=\"pageSize\"\n            [pageSizeOptions]=\"pageSizeOptions\"\n            (pageIndexChange)=\"onPageIndexChange($event)\"\n            (pageSizeChange)=\"onPageSizeChange($event)\"\n          ></li>\n        }\n      </ul>\n    </ng-template>\n  `,\n      imports: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    template: [{\n      type: ViewChild,\n      args: ['containerTemplate', {\n        static: true\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    showTotal: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    showSizeChanger: [{\n      type: Input\n    }],\n    showQuickJumper: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }],\n    pageSizeChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzPaginationSimpleComponent {\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this.itemRender = null;\n    this.disabled = false;\n    this.total = 0;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.pageIndexChange = new EventEmitter();\n    this.lastIndex = 0;\n    this.isFirstIndex = false;\n    this.isLastIndex = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.updateRtlStyle();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateRtlStyle();\n  }\n  updateRtlStyle() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  jumpToPageViaInput($event) {\n    const target = $event.target;\n    const index = toNumber(target.value, this.pageIndex);\n    this.onPageIndexChange(index);\n    target.value = `${this.pageIndex}`;\n  }\n  prePage() {\n    this.onPageIndexChange(this.pageIndex - 1);\n  }\n  nextPage() {\n    this.onPageIndexChange(this.pageIndex + 1);\n  }\n  onPageIndexChange(index) {\n    this.pageIndexChange.next(index);\n  }\n  updateBindingValue() {\n    this.lastIndex = Math.ceil(this.total / this.pageSize);\n    this.isFirstIndex = this.pageIndex === 1;\n    this.isLastIndex = this.pageIndex === this.lastIndex;\n  }\n  ngOnChanges(changes) {\n    const {\n      pageIndex,\n      total,\n      pageSize\n    } = changes;\n    if (pageIndex || total || pageSize) {\n      this.updateBindingValue();\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationSimpleComponent_Factory(t) {\n      return new (t || NzPaginationSimpleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationSimpleComponent,\n      selectors: [[\"nz-pagination-simple\"]],\n      viewQuery: function NzPaginationSimpleComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        }\n      },\n      inputs: {\n        itemRender: \"itemRender\",\n        disabled: \"disabled\",\n        locale: \"locale\",\n        total: \"total\",\n        pageIndex: \"pageIndex\",\n        pageSize: \"pageSize\"\n      },\n      outputs: {\n        pageIndexChange: \"pageIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"containerTemplate\", \"\"], [\"nz-pagination-item\", \"\", \"type\", \"prev\", 3, \"click\", \"locale\", \"disabled\", \"direction\", \"itemRender\"], [1, \"ant-pagination-simple-pager\"], [\"size\", \"3\", 3, \"keydown.enter\", \"disabled\", \"value\"], [1, \"ant-pagination-slash\"], [\"nz-pagination-item\", \"\", \"type\", \"next\", 3, \"click\", \"locale\", \"disabled\", \"direction\", \"itemRender\"]],\n      template: function NzPaginationSimpleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationSimpleComponent_ng_template_0_Template, 8, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [NzPaginationItemComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationSimpleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination-simple',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #containerTemplate>\n      <ul>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale.prev_page\"\n          [disabled]=\"isFirstIndex\"\n          [direction]=\"dir\"\n          (click)=\"prePage()\"\n          type=\"prev\"\n          [itemRender]=\"itemRender\"\n        ></li>\n        <li [attr.title]=\"pageIndex + '/' + lastIndex\" class=\"ant-pagination-simple-pager\">\n          <input [disabled]=\"disabled\" [value]=\"pageIndex\" (keydown.enter)=\"jumpToPageViaInput($event)\" size=\"3\" />\n          <span class=\"ant-pagination-slash\">/</span>\n          {{ lastIndex }}\n        </li>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale?.next_page\"\n          [disabled]=\"isLastIndex\"\n          [direction]=\"dir\"\n          (click)=\"nextPage()\"\n          type=\"next\"\n          [itemRender]=\"itemRender\"\n        ></li>\n      </ul>\n    </ng-template>\n  `,\n      imports: [NzPaginationItemComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    template: [{\n      type: ViewChild,\n      args: ['containerTemplate', {\n        static: true\n      }]\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'pagination';\nclass NzPaginationComponent {\n  validatePageIndex(value, lastIndex) {\n    if (value > lastIndex) {\n      return lastIndex;\n    } else if (value < 1) {\n      return 1;\n    } else {\n      return value;\n    }\n  }\n  onPageIndexChange(index) {\n    const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n    const validIndex = this.validatePageIndex(index, lastIndex);\n    if (validIndex !== this.nzPageIndex && !this.nzDisabled) {\n      this.nzPageIndex = validIndex;\n      this.nzPageIndexChange.emit(this.nzPageIndex);\n    }\n  }\n  onPageSizeChange(size) {\n    this.nzPageSize = size;\n    this.nzPageSizeChange.emit(size);\n    const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n    if (this.nzPageIndex > lastIndex) {\n      this.onPageIndexChange(lastIndex);\n    }\n  }\n  onTotalChange(total) {\n    const lastIndex = this.getLastIndex(total, this.nzPageSize);\n    if (this.nzPageIndex > lastIndex) {\n      Promise.resolve().then(() => {\n        this.onPageIndexChange(lastIndex);\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  getLastIndex(total, pageSize) {\n    return Math.ceil(total / pageSize);\n  }\n  constructor(i18n, cdr, breakpointService, nzConfigService, directionality) {\n    this.i18n = i18n;\n    this.cdr = cdr;\n    this.breakpointService = breakpointService;\n    this.nzConfigService = nzConfigService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzPageSizeChange = new EventEmitter();\n    this.nzPageIndexChange = new EventEmitter();\n    this.nzShowTotal = null;\n    this.nzItemRender = null;\n    this.nzSize = 'default';\n    this.nzPageSizeOptions = [10, 20, 30, 40];\n    this.nzShowSizeChanger = false;\n    this.nzShowQuickJumper = false;\n    this.nzSimple = false;\n    this.nzDisabled = false;\n    this.nzResponsive = false;\n    this.nzHideOnSinglePage = false;\n    this.nzTotal = 0;\n    this.nzPageIndex = 1;\n    this.nzPageSize = 10;\n    this.showPagination = true;\n    this.size = 'default';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.total$ = new ReplaySubject(1);\n  }\n  ngOnInit() {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Pagination');\n      this.cdr.markForCheck();\n    });\n    this.total$.pipe(takeUntil(this.destroy$)).subscribe(total => {\n      this.onTotalChange(total);\n    });\n    this.breakpointService.subscribe(gridResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe(bp => {\n      if (this.nzResponsive) {\n        this.size = bp === NzBreakpointEnum.xs ? 'small' : 'default';\n        this.cdr.markForCheck();\n      }\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzHideOnSinglePage,\n      nzTotal,\n      nzPageSize,\n      nzSize\n    } = changes;\n    if (nzTotal) {\n      this.total$.next(this.nzTotal);\n    }\n    if (nzHideOnSinglePage || nzTotal || nzPageSize) {\n      this.showPagination = this.nzHideOnSinglePage && this.nzTotal > this.nzPageSize || this.nzTotal > 0 && !this.nzHideOnSinglePage;\n    }\n    if (nzSize) {\n      this.size = nzSize.currentValue;\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationComponent_Factory(t) {\n      return new (t || NzPaginationComponent)(i0.ɵɵdirectiveInject(i1$3.NzI18nService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.NzBreakpointService), i0.ɵɵdirectiveInject(i3.NzConfigService), i0.ɵɵdirectiveInject(i1$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationComponent,\n      selectors: [[\"nz-pagination\"]],\n      hostAttrs: [1, \"ant-pagination\"],\n      hostVars: 8,\n      hostBindings: function NzPaginationComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-pagination-simple\", ctx.nzSimple)(\"ant-pagination-disabled\", ctx.nzDisabled)(\"ant-pagination-mini\", !ctx.nzSimple && ctx.size === \"small\")(\"ant-pagination-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzShowTotal: \"nzShowTotal\",\n        nzItemRender: \"nzItemRender\",\n        nzSize: \"nzSize\",\n        nzPageSizeOptions: \"nzPageSizeOptions\",\n        nzShowSizeChanger: \"nzShowSizeChanger\",\n        nzShowQuickJumper: \"nzShowQuickJumper\",\n        nzSimple: \"nzSimple\",\n        nzDisabled: \"nzDisabled\",\n        nzResponsive: \"nzResponsive\",\n        nzHideOnSinglePage: \"nzHideOnSinglePage\",\n        nzTotal: \"nzTotal\",\n        nzPageIndex: \"nzPageIndex\",\n        nzPageSize: \"nzPageSize\"\n      },\n      outputs: {\n        nzPageSizeChange: \"nzPageSizeChange\",\n        nzPageIndexChange: \"nzPageIndexChange\"\n      },\n      exportAs: [\"nzPagination\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 18,\n      consts: [[\"simplePagination\", \"\"], [\"defaultPagination\", \"\"], [3, \"pageIndexChange\", \"disabled\", \"itemRender\", \"locale\", \"pageSize\", \"total\", \"pageIndex\"], [3, \"pageIndexChange\", \"pageSizeChange\", \"nzSize\", \"itemRender\", \"showTotal\", \"disabled\", \"locale\", \"showSizeChanger\", \"showQuickJumper\", \"total\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzPaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Template, 2, 1);\n          i0.ɵɵelementStart(1, \"nz-pagination-simple\", 2, 0);\n          i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationComponent_Template_nz_pagination_simple_pageIndexChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageIndexChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nz-pagination-default\", 3, 1);\n          i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationComponent_Template_nz_pagination_default_pageIndexChange_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageIndexChange($event));\n          })(\"pageSizeChange\", function NzPaginationComponent_Template_nz_pagination_default_pageSizeChange_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageSizeChange($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.showPagination ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"itemRender\", ctx.nzItemRender)(\"locale\", ctx.locale)(\"pageSize\", ctx.nzPageSize)(\"total\", ctx.nzTotal)(\"pageIndex\", ctx.nzPageIndex);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzSize\", ctx.size)(\"itemRender\", ctx.nzItemRender)(\"showTotal\", ctx.nzShowTotal)(\"disabled\", ctx.nzDisabled)(\"locale\", ctx.locale)(\"showSizeChanger\", ctx.nzShowSizeChanger)(\"showQuickJumper\", ctx.nzShowQuickJumper)(\"total\", ctx.nzTotal)(\"pageIndex\", ctx.nzPageIndex)(\"pageSize\", ctx.nzPageSize)(\"pageSizeOptions\", ctx.nzPageSizeOptions);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], NzPaginationComponent.prototype, \"nzSize\", void 0);\n__decorate([WithConfig()], NzPaginationComponent.prototype, \"nzPageSizeOptions\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzPaginationComponent.prototype, \"nzShowSizeChanger\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzPaginationComponent.prototype, \"nzShowQuickJumper\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzPaginationComponent.prototype, \"nzSimple\", void 0);\n__decorate([InputBoolean()], NzPaginationComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzPaginationComponent.prototype, \"nzResponsive\", void 0);\n__decorate([InputBoolean()], NzPaginationComponent.prototype, \"nzHideOnSinglePage\", void 0);\n__decorate([InputNumber()], NzPaginationComponent.prototype, \"nzTotal\", void 0);\n__decorate([InputNumber()], NzPaginationComponent.prototype, \"nzPageIndex\", void 0);\n__decorate([InputNumber()], NzPaginationComponent.prototype, \"nzPageSize\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination',\n      exportAs: 'nzPagination',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (showPagination) {\n      @if (nzSimple) {\n        <ng-template [ngTemplateOutlet]=\"simplePagination.template\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"defaultPagination.template\" />\n      }\n    }\n\n    <nz-pagination-simple\n      #simplePagination\n      [disabled]=\"nzDisabled\"\n      [itemRender]=\"nzItemRender\"\n      [locale]=\"locale\"\n      [pageSize]=\"nzPageSize\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n    ></nz-pagination-simple>\n    <nz-pagination-default\n      #defaultPagination\n      [nzSize]=\"size\"\n      [itemRender]=\"nzItemRender\"\n      [showTotal]=\"nzShowTotal\"\n      [disabled]=\"nzDisabled\"\n      [locale]=\"locale\"\n      [showSizeChanger]=\"nzShowSizeChanger\"\n      [showQuickJumper]=\"nzShowQuickJumper\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      [pageSize]=\"nzPageSize\"\n      [pageSizeOptions]=\"nzPageSizeOptions\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n      (pageSizeChange)=\"onPageSizeChange($event)\"\n    ></nz-pagination-default>\n  `,\n      host: {\n        class: 'ant-pagination',\n        '[class.ant-pagination-simple]': 'nzSimple',\n        '[class.ant-pagination-disabled]': 'nzDisabled',\n        '[class.ant-pagination-mini]': `!nzSimple && size === 'small'`,\n        '[class.ant-pagination-rtl]': `dir === 'rtl'`\n      },\n      imports: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$3.NzI18nService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.NzBreakpointService\n  }, {\n    type: i3.NzConfigService\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzPageSizeChange: [{\n      type: Output\n    }],\n    nzPageIndexChange: [{\n      type: Output\n    }],\n    nzShowTotal: [{\n      type: Input\n    }],\n    nzItemRender: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzPageSizeOptions: [{\n      type: Input\n    }],\n    nzShowSizeChanger: [{\n      type: Input\n    }],\n    nzShowQuickJumper: [{\n      type: Input\n    }],\n    nzSimple: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzResponsive: [{\n      type: Input\n    }],\n    nzHideOnSinglePage: [{\n      type: Input\n    }],\n    nzTotal: [{\n      type: Input\n    }],\n    nzPageIndex: [{\n      type: Input\n    }],\n    nzPageSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationModule {\n  static {\n    this.ɵfac = function NzPaginationModule_Factory(t) {\n      return new (t || NzPaginationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzPaginationModule,\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent],\n      exports: [NzPaginationComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent],\n      exports: [NzPaginationComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzPaginationComponent, NzPaginationDefaultComponent, NzPaginationItemComponent, NzPaginationModule, NzPaginationOptionsComponent, NzPaginationSimpleComponent };\n", "import * as i0 from '@angular/core';\nimport { EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, Directive, Injectable, Optional, NgZone, ViewChildren, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { fromEvent, Subject, ReplaySubject, BehaviorSubject, combineLatest, merge, EMPTY, of } from 'rxjs';\nimport { takeUntil, map, distinctUntilChanged, debounceTime, skip, filter, switchMap, startWith, delay, mergeMap } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { InputBoolean, arraysEqual, isNil, measureScrollbar } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownDirective, NzDropDownModule } from 'ng-zorro-antd/dropdown';\nimport { NgTemplateOutlet, NgIf, NgForOf, AsyncPipe, NgStyle } from '@angular/common';\nimport * as i6 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i7 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i5 from 'ng-zorro-antd/checkbox';\nimport { NzCheckboxModule } from 'ng-zorro-antd/checkbox';\nimport * as i2$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzRadioComponent } from 'ng-zorro-antd/radio';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i3 from 'ng-zorro-antd/menu';\nimport * as i8 from 'ng-zorro-antd/core/transition-patch';\nimport * as i9 from 'ng-zorro-antd/core/wave';\nimport * as i3$1 from '@angular/cdk/scrolling';\nimport { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';\nimport * as i2$2 from 'ng-zorro-antd/empty';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport * as i1$2 from 'ng-zorro-antd/cdk/resize-observer';\nimport * as i1$3 from '@angular/cdk/platform';\nimport * as i6$1 from 'ng-zorro-antd/pagination';\nimport { NzPaginationModule } from 'ng-zorro-antd/pagination';\nimport { NzSpinComponent } from 'ng-zorro-antd/spin';\nimport * as i1$4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i5$1 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzTableFilterComponent_ng_template_1_Template(rf, ctx) {}\nfunction NzTableFilterComponent_ng_container_2_li_7_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableFilterComponent_ng_container_2_li_7_label_1_Template_label_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const f_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const f_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", f_r4.checked);\n  }\n}\nfunction NzTableFilterComponent_ng_container_2_li_7_label_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableFilterComponent_ng_container_2_li_7_label_2_Template_label_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const f_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const f_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", f_r4.checked);\n  }\n}\nfunction NzTableFilterComponent_ng_container_2_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 12);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_ng_container_2_li_7_Template_li_click_0_listener() {\n      const f_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵtemplate(1, NzTableFilterComponent_ng_container_2_li_7_label_1_Template, 1, 1, \"label\", 13)(2, NzTableFilterComponent_ng_container_2_li_7_label_2_Template, 1, 1, \"label\", 14);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const f_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzSelected\", f_r4.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.filterMultiple);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filterMultiple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(f_r4.text);\n  }\n}\nfunction NzTableFilterComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"nz-filter-trigger\", 4);\n    i0.ɵɵlistener(\"nzVisibleChange\", function NzTableFilterComponent_ng_container_2_Template_nz_filter_trigger_nzVisibleChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onVisibleChange($event));\n    });\n    i0.ɵɵelement(2, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nz-dropdown-menu\", null, 0)(5, \"div\", 6)(6, \"ul\", 7);\n    i0.ɵɵtemplate(7, NzTableFilterComponent_ng_container_2_li_7_Template, 5, 4, \"li\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_ng_container_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.reset());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_ng_container_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.confirm());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const filterMenu_r7 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzVisible\", ctx_r1.isVisible)(\"nzActive\", ctx_r1.isChecked)(\"nzDropdownMenu\", filterMenu_r7);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listOfParsedFilter)(\"ngForTrackBy\", ctx_r1.trackByValue);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isChecked);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.filterReset, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.locale.filterConfirm);\n  }\n}\nfunction NzTableSelectionComponent_label_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 3);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableSelectionComponent_label_0_Template_label_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-table-selection-select-all-custom\", ctx_r1.showRowSelection);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.checked)(\"nzDisabled\", ctx_r1.disabled)(\"nzIndeterminate\", ctx_r1.indeterminate);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.label);\n  }\n}\nfunction NzTableSelectionComponent_div_1_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵlistener(\"click\", function NzTableSelectionComponent_div_1_li_6_Template_li_click_0_listener() {\n      const selection_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      return i0.ɵɵresetView(selection_r4.onSelect());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const selection_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", selection_r4.text, \" \");\n  }\n}\nfunction NzTableSelectionComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"span\", 5);\n    i0.ɵɵelement(2, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nz-dropdown-menu\", null, 0)(5, \"ul\", 7);\n    i0.ɵɵtemplate(6, NzTableSelectionComponent_div_1_li_6_Template, 2, 1, \"li\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const selectionMenu_r5 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzDropdownMenu\", selectionMenu_r5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listOfSelections);\n  }\n}\nfunction NzTableSortersComponent_ng_template_1_Template(rf, ctx) {}\nfunction NzTableSortersComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.sortOrder === \"ascend\");\n  }\n}\nfunction NzTableSortersComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.sortOrder === \"descend\");\n  }\n}\nconst _c1 = [\"nzChecked\", \"\"];\nfunction NzTdAddOnComponent_ng_container_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"expandChange\", function NzTdAddOnComponent_ng_container_0_ng_template_2_Template_button_expandChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onExpandChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"expand\", ctx_r1.nzExpand)(\"spaceMode\", !ctx_r1.nzShowExpand);\n  }\n}\nfunction NzTdAddOnComponent_ng_container_0_ng_container_4_ng_template_1_Template(rf, ctx) {}\nfunction NzTdAddOnComponent_ng_container_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTdAddOnComponent_ng_container_0_ng_container_4_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzExpandIcon);\n  }\n}\nfunction NzTdAddOnComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nz-row-indent\", 3);\n    i0.ɵɵtemplate(2, NzTdAddOnComponent_ng_container_0_ng_template_2_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzTdAddOnComponent_ng_container_0_ng_container_4_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const rowExpand_r3 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"indentSize\", ctx_r1.nzIndentSize);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nzExpandIcon)(\"ngIfElse\", rowExpand_r3);\n  }\n}\nfunction NzTdAddOnComponent_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 7);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTdAddOnComponent_label_1_Template_label_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", ctx_r1.nzDisabled)(\"ngModel\", ctx_r1.nzChecked)(\"nzIndeterminate\", ctx_r1.nzIndeterminate);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.nzLabel);\n  }\n}\nconst _c2 = [\"nzColumnKey\", \"\"];\nconst _c3 = [[[\"\", \"nz-th-extra\", \"\"]], [[\"nz-filter-trigger\"]], \"*\"];\nconst _c4 = [\"[nz-th-extra]\", \"nz-filter-trigger\", \"*\"];\nfunction NzThAddOnComponent_nz_table_filter_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-table-filter\", 5);\n    i0.ɵɵlistener(\"filterChange\", function NzThAddOnComponent_nz_table_filter_0_Template_nz_table_filter_filterChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterValueChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const notFilterTemplate_r3 = i0.ɵɵreference(2);\n    const extraTemplate_r4 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"contentTemplate\", notFilterTemplate_r3)(\"extraTemplate\", extraTemplate_r4)(\"customFilter\", ctx_r1.nzCustomFilter)(\"filterMultiple\", ctx_r1.nzFilterMultiple)(\"listOfFilter\", ctx_r1.nzFilters);\n  }\n}\nfunction NzThAddOnComponent_ng_template_1_ng_template_0_Template(rf, ctx) {}\nfunction NzThAddOnComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzThAddOnComponent_ng_template_1_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const sortTemplate_r5 = i0.ɵɵreference(6);\n    const contentTemplate_r6 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzShowSort ? sortTemplate_r5 : contentTemplate_r6);\n  }\n}\nfunction NzThAddOnComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵprojection(1, 1);\n  }\n}\nfunction NzThAddOnComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-sorters\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const contentTemplate_r6 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"sortOrder\", ctx_r1.sortOrder)(\"sortDirections\", ctx_r1.sortDirections)(\"contentTemplate\", contentTemplate_r6);\n  }\n}\nfunction NzThAddOnComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nconst _c5 = [\"nzSelections\", \"\"];\nconst _c6 = [\"nz-table-content\", \"\"];\nfunction NzTableContentComponent_col_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"col\");\n  }\n  if (rf & 2) {\n    const width_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"width\", width_r1)(\"min-width\", width_r1);\n  }\n}\nfunction NzTableContentComponent_thead_1_ng_template_1_Template(rf, ctx) {}\nfunction NzTableContentComponent_thead_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 3);\n    i0.ɵɵtemplate(1, NzTableContentComponent_thead_1_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.theadTemplate);\n  }\n}\nfunction NzTableContentComponent_ng_template_2_Template(rf, ctx) {}\nconst _c7 = [\"tdElement\"];\nconst _c8 = [\"nz-table-fixed-row\", \"\"];\nfunction NzTableFixedRowComponent_div_2_ng_template_2_Template(rf, ctx) {}\nfunction NzTableFixedRowComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵtemplate(2, NzTableFixedRowComponent_div_2_ng_template_2_Template, 0, 0, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const contentTemplate_r2 = i0.ɵɵreference(5);\n    i0.ɵɵstyleProp(\"width\", i0.ɵɵpipeBind1(1, 3, ctx_r0.hostWidth$), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r2);\n  }\n}\nfunction NzTableFixedRowComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c9 = [\"nz-table-measure-row\", \"\"];\nfunction NzTrMeasureComponent_td_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 2, 0);\n  }\n}\nfunction NzTbodyComponent_ng_container_0_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 3);\n    i0.ɵɵlistener(\"listOfAutoWidth\", function NzTbodyComponent_ng_container_0_tr_1_Template_tr_listOfAutoWidth_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onListOfAutoWidthChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const listOfMeasureColumn_r3 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵproperty(\"listOfMeasureColumn\", listOfMeasureColumn_r3);\n  }\n}\nfunction NzTbodyComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTbodyComponent_ng_container_0_tr_1_Template, 1, 1, \"tr\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const listOfMeasureColumn_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInsideTable && listOfMeasureColumn_r3.length);\n  }\n}\nfunction NzTbodyComponent_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 4);\n    i0.ɵɵelement(1, \"nz-embed-empty\", 5);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"specificContent\", i0.ɵɵpipeBind1(2, 1, ctx_r1.noResult$));\n  }\n}\nconst _c10 = [\"tableHeaderElement\"];\nconst _c11 = [\"tableBodyElement\"];\nconst _c12 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction NzTableInnerScrollComponent_ng_container_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8, 1);\n    i0.ɵɵelement(2, \"table\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.bodyStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"contentTemplate\", ctx_r0.contentTemplate);\n  }\n}\nfunction NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_ng_template_1_Template(rf, ctx) {}\nfunction NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_ng_template_1_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.virtualTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c12, item_r2, i_r3));\n  }\n}\nfunction NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"cdk-virtual-scroll-viewport\", 10, 1)(2, \"table\", 11)(3, \"tbody\");\n    i0.ɵɵtemplate(4, NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_Template, 2, 5, \"ng-container\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r0.data.length ? ctx_r0.scrollY : ctx_r0.noDateVirtualHeight);\n    i0.ɵɵproperty(\"itemSize\", ctx_r0.virtualItemSize)(\"maxBufferPx\", ctx_r0.virtualMaxBufferPx)(\"minBufferPx\", ctx_r0.virtualMinBufferPx);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkVirtualForOf\", ctx_r0.data)(\"cdkVirtualForTrackBy\", ctx_r0.virtualForTrackBy);\n  }\n}\nfunction NzTableInnerScrollComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4, 0);\n    i0.ɵɵelement(3, \"table\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, NzTableInnerScrollComponent_ng_container_0_div_4_Template, 3, 4, \"div\", 6)(5, NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_Template, 5, 9, \"cdk-virtual-scroll-viewport\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.headerStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"theadTemplate\", ctx_r0.theadTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.virtualTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.virtualTemplate);\n  }\n}\nfunction NzTableInnerScrollComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14, 1);\n    i0.ɵɵelement(2, \"table\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.bodyStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"theadTemplate\", ctx_r0.theadTemplate)(\"contentTemplate\", ctx_r0.contentTemplate);\n  }\n}\nfunction NzTableTitleFooterComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.title);\n  }\n}\nfunction NzTableTitleFooterComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.footer);\n  }\n}\nfunction NzTableComponent_ng_container_1_ng_template_1_Template(rf, ctx) {}\nfunction NzTableComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableComponent_ng_container_1_ng_template_1_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const paginationTemplate_r1 = i0.ɵɵreference(11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paginationTemplate_r1);\n  }\n}\nfunction NzTableComponent_nz_table_title_footer_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-title-footer\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", ctx_r1.nzTitle);\n  }\n}\nfunction NzTableComponent_nz_table_inner_scroll_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-inner-scroll\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const tableMainElement_r3 = i0.ɵɵreference(3);\n    const contentTemplate_r4 = i0.ɵɵreference(13);\n    i0.ɵɵproperty(\"data\", ctx_r1.data)(\"scrollX\", ctx_r1.scrollX)(\"scrollY\", ctx_r1.scrollY)(\"contentTemplate\", contentTemplate_r4)(\"listOfColWidth\", ctx_r1.listOfAutoColWidth)(\"theadTemplate\", ctx_r1.theadTemplate)(\"verticalScrollBarWidth\", ctx_r1.verticalScrollBarWidth)(\"virtualTemplate\", ctx_r1.nzVirtualScrollDirective ? ctx_r1.nzVirtualScrollDirective.templateRef : null)(\"virtualItemSize\", ctx_r1.nzVirtualItemSize)(\"virtualMaxBufferPx\", ctx_r1.nzVirtualMaxBufferPx)(\"virtualMinBufferPx\", ctx_r1.nzVirtualMinBufferPx)(\"tableMainElement\", tableMainElement_r3)(\"virtualForTrackBy\", ctx_r1.nzVirtualForTrackBy);\n  }\n}\nfunction NzTableComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-inner-default\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const contentTemplate_r4 = i0.ɵɵreference(13);\n    i0.ɵɵproperty(\"tableLayout\", ctx_r1.nzTableLayout)(\"listOfColWidth\", ctx_r1.listOfManualColWidth)(\"theadTemplate\", ctx_r1.theadTemplate)(\"contentTemplate\", contentTemplate_r4);\n  }\n}\nfunction NzTableComponent_nz_table_title_footer_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-title-footer\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"footer\", ctx_r1.nzFooter);\n  }\n}\nfunction NzTableComponent_ng_container_9_ng_template_1_Template(rf, ctx) {}\nfunction NzTableComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableComponent_ng_container_9_ng_template_1_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const paginationTemplate_r1 = i0.ɵɵreference(11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paginationTemplate_r1);\n  }\n}\nfunction NzTableComponent_ng_template_10_nz_pagination_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-pagination\", 16);\n    i0.ɵɵlistener(\"nzPageSizeChange\", function NzTableComponent_ng_template_10_nz_pagination_0_Template_nz_pagination_nzPageSizeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange($event));\n    })(\"nzPageIndexChange\", function NzTableComponent_ng_template_10_nz_pagination_0_Template_nz_pagination_nzPageIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPageIndexChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"hidden\", !ctx_r1.showPagination)(\"nzShowSizeChanger\", ctx_r1.nzShowSizeChanger)(\"nzPageSizeOptions\", ctx_r1.nzPageSizeOptions)(\"nzItemRender\", ctx_r1.nzItemRender)(\"nzShowQuickJumper\", ctx_r1.nzShowQuickJumper)(\"nzHideOnSinglePage\", ctx_r1.nzHideOnSinglePage)(\"nzShowTotal\", ctx_r1.nzShowTotal)(\"nzSize\", ctx_r1.nzPaginationType === \"small\" ? \"small\" : ctx_r1.nzSize === \"default\" ? \"default\" : \"small\")(\"nzPageSize\", ctx_r1.nzPageSize)(\"nzTotal\", ctx_r1.nzTotal)(\"nzSimple\", ctx_r1.nzSimple)(\"nzPageIndex\", ctx_r1.nzPageIndex);\n  }\n}\nfunction NzTableComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTableComponent_ng_template_10_nz_pagination_0_Template, 1, 12, \"nz-pagination\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nzShowPagination && ctx_r1.data.length);\n  }\n}\nfunction NzTableComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c13 = [\"contentTemplate\"];\nfunction NzTheadComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzTheadComponent_ng_container_2_ng_template_1_Template(rf, ctx) {}\nfunction NzTheadComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTheadComponent_ng_container_2_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const contentTemplate_r1 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r1);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME$1 = 'filterTrigger';\nclass NzFilterTriggerComponent {\n  onVisibleChange(visible) {\n    this.nzVisible = visible;\n    this.nzVisibleChange.next(visible);\n  }\n  hide() {\n    this.nzVisible = false;\n    this.cdr.markForCheck();\n  }\n  show() {\n    this.nzVisible = true;\n    this.cdr.markForCheck();\n  }\n  constructor(nzConfigService, ngZone, cdr, destroy$) {\n    this.nzConfigService = nzConfigService;\n    this.ngZone = ngZone;\n    this.cdr = cdr;\n    this.destroy$ = destroy$;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME$1;\n    this.nzActive = false;\n    this.nzVisible = false;\n    this.nzBackdrop = false;\n    this.nzVisibleChange = new EventEmitter();\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.nzDropdown.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.stopPropagation();\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NzFilterTriggerComponent_Factory(t) {\n      return new (t || NzFilterTriggerComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFilterTriggerComponent,\n      selectors: [[\"nz-filter-trigger\"]],\n      viewQuery: function NzFilterTriggerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzDropDownDirective, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzDropdown = _t.first);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzDropdownMenu: \"nzDropdownMenu\",\n        nzVisible: \"nzVisible\",\n        nzBackdrop: \"nzBackdrop\"\n      },\n      outputs: {\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzFilterTrigger\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 8,\n      consts: [[\"nz-dropdown\", \"\", \"nzTrigger\", \"click\", \"nzPlacement\", \"bottomRight\", 1, \"ant-table-filter-trigger\", 3, \"nzVisibleChange\", \"nzBackdrop\", \"nzClickHide\", \"nzDropdownMenu\", \"nzVisible\"]],\n      template: function NzFilterTriggerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵlistener(\"nzVisibleChange\", function NzFilterTriggerComponent_Template_span_nzVisibleChange_0_listener($event) {\n            return ctx.onVisibleChange($event);\n          });\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.nzActive)(\"ant-table-filter-open\", ctx.nzVisible);\n          i0.ɵɵproperty(\"nzBackdrop\", ctx.nzBackdrop)(\"nzClickHide\", false)(\"nzDropdownMenu\", ctx.nzDropdownMenu)(\"nzVisible\", ctx.nzVisible);\n        }\n      },\n      dependencies: [NzDropDownModule, i4.NzDropDownDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzFilterTriggerComponent.prototype, \"nzBackdrop\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFilterTriggerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-filter-trigger',\n      exportAs: `nzFilterTrigger`,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      nz-dropdown\n      class=\"ant-table-filter-trigger\"\n      nzTrigger=\"click\"\n      nzPlacement=\"bottomRight\"\n      [nzBackdrop]=\"nzBackdrop\"\n      [nzClickHide]=\"false\"\n      [nzDropdownMenu]=\"nzDropdownMenu\"\n      [class.active]=\"nzActive\"\n      [class.ant-table-filter-open]=\"nzVisible\"\n      [nzVisible]=\"nzVisible\"\n      (nzVisibleChange)=\"onVisibleChange($event)\"\n    >\n      <ng-content></ng-content>\n    </span>\n  `,\n      providers: [NzDestroyService],\n      imports: [NzDropDownModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzActive: [{\n      type: Input\n    }],\n    nzDropdownMenu: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }],\n    nzDropdown: [{\n      type: ViewChild,\n      args: [NzDropDownDirective, {\n        static: true,\n        read: ElementRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFilterComponent {\n  trackByValue(_, item) {\n    return item.value;\n  }\n  check(filter) {\n    if (this.filterMultiple) {\n      this.listOfParsedFilter = this.listOfParsedFilter.map(item => {\n        if (item === filter) {\n          return {\n            ...item,\n            checked: !filter.checked\n          };\n        } else {\n          return item;\n        }\n      });\n      filter.checked = !filter.checked;\n    } else {\n      this.listOfParsedFilter = this.listOfParsedFilter.map(item => ({\n        ...item,\n        checked: item === filter\n      }));\n    }\n    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n  }\n  confirm() {\n    this.isVisible = false;\n    this.emitFilterData();\n  }\n  reset() {\n    this.isVisible = false;\n    this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter, true);\n    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    this.emitFilterData();\n  }\n  onVisibleChange(value) {\n    this.isVisible = value;\n    if (!value) {\n      this.emitFilterData();\n    } else {\n      this.listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n    }\n  }\n  emitFilterData() {\n    const listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n    if (!arraysEqual(this.listOfChecked, listOfChecked)) {\n      if (this.filterMultiple) {\n        this.filterChange.emit(listOfChecked);\n      } else {\n        this.filterChange.emit(listOfChecked.length > 0 ? listOfChecked[0] : null);\n      }\n    }\n  }\n  parseListOfFilter(listOfFilter, reset) {\n    return listOfFilter.map(item => {\n      const checked = reset ? false : !!item.byDefault;\n      return {\n        text: item.text,\n        value: item.value,\n        checked\n      };\n    });\n  }\n  getCheckedStatus(listOfParsedFilter) {\n    return listOfParsedFilter.some(item => item.checked);\n  }\n  constructor(cdr, i18n) {\n    this.cdr = cdr;\n    this.i18n = i18n;\n    this.contentTemplate = null;\n    this.customFilter = false;\n    this.extraTemplate = null;\n    this.filterMultiple = true;\n    this.listOfFilter = [];\n    this.filterChange = new EventEmitter();\n    this.destroy$ = new Subject();\n    this.isChecked = false;\n    this.isVisible = false;\n    this.listOfParsedFilter = [];\n    this.listOfChecked = [];\n  }\n  ngOnInit() {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Table');\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      listOfFilter\n    } = changes;\n    if (listOfFilter && this.listOfFilter && this.listOfFilter.length) {\n      this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter);\n      this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableFilterComponent_Factory(t) {\n      return new (t || NzTableFilterComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.NzI18nService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableFilterComponent,\n      selectors: [[\"nz-table-filter\"]],\n      hostAttrs: [1, \"ant-table-filter-column\"],\n      inputs: {\n        contentTemplate: \"contentTemplate\",\n        customFilter: \"customFilter\",\n        extraTemplate: \"extraTemplate\",\n        filterMultiple: \"filterMultiple\",\n        listOfFilter: \"listOfFilter\"\n      },\n      outputs: {\n        filterChange: \"filterChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"filterMenu\", \"nzDropdownMenu\"], [1, \"ant-table-column-title\"], [3, \"ngTemplateOutlet\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"nzVisibleChange\", \"nzVisible\", \"nzActive\", \"nzDropdownMenu\"], [\"nz-icon\", \"\", \"nzType\", \"filter\", \"nzTheme\", \"fill\"], [1, \"ant-table-filter-dropdown\"], [\"nz-menu\", \"\"], [\"nz-menu-item\", \"\", 3, \"nzSelected\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"ant-table-filter-dropdown-btns\"], [\"nz-button\", \"\", \"nzType\", \"link\", \"nzSize\", \"small\", 3, \"click\", \"disabled\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nzSize\", \"small\", 3, \"click\"], [\"nz-menu-item\", \"\", 3, \"click\", \"nzSelected\"], [\"nz-radio\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nz-checkbox\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nz-radio\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function NzTableFilterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 1);\n          i0.ɵɵtemplate(1, NzTableFilterComponent_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, NzTableFilterComponent_ng_container_2_Template, 13, 8, \"ng-container\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.customFilter)(\"ngIfElse\", ctx.extraTemplate);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NgIf, NzFilterTriggerComponent, NzIconModule, i2$1.NzIconDirective, NzDropDownModule, i3.NzMenuDirective, i3.NzMenuItemComponent, i4.NzDropdownMenuComponent, NgForOf, NzRadioComponent, NzCheckboxModule, i5.NzCheckboxComponent, FormsModule, i6.NgControlStatus, i6.NgModel, NzButtonModule, i7.NzButtonComponent, i8.ɵNzTransitionPatchDirective, i9.NzWaveDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableFilterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-filter',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span class=\"ant-table-column-title\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </span>\n    <ng-container *ngIf=\"!customFilter; else extraTemplate\">\n      <nz-filter-trigger\n        [nzVisible]=\"isVisible\"\n        [nzActive]=\"isChecked\"\n        [nzDropdownMenu]=\"filterMenu\"\n        (nzVisibleChange)=\"onVisibleChange($event)\"\n      >\n        <span nz-icon nzType=\"filter\" nzTheme=\"fill\"></span>\n      </nz-filter-trigger>\n      <nz-dropdown-menu #filterMenu=\"nzDropdownMenu\">\n        <div class=\"ant-table-filter-dropdown\">\n          <ul nz-menu>\n            <li\n              nz-menu-item\n              [nzSelected]=\"f.checked\"\n              *ngFor=\"let f of listOfParsedFilter; trackBy: trackByValue\"\n              (click)=\"check(f)\"\n            >\n              <label nz-radio *ngIf=\"!filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <label nz-checkbox *ngIf=\"filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <span>{{ f.text }}</span>\n            </li>\n          </ul>\n          <div class=\"ant-table-filter-dropdown-btns\">\n            <button nz-button nzType=\"link\" nzSize=\"small\" (click)=\"reset()\" [disabled]=\"!isChecked\">\n              {{ locale.filterReset }}\n            </button>\n            <button nz-button nzType=\"primary\" nzSize=\"small\" (click)=\"confirm()\">{{ locale.filterConfirm }}</button>\n          </div>\n        </div>\n      </nz-dropdown-menu>\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-table-filter-column'\n      },\n      imports: [NgTemplateOutlet, NgIf, NzFilterTriggerComponent, NzIconModule, NzDropDownModule, NgForOf, NzRadioComponent, NzCheckboxModule, FormsModule, NzButtonModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.NzI18nService\n  }], {\n    contentTemplate: [{\n      type: Input\n    }],\n    customFilter: [{\n      type: Input\n    }],\n    extraTemplate: [{\n      type: Input\n    }],\n    filterMultiple: [{\n      type: Input\n    }],\n    listOfFilter: [{\n      type: Input\n    }],\n    filterChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowExpandButtonDirective {\n  constructor() {\n    this.expand = false;\n    this.spaceMode = false;\n    this.expandChange = new EventEmitter();\n  }\n  onHostClick() {\n    if (!this.spaceMode) {\n      this.expand = !this.expand;\n      this.expandChange.next(this.expand);\n    }\n  }\n  static {\n    this.ɵfac = function NzRowExpandButtonDirective_Factory(t) {\n      return new (t || NzRowExpandButtonDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzRowExpandButtonDirective,\n      selectors: [[\"button\", \"nz-row-expand-button\", \"\"]],\n      hostAttrs: [1, \"ant-table-row-expand-icon\"],\n      hostVars: 7,\n      hostBindings: function NzRowExpandButtonDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzRowExpandButtonDirective_click_HostBindingHandler() {\n            return ctx.onHostClick();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", \"button\");\n          i0.ɵɵclassProp(\"ant-table-row-expand-icon-expanded\", !ctx.spaceMode && ctx.expand === true)(\"ant-table-row-expand-icon-collapsed\", !ctx.spaceMode && ctx.expand === false)(\"ant-table-row-expand-icon-spaced\", ctx.spaceMode);\n        }\n      },\n      inputs: {\n        expand: \"expand\",\n        spaceMode: \"spaceMode\"\n      },\n      outputs: {\n        expandChange: \"expandChange\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRowExpandButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'button[nz-row-expand-button]',\n      host: {\n        class: 'ant-table-row-expand-icon',\n        '[type]': `'button'`,\n        '[class.ant-table-row-expand-icon-expanded]': `!spaceMode && expand === true`,\n        '[class.ant-table-row-expand-icon-collapsed]': `!spaceMode && expand === false`,\n        '[class.ant-table-row-expand-icon-spaced]': 'spaceMode',\n        '(click)': 'onHostClick()'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    expand: [{\n      type: Input\n    }],\n    spaceMode: [{\n      type: Input\n    }],\n    expandChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowIndentDirective {\n  constructor() {\n    this.indentSize = 0;\n  }\n  static {\n    this.ɵfac = function NzRowIndentDirective_Factory(t) {\n      return new (t || NzRowIndentDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzRowIndentDirective,\n      selectors: [[\"nz-row-indent\"]],\n      hostAttrs: [1, \"ant-table-row-indent\"],\n      hostVars: 2,\n      hostBindings: function NzRowIndentDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.indentSize, \"px\");\n        }\n      },\n      inputs: {\n        indentSize: \"indentSize\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRowIndentDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-row-indent',\n      host: {\n        class: 'ant-table-row-indent',\n        '[style.padding-left.px]': 'indentSize'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    indentSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSelectionComponent {\n  constructor() {\n    this.listOfSelections = [];\n    this.checked = false;\n    this.disabled = false;\n    this.indeterminate = false;\n    this.label = null;\n    this.showCheckbox = false;\n    this.showRowSelection = false;\n    this.checkedChange = new EventEmitter();\n  }\n  onCheckedChange(checked) {\n    this.checked = checked;\n    this.checkedChange.emit(checked);\n  }\n  static {\n    this.ɵfac = function NzTableSelectionComponent_Factory(t) {\n      return new (t || NzTableSelectionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableSelectionComponent,\n      selectors: [[\"nz-table-selection\"]],\n      hostAttrs: [1, \"ant-table-selection\"],\n      inputs: {\n        listOfSelections: \"listOfSelections\",\n        checked: \"checked\",\n        disabled: \"disabled\",\n        indeterminate: \"indeterminate\",\n        label: \"label\",\n        showCheckbox: \"showCheckbox\",\n        showRowSelection: \"showRowSelection\"\n      },\n      outputs: {\n        checkedChange: \"checkedChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"selectionMenu\", \"nzDropdownMenu\"], [\"nz-checkbox\", \"\", 3, \"ant-table-selection-select-all-custom\", \"ngModel\", \"nzDisabled\", \"nzIndeterminate\", \"ngModelChange\", 4, \"ngIf\"], [\"class\", \"ant-table-selection-extra\", 4, \"ngIf\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"ngModel\", \"nzDisabled\", \"nzIndeterminate\"], [1, \"ant-table-selection-extra\"], [\"nz-dropdown\", \"\", \"nzPlacement\", \"bottomLeft\", 1, \"ant-table-selection-down\", 3, \"nzDropdownMenu\"], [\"nz-icon\", \"\", \"nzType\", \"down\"], [\"nz-menu\", \"\", 1, \"ant-table-selection-menu\"], [\"nz-menu-item\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"nz-menu-item\", \"\", 3, \"click\"]],\n      template: function NzTableSelectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTableSelectionComponent_label_0_Template, 1, 6, \"label\", 1)(1, NzTableSelectionComponent_div_1_Template, 7, 2, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showCheckbox);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showRowSelection);\n        }\n      },\n      dependencies: [NgIf, FormsModule, i6.NgControlStatus, i6.NgModel, NzCheckboxModule, i5.NzCheckboxComponent, NzDropDownModule, i3.NzMenuDirective, i3.NzMenuItemComponent, i4.NzDropDownDirective, i4.NzDropdownMenuComponent, NzIconModule, i2$1.NzIconDirective, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableSelectionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-selection',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <label\n      *ngIf=\"showCheckbox\"\n      nz-checkbox\n      [class.ant-table-selection-select-all-custom]=\"showRowSelection\"\n      [ngModel]=\"checked\"\n      [nzDisabled]=\"disabled\"\n      [nzIndeterminate]=\"indeterminate\"\n      [attr.aria-label]=\"label\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <div class=\"ant-table-selection-extra\" *ngIf=\"showRowSelection\">\n      <span nz-dropdown class=\"ant-table-selection-down\" nzPlacement=\"bottomLeft\" [nzDropdownMenu]=\"selectionMenu\">\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n      <nz-dropdown-menu #selectionMenu=\"nzDropdownMenu\">\n        <ul nz-menu class=\"ant-table-selection-menu\">\n          <li nz-menu-item *ngFor=\"let selection of listOfSelections\" (click)=\"selection.onSelect()\">\n            {{ selection.text }}\n          </li>\n        </ul>\n      </nz-dropdown-menu>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-selection'\n      },\n      imports: [NgIf, FormsModule, NzCheckboxModule, NzDropDownModule, NzIconModule, NgForOf],\n      standalone: true\n    }]\n  }], () => [], {\n    listOfSelections: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    indeterminate: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    showCheckbox: [{\n      type: Input\n    }],\n    showRowSelection: [{\n      type: Input\n    }],\n    checkedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSortersComponent {\n  constructor() {\n    this.sortDirections = ['ascend', 'descend', null];\n    this.sortOrder = null;\n    this.contentTemplate = null;\n    this.isUp = false;\n    this.isDown = false;\n  }\n  ngOnChanges(changes) {\n    const {\n      sortDirections\n    } = changes;\n    if (sortDirections) {\n      this.isUp = this.sortDirections.indexOf('ascend') !== -1;\n      this.isDown = this.sortDirections.indexOf('descend') !== -1;\n    }\n  }\n  static {\n    this.ɵfac = function NzTableSortersComponent_Factory(t) {\n      return new (t || NzTableSortersComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableSortersComponent,\n      selectors: [[\"nz-table-sorters\"]],\n      hostAttrs: [1, \"ant-table-column-sorters\"],\n      inputs: {\n        sortDirections: \"sortDirections\",\n        sortOrder: \"sortOrder\",\n        contentTemplate: \"contentTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 5,\n      consts: [[1, \"ant-table-column-title\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-column-sorter\"], [1, \"ant-table-column-sorter-inner\"], [\"nz-icon\", \"\", \"nzType\", \"caret-up\", \"class\", \"ant-table-column-sorter-up\", 3, \"active\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"caret-down\", \"class\", \"ant-table-column-sorter-down\", 3, \"active\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"caret-up\", 1, \"ant-table-column-sorter-up\"], [\"nz-icon\", \"\", \"nzType\", \"caret-down\", 1, \"ant-table-column-sorter-down\"]],\n      template: function NzTableSortersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵtemplate(1, NzTableSortersComponent_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n          i0.ɵɵtemplate(4, NzTableSortersComponent_span_4_Template, 1, 2, \"span\", 4)(5, NzTableSortersComponent_span_5_Template, 1, 2, \"span\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ant-table-column-sorter-full\", ctx.isDown && ctx.isUp);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUp);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isDown);\n        }\n      },\n      dependencies: [NzIconModule, i2$1.NzIconDirective, NgTemplateOutlet, NgIf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableSortersComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-sorters',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span class=\"ant-table-column-title\"><ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template></span>\n    <span class=\"ant-table-column-sorter\" [class.ant-table-column-sorter-full]=\"isDown && isUp\">\n      <span class=\"ant-table-column-sorter-inner\">\n        <span\n          nz-icon\n          nzType=\"caret-up\"\n          *ngIf=\"isUp\"\n          class=\"ant-table-column-sorter-up\"\n          [class.active]=\"sortOrder === 'ascend'\"\n        ></span>\n        <span\n          nz-icon\n          nzType=\"caret-down\"\n          *ngIf=\"isDown\"\n          class=\"ant-table-column-sorter-down\"\n          [class.active]=\"sortOrder === 'descend'\"\n        ></span>\n      </span>\n    </span>\n  `,\n      host: {\n        class: 'ant-table-column-sorters'\n      },\n      imports: [NzIconModule, NgTemplateOutlet, NgIf],\n      standalone: true\n    }]\n  }], () => [], {\n    sortDirections: [{\n      type: Input\n    }],\n    sortOrder: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellFixedDirective {\n  setAutoLeftWidth(autoLeft) {\n    this.renderer.setStyle(this.elementRef.nativeElement, 'left', autoLeft);\n  }\n  setAutoRightWidth(autoRight) {\n    this.renderer.setStyle(this.elementRef.nativeElement, 'right', autoRight);\n  }\n  setIsFirstRight(isFirstRight) {\n    this.setFixClass(isFirstRight, 'ant-table-cell-fix-right-first');\n  }\n  setIsLastLeft(isLastLeft) {\n    this.setFixClass(isLastLeft, 'ant-table-cell-fix-left-last');\n  }\n  setFixClass(flag, className) {\n    // the setFixClass function may call many times, so remove it first.\n    this.renderer.removeClass(this.elementRef.nativeElement, className);\n    if (flag) {\n      this.renderer.addClass(this.elementRef.nativeElement, className);\n    }\n  }\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.nzRight = false;\n    this.nzLeft = false;\n    this.colspan = null;\n    this.colSpan = null;\n    this.changes$ = new Subject();\n    this.isAutoLeft = false;\n    this.isAutoRight = false;\n    this.isFixedLeft = false;\n    this.isFixedRight = false;\n    this.isFixed = false;\n  }\n  ngOnChanges() {\n    this.setIsFirstRight(false);\n    this.setIsLastLeft(false);\n    this.isAutoLeft = this.nzLeft === '' || this.nzLeft === true;\n    this.isAutoRight = this.nzRight === '' || this.nzRight === true;\n    this.isFixedLeft = this.nzLeft !== false;\n    this.isFixedRight = this.nzRight !== false;\n    this.isFixed = this.isFixedLeft || this.isFixedRight;\n    const validatePx = value => {\n      if (typeof value === 'string' && value !== '') {\n        return value;\n      } else {\n        return null;\n      }\n    };\n    this.setAutoLeftWidth(validatePx(this.nzLeft));\n    this.setAutoRightWidth(validatePx(this.nzRight));\n    this.changes$.next();\n  }\n  static {\n    this.ɵfac = function NzCellFixedDirective_Factory(t) {\n      return new (t || NzCellFixedDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellFixedDirective,\n      selectors: [[\"td\", \"nzRight\", \"\"], [\"th\", \"nzRight\", \"\"], [\"td\", \"nzLeft\", \"\"], [\"th\", \"nzLeft\", \"\"]],\n      hostVars: 6,\n      hostBindings: function NzCellFixedDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"position\", ctx.isFixed ? \"sticky\" : null);\n          i0.ɵɵclassProp(\"ant-table-cell-fix-right\", ctx.isFixedRight)(\"ant-table-cell-fix-left\", ctx.isFixedLeft);\n        }\n      },\n      inputs: {\n        nzRight: \"nzRight\",\n        nzLeft: \"nzLeft\",\n        colspan: \"colspan\",\n        colSpan: \"colSpan\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellFixedDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'td[nzRight],th[nzRight],td[nzLeft],th[nzLeft]',\n      host: {\n        '[class.ant-table-cell-fix-right]': `isFixedRight`,\n        '[class.ant-table-cell-fix-left]': `isFixedLeft`,\n        '[style.position]': `isFixed? 'sticky' : null`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzRight: [{\n      type: Input\n    }],\n    nzLeft: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }],\n    colSpan: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableStyleService {\n  setTheadTemplate(template) {\n    this.theadTemplate$.next(template);\n  }\n  setHasFixLeft(hasFixLeft) {\n    this.hasFixLeft$.next(hasFixLeft);\n  }\n  setHasFixRight(hasFixRight) {\n    this.hasFixRight$.next(hasFixRight);\n  }\n  setTableWidthConfig(widthConfig) {\n    this.tableWidthConfigPx$.next(widthConfig);\n  }\n  setListOfTh(listOfTh) {\n    let columnCount = 0;\n    listOfTh.forEach(th => {\n      columnCount += th.colspan && +th.colspan || th.colSpan && +th.colSpan || 1;\n    });\n    const listOfThPx = listOfTh.map(item => item.nzWidth);\n    this.columnCount$.next(columnCount);\n    this.listOfThWidthConfigPx$.next(listOfThPx);\n  }\n  setListOfMeasureColumn(listOfTh) {\n    const listOfKeys = [];\n    listOfTh.forEach(th => {\n      const length = th.colspan && +th.colspan || th.colSpan && +th.colSpan || 1;\n      for (let i = 0; i < length; i++) {\n        listOfKeys.push(`measure_key_${i}`);\n      }\n    });\n    this.listOfMeasureColumn$.next(listOfKeys);\n  }\n  setListOfAutoWidth(listOfAutoWidth) {\n    this.listOfAutoWidthPx$.next(listOfAutoWidth.map(width => `${width}px`));\n  }\n  setShowEmpty(showEmpty) {\n    this.showEmpty$.next(showEmpty);\n  }\n  setNoResult(noResult) {\n    this.noResult$.next(noResult);\n  }\n  setScroll(scrollX, scrollY) {\n    const enableAutoMeasure = !!(scrollX || scrollY);\n    if (!enableAutoMeasure) {\n      this.setListOfAutoWidth([]);\n    }\n    this.enableAutoMeasure$.next(enableAutoMeasure);\n  }\n  constructor() {\n    this.theadTemplate$ = new ReplaySubject(1);\n    this.hasFixLeft$ = new ReplaySubject(1);\n    this.hasFixRight$ = new ReplaySubject(1);\n    this.hostWidth$ = new ReplaySubject(1);\n    this.columnCount$ = new ReplaySubject(1);\n    this.showEmpty$ = new ReplaySubject(1);\n    this.noResult$ = new ReplaySubject(1);\n    this.listOfThWidthConfigPx$ = new BehaviorSubject([]);\n    this.tableWidthConfigPx$ = new BehaviorSubject([]);\n    this.manualWidthConfigPx$ = combineLatest([this.tableWidthConfigPx$, this.listOfThWidthConfigPx$]).pipe(map(([widthConfig, listOfWidth]) => widthConfig.length ? widthConfig : listOfWidth));\n    this.listOfAutoWidthPx$ = new ReplaySubject(1);\n    this.listOfListOfThWidthPx$ = merge( /** init with manual width **/\n    this.manualWidthConfigPx$, combineLatest([this.listOfAutoWidthPx$, this.manualWidthConfigPx$]).pipe(map(([autoWidth, manualWidth]) => {\n      /** use autoWidth until column length match **/\n      if (autoWidth.length === manualWidth.length) {\n        return autoWidth.map((width, index) => {\n          if (width === '0px') {\n            return manualWidth[index] || null;\n          } else {\n            return manualWidth[index] || width;\n          }\n        });\n      } else {\n        return manualWidth;\n      }\n    })));\n    this.listOfMeasureColumn$ = new ReplaySubject(1);\n    this.listOfListOfThWidth$ = this.listOfAutoWidthPx$.pipe(map(list => list.map(width => parseInt(width, 10))));\n    this.enableAutoMeasure$ = new ReplaySubject(1);\n  }\n  static {\n    this.ɵfac = function NzTableStyleService_Factory(t) {\n      return new (t || NzTableStyleService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzTableStyleService,\n      factory: NzTableStyleService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableStyleService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableCellDirective {\n  constructor(nzTableStyleService) {\n    this.isInsideTable = false;\n    this.isInsideTable = !!nzTableStyleService;\n  }\n  static {\n    this.ɵfac = function NzTableCellDirective_Factory(t) {\n      return new (t || NzTableCellDirective)(i0.ɵɵdirectiveInject(NzTableStyleService, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTableCellDirective,\n      selectors: [[\"th\", 9, \"nz-disable-th\", 3, \"mat-cell\", \"\"], [\"td\", 9, \"nz-disable-td\", 3, \"mat-cell\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzTableCellDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-cell\", ctx.isInsideTable);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableCellDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th:not(.nz-disable-th):not([mat-cell]), td:not(.nz-disable-td):not([mat-cell])',\n      host: {\n        '[class.ant-table-cell]': 'isInsideTable'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableDataService {\n  updatePageSize(size) {\n    this.pageSize$.next(size);\n  }\n  updateFrontPagination(pagination) {\n    this.frontPagination$.next(pagination);\n  }\n  updatePageIndex(index) {\n    this.pageIndex$.next(index);\n  }\n  updateListOfData(list) {\n    this.listOfData$.next(list);\n  }\n  updateListOfCustomColumn(list) {\n    this.listOfCustomColumn$.next(list);\n  }\n  constructor() {\n    this.destroy$ = new Subject();\n    this.pageIndex$ = new BehaviorSubject(1);\n    this.frontPagination$ = new BehaviorSubject(true);\n    this.pageSize$ = new BehaviorSubject(10);\n    this.listOfData$ = new BehaviorSubject([]);\n    this.listOfCustomColumn$ = new BehaviorSubject([]);\n    this.pageIndexDistinct$ = this.pageIndex$.pipe(distinctUntilChanged());\n    this.pageSizeDistinct$ = this.pageSize$.pipe(distinctUntilChanged());\n    this.listOfCalcOperator$ = new BehaviorSubject([]);\n    this.queryParams$ = combineLatest([this.pageIndexDistinct$, this.pageSizeDistinct$, this.listOfCalcOperator$]).pipe(debounceTime(0), skip(1), map(([pageIndex, pageSize, listOfCalc]) => ({\n      pageIndex,\n      pageSize,\n      sort: listOfCalc.filter(item => item.sortFn).map(item => ({\n        key: item.key,\n        value: item.sortOrder\n      })),\n      filter: listOfCalc.filter(item => item.filterFn).map(item => ({\n        key: item.key,\n        value: item.filterValue\n      }))\n    })));\n    this.listOfDataAfterCalc$ = combineLatest([this.listOfData$, this.listOfCalcOperator$]).pipe(map(([listOfData, listOfCalcOperator]) => {\n      let listOfDataAfterCalc = [...listOfData];\n      const listOfFilterOperator = listOfCalcOperator.filter(item => {\n        const {\n          filterValue,\n          filterFn\n        } = item;\n        const isReset = filterValue === null || filterValue === undefined || Array.isArray(filterValue) && filterValue.length === 0;\n        return !isReset && typeof filterFn === 'function';\n      });\n      for (const item of listOfFilterOperator) {\n        const {\n          filterFn,\n          filterValue\n        } = item;\n        listOfDataAfterCalc = listOfDataAfterCalc.filter(data => filterFn(filterValue, data));\n      }\n      const listOfSortOperator = listOfCalcOperator.filter(item => item.sortOrder !== null && typeof item.sortFn === 'function').sort((a, b) => +b.sortPriority - +a.sortPriority);\n      if (listOfCalcOperator.length) {\n        listOfDataAfterCalc.sort((record1, record2) => {\n          for (const item of listOfSortOperator) {\n            const {\n              sortFn,\n              sortOrder\n            } = item;\n            if (sortFn && sortOrder) {\n              const compareResult = sortFn(record1, record2, sortOrder);\n              if (compareResult !== 0) {\n                return sortOrder === 'ascend' ? compareResult : -compareResult;\n              }\n            }\n          }\n          return 0;\n        });\n      }\n      return listOfDataAfterCalc;\n    }));\n    this.listOfFrontEndCurrentPageData$ = combineLatest([this.pageIndexDistinct$, this.pageSizeDistinct$, this.listOfDataAfterCalc$]).pipe(takeUntil(this.destroy$), filter(value => {\n      const [pageIndex, pageSize, listOfData] = value;\n      const maxPageIndex = Math.ceil(listOfData.length / pageSize) || 1;\n      return pageIndex <= maxPageIndex;\n    }), map(([pageIndex, pageSize, listOfData]) => listOfData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)));\n    this.listOfCurrentPageData$ = this.frontPagination$.pipe(switchMap(pagination => pagination ? this.listOfFrontEndCurrentPageData$ : this.listOfDataAfterCalc$));\n    this.total$ = this.frontPagination$.pipe(switchMap(pagination => pagination ? this.listOfDataAfterCalc$ : this.listOfData$), map(list => list.length), distinctUntilChanged());\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableDataService_Factory(t) {\n      return new (t || NzTableDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzTableDataService,\n      factory: NzTableDataService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableDataService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCustomColumnDirective {\n  constructor(el, renderer, nzTableDataService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.nzTableDataService = nzTableDataService;\n    this.nzCellControl = null;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.nzTableDataService.listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(item => {\n      if (item.length) {\n        item.forEach((v, i) => {\n          if (v.value === this.nzCellControl) {\n            if (!v.default) {\n              this.renderer.setStyle(this.el.nativeElement, 'display', 'none');\n            } else {\n              this.renderer.setStyle(this.el.nativeElement, 'display', 'block');\n            }\n            this.renderer.setStyle(this.el.nativeElement, 'order', i);\n            if (!v?.fixWidth) {\n              this.renderer.setStyle(this.el.nativeElement, 'flex', `1 1 ${v.width}px`);\n            } else {\n              this.renderer.setStyle(this.el.nativeElement, 'flex', `1 0 ${v.width}px`);\n            }\n          }\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzCustomColumnDirective_Factory(t) {\n      return new (t || NzCustomColumnDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzTableDataService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCustomColumnDirective,\n      selectors: [[\"td\", \"nzCellControl\", \"\"], [\"th\", \"nzCellControl\", \"\"]],\n      inputs: {\n        nzCellControl: \"nzCellControl\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCustomColumnDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'td[nzCellControl],th[nzCellControl]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: NzTableDataService\n  }], {\n    nzCellControl: [{\n      type: Input\n    }]\n  });\n})();\nclass NzTdAddOnComponent {\n  constructor() {\n    this.nzChecked = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzLabel = null;\n    this.nzIndentSize = 0;\n    this.nzShowExpand = false;\n    this.nzShowCheckbox = false;\n    this.nzExpand = false;\n    this.nzExpandIcon = null;\n    this.nzCheckedChange = new EventEmitter();\n    this.nzExpandChange = new EventEmitter();\n    this.isNzShowExpandChanged = false;\n    this.isNzShowCheckboxChanged = false;\n  }\n  onCheckedChange(checked) {\n    this.nzChecked = checked;\n    this.nzCheckedChange.emit(checked);\n  }\n  onExpandChange(expand) {\n    this.nzExpand = expand;\n    this.nzExpandChange.emit(expand);\n  }\n  ngOnChanges(changes) {\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    const {\n      nzExpand,\n      nzChecked,\n      nzShowExpand,\n      nzShowCheckbox\n    } = changes;\n    if (nzShowExpand) {\n      this.isNzShowExpandChanged = true;\n    }\n    if (nzShowCheckbox) {\n      this.isNzShowCheckboxChanged = true;\n    }\n    if (isFirstChange(nzExpand) && !this.isNzShowExpandChanged) {\n      this.nzShowExpand = true;\n    }\n    if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n      this.nzShowCheckbox = true;\n    }\n  }\n  static {\n    this.ɵfac = function NzTdAddOnComponent_Factory(t) {\n      return new (t || NzTdAddOnComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTdAddOnComponent,\n      selectors: [[\"td\", \"nzChecked\", \"\"], [\"td\", \"nzDisabled\", \"\"], [\"td\", \"nzIndeterminate\", \"\"], [\"td\", \"nzIndentSize\", \"\"], [\"td\", \"nzExpand\", \"\"], [\"td\", \"nzShowExpand\", \"\"], [\"td\", \"nzShowCheckbox\", \"\"]],\n      hostVars: 4,\n      hostBindings: function NzTdAddOnComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-cell-with-append\", ctx.nzShowExpand || ctx.nzIndentSize > 0)(\"ant-table-selection-column\", ctx.nzShowCheckbox);\n        }\n      },\n      inputs: {\n        nzChecked: \"nzChecked\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzLabel: \"nzLabel\",\n        nzIndentSize: \"nzIndentSize\",\n        nzShowExpand: \"nzShowExpand\",\n        nzShowCheckbox: \"nzShowCheckbox\",\n        nzExpand: \"nzExpand\",\n        nzExpandIcon: \"nzExpandIcon\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\",\n        nzExpandChange: \"nzExpandChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[\"rowExpand\", \"\"], [4, \"ngIf\"], [\"nz-checkbox\", \"\", 3, \"nzDisabled\", \"ngModel\", \"nzIndeterminate\", \"ngModelChange\", 4, \"ngIf\"], [3, \"indentSize\"], [4, \"ngIf\", \"ngIfElse\"], [\"nz-row-expand-button\", \"\", 3, \"expandChange\", \"expand\", \"spaceMode\"], [3, \"ngTemplateOutlet\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"nzDisabled\", \"ngModel\", \"nzIndeterminate\"]],\n      template: function NzTdAddOnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTdAddOnComponent_ng_container_0_Template, 5, 3, \"ng-container\", 1)(1, NzTdAddOnComponent_label_1_Template, 1, 4, \"label\", 2);\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowExpand || ctx.nzIndentSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowCheckbox);\n        }\n      },\n      dependencies: [NzRowIndentDirective, NzRowExpandButtonDirective, NgIf, NgTemplateOutlet, NzCheckboxModule, i5.NzCheckboxComponent, FormsModule, i6.NgControlStatus, i6.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzTdAddOnComponent.prototype, \"nzShowExpand\", void 0);\n__decorate([InputBoolean()], NzTdAddOnComponent.prototype, \"nzShowCheckbox\", void 0);\n__decorate([InputBoolean()], NzTdAddOnComponent.prototype, \"nzExpand\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTdAddOnComponent, [{\n    type: Component,\n    args: [{\n      selector: 'td[nzChecked], td[nzDisabled], td[nzIndeterminate], td[nzIndentSize], td[nzExpand], td[nzShowExpand], td[nzShowCheckbox]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *ngIf=\"nzShowExpand || nzIndentSize > 0\">\n      <nz-row-indent [indentSize]=\"nzIndentSize\"></nz-row-indent>\n      <ng-template #rowExpand>\n        <button\n          nz-row-expand-button\n          [expand]=\"nzExpand\"\n          (expandChange)=\"onExpandChange($event)\"\n          [spaceMode]=\"!nzShowExpand\"\n        ></button>\n      </ng-template>\n      <ng-container *ngIf=\"nzExpandIcon; else rowExpand\">\n        <ng-template [ngTemplateOutlet]=\"nzExpandIcon\"></ng-template>\n      </ng-container>\n    </ng-container>\n    <label\n      nz-checkbox\n      *ngIf=\"nzShowCheckbox\"\n      [nzDisabled]=\"nzDisabled\"\n      [ngModel]=\"nzChecked\"\n      [nzIndeterminate]=\"nzIndeterminate\"\n      [attr.aria-label]=\"nzLabel\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class.ant-table-cell-with-append]': `nzShowExpand || nzIndentSize > 0`,\n        '[class.ant-table-selection-column]': `nzShowCheckbox`\n      },\n      imports: [NzRowIndentDirective, NzRowExpandButtonDirective, NgIf, NgTemplateOutlet, NzCheckboxModule, FormsModule],\n      standalone: true\n    }]\n  }], null, {\n    nzChecked: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzIndentSize: [{\n      type: Input\n    }],\n    nzShowExpand: [{\n      type: Input\n    }],\n    nzShowCheckbox: [{\n      type: Input\n    }],\n    nzExpand: [{\n      type: Input\n    }],\n    nzExpandIcon: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzExpandChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzThAddOnComponent {\n  getNextSortDirection(sortDirections, current) {\n    const index = sortDirections.indexOf(current);\n    if (index === sortDirections.length - 1) {\n      return sortDirections[0];\n    } else {\n      return sortDirections[index + 1];\n    }\n  }\n  setSortOrder(order) {\n    this.sortOrderChange$.next(order);\n  }\n  clearSortOrder() {\n    if (this.sortOrder !== null) {\n      this.setSortOrder(null);\n    }\n  }\n  onFilterValueChange(value) {\n    this.nzFilterChange.emit(value);\n    this.nzFilterValue = value;\n    this.updateCalcOperator();\n  }\n  updateCalcOperator() {\n    this.calcOperatorChange$.next();\n  }\n  constructor(host, cdr, ngZone, destroy$) {\n    this.host = host;\n    this.cdr = cdr;\n    this.ngZone = ngZone;\n    this.destroy$ = destroy$;\n    this.manualClickOrder$ = new Subject();\n    this.calcOperatorChange$ = new Subject();\n    this.nzFilterValue = null;\n    this.sortOrder = null;\n    this.sortDirections = ['ascend', 'descend', null];\n    this.sortOrderChange$ = new Subject();\n    this.isNzShowSortChanged = false;\n    this.isNzShowFilterChanged = false;\n    this.nzFilterMultiple = true;\n    this.nzSortOrder = null;\n    this.nzSortPriority = false;\n    this.nzSortDirections = ['ascend', 'descend', null];\n    this.nzFilters = [];\n    this.nzSortFn = null;\n    this.nzFilterFn = null;\n    this.nzShowSort = false;\n    this.nzShowFilter = false;\n    this.nzCustomFilter = false;\n    this.nzCheckedChange = new EventEmitter();\n    this.nzSortOrderChange = new EventEmitter();\n    this.nzFilterChange = new EventEmitter();\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => fromEvent(this.host.nativeElement, 'click').pipe(filter(() => this.nzShowSort), takeUntil(this.destroy$)).subscribe(() => {\n      const nextOrder = this.getNextSortDirection(this.sortDirections, this.sortOrder);\n      this.ngZone.run(() => {\n        this.setSortOrder(nextOrder);\n        this.manualClickOrder$.next(this);\n      });\n    }));\n    this.sortOrderChange$.pipe(takeUntil(this.destroy$)).subscribe(order => {\n      if (this.sortOrder !== order) {\n        this.sortOrder = order;\n        this.nzSortOrderChange.emit(order);\n      }\n      this.updateCalcOperator();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSortDirections,\n      nzFilters,\n      nzSortOrder,\n      nzSortFn,\n      nzFilterFn,\n      nzSortPriority,\n      nzFilterMultiple,\n      nzShowSort,\n      nzShowFilter\n    } = changes;\n    if (nzSortDirections) {\n      if (this.nzSortDirections && this.nzSortDirections.length) {\n        this.sortDirections = this.nzSortDirections;\n      }\n    }\n    if (nzSortOrder) {\n      this.sortOrder = this.nzSortOrder;\n      this.setSortOrder(this.nzSortOrder);\n    }\n    if (nzShowSort) {\n      this.isNzShowSortChanged = true;\n    }\n    if (nzShowFilter) {\n      this.isNzShowFilterChanged = true;\n    }\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    if ((isFirstChange(nzSortOrder) || isFirstChange(nzSortFn)) && !this.isNzShowSortChanged) {\n      this.nzShowSort = true;\n    }\n    if (isFirstChange(nzFilters) && !this.isNzShowFilterChanged) {\n      this.nzShowFilter = true;\n    }\n    if ((nzFilters || nzFilterMultiple) && this.nzShowFilter) {\n      const listOfValue = this.nzFilters.filter(item => item.byDefault).map(item => item.value);\n      this.nzFilterValue = this.nzFilterMultiple ? listOfValue : listOfValue[0] || null;\n    }\n    if (nzSortFn || nzFilterFn || nzSortPriority || nzFilters) {\n      this.updateCalcOperator();\n    }\n  }\n  static {\n    this.ɵfac = function NzThAddOnComponent_Factory(t) {\n      return new (t || NzThAddOnComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzThAddOnComponent,\n      selectors: [[\"th\", \"nzColumnKey\", \"\"], [\"th\", \"nzSortFn\", \"\"], [\"th\", \"nzSortOrder\", \"\"], [\"th\", \"nzFilters\", \"\"], [\"th\", \"nzShowSort\", \"\"], [\"th\", \"nzShowFilter\", \"\"], [\"th\", \"nzCustomFilter\", \"\"]],\n      hostVars: 4,\n      hostBindings: function NzThAddOnComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-column-has-sorters\", ctx.nzShowSort)(\"ant-table-column-sort\", ctx.sortOrder === \"descend\" || ctx.sortOrder === \"ascend\");\n        }\n      },\n      inputs: {\n        nzColumnKey: \"nzColumnKey\",\n        nzFilterMultiple: \"nzFilterMultiple\",\n        nzSortOrder: \"nzSortOrder\",\n        nzSortPriority: \"nzSortPriority\",\n        nzSortDirections: \"nzSortDirections\",\n        nzFilters: \"nzFilters\",\n        nzSortFn: \"nzSortFn\",\n        nzFilterFn: \"nzFilterFn\",\n        nzShowSort: \"nzShowSort\",\n        nzShowFilter: \"nzShowFilter\",\n        nzCustomFilter: \"nzCustomFilter\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\",\n        nzSortOrderChange: \"nzSortOrderChange\",\n        nzFilterChange: \"nzFilterChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c4,\n      decls: 9,\n      vars: 2,\n      consts: [[\"notFilterTemplate\", \"\"], [\"extraTemplate\", \"\"], [\"sortTemplate\", \"\"], [\"contentTemplate\", \"\"], [3, \"contentTemplate\", \"extraTemplate\", \"customFilter\", \"filterMultiple\", \"listOfFilter\", \"filterChange\", 4, \"ngIf\", \"ngIfElse\"], [3, \"filterChange\", \"contentTemplate\", \"extraTemplate\", \"customFilter\", \"filterMultiple\", \"listOfFilter\"], [3, \"ngTemplateOutlet\"], [3, \"sortOrder\", \"sortDirections\", \"contentTemplate\"]],\n      template: function NzThAddOnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, NzThAddOnComponent_nz_table_filter_0_Template, 1, 5, \"nz-table-filter\", 4)(1, NzThAddOnComponent_ng_template_1_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, NzThAddOnComponent_ng_template_3_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, NzThAddOnComponent_ng_template_5_Template, 1, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(7, NzThAddOnComponent_ng_template_7_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const notFilterTemplate_r3 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowFilter || ctx.nzCustomFilter)(\"ngIfElse\", notFilterTemplate_r3);\n        }\n      },\n      dependencies: [NzTableFilterComponent, NgIf, NgTemplateOutlet, NzTableSortersComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzThAddOnComponent.prototype, \"nzShowSort\", void 0);\n__decorate([InputBoolean()], NzThAddOnComponent.prototype, \"nzShowFilter\", void 0);\n__decorate([InputBoolean()], NzThAddOnComponent.prototype, \"nzCustomFilter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThAddOnComponent, [{\n    type: Component,\n    args: [{\n      selector: 'th[nzColumnKey], th[nzSortFn], th[nzSortOrder], th[nzFilters], th[nzShowSort], th[nzShowFilter], th[nzCustomFilter]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <nz-table-filter\n      *ngIf=\"nzShowFilter || nzCustomFilter; else notFilterTemplate\"\n      [contentTemplate]=\"notFilterTemplate\"\n      [extraTemplate]=\"extraTemplate\"\n      [customFilter]=\"nzCustomFilter\"\n      [filterMultiple]=\"nzFilterMultiple\"\n      [listOfFilter]=\"nzFilters\"\n      (filterChange)=\"onFilterValueChange($event)\"\n    ></nz-table-filter>\n    <ng-template #notFilterTemplate>\n      <ng-template [ngTemplateOutlet]=\"nzShowSort ? sortTemplate : contentTemplate\"></ng-template>\n    </ng-template>\n    <ng-template #extraTemplate>\n      <ng-content select=\"[nz-th-extra]\"></ng-content>\n      <ng-content select=\"nz-filter-trigger\"></ng-content>\n    </ng-template>\n    <ng-template #sortTemplate>\n      <nz-table-sorters\n        [sortOrder]=\"sortOrder\"\n        [sortDirections]=\"sortDirections\"\n        [contentTemplate]=\"contentTemplate\"\n      ></nz-table-sorters>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-table-column-has-sorters]': 'nzShowSort',\n        '[class.ant-table-column-sort]': `sortOrder === 'descend' || sortOrder === 'ascend'`\n      },\n      providers: [NzDestroyService],\n      imports: [NzTableFilterComponent, NgIf, NgTemplateOutlet, NzTableSortersComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzColumnKey: [{\n      type: Input\n    }],\n    nzFilterMultiple: [{\n      type: Input\n    }],\n    nzSortOrder: [{\n      type: Input\n    }],\n    nzSortPriority: [{\n      type: Input\n    }],\n    nzSortDirections: [{\n      type: Input\n    }],\n    nzFilters: [{\n      type: Input\n    }],\n    nzSortFn: [{\n      type: Input\n    }],\n    nzFilterFn: [{\n      type: Input\n    }],\n    nzShowSort: [{\n      type: Input\n    }],\n    nzShowFilter: [{\n      type: Input\n    }],\n    nzCustomFilter: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzSortOrderChange: [{\n      type: Output\n    }],\n    nzFilterChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzThMeasureDirective {\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.changes$ = new Subject();\n    this.nzWidth = null;\n    this.colspan = null;\n    this.colSpan = null;\n    this.rowspan = null;\n    this.rowSpan = null;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzWidth,\n      colspan,\n      rowspan,\n      colSpan,\n      rowSpan\n    } = changes;\n    if (colspan || colSpan) {\n      const col = this.colspan || this.colSpan;\n      if (!isNil(col)) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'colspan', `${col}`);\n      } else {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'colspan');\n      }\n    }\n    if (rowspan || rowSpan) {\n      const row = this.rowspan || this.rowSpan;\n      if (!isNil(row)) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'rowspan', `${row}`);\n      } else {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'rowspan');\n      }\n    }\n    if (nzWidth || colspan) {\n      this.changes$.next();\n    }\n  }\n  static {\n    this.ɵfac = function NzThMeasureDirective_Factory(t) {\n      return new (t || NzThMeasureDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzThMeasureDirective,\n      selectors: [[\"th\"]],\n      inputs: {\n        nzWidth: \"nzWidth\",\n        colspan: \"colspan\",\n        colSpan: \"colSpan\",\n        rowspan: \"rowspan\",\n        rowSpan: \"rowSpan\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThMeasureDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzWidth: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }],\n    colSpan: [{\n      type: Input\n    }],\n    rowspan: [{\n      type: Input\n    }],\n    rowSpan: [{\n      type: Input\n    }]\n  });\n})();\nclass NzThSelectionComponent {\n  constructor() {\n    this.nzSelections = [];\n    this.nzChecked = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzLabel = null;\n    this.nzShowCheckbox = false;\n    this.nzShowRowSelection = false;\n    this.nzCheckedChange = new EventEmitter();\n    this.isNzShowExpandChanged = false;\n    this.isNzShowCheckboxChanged = false;\n  }\n  onCheckedChange(checked) {\n    this.nzChecked = checked;\n    this.nzCheckedChange.emit(checked);\n  }\n  ngOnChanges(changes) {\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    const {\n      nzChecked,\n      nzSelections,\n      nzShowExpand,\n      nzShowCheckbox\n    } = changes;\n    if (nzShowExpand) {\n      this.isNzShowExpandChanged = true;\n    }\n    if (nzShowCheckbox) {\n      this.isNzShowCheckboxChanged = true;\n    }\n    if (isFirstChange(nzSelections) && !this.isNzShowExpandChanged) {\n      this.nzShowRowSelection = true;\n    }\n    if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n      this.nzShowCheckbox = true;\n    }\n  }\n  static {\n    this.ɵfac = function NzThSelectionComponent_Factory(t) {\n      return new (t || NzThSelectionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzThSelectionComponent,\n      selectors: [[\"th\", \"nzSelections\", \"\"], [\"th\", \"nzChecked\", \"\"], [\"th\", \"nzShowCheckbox\", \"\"], [\"th\", \"nzShowRowSelection\", \"\"]],\n      hostAttrs: [1, \"ant-table-selection-column\"],\n      inputs: {\n        nzSelections: \"nzSelections\",\n        nzChecked: \"nzChecked\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzLabel: \"nzLabel\",\n        nzShowCheckbox: \"nzShowCheckbox\",\n        nzShowRowSelection: \"nzShowRowSelection\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 7,\n      consts: [[3, \"checkedChange\", \"checked\", \"disabled\", \"indeterminate\", \"label\", \"listOfSelections\", \"showCheckbox\", \"showRowSelection\"]],\n      template: function NzThSelectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"nz-table-selection\", 0);\n          i0.ɵɵlistener(\"checkedChange\", function NzThSelectionComponent_Template_nz_table_selection_checkedChange_0_listener($event) {\n            return ctx.onCheckedChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled)(\"indeterminate\", ctx.nzIndeterminate)(\"label\", ctx.nzLabel)(\"listOfSelections\", ctx.nzSelections)(\"showCheckbox\", ctx.nzShowCheckbox)(\"showRowSelection\", ctx.nzShowRowSelection);\n        }\n      },\n      dependencies: [NzTableSelectionComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzThSelectionComponent.prototype, \"nzShowCheckbox\", void 0);\n__decorate([InputBoolean()], NzThSelectionComponent.prototype, \"nzShowRowSelection\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThSelectionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'th[nzSelections],th[nzChecked],th[nzShowCheckbox],th[nzShowRowSelection]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <nz-table-selection\n      [checked]=\"nzChecked\"\n      [disabled]=\"nzDisabled\"\n      [indeterminate]=\"nzIndeterminate\"\n      [label]=\"nzLabel\"\n      [listOfSelections]=\"nzSelections\"\n      [showCheckbox]=\"nzShowCheckbox\"\n      [showRowSelection]=\"nzShowRowSelection\"\n      (checkedChange)=\"onCheckedChange($event)\"\n    ></nz-table-selection>\n    <ng-content></ng-content>\n  `,\n      host: {\n        class: 'ant-table-selection-column'\n      },\n      imports: [NzTableSelectionComponent],\n      standalone: true\n    }]\n  }], () => [], {\n    nzSelections: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzShowCheckbox: [{\n      type: Input\n    }],\n    nzShowRowSelection: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellAlignDirective {\n  constructor() {\n    this.nzAlign = null;\n  }\n  static {\n    this.ɵfac = function NzCellAlignDirective_Factory(t) {\n      return new (t || NzCellAlignDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellAlignDirective,\n      selectors: [[\"th\", \"nzAlign\", \"\"], [\"td\", \"nzAlign\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzCellAlignDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"text-align\", ctx.nzAlign);\n        }\n      },\n      inputs: {\n        nzAlign: \"nzAlign\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellAlignDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzAlign],td[nzAlign]',\n      host: {\n        '[style.text-align]': 'nzAlign'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzAlign: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCellEllipsisDirective {\n  constructor() {\n    this.nzEllipsis = true;\n  }\n  static {\n    this.ɵfac = function NzCellEllipsisDirective_Factory(t) {\n      return new (t || NzCellEllipsisDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellEllipsisDirective,\n      selectors: [[\"th\", \"nzEllipsis\", \"\"], [\"td\", \"nzEllipsis\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzCellEllipsisDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-cell-ellipsis\", ctx.nzEllipsis);\n        }\n      },\n      inputs: {\n        nzEllipsis: \"nzEllipsis\"\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzCellEllipsisDirective.prototype, \"nzEllipsis\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellEllipsisDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzEllipsis],td[nzEllipsis]',\n      host: {\n        '[class.ant-table-cell-ellipsis]': 'nzEllipsis'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzEllipsis: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCellBreakWordDirective {\n  constructor() {\n    this.nzBreakWord = true;\n  }\n  static {\n    this.ɵfac = function NzCellBreakWordDirective_Factory(t) {\n      return new (t || NzCellBreakWordDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellBreakWordDirective,\n      selectors: [[\"th\", \"nzBreakWord\", \"\"], [\"td\", \"nzBreakWord\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzCellBreakWordDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"word-break\", ctx.nzBreakWord ? \"break-all\" : \"\");\n        }\n      },\n      inputs: {\n        nzBreakWord: \"nzBreakWord\"\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzCellBreakWordDirective.prototype, \"nzBreakWord\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellBreakWordDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzBreakWord],td[nzBreakWord]',\n      host: {\n        '[style.word-break]': `nzBreakWord ? 'break-all' : ''`\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzBreakWord: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableContentComponent {\n  constructor() {\n    this.tableLayout = 'auto';\n    this.theadTemplate = null;\n    this.contentTemplate = null;\n    this.listOfColWidth = [];\n    this.scrollX = null;\n  }\n  static {\n    this.ɵfac = function NzTableContentComponent_Factory(t) {\n      return new (t || NzTableContentComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableContentComponent,\n      selectors: [[\"table\", \"nz-table-content\", \"\"]],\n      hostVars: 8,\n      hostBindings: function NzTableContentComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"table-layout\", ctx.tableLayout)(\"width\", ctx.scrollX)(\"min-width\", ctx.scrollX ? \"100%\" : null);\n          i0.ɵɵclassProp(\"ant-table-fixed\", ctx.scrollX);\n        }\n      },\n      inputs: {\n        tableLayout: \"tableLayout\",\n        theadTemplate: \"theadTemplate\",\n        contentTemplate: \"contentTemplate\",\n        listOfColWidth: \"listOfColWidth\",\n        scrollX: \"scrollX\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c6,\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 3,\n      consts: [[3, \"width\", \"minWidth\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ant-table-thead\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-thead\"]],\n      template: function NzTableContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTableContentComponent_col_0_Template, 1, 4, \"col\", 0)(1, NzTableContentComponent_thead_1_Template, 2, 1, \"thead\", 1)(2, NzTableContentComponent_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵprojection(3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.listOfColWidth);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.theadTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NgIf, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableContentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'table[nz-table-content]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <col [style.width]=\"width\" [style.minWidth]=\"width\" *ngFor=\"let width of listOfColWidth\" />\n    <thead class=\"ant-table-thead\" *ngIf=\"theadTemplate\">\n      <ng-template [ngTemplateOutlet]=\"theadTemplate\"></ng-template>\n    </thead>\n    <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[style.table-layout]': 'tableLayout',\n        '[class.ant-table-fixed]': 'scrollX',\n        '[style.width]': 'scrollX',\n        '[style.min-width]': `scrollX ? '100%' : null`\n      },\n      imports: [NgTemplateOutlet, NgIf, NgForOf],\n      standalone: true\n    }]\n  }], null, {\n    tableLayout: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    scrollX: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFixedRowComponent {\n  constructor(nzTableStyleService, renderer) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.renderer = renderer;\n    this.hostWidth$ = new BehaviorSubject(null);\n    this.enableAutoMeasure$ = new BehaviorSubject(false);\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.nzTableStyleService) {\n      const {\n        enableAutoMeasure$,\n        hostWidth$\n      } = this.nzTableStyleService;\n      enableAutoMeasure$.pipe(takeUntil(this.destroy$)).subscribe(this.enableAutoMeasure$);\n      hostWidth$.pipe(takeUntil(this.destroy$)).subscribe(this.hostWidth$);\n    }\n  }\n  ngAfterViewInit() {\n    this.nzTableStyleService.columnCount$.pipe(takeUntil(this.destroy$)).subscribe(count => {\n      this.renderer.setAttribute(this.tdElement.nativeElement, 'colspan', `${count}`);\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableFixedRowComponent_Factory(t) {\n      return new (t || NzTableFixedRowComponent)(i0.ɵɵdirectiveInject(NzTableStyleService), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableFixedRowComponent,\n      selectors: [[\"tr\", \"nz-table-fixed-row\", \"\"], [\"tr\", \"nzExpand\", \"\"]],\n      viewQuery: function NzTableFixedRowComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c7, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tdElement = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c8,\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 4,\n      consts: [[\"tdElement\", \"\"], [\"contentTemplate\", \"\"], [1, \"nz-disable-td\", \"ant-table-cell\"], [\"class\", \"ant-table-expanded-row-fixed\", \"style\", \"position: sticky; left: 0px; overflow: hidden;\", 3, \"width\", 4, \"ngIf\", \"ngIfElse\"], [1, \"ant-table-expanded-row-fixed\", 2, \"position\", \"sticky\", \"left\", \"0px\", \"overflow\", \"hidden\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzTableFixedRowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"td\", 2, 0);\n          i0.ɵɵtemplate(2, NzTableFixedRowComponent_div_2_Template, 3, 5, \"div\", 3);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NzTableFixedRowComponent_ng_template_4_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const contentTemplate_r2 = i0.ɵɵreference(5);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 2, ctx.enableAutoMeasure$))(\"ngIfElse\", contentTemplate_r2);\n        }\n      },\n      dependencies: [NgIf, AsyncPipe, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableFixedRowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tr[nz-table-fixed-row], tr[nzExpand]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <td class=\"nz-disable-td ant-table-cell\" #tdElement>\n      <div\n        class=\"ant-table-expanded-row-fixed\"\n        *ngIf=\"enableAutoMeasure$ | async; else contentTemplate\"\n        style=\"position: sticky; left: 0px; overflow: hidden;\"\n        [style.width.px]=\"hostWidth$ | async\"\n      >\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n      </div>\n    </td>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      imports: [NgIf, AsyncPipe, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService\n  }, {\n    type: i0.Renderer2\n  }], {\n    tdElement: [{\n      type: ViewChild,\n      args: ['tdElement', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableInnerDefaultComponent {\n  constructor() {\n    this.tableLayout = 'auto';\n    this.listOfColWidth = [];\n    this.theadTemplate = null;\n    this.contentTemplate = null;\n  }\n  static {\n    this.ɵfac = function NzTableInnerDefaultComponent_Factory(t) {\n      return new (t || NzTableInnerDefaultComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableInnerDefaultComponent,\n      selectors: [[\"nz-table-inner-default\"]],\n      hostAttrs: [1, \"ant-table-container\"],\n      inputs: {\n        tableLayout: \"tableLayout\",\n        listOfColWidth: \"listOfColWidth\",\n        theadTemplate: \"theadTemplate\",\n        contentTemplate: \"contentTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 4,\n      consts: [[1, \"ant-table-content\"], [\"nz-table-content\", \"\", 3, \"contentTemplate\", \"tableLayout\", \"listOfColWidth\", \"theadTemplate\"]],\n      template: function NzTableInnerDefaultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"table\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"contentTemplate\", ctx.contentTemplate)(\"tableLayout\", ctx.tableLayout)(\"listOfColWidth\", ctx.listOfColWidth)(\"theadTemplate\", ctx.theadTemplate);\n        }\n      },\n      dependencies: [NzTableContentComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableInnerDefaultComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-inner-default',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div class=\"ant-table-content\">\n      <table\n        nz-table-content\n        [contentTemplate]=\"contentTemplate\"\n        [tableLayout]=\"tableLayout\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n      ></table>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-container'\n      },\n      imports: [NzTableContentComponent],\n      standalone: true\n    }]\n  }], () => [], {\n    tableLayout: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTrMeasureComponent {\n  constructor(nzResizeObserver, ngZone) {\n    this.nzResizeObserver = nzResizeObserver;\n    this.ngZone = ngZone;\n    this.listOfMeasureColumn = [];\n    this.listOfAutoWidth = new EventEmitter();\n    this.destroy$ = new Subject();\n  }\n  trackByFunc(_, key) {\n    return key;\n  }\n  ngAfterViewInit() {\n    this.listOfTdElement.changes.pipe(startWith(this.listOfTdElement)).pipe(switchMap(list => combineLatest(list.toArray().map(item => this.nzResizeObserver.observe(item).pipe(map(([entry]) => {\n      const {\n        width\n      } = entry.target.getBoundingClientRect();\n      return Math.floor(width);\n    }))))), debounceTime(16), takeUntil(this.destroy$)).subscribe(data => {\n      // Caretaker note: we don't have to re-enter the Angular zone each time the stream emits.\n      // The below check is necessary to be sure that zone is not nooped through `BootstrapOptions`\n      // (`bootstrapModule(AppModule, { ngZone: 'noop' }))`. The `ngZone instanceof NgZone` may return\n      // `false` if zone is nooped, since `ngZone` will be an instance of the `NoopNgZone`.\n      // The `ResizeObserver` might be also patched through `zone.js/dist/zone-patch-resize-observer`,\n      // thus calling `ngZone.run` again will cause another change detection.\n      if (this.ngZone instanceof NgZone && NgZone.isInAngularZone()) {\n        this.listOfAutoWidth.next(data);\n      } else {\n        this.ngZone.run(() => this.listOfAutoWidth.next(data));\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTrMeasureComponent_Factory(t) {\n      return new (t || NzTrMeasureComponent)(i0.ɵɵdirectiveInject(i1$2.NzResizeObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTrMeasureComponent,\n      selectors: [[\"tr\", \"nz-table-measure-row\", \"\"]],\n      viewQuery: function NzTrMeasureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c7, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTdElement = _t);\n        }\n      },\n      hostAttrs: [1, \"ant-table-measure-now\"],\n      inputs: {\n        listOfMeasureColumn: \"listOfMeasureColumn\"\n      },\n      outputs: {\n        listOfAutoWidth: \"listOfAutoWidth\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c9,\n      decls: 1,\n      vars: 2,\n      consts: [[\"tdElement\", \"\"], [\"class\", \"nz-disable-td\", \"style\", \"padding: 0px; border: 0px; height: 0px;\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"nz-disable-td\", 2, \"padding\", \"0px\", \"border\", \"0px\", \"height\", \"0px\"]],\n      template: function NzTrMeasureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTrMeasureComponent_td_0_Template, 2, 0, \"td\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.listOfMeasureColumn)(\"ngForTrackBy\", ctx.trackByFunc);\n        }\n      },\n      dependencies: [NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrMeasureComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tr[nz-table-measure-row]',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <td\n      #tdElement\n      class=\"nz-disable-td\"\n      style=\"padding: 0px; border: 0px; height: 0px;\"\n      *ngFor=\"let th of listOfMeasureColumn; trackBy: trackByFunc\"\n    ></td>\n  `,\n      host: {\n        class: 'ant-table-measure-now'\n      },\n      imports: [NgForOf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$2.NzResizeObserver\n  }, {\n    type: i0.NgZone\n  }], {\n    listOfMeasureColumn: [{\n      type: Input\n    }],\n    listOfAutoWidth: [{\n      type: Output\n    }],\n    listOfTdElement: [{\n      type: ViewChildren,\n      args: ['tdElement']\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTbodyComponent {\n  constructor(nzTableStyleService) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.isInsideTable = false;\n    this.showEmpty$ = new BehaviorSubject(false);\n    this.noResult$ = new BehaviorSubject(undefined);\n    this.listOfMeasureColumn$ = new BehaviorSubject([]);\n    this.destroy$ = new Subject();\n    this.isInsideTable = !!this.nzTableStyleService;\n    if (this.nzTableStyleService) {\n      const {\n        showEmpty$,\n        noResult$,\n        listOfMeasureColumn$\n      } = this.nzTableStyleService;\n      noResult$.pipe(takeUntil(this.destroy$)).subscribe(this.noResult$);\n      listOfMeasureColumn$.pipe(takeUntil(this.destroy$)).subscribe(this.listOfMeasureColumn$);\n      showEmpty$.pipe(takeUntil(this.destroy$)).subscribe(this.showEmpty$);\n    }\n  }\n  onListOfAutoWidthChange(listOfAutoWidth) {\n    this.nzTableStyleService.setListOfAutoWidth(listOfAutoWidth);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTbodyComponent_Factory(t) {\n      return new (t || NzTbodyComponent)(i0.ɵɵdirectiveInject(NzTableStyleService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTbodyComponent,\n      selectors: [[\"tbody\"]],\n      hostVars: 2,\n      hostBindings: function NzTbodyComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-tbody\", ctx.isInsideTable);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 6,\n      consts: [[4, \"ngIf\"], [\"class\", \"ant-table-placeholder\", \"nz-table-fixed-row\", \"\", 4, \"ngIf\"], [\"nz-table-measure-row\", \"\", 3, \"listOfMeasureColumn\", \"listOfAutoWidth\", 4, \"ngIf\"], [\"nz-table-measure-row\", \"\", 3, \"listOfAutoWidth\", \"listOfMeasureColumn\"], [\"nz-table-fixed-row\", \"\", 1, \"ant-table-placeholder\"], [\"nzComponentName\", \"table\", 3, \"specificContent\"]],\n      template: function NzTbodyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTbodyComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵprojection(2);\n          i0.ɵɵtemplate(3, NzTbodyComponent_tr_3_Template, 3, 3, \"tr\", 1);\n          i0.ɵɵpipe(4, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 2, ctx.listOfMeasureColumn$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 4, ctx.showEmpty$));\n        }\n      },\n      dependencies: [NgIf, AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule, i2$2.NzEmbedEmptyComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTbodyComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tbody',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *ngIf=\"listOfMeasureColumn$ | async as listOfMeasureColumn\">\n      <tr\n        nz-table-measure-row\n        *ngIf=\"isInsideTable && listOfMeasureColumn.length\"\n        [listOfMeasureColumn]=\"listOfMeasureColumn\"\n        (listOfAutoWidth)=\"onListOfAutoWidthChange($event)\"\n      ></tr>\n    </ng-container>\n    <ng-content></ng-content>\n    <tr class=\"ant-table-placeholder\" nz-table-fixed-row *ngIf=\"showEmpty$ | async\">\n      <nz-embed-empty nzComponentName=\"table\" [specificContent]=\"(noResult$ | async)!\"></nz-embed-empty>\n    </tr>\n  `,\n      host: {\n        '[class.ant-table-tbody]': 'isInsideTable'\n      },\n      imports: [NgIf, AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nclass NzTableInnerScrollComponent {\n  setScrollPositionClassName(clear = false) {\n    const {\n      scrollWidth,\n      scrollLeft,\n      clientWidth\n    } = this.tableBodyElement.nativeElement;\n    const leftClassName = 'ant-table-ping-left';\n    const rightClassName = 'ant-table-ping-right';\n    if (scrollWidth === clientWidth && scrollWidth !== 0 || clear) {\n      this.renderer.removeClass(this.tableMainElement, leftClassName);\n      this.renderer.removeClass(this.tableMainElement, rightClassName);\n    } else if (scrollLeft === 0) {\n      this.renderer.removeClass(this.tableMainElement, leftClassName);\n      this.renderer.addClass(this.tableMainElement, rightClassName);\n    } else if (scrollWidth === scrollLeft + clientWidth) {\n      this.renderer.removeClass(this.tableMainElement, rightClassName);\n      this.renderer.addClass(this.tableMainElement, leftClassName);\n    } else {\n      this.renderer.addClass(this.tableMainElement, leftClassName);\n      this.renderer.addClass(this.tableMainElement, rightClassName);\n    }\n  }\n  constructor(renderer, ngZone, platform, resizeService) {\n    this.renderer = renderer;\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.resizeService = resizeService;\n    this.data = [];\n    this.scrollX = null;\n    this.scrollY = null;\n    this.contentTemplate = null;\n    this.widthConfig = [];\n    this.listOfColWidth = [];\n    this.theadTemplate = null;\n    this.virtualTemplate = null;\n    this.virtualItemSize = 0;\n    this.virtualMaxBufferPx = 200;\n    this.virtualMinBufferPx = 100;\n    this.virtualForTrackBy = index => index;\n    this.headerStyleMap = {};\n    this.bodyStyleMap = {};\n    this.verticalScrollBarWidth = 0;\n    this.noDateVirtualHeight = '182px';\n    this.data$ = new Subject();\n    this.scroll$ = new Subject();\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      scrollX,\n      scrollY,\n      data\n    } = changes;\n    if (scrollX || scrollY) {\n      const hasVerticalScrollBar = this.verticalScrollBarWidth !== 0;\n      this.headerStyleMap = {\n        overflowX: 'hidden',\n        overflowY: this.scrollY && hasVerticalScrollBar ? 'scroll' : 'hidden'\n      };\n      this.bodyStyleMap = {\n        overflowY: this.scrollY ? 'scroll' : 'hidden',\n        overflowX: this.scrollX ? 'auto' : null,\n        maxHeight: this.scrollY\n      };\n      // Caretaker note: we have to emit the value outside of the Angular zone, thus DOM timer (`delay(0)`) and `scroll`\n      // event listener will be also added outside of the Angular zone.\n      this.ngZone.runOutsideAngular(() => this.scroll$.next());\n    }\n    if (data) {\n      // See the comment above.\n      this.ngZone.runOutsideAngular(() => this.data$.next());\n    }\n  }\n  ngAfterViewInit() {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        const scrollEvent$ = this.scroll$.pipe(startWith(null), delay(0), switchMap(() => fromEvent(this.tableBodyElement.nativeElement, 'scroll').pipe(startWith(true))), takeUntil(this.destroy$));\n        const resize$ = this.resizeService.subscribe().pipe(takeUntil(this.destroy$));\n        const data$ = this.data$.pipe(takeUntil(this.destroy$));\n        const setClassName$ = merge(scrollEvent$, resize$, data$, this.scroll$).pipe(startWith(true), delay(0), takeUntil(this.destroy$));\n        setClassName$.subscribe(() => this.setScrollPositionClassName());\n        scrollEvent$.pipe(filter(() => !!this.scrollY)).subscribe(() => this.tableHeaderElement.nativeElement.scrollLeft = this.tableBodyElement.nativeElement.scrollLeft);\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.setScrollPositionClassName(true);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableInnerScrollComponent_Factory(t) {\n      return new (t || NzTableInnerScrollComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$3.Platform), i0.ɵɵdirectiveInject(i2.NzResizeService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableInnerScrollComponent,\n      selectors: [[\"nz-table-inner-scroll\"]],\n      viewQuery: function NzTableInnerScrollComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c10, 5, ElementRef);\n          i0.ɵɵviewQuery(_c11, 5, ElementRef);\n          i0.ɵɵviewQuery(CdkVirtualScrollViewport, 5, CdkVirtualScrollViewport);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableHeaderElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableBodyElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkVirtualScrollViewport = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-table-container\"],\n      inputs: {\n        data: \"data\",\n        scrollX: \"scrollX\",\n        scrollY: \"scrollY\",\n        contentTemplate: \"contentTemplate\",\n        widthConfig: \"widthConfig\",\n        listOfColWidth: \"listOfColWidth\",\n        theadTemplate: \"theadTemplate\",\n        virtualTemplate: \"virtualTemplate\",\n        virtualItemSize: \"virtualItemSize\",\n        virtualMaxBufferPx: \"virtualMaxBufferPx\",\n        virtualMinBufferPx: \"virtualMinBufferPx\",\n        tableMainElement: \"tableMainElement\",\n        virtualForTrackBy: \"virtualForTrackBy\",\n        verticalScrollBarWidth: \"verticalScrollBarWidth\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"tableHeaderElement\", \"\"], [\"tableBodyElement\", \"\"], [4, \"ngIf\"], [\"class\", \"ant-table-content\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"ant-table-header\", \"nz-table-hide-scrollbar\", 3, \"ngStyle\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"theadTemplate\"], [\"class\", \"ant-table-body\", 3, \"ngStyle\", 4, \"ngIf\"], [3, \"itemSize\", \"maxBufferPx\", \"minBufferPx\", \"height\", 4, \"ngIf\"], [1, \"ant-table-body\", 3, \"ngStyle\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"contentTemplate\"], [3, \"itemSize\", \"maxBufferPx\", \"minBufferPx\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\"], [4, \"cdkVirtualFor\", \"cdkVirtualForOf\", \"cdkVirtualForTrackBy\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ant-table-content\", 3, \"ngStyle\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\"]],\n      template: function NzTableInnerScrollComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTableInnerScrollComponent_ng_container_0_Template, 6, 6, \"ng-container\", 2)(1, NzTableInnerScrollComponent_div_1_Template, 3, 5, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollY);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.scrollY);\n        }\n      },\n      dependencies: [NzTableContentComponent, NgIf, NgStyle, ScrollingModule, i3$1.CdkFixedSizeVirtualScroll, i3$1.CdkVirtualForOf, i3$1.CdkVirtualScrollViewport, NgTemplateOutlet, NzTbodyComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableInnerScrollComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-inner-scroll',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *ngIf=\"scrollY\">\n      <div #tableHeaderElement [ngStyle]=\"headerStyleMap\" class=\"ant-table-header nz-table-hide-scrollbar\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n        ></table>\n      </div>\n      <div #tableBodyElement *ngIf=\"!virtualTemplate\" class=\"ant-table-body\" [ngStyle]=\"bodyStyleMap\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [contentTemplate]=\"contentTemplate\"\n        ></table>\n      </div>\n      <cdk-virtual-scroll-viewport\n        #tableBodyElement\n        *ngIf=\"virtualTemplate\"\n        [itemSize]=\"virtualItemSize\"\n        [maxBufferPx]=\"virtualMaxBufferPx\"\n        [minBufferPx]=\"virtualMinBufferPx\"\n        [style.height]=\"data.length ? scrollY : noDateVirtualHeight\"\n      >\n        <table nz-table-content tableLayout=\"fixed\" [scrollX]=\"scrollX\" [listOfColWidth]=\"listOfColWidth\">\n          <tbody>\n            <ng-container *cdkVirtualFor=\"let item of data; let i = index; trackBy: virtualForTrackBy\">\n              <ng-template\n                [ngTemplateOutlet]=\"virtualTemplate\"\n                [ngTemplateOutletContext]=\"{ $implicit: item, index: i }\"\n              ></ng-template>\n            </ng-container>\n          </tbody>\n        </table>\n      </cdk-virtual-scroll-viewport>\n    </ng-container>\n    <div class=\"ant-table-content\" #tableBodyElement *ngIf=\"!scrollY\" [ngStyle]=\"bodyStyleMap\">\n      <table\n        nz-table-content\n        tableLayout=\"fixed\"\n        [scrollX]=\"scrollX\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n        [contentTemplate]=\"contentTemplate\"\n      ></table>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-container'\n      },\n      imports: [NzTableContentComponent, NgIf, NgStyle, ScrollingModule, NgTemplateOutlet, NzTbodyComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$3.Platform\n  }, {\n    type: i2.NzResizeService\n  }], {\n    data: [{\n      type: Input\n    }],\n    scrollX: [{\n      type: Input\n    }],\n    scrollY: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    widthConfig: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    virtualTemplate: [{\n      type: Input\n    }],\n    virtualItemSize: [{\n      type: Input\n    }],\n    virtualMaxBufferPx: [{\n      type: Input\n    }],\n    virtualMinBufferPx: [{\n      type: Input\n    }],\n    tableMainElement: [{\n      type: Input\n    }],\n    virtualForTrackBy: [{\n      type: Input\n    }],\n    tableHeaderElement: [{\n      type: ViewChild,\n      args: ['tableHeaderElement', {\n        read: ElementRef\n      }]\n    }],\n    tableBodyElement: [{\n      type: ViewChild,\n      args: ['tableBodyElement', {\n        read: ElementRef\n      }]\n    }],\n    cdkVirtualScrollViewport: [{\n      type: ViewChild,\n      args: [CdkVirtualScrollViewport, {\n        read: CdkVirtualScrollViewport\n      }]\n    }],\n    verticalScrollBarWidth: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableVirtualScrollDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function NzTableVirtualScrollDirective_Factory(t) {\n      return new (t || NzTableVirtualScrollDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTableVirtualScrollDirective,\n      selectors: [[\"\", \"nz-virtual-scroll\", \"\"]],\n      exportAs: [\"nzVirtualScroll\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableVirtualScrollDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-virtual-scroll]',\n      exportAs: 'nzVirtualScroll',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableTitleFooterComponent {\n  constructor() {\n    this.title = null;\n    this.footer = null;\n  }\n  static {\n    this.ɵfac = function NzTableTitleFooterComponent_Factory(t) {\n      return new (t || NzTableTitleFooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableTitleFooterComponent,\n      selectors: [[\"nz-table-title-footer\"]],\n      hostVars: 4,\n      hostBindings: function NzTableTitleFooterComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-title\", ctx.title !== null)(\"ant-table-footer\", ctx.footer !== null);\n        }\n      },\n      inputs: {\n        title: \"title\",\n        footer: \"footer\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"nzStringTemplateOutlet\"]],\n      template: function NzTableTitleFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTableTitleFooterComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0)(1, NzTableTitleFooterComponent_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.title);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.footer);\n        }\n      },\n      dependencies: [NzOutletModule, i1$4.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableTitleFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-title-footer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"title\">{{ title }}</ng-container>\n    <ng-container *nzStringTemplateOutlet=\"footer\">{{ footer }}</ng-container>\n  `,\n      host: {\n        '[class.ant-table-title]': `title !== null`,\n        '[class.ant-table-footer]': `footer !== null`\n      },\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], null, {\n    title: [{\n      type: Input\n    }],\n    footer: [{\n      type: Input\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'table';\nclass NzTableComponent {\n  onPageSizeChange(size) {\n    this.nzTableDataService.updatePageSize(size);\n  }\n  onPageIndexChange(index) {\n    this.nzTableDataService.updatePageIndex(index);\n  }\n  constructor(elementRef, nzResizeObserver, nzConfigService, cdr, nzTableStyleService, nzTableDataService, directionality) {\n    this.elementRef = elementRef;\n    this.nzResizeObserver = nzResizeObserver;\n    this.nzConfigService = nzConfigService;\n    this.cdr = cdr;\n    this.nzTableStyleService = nzTableStyleService;\n    this.nzTableDataService = nzTableDataService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzTableLayout = 'auto';\n    this.nzShowTotal = null;\n    this.nzItemRender = null;\n    this.nzTitle = null;\n    this.nzFooter = null;\n    this.nzNoResult = undefined;\n    this.nzPageSizeOptions = [10, 20, 30, 40, 50];\n    this.nzVirtualItemSize = 0;\n    this.nzVirtualMaxBufferPx = 200;\n    this.nzVirtualMinBufferPx = 100;\n    this.nzVirtualForTrackBy = index => index;\n    this.nzLoadingDelay = 0;\n    this.nzPageIndex = 1;\n    this.nzPageSize = 10;\n    this.nzTotal = 0;\n    this.nzWidthConfig = [];\n    this.nzData = [];\n    this.nzCustomColumn = [];\n    this.nzPaginationPosition = 'bottom';\n    this.nzScroll = {\n      x: null,\n      y: null\n    };\n    this.nzPaginationType = 'default';\n    this.nzFrontPagination = true;\n    this.nzTemplateMode = false;\n    this.nzShowPagination = true;\n    this.nzLoading = false;\n    this.nzOuterBordered = false;\n    this.nzLoadingIndicator = null;\n    this.nzBordered = false;\n    this.nzSize = 'default';\n    this.nzShowSizeChanger = false;\n    this.nzHideOnSinglePage = false;\n    this.nzShowQuickJumper = false;\n    this.nzSimple = false;\n    this.nzPageSizeChange = new EventEmitter();\n    this.nzPageIndexChange = new EventEmitter();\n    this.nzQueryParams = new EventEmitter();\n    this.nzCurrentPageDataChange = new EventEmitter();\n    this.nzCustomColumnChange = new EventEmitter();\n    /** public data for ngFor tr */\n    this.data = [];\n    this.scrollX = null;\n    this.scrollY = null;\n    this.theadTemplate = null;\n    this.listOfAutoColWidth = [];\n    this.listOfManualColWidth = [];\n    this.hasFixLeft = false;\n    this.hasFixRight = false;\n    this.showPagination = true;\n    this.destroy$ = new Subject();\n    this.templateMode$ = new BehaviorSubject(false);\n    this.dir = 'ltr';\n    this.verticalScrollBarWidth = 0;\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnInit() {\n    const {\n      pageIndexDistinct$,\n      pageSizeDistinct$,\n      listOfCurrentPageData$,\n      total$,\n      queryParams$,\n      listOfCustomColumn$\n    } = this.nzTableDataService;\n    const {\n      theadTemplate$,\n      hasFixLeft$,\n      hasFixRight$\n    } = this.nzTableStyleService;\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    queryParams$.pipe(takeUntil(this.destroy$)).subscribe(this.nzQueryParams);\n    pageIndexDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageIndex => {\n      if (pageIndex !== this.nzPageIndex) {\n        this.nzPageIndex = pageIndex;\n        this.nzPageIndexChange.next(pageIndex);\n      }\n    });\n    pageSizeDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageSize => {\n      if (pageSize !== this.nzPageSize) {\n        this.nzPageSize = pageSize;\n        this.nzPageSizeChange.next(pageSize);\n      }\n    });\n    total$.pipe(takeUntil(this.destroy$), filter(() => this.nzFrontPagination)).subscribe(total => {\n      if (total !== this.nzTotal) {\n        this.nzTotal = total;\n        this.cdr.markForCheck();\n      }\n    });\n    listOfCurrentPageData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.data = data;\n      this.nzCurrentPageDataChange.next(data);\n      this.cdr.markForCheck();\n    });\n    listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.nzCustomColumn = data;\n      this.nzCustomColumnChange.next(data);\n      this.cdr.markForCheck();\n    });\n    theadTemplate$.pipe(takeUntil(this.destroy$)).subscribe(theadTemplate => {\n      this.theadTemplate = theadTemplate;\n      this.cdr.markForCheck();\n    });\n    hasFixLeft$.pipe(takeUntil(this.destroy$)).subscribe(hasFixLeft => {\n      this.hasFixLeft = hasFixLeft;\n      this.cdr.markForCheck();\n    });\n    hasFixRight$.pipe(takeUntil(this.destroy$)).subscribe(hasFixRight => {\n      this.hasFixRight = hasFixRight;\n      this.cdr.markForCheck();\n    });\n    combineLatest([total$, this.templateMode$]).pipe(map(([total, templateMode]) => total === 0 && !templateMode), takeUntil(this.destroy$)).subscribe(empty => {\n      this.nzTableStyleService.setShowEmpty(empty);\n    });\n    this.verticalScrollBarWidth = measureScrollbar('vertical');\n    this.nzTableStyleService.listOfListOfThWidthPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n      this.listOfAutoColWidth = listOfWidth;\n      this.cdr.markForCheck();\n    });\n    this.nzTableStyleService.manualWidthConfigPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n      this.listOfManualColWidth = listOfWidth;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzScroll,\n      nzPageIndex,\n      nzPageSize,\n      nzFrontPagination,\n      nzData,\n      nzCustomColumn,\n      nzWidthConfig,\n      nzNoResult,\n      nzTemplateMode\n    } = changes;\n    if (nzPageIndex) {\n      this.nzTableDataService.updatePageIndex(this.nzPageIndex);\n    }\n    if (nzPageSize) {\n      this.nzTableDataService.updatePageSize(this.nzPageSize);\n    }\n    if (nzData) {\n      this.nzData = this.nzData || [];\n      this.nzTableDataService.updateListOfData(this.nzData);\n    }\n    if (nzCustomColumn) {\n      this.nzCustomColumn = this.nzCustomColumn || [];\n      this.nzTableDataService.updateListOfCustomColumn(this.nzCustomColumn);\n    }\n    if (nzFrontPagination) {\n      this.nzTableDataService.updateFrontPagination(this.nzFrontPagination);\n    }\n    if (nzScroll) {\n      this.setScrollOnChanges();\n    }\n    if (nzWidthConfig) {\n      this.nzTableStyleService.setTableWidthConfig(this.nzWidthConfig);\n    }\n    if (nzTemplateMode) {\n      this.templateMode$.next(this.nzTemplateMode);\n    }\n    if (nzNoResult) {\n      this.nzTableStyleService.setNoResult(this.nzNoResult);\n    }\n    this.updateShowPagination();\n  }\n  ngAfterViewInit() {\n    this.nzResizeObserver.observe(this.elementRef).pipe(map(([entry]) => {\n      const {\n        width\n      } = entry.target.getBoundingClientRect();\n      const scrollBarWidth = this.scrollY ? this.verticalScrollBarWidth : 0;\n      return Math.floor(width - scrollBarWidth);\n    }), takeUntil(this.destroy$)).subscribe(this.nzTableStyleService.hostWidth$);\n    if (this.nzTableInnerScrollComponent && this.nzTableInnerScrollComponent.cdkVirtualScrollViewport) {\n      this.cdkVirtualScrollViewport = this.nzTableInnerScrollComponent.cdkVirtualScrollViewport;\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setScrollOnChanges() {\n    this.scrollX = this.nzScroll && this.nzScroll.x || null;\n    this.scrollY = this.nzScroll && this.nzScroll.y || null;\n    this.nzTableStyleService.setScroll(this.scrollX, this.scrollY);\n  }\n  updateShowPagination() {\n    this.showPagination = this.nzHideOnSinglePage && this.nzData.length > this.nzPageSize || this.nzData.length > 0 && !this.nzHideOnSinglePage || !this.nzFrontPagination && this.nzTotal > this.nzPageSize;\n  }\n  static {\n    this.ɵfac = function NzTableComponent_Factory(t) {\n      return new (t || NzTableComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.NzResizeObserver), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzTableStyleService), i0.ɵɵdirectiveInject(NzTableDataService), i0.ɵɵdirectiveInject(i5$1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableComponent,\n      selectors: [[\"nz-table\"]],\n      contentQueries: function NzTableComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTableVirtualScrollDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzVirtualScrollDirective = _t.first);\n        }\n      },\n      viewQuery: function NzTableComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzTableInnerScrollComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzTableInnerScrollComponent = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-table-wrapper\"],\n      hostVars: 4,\n      hostBindings: function NzTableComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-table-custom-column\", ctx.nzCustomColumn.length);\n        }\n      },\n      inputs: {\n        nzTableLayout: \"nzTableLayout\",\n        nzShowTotal: \"nzShowTotal\",\n        nzItemRender: \"nzItemRender\",\n        nzTitle: \"nzTitle\",\n        nzFooter: \"nzFooter\",\n        nzNoResult: \"nzNoResult\",\n        nzPageSizeOptions: \"nzPageSizeOptions\",\n        nzVirtualItemSize: \"nzVirtualItemSize\",\n        nzVirtualMaxBufferPx: \"nzVirtualMaxBufferPx\",\n        nzVirtualMinBufferPx: \"nzVirtualMinBufferPx\",\n        nzVirtualForTrackBy: \"nzVirtualForTrackBy\",\n        nzLoadingDelay: \"nzLoadingDelay\",\n        nzPageIndex: \"nzPageIndex\",\n        nzPageSize: \"nzPageSize\",\n        nzTotal: \"nzTotal\",\n        nzWidthConfig: \"nzWidthConfig\",\n        nzData: \"nzData\",\n        nzCustomColumn: \"nzCustomColumn\",\n        nzPaginationPosition: \"nzPaginationPosition\",\n        nzScroll: \"nzScroll\",\n        nzPaginationType: \"nzPaginationType\",\n        nzFrontPagination: \"nzFrontPagination\",\n        nzTemplateMode: \"nzTemplateMode\",\n        nzShowPagination: \"nzShowPagination\",\n        nzLoading: \"nzLoading\",\n        nzOuterBordered: \"nzOuterBordered\",\n        nzLoadingIndicator: \"nzLoadingIndicator\",\n        nzBordered: \"nzBordered\",\n        nzSize: \"nzSize\",\n        nzShowSizeChanger: \"nzShowSizeChanger\",\n        nzHideOnSinglePage: \"nzHideOnSinglePage\",\n        nzShowQuickJumper: \"nzShowQuickJumper\",\n        nzSimple: \"nzSimple\"\n      },\n      outputs: {\n        nzPageSizeChange: \"nzPageSizeChange\",\n        nzPageIndexChange: \"nzPageIndexChange\",\n        nzQueryParams: \"nzQueryParams\",\n        nzCurrentPageDataChange: \"nzCurrentPageDataChange\",\n        nzCustomColumnChange: \"nzCustomColumnChange\"\n      },\n      exportAs: [\"nzTable\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzTableStyleService, NzTableDataService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 14,\n      vars: 27,\n      consts: [[\"tableMainElement\", \"\"], [\"defaultTemplate\", \"\"], [\"paginationTemplate\", \"\"], [\"contentTemplate\", \"\"], [3, \"nzDelay\", \"nzSpinning\", \"nzIndicator\"], [4, \"ngIf\"], [1, \"ant-table\"], [3, \"title\", 4, \"ngIf\"], [3, \"data\", \"scrollX\", \"scrollY\", \"contentTemplate\", \"listOfColWidth\", \"theadTemplate\", \"verticalScrollBarWidth\", \"virtualTemplate\", \"virtualItemSize\", \"virtualMaxBufferPx\", \"virtualMinBufferPx\", \"tableMainElement\", \"virtualForTrackBy\", 4, \"ngIf\", \"ngIfElse\"], [3, \"footer\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [3, \"title\"], [3, \"data\", \"scrollX\", \"scrollY\", \"contentTemplate\", \"listOfColWidth\", \"theadTemplate\", \"verticalScrollBarWidth\", \"virtualTemplate\", \"virtualItemSize\", \"virtualMaxBufferPx\", \"virtualMinBufferPx\", \"tableMainElement\", \"virtualForTrackBy\"], [3, \"tableLayout\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\"], [3, \"footer\"], [\"class\", \"ant-table-pagination ant-table-pagination-right\", 3, \"hidden\", \"nzShowSizeChanger\", \"nzPageSizeOptions\", \"nzItemRender\", \"nzShowQuickJumper\", \"nzHideOnSinglePage\", \"nzShowTotal\", \"nzSize\", \"nzPageSize\", \"nzTotal\", \"nzSimple\", \"nzPageIndex\", \"nzPageSizeChange\", \"nzPageIndexChange\", 4, \"ngIf\"], [1, \"ant-table-pagination\", \"ant-table-pagination-right\", 3, \"nzPageSizeChange\", \"nzPageIndexChange\", \"hidden\", \"nzShowSizeChanger\", \"nzPageSizeOptions\", \"nzItemRender\", \"nzShowQuickJumper\", \"nzHideOnSinglePage\", \"nzShowTotal\", \"nzSize\", \"nzPageSize\", \"nzTotal\", \"nzSimple\", \"nzPageIndex\"]],\n      template: function NzTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"nz-spin\", 4);\n          i0.ɵɵtemplate(1, NzTableComponent_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementStart(2, \"div\", 6, 0);\n          i0.ɵɵtemplate(4, NzTableComponent_nz_table_title_footer_4_Template, 1, 1, \"nz-table-title-footer\", 7)(5, NzTableComponent_nz_table_inner_scroll_5_Template, 1, 13, \"nz-table-inner-scroll\", 8)(6, NzTableComponent_ng_template_6_Template, 1, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(8, NzTableComponent_nz_table_title_footer_8_Template, 1, 1, \"nz-table-title-footer\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, NzTableComponent_ng_container_9_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, NzTableComponent_ng_template_10_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(12, NzTableComponent_ng_template_12_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const defaultTemplate_r6 = i0.ɵɵreference(7);\n          i0.ɵɵproperty(\"nzDelay\", ctx.nzLoadingDelay)(\"nzSpinning\", ctx.nzLoading)(\"nzIndicator\", ctx.nzLoadingIndicator);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzPaginationPosition === \"both\" || ctx.nzPaginationPosition === \"top\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ant-table-rtl\", ctx.dir === \"rtl\")(\"ant-table-fixed-header\", ctx.nzData.length && ctx.scrollY)(\"ant-table-fixed-column\", ctx.scrollX)(\"ant-table-has-fix-left\", ctx.hasFixLeft)(\"ant-table-has-fix-right\", ctx.hasFixRight)(\"ant-table-bordered\", ctx.nzBordered)(\"nz-table-out-bordered\", ctx.nzOuterBordered && !ctx.nzBordered)(\"ant-table-middle\", ctx.nzSize === \"middle\")(\"ant-table-small\", ctx.nzSize === \"small\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollY || ctx.scrollX)(\"ngIfElse\", defaultTemplate_r6);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzFooter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzPaginationPosition === \"both\" || ctx.nzPaginationPosition === \"bottom\");\n        }\n      },\n      dependencies: [NzSpinComponent, NgIf, NgTemplateOutlet, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableInnerDefaultComponent, NzPaginationModule, i6$1.NzPaginationComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzFrontPagination\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzTemplateMode\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzShowPagination\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzLoading\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzOuterBordered\", void 0);\n__decorate([WithConfig()], NzTableComponent.prototype, \"nzLoadingIndicator\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzBordered\", void 0);\n__decorate([WithConfig()], NzTableComponent.prototype, \"nzSize\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzShowSizeChanger\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzHideOnSinglePage\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzShowQuickJumper\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzSimple\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table',\n      exportAs: 'nzTable',\n      providers: [NzTableStyleService, NzTableDataService],\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <nz-spin [nzDelay]=\"nzLoadingDelay\" [nzSpinning]=\"nzLoading\" [nzIndicator]=\"nzLoadingIndicator\">\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'top'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n      <div\n        #tableMainElement\n        class=\"ant-table\"\n        [class.ant-table-rtl]=\"dir === 'rtl'\"\n        [class.ant-table-fixed-header]=\"nzData.length && scrollY\"\n        [class.ant-table-fixed-column]=\"scrollX\"\n        [class.ant-table-has-fix-left]=\"hasFixLeft\"\n        [class.ant-table-has-fix-right]=\"hasFixRight\"\n        [class.ant-table-bordered]=\"nzBordered\"\n        [class.nz-table-out-bordered]=\"nzOuterBordered && !nzBordered\"\n        [class.ant-table-middle]=\"nzSize === 'middle'\"\n        [class.ant-table-small]=\"nzSize === 'small'\"\n      >\n        <nz-table-title-footer [title]=\"nzTitle\" *ngIf=\"nzTitle\"></nz-table-title-footer>\n        <nz-table-inner-scroll\n          *ngIf=\"scrollY || scrollX; else defaultTemplate\"\n          [data]=\"data\"\n          [scrollX]=\"scrollX\"\n          [scrollY]=\"scrollY\"\n          [contentTemplate]=\"contentTemplate\"\n          [listOfColWidth]=\"listOfAutoColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n          [verticalScrollBarWidth]=\"verticalScrollBarWidth\"\n          [virtualTemplate]=\"nzVirtualScrollDirective ? nzVirtualScrollDirective.templateRef : null\"\n          [virtualItemSize]=\"nzVirtualItemSize\"\n          [virtualMaxBufferPx]=\"nzVirtualMaxBufferPx\"\n          [virtualMinBufferPx]=\"nzVirtualMinBufferPx\"\n          [tableMainElement]=\"tableMainElement\"\n          [virtualForTrackBy]=\"nzVirtualForTrackBy\"\n        ></nz-table-inner-scroll>\n        <ng-template #defaultTemplate>\n          <nz-table-inner-default\n            [tableLayout]=\"nzTableLayout\"\n            [listOfColWidth]=\"listOfManualColWidth\"\n            [theadTemplate]=\"theadTemplate\"\n            [contentTemplate]=\"contentTemplate\"\n          ></nz-table-inner-default>\n        </ng-template>\n        <nz-table-title-footer [footer]=\"nzFooter\" *ngIf=\"nzFooter\"></nz-table-title-footer>\n      </div>\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'bottom'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n    </nz-spin>\n    <ng-template #paginationTemplate>\n      <nz-pagination\n        *ngIf=\"nzShowPagination && data.length\"\n        [hidden]=\"!showPagination\"\n        class=\"ant-table-pagination ant-table-pagination-right\"\n        [nzShowSizeChanger]=\"nzShowSizeChanger\"\n        [nzPageSizeOptions]=\"nzPageSizeOptions\"\n        [nzItemRender]=\"nzItemRender!\"\n        [nzShowQuickJumper]=\"nzShowQuickJumper\"\n        [nzHideOnSinglePage]=\"nzHideOnSinglePage\"\n        [nzShowTotal]=\"nzShowTotal\"\n        [nzSize]=\"nzPaginationType === 'small' ? 'small' : nzSize === 'default' ? 'default' : 'small'\"\n        [nzPageSize]=\"nzPageSize\"\n        [nzTotal]=\"nzTotal\"\n        [nzSimple]=\"nzSimple\"\n        [nzPageIndex]=\"nzPageIndex\"\n        (nzPageSizeChange)=\"onPageSizeChange($event)\"\n        (nzPageIndexChange)=\"onPageIndexChange($event)\"\n      ></nz-pagination>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      host: {\n        class: 'ant-table-wrapper',\n        '[class.ant-table-wrapper-rtl]': 'dir === \"rtl\"',\n        '[class.ant-table-custom-column]': `nzCustomColumn.length`\n      },\n      imports: [NzSpinComponent, NgIf, NgTemplateOutlet, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableInnerDefaultComponent, NzPaginationModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1$2.NzResizeObserver\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzTableStyleService\n  }, {\n    type: NzTableDataService\n  }, {\n    type: i5$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzTableLayout: [{\n      type: Input\n    }],\n    nzShowTotal: [{\n      type: Input\n    }],\n    nzItemRender: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzFooter: [{\n      type: Input\n    }],\n    nzNoResult: [{\n      type: Input\n    }],\n    nzPageSizeOptions: [{\n      type: Input\n    }],\n    nzVirtualItemSize: [{\n      type: Input\n    }],\n    nzVirtualMaxBufferPx: [{\n      type: Input\n    }],\n    nzVirtualMinBufferPx: [{\n      type: Input\n    }],\n    nzVirtualForTrackBy: [{\n      type: Input\n    }],\n    nzLoadingDelay: [{\n      type: Input\n    }],\n    nzPageIndex: [{\n      type: Input\n    }],\n    nzPageSize: [{\n      type: Input\n    }],\n    nzTotal: [{\n      type: Input\n    }],\n    nzWidthConfig: [{\n      type: Input\n    }],\n    nzData: [{\n      type: Input\n    }],\n    nzCustomColumn: [{\n      type: Input\n    }],\n    nzPaginationPosition: [{\n      type: Input\n    }],\n    nzScroll: [{\n      type: Input\n    }],\n    nzPaginationType: [{\n      type: Input\n    }],\n    nzFrontPagination: [{\n      type: Input\n    }],\n    nzTemplateMode: [{\n      type: Input\n    }],\n    nzShowPagination: [{\n      type: Input\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzOuterBordered: [{\n      type: Input\n    }],\n    nzLoadingIndicator: [{\n      type: Input\n    }],\n    nzBordered: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzShowSizeChanger: [{\n      type: Input\n    }],\n    nzHideOnSinglePage: [{\n      type: Input\n    }],\n    nzShowQuickJumper: [{\n      type: Input\n    }],\n    nzSimple: [{\n      type: Input\n    }],\n    nzPageSizeChange: [{\n      type: Output\n    }],\n    nzPageIndexChange: [{\n      type: Output\n    }],\n    nzQueryParams: [{\n      type: Output\n    }],\n    nzCurrentPageDataChange: [{\n      type: Output\n    }],\n    nzCustomColumnChange: [{\n      type: Output\n    }],\n    nzVirtualScrollDirective: [{\n      type: ContentChild,\n      args: [NzTableVirtualScrollDirective, {\n        static: false\n      }]\n    }],\n    nzTableInnerScrollComponent: [{\n      type: ViewChild,\n      args: [NzTableInnerScrollComponent]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrDirective {\n  constructor(nzTableStyleService) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.destroy$ = new Subject();\n    this.listOfFixedColumns$ = new ReplaySubject(1);\n    this.listOfColumns$ = new ReplaySubject(1);\n    this.listOfFixedColumnsChanges$ = this.listOfFixedColumns$.pipe(switchMap(list => merge(...[this.listOfFixedColumns$, ...list.map(c => c.changes$)]).pipe(mergeMap(() => this.listOfFixedColumns$))), takeUntil(this.destroy$));\n    this.listOfFixedLeftColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzLeft !== false)));\n    this.listOfFixedRightColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzRight !== false)));\n    this.listOfColumnsChanges$ = this.listOfColumns$.pipe(switchMap(list => merge(...[this.listOfColumns$, ...list.map(c => c.changes$)]).pipe(mergeMap(() => this.listOfColumns$))), takeUntil(this.destroy$));\n    this.isInsideTable = false;\n    this.isInsideTable = !!nzTableStyleService;\n  }\n  ngAfterContentInit() {\n    if (this.nzTableStyleService) {\n      this.listOfCellFixedDirective.changes.pipe(startWith(this.listOfCellFixedDirective), takeUntil(this.destroy$)).subscribe(this.listOfFixedColumns$);\n      this.listOfNzThDirective.changes.pipe(startWith(this.listOfNzThDirective), takeUntil(this.destroy$)).subscribe(this.listOfColumns$);\n      /** set last left and first right **/\n      this.listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeft => {\n        listOfFixedLeft.forEach(cell => cell.setIsLastLeft(cell === listOfFixedLeft[listOfFixedLeft.length - 1]));\n      });\n      this.listOfFixedRightColumnChanges$.subscribe(listOfFixedRight => {\n        listOfFixedRight.forEach(cell => cell.setIsFirstRight(cell === listOfFixedRight[0]));\n      });\n      /** calculate fixed nzLeft and nzRight **/\n      combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedLeftColumnChanges$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfAutoWidth, listOfLeftCell]) => {\n        listOfLeftCell.forEach((cell, index) => {\n          if (cell.isAutoLeft) {\n            const currentArray = listOfLeftCell.slice(0, index);\n            const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n            const width = listOfAutoWidth.slice(0, count).reduce((pre, cur) => pre + cur, 0);\n            cell.setAutoLeftWidth(`${width}px`);\n          }\n        });\n      });\n      combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedRightColumnChanges$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfAutoWidth, listOfRightCell]) => {\n        listOfRightCell.forEach((_, index) => {\n          const cell = listOfRightCell[listOfRightCell.length - index - 1];\n          if (cell.isAutoRight) {\n            const currentArray = listOfRightCell.slice(listOfRightCell.length - index, listOfRightCell.length);\n            const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n            const width = listOfAutoWidth.slice(listOfAutoWidth.length - count, listOfAutoWidth.length).reduce((pre, cur) => pre + cur, 0);\n            cell.setAutoRightWidth(`${width}px`);\n          }\n        });\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTrDirective_Factory(t) {\n      return new (t || NzTrDirective)(i0.ɵɵdirectiveInject(NzTableStyleService, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTrDirective,\n      selectors: [[\"tr\", 3, \"mat-row\", \"\", 3, \"mat-header-row\", \"\", 3, \"nz-table-measure-row\", \"\", 3, \"nzExpand\", \"\", 3, \"nz-table-fixed-row\", \"\"]],\n      contentQueries: function NzTrDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzThMeasureDirective, 4);\n          i0.ɵɵcontentQuery(dirIndex, NzCellFixedDirective, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzThDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfCellFixedDirective = _t);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function NzTrDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-row\", ctx.isInsideTable);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr:not([mat-row]):not([mat-header-row]):not([nz-table-measure-row]):not([nzExpand]):not([nz-table-fixed-row])',\n      host: {\n        '[class.ant-table-row]': 'isInsideTable'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    listOfNzThDirective: [{\n      type: ContentChildren,\n      args: [NzThMeasureDirective]\n    }],\n    listOfCellFixedDirective: [{\n      type: ContentChildren,\n      args: [NzCellFixedDirective]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTheadComponent {\n  constructor(elementRef, renderer, nzTableStyleService, nzTableDataService) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.nzTableStyleService = nzTableStyleService;\n    this.nzTableDataService = nzTableDataService;\n    this.destroy$ = new Subject();\n    this.isInsideTable = false;\n    this.nzSortOrderChange = new EventEmitter();\n    this.isInsideTable = !!this.nzTableStyleService;\n  }\n  ngOnInit() {\n    if (this.nzTableStyleService) {\n      this.nzTableStyleService.setTheadTemplate(this.templateRef);\n    }\n  }\n  ngAfterContentInit() {\n    if (this.nzTableStyleService) {\n      const firstTableRow$ = this.listOfNzTrDirective.changes.pipe(startWith(this.listOfNzTrDirective), map(item => item && item.first));\n      const listOfColumnsChanges$ = firstTableRow$.pipe(switchMap(firstTableRow => firstTableRow ? firstTableRow.listOfColumnsChanges$ : EMPTY), takeUntil(this.destroy$));\n      listOfColumnsChanges$.subscribe(data => this.nzTableStyleService.setListOfTh(data));\n      /** TODO: need reset the measure row when scrollX change **/\n      this.nzTableStyleService.enableAutoMeasure$.pipe(switchMap(enable => enable ? listOfColumnsChanges$ : of([]))).pipe(takeUntil(this.destroy$)).subscribe(data => this.nzTableStyleService.setListOfMeasureColumn(data));\n      const listOfFixedLeftColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => firstTr ? firstTr.listOfFixedLeftColumnChanges$ : EMPTY), takeUntil(this.destroy$));\n      const listOfFixedRightColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => firstTr ? firstTr.listOfFixedRightColumnChanges$ : EMPTY), takeUntil(this.destroy$));\n      listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeftColumn => {\n        this.nzTableStyleService.setHasFixLeft(listOfFixedLeftColumn.length !== 0);\n      });\n      listOfFixedRightColumnChanges$.subscribe(listOfFixedRightColumn => {\n        this.nzTableStyleService.setHasFixRight(listOfFixedRightColumn.length !== 0);\n      });\n    }\n    if (this.nzTableDataService) {\n      const listOfColumn$ = this.listOfNzThAddOnComponent.changes.pipe(startWith(this.listOfNzThAddOnComponent));\n      const manualSort$ = listOfColumn$.pipe(switchMap(() => merge(...this.listOfNzThAddOnComponent.map(th => th.manualClickOrder$))), takeUntil(this.destroy$));\n      manualSort$.subscribe(data => {\n        const emitValue = {\n          key: data.nzColumnKey,\n          value: data.sortOrder\n        };\n        this.nzSortOrderChange.emit(emitValue);\n        if (data.nzSortFn && data.nzSortPriority === false) {\n          this.listOfNzThAddOnComponent.filter(th => th !== data).forEach(th => th.clearSortOrder());\n        }\n      });\n      const listOfCalcOperator$ = listOfColumn$.pipe(switchMap(list => merge(...[listOfColumn$, ...list.map(c => c.calcOperatorChange$)]).pipe(mergeMap(() => listOfColumn$))), map(list => list.filter(item => !!item.nzSortFn || !!item.nzFilterFn).map(item => {\n        const {\n          nzSortFn,\n          sortOrder,\n          nzFilterFn,\n          nzFilterValue,\n          nzSortPriority,\n          nzColumnKey\n        } = item;\n        return {\n          key: nzColumnKey,\n          sortFn: nzSortFn,\n          sortPriority: nzSortPriority,\n          sortOrder: sortOrder,\n          filterFn: nzFilterFn,\n          filterValue: nzFilterValue\n        };\n      })),\n      // TODO: after checked error here\n      delay(0), takeUntil(this.destroy$));\n      listOfCalcOperator$.subscribe(list => {\n        this.nzTableDataService.listOfCalcOperator$.next(list);\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this.nzTableStyleService) {\n      this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTheadComponent_Factory(t) {\n      return new (t || NzTheadComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzTableStyleService, 8), i0.ɵɵdirectiveInject(NzTableDataService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTheadComponent,\n      selectors: [[\"thead\", 9, \"ant-table-thead\"]],\n      contentQueries: function NzTheadComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTrDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzThAddOnComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzTrDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzThAddOnComponent = _t);\n        }\n      },\n      viewQuery: function NzTheadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c13, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      outputs: {\n        nzSortOrderChange: \"nzSortOrderChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 1,\n      consts: [[\"contentTemplate\", \"\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzTheadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTheadComponent_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzTheadComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isInsideTable);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTheadComponent, [{\n    type: Component,\n    args: [{\n      selector: 'thead:not(.ant-table-thead)',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    <ng-container *ngIf=\"!isInsideTable\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </ng-container>\n  `,\n      imports: [NgIf, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: NzTableDataService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: ['contentTemplate', {\n        static: true\n      }]\n    }],\n    listOfNzTrDirective: [{\n      type: ContentChildren,\n      args: [NzTrDirective, {\n        descendants: true\n      }]\n    }],\n    listOfNzThAddOnComponent: [{\n      type: ContentChildren,\n      args: [NzThAddOnComponent, {\n        descendants: true\n      }]\n    }],\n    nzSortOrderChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrExpandDirective {\n  constructor() {\n    this.nzExpand = true;\n  }\n  static {\n    this.ɵfac = function NzTrExpandDirective_Factory(t) {\n      return new (t || NzTrExpandDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTrExpandDirective,\n      selectors: [[\"tr\", \"nzExpand\", \"\"]],\n      hostAttrs: [1, \"ant-table-expanded-row\"],\n      hostVars: 1,\n      hostBindings: function NzTrExpandDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"hidden\", !ctx.nzExpand);\n        }\n      },\n      inputs: {\n        nzExpand: \"nzExpand\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrExpandDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr[nzExpand]',\n      host: {\n        class: 'ant-table-expanded-row',\n        '[hidden]': `!nzExpand`\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    nzExpand: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableModule {\n  static {\n    this.ɵfac = function NzTableModule_Factory(t) {\n      return new (t || NzTableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzTableModule,\n      imports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTrExpandDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzTableContentComponent, NzTableTitleFooterComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTrMeasureComponent, NzRowIndentDirective, NzRowExpandButtonDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzCellEllipsisDirective, NzFilterTriggerComponent, NzTableFixedRowComponent, NzThSelectionComponent],\n      exports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzTrExpandDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzCellEllipsisDirective, NzTableFixedRowComponent, NzThSelectionComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzTableComponent, NzThAddOnComponent, NzTdAddOnComponent, NzTbodyComponent, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzFilterTriggerComponent, NzThSelectionComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTrExpandDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzTableContentComponent, NzTableTitleFooterComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTrMeasureComponent, NzRowIndentDirective, NzRowExpandButtonDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzCellEllipsisDirective, NzFilterTriggerComponent, NzTableFixedRowComponent, NzThSelectionComponent],\n      exports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzTrExpandDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzCellEllipsisDirective, NzTableFixedRowComponent, NzThSelectionComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCellAlignDirective, NzCellBreakWordDirective, NzCellEllipsisDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzRowExpandButtonDirective, NzRowIndentDirective, NzTableCellDirective, NzTableComponent, NzTableContentComponent, NzTableDataService, NzTableFilterComponent, NzTableFixedRowComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTableModule, NzTableSelectionComponent, NzTableSortersComponent, NzTableStyleService, NzTableTitleFooterComponent, NzTableVirtualScrollDirective, NzTbodyComponent, NzTdAddOnComponent, NzThAddOnComponent, NzThMeasureDirective, NzThSelectionComponent, NzTheadComponent, NzTrDirective, NzTrExpandDirective, NzTrMeasureComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,eAAe,EAAE;AAC9B,IAAM,aAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,mBAAmB,SAAS,yEAAyE,QAAQ;AACzH,YAAM,YAAe,cAAc,GAAG,EAAE;AACxC,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,WAAW,MAAM,CAAC;AAAA,IACjE,CAAC;AACD,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,UAAU,YAAY,OAAO,UAAU,EAAE,aAAa,UAAU,OAAO;AACnG,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,UAAU,KAAK;AAAA,EACtC;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,eAAe,CAAC;AAAA,EACvB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,aAAa,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,eAAe,OAAO;AACpB,SAAK,aAAa,OAAO,KAAK,aAAa,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC9D;AAAA,EACA,WAAW;AACT,UAAM,qBAAqB,KAAK,aAAa,OAAO,UAAQ,KAAK,SAAS,EAAE,IAAI,UAAQ,KAAK,OAAO;AACpG,SAAK,WAAW,KAAK,kBAAkB;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,WAAW,CAAC,GAAG,oBAAoB;AAAA,MACnC,SAAS;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,mBAAmB,SAAS;AAC1B,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,YAAY;AACjB,WAAK,SAAS,KAAK,SAAS;AAC5B,WAAK,gBAAgB,KAAK,KAAK,SAAS;AACxC,UAAI,KAAK,4BAA4B;AACnC,aAAK,2BAA2B,SAAS;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,aAAa,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,YAAY,QAAQ,YAAY,4BAA4B,KAAK,cAAc,gBAAgB,qBAAqB;AAClH,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,6BAA6B;AAClC,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW;AACT,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,QAAI,KAAK,4BAA4B;AACnC,WAAK,2BAA2B,YAAY,IAAI;AAAA,IAClD;AACA,SAAK,eAAe,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC/E,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAClG,cAAM,eAAe;AACrB,aAAK,MAAM;AACX,YAAI,KAAK,YAAY;AACnB;AAAA,QACF;AACA,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,mBAAmB,CAAC,KAAK,SAAS;AACvC,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AACD,gBAAU,KAAK,aAAa,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS,MAAM,gBAAgB,CAAC;AAAA,IAC/H,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,UAAU;AAChD,QAAI,KAAK,4BAA4B;AACnC,WAAK,2BAA2B,eAAe,IAAI;AAAA,IACrD;AACA,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,4BAA4B,CAAC,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,qBAAqB,CAAC,CAAC;AAAA,IACjV;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,WAAW,SAAS,0BAA0B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qCAAqC,CAAC,CAAC,IAAI,mBAAmB,EAAE,gCAAgC,IAAI,SAAS,EAAE,oBAAoB,IAAI,QAAQ,KAAK;AAAA,QACrK;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,oBAAmB;AAAA,QACjD,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,QAAQ,YAAY,GAAG,sBAAsB,GAAG,iBAAiB,WAAW,WAAW,UAAU,GAAG,CAAC,GAAG,oBAAoB,CAAC;AAAA,MAClL,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,SAAS,GAAG,CAAC;AAChD,UAAG,WAAW,iBAAiB,SAAS,4DAA4D,QAAQ;AAC1G,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,UACtD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,MAAM;AAC3B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,wBAAwB,IAAI,aAAa,CAAC,IAAI,eAAe,EAAE,yBAAyB,IAAI,UAAU,EAAE,8BAA8B,IAAI,eAAe;AACxK,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,SAAS,EAAE,WAAW,IAAI,SAAS,EAAE,YAAY,IAAI,UAAU;AAC5F,UAAG,YAAY,aAAa,IAAI,cAAc,cAAc,IAAI,EAAE,MAAM,IAAI,IAAI;AAAA,QAClF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,aAAgB,8BAAiC,iBAAoB,OAAO;AAAA,MAC3F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,eAAe,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,mBAAmB,MAAM;AACrF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,aAAa,MAAM;AAAA,CAC9E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,mBAAmB;AAAA,QACjD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6CAA6C;AAAA,QAC7C,wCAAwC;AAAA,QACxC,4BAA4B;AAAA,MAC9B;AAAA,MACA,SAAS,CAAC,WAAW;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,gBAAgB,QAAQ,SAAS;AAC/B,WAAO,UAAU;AACjB,SAAK,SAAS,KAAK,OAAO;AAAA,EAC5B;AAAA,EACA,YAAY,YAAY,cAAc,KAAK,gBAAgB;AACzD,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,WAAW;AACT,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,eAAe,KAAK,UAAU;AAChD,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,UAAU;AACf,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA6B,kBAAqB,UAAU,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAC/M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,CAAC,GAAG,oBAAoB;AAAA,MACnC,UAAU;AAAA,MACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,QAAQ,KAAK;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,yBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,IAAI,GAAG,2BAA2B,GAAG,cAAc,WAAW,GAAG,CAAC,eAAe,IAAI,GAAG,2BAA2B,GAAG,mBAAmB,cAAc,WAAW,CAAC;AAAA,MAC5L,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,iBAAiB,GAAG,yCAAyC,GAAG,GAAG,SAAS,GAAG,UAAU;AAAA,QAC9F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,cAAc,CAAC,mBAAmB;AAAA,MAClC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,yBAAyB,WAAW,cAAc,MAAM;AAAA,CACpF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,wBAAwB;AAAA,QACtD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,kCAAkC;AAAA,MACpC;AAAA,MACA,SAAS,CAAC,mBAAmB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,MACnF,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,IACrF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,qBAAqB,wBAAwB;AAAA,IACzD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,MACnF,SAAS,CAAC,qBAAqB,0BAA0B,0BAA0B;AAAA,IACrF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC9hBH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,mBAAmB,cAAc,OAAO,IAAI,eAAe,QAAQ;AAAA,EACnF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,yBAAyB;AACnC,SAAK,0BAA0B;AAE/B,SAAK,mBAAmB,oBAAI,IAAI;AAAA,EAClC;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,QAAQ,CAAC,GAAG,YAAY,KAAK,gBAAgB,OAAO,CAAC;AAAA,EAC7E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,SAAS,KAAK,eAAe,OAAO;AAC1C,YAAM,eAAe,OAAO,UAAU,QAAQ;AAC9C,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,QAAI,CAAC,KAAK,iBAAiB,IAAI,OAAO,GAAG;AACvC,YAAM,SAAS,IAAI,QAAQ;AAC3B,YAAM,WAAW,KAAK,wBAAwB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACxF,UAAI,UAAU;AACZ,iBAAS,QAAQ,OAAO;AAAA,MAC1B;AACA,WAAK,iBAAiB,IAAI,SAAS;AAAA,QACjC;AAAA,QACA;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,WAAK,iBAAiB,IAAI,OAAO,EAAE;AAAA,IACrC;AACA,WAAO,KAAK,iBAAiB,IAAI,OAAO,EAAE;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,SAAS;AACxB,QAAI,KAAK,iBAAiB,IAAI,OAAO,GAAG;AACtC,WAAK,iBAAiB,IAAI,OAAO,EAAE;AACnC,UAAI,CAAC,KAAK,iBAAiB,IAAI,OAAO,EAAE,OAAO;AAC7C,aAAK,gBAAgB,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,SAAS;AACvB,QAAI,KAAK,iBAAiB,IAAI,OAAO,GAAG;AACtC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,iBAAiB,IAAI,OAAO;AACrC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,iBAAiB,OAAO,OAAO;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,SAAS,uBAAuB,CAAC;AAAA,IACzE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY;AACV,SAAK,YAAY;AACjB,SAAK,sBAAsB,KAAK,iBAAiB,QAAQ,KAAK,UAAU,EAAE,UAAU,KAAK,eAAe;AAAA,EAC1G;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB,YAAY;AAAA,EACxC;AAAA,EACA,YAAY,kBAAkB,YAAY;AACxC,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,2BAA2B;AAChC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,uBAAuB,CAAC,KAAK,0BAA0B;AAC/D,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,iBAAiB;AACnB,UAAI,KAAK,0BAA0B;AACjC,aAAK,YAAY;AAAA,MACnB,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA8B,kBAAkB,gBAAgB,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,QAAQ;AAAA,QACN,0BAA0B;AAAA,MAC5B;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,uBAAuB,CAAC,GAAM,oBAAoB;AAAA,IACtF,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,0BAA0B,WAAW,4BAA4B,MAAM;AAAA,CACnG,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,yBAAyB;AAAA,MACnC,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,yBAAyB;AAAA,MACnC,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACpNH,IAAMA,OAAM,CAAC,sBAAsB,EAAE;AACrC,IAAMC,OAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,MAAM;AACR;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,GAAG;AACxB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO;AAAA,EAC9B;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,uEAAuE,GAAG,CAAC;AACvL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,YAAY,SAAS,OAAO,OAAO,SAAS;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACxD;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,sEAAsE,IAAI,KAAK;AACtF,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,GAAG,uEAAuE,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,uEAAuE,GAAG,CAAC;AACvL,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,YAAY,SAAS,OAAO,OAAO,SAAS;AAC/C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACxD;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8EAA8E,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,8EAA8E,GAAG,CAAC;AAAA,EACvM;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,GAAG,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACxD;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,6EAA6E,IAAI,KAAK;AAC7F,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,8EAA8E,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,8EAA8E,GAAG,CAAC;AAAA,EACvM;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,GAAG,OAAO,cAAc,QAAQ,IAAI,CAAC;AAAA,EACxD;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACxC,IAAG,WAAW,GAAG,gEAAgE,GAAG,CAAC,EAAE,GAAG,gEAAgE,GAAG,CAAC;AAC9J,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,KAAoB;AACjC,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,UAAa,cAAc,EAAE;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,IAAI,UAAU,aAAa,WAAW,IAAI,YAAY,WAAW,IAAI,EAAE;AAAA,EAC1F;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,CAAC,EAAE,GAAG,yDAAyD,GAAG,CAAC,EAAE,GAAG,yDAAyD,GAAG,CAAC,EAAE,GAAG,yDAAyD,GAAG,CAAC;AAAA,EACtR;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,UAAU,IAAI;AACpB,IAAG,cAAc,IAAI,UAAU,aAAa,SAAS,IAAI,YAAY,SAAS,IAAI,YAAY,SAAS,IAAI,CAAC;AAAA,EAC9G;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AAAC;AACpE,IAAMC,OAAM,CAAC,yBAAyB,EAAE;AACxC,IAAMC,cAAa,CAAC,QAAQ,UAAU,MAAM;AAC5C,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,aAAa,CAAC;AAAA,EAChC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,YAAY,IAAI;AACtB,IAAG,WAAW,WAAW,UAAU,KAAK,EAAE,WAAW,UAAU,KAAK;AAAA,EACtE;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,aAAa,CAAC;AACnC,IAAG,WAAW,iBAAiB,SAAS,uFAAuF,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,iBAAiB,GAAG,2DAA2D,GAAG,GAAG,aAAa,GAAGA,WAAU;AAClH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,WAAW,OAAO,QAAQ;AAChG,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,oBAAoB;AAAA,EAC3C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,CAAC;AACX,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,iBAAiB,SAAS,mFAAmF,QAAQ;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,SAAS,GAAG;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ;AACzC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,MAAM,GAAG;AAAA,EACpD;AACF;AACA,IAAM,MAAM,CAAC,mBAAmB;AAChC,SAAS,WAAW,QAAQ,OAAO;AACjC,SAAO,KAAK;AACd;AACA,IAAM,MAAM,CAAC,IAAI,QAAQ;AAAA,EACvB,WAAW;AAAA,EACX,OAAO;AACT;AACA,SAAS,gFAAgF,IAAI,KAAK;AAAC;AACnG,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,iFAAiF,GAAG,GAAG,eAAe,CAAC;AACxH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,SAAS,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,EACxI;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,aAAa,SAAS,kFAAkF,QAAQ;AAC5H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC,EAAE,aAAa,SAAS,kFAAkF,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,SAAS,MAAM,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,QAAQ,QAAQ,IAAI,EAAE,SAAS,QAAQ,KAAK,EAAE,YAAY,CAAC,CAAC,QAAQ,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,UAAU,OAAO,cAAc,QAAQ,KAAK,EAAE,aAAa,OAAO,GAAG;AAAA,EAC7N;AACF;AACA,SAAS,kEAAkE,IAAI,KAAK;AAClF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,mBAAmB,SAAS,gGAAgG,QAAQ;AAChJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,kBAAkB,SAAS,+FAA+F,QAAQ;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,mBAAmB,OAAO,eAAe,EAAE,mBAAmB,OAAO,eAAe,EAAE,aAAa,OAAO,SAAS,EAAE,YAAY,OAAO,QAAQ,EAAE,mBAAmB,OAAO,eAAe;AAAA,EACjT;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,IAAI;AACzB,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,MAAM,CAAC;AACjG,IAAG,iBAAiB,GAAG,2DAA2D,GAAG,GAAG,MAAM,GAAG,YAAY,IAAI;AACjH,IAAG,WAAW,GAAG,mEAAmE,GAAG,GAAG,MAAM,CAAC;AACjG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,YAAY,IAAI,EAAE;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,cAAc;AACnC,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,GAAG,OAAO,mBAAmB,OAAO,kBAAkB,IAAI,EAAE;AAAA,EAC/E;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;AACrC,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM,CAAC,EAAE,GAAG,SAAS,CAAC;AAC3C,IAAG,WAAW,iBAAiB,SAAS,kFAAkF,QAAQ;AAChI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,GAAG,GAAG;AAChB,IAAG,aAAa;AAChB,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,YAAY,EAAE,aAAa,OAAO,GAAG,EAAE,cAAc,OAAO,UAAU;AAChI,IAAG,YAAY,SAAS,OAAO,OAAO,SAAS;AAC/C,IAAG,UAAU;AACb,IAAG,YAAY,SAAS,OAAO,YAAY,MAAM,OAAO,SAAS;AACjE,IAAG,UAAU;AACb,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,SAAS,OAAO,SAAS;AACpE,IAAG,UAAU,CAAC;AACd,IAAG,mBAAmB,KAAK,OAAO,WAAW,GAAG;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,WAAW,EAAE,aAAa,OAAO,GAAG,EAAE,cAAc,OAAO,UAAU;AAC/H,IAAG,YAAY,SAAS,OAAO,UAAU,OAAO,OAAO,OAAO,OAAO,SAAS;AAAA,EAChF;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,sBAAyB,YAAY,CAAC;AAC5C,IAAG,WAAW,oBAAoB,oBAAoB,QAAQ;AAAA,EAChE;AACF;AACA,SAAS,yEAAyE,IAAI,KAAK;AAAC;AAC5F,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0EAA0E,GAAG,GAAG,eAAe,CAAC;AAAA,EACnH;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc,CAAC;AAClB,UAAM,uBAA0B,YAAY,CAAC;AAC7C,IAAG,WAAW,oBAAoB,qBAAqB,QAAQ;AAAA,EACjE;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,MAAM,CAAC,EAAE,GAAG,4DAA4D,GAAG,CAAC;AAAA,EACjK;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,cAAc,GAAG,OAAO,WAAW,IAAI,CAAC;AAAA,EAC7C;AACF;AACA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,UAAU;AAClB,UAAI,KAAK,SAAS,QAAQ;AACxB,aAAK,UAAU,KAAK,KAAK,KAAK;AAAA,MAChC,OAAO;AACL,aAAK,UAAU,KAAK;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV,EAAE,KAAK,IAAI,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,UAAU,SAAS,MAAM;AAC3B,WAAK,QAAQ;AAAA,QACX,MAAM,GAAG,KAAK,KAAK;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,QACnB,QAAQ,KAAK,QAAQ;AAAA,QACrB,QAAQ,KAAK,QAAQ;AAAA,MACvB,EAAE,KAAK,IAAI;AAAA,IACb;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA2B;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,sBAAsB,EAAE,CAAC;AAAA,MAC5C,UAAU;AAAA,MACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,qDAAqD;AACnF,mBAAO,IAAI,UAAU;AAAA,UACvB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,SAAS,IAAI,KAAK;AACjC,UAAG,YAAY,uBAAuB,IAAI,SAAS,MAAM,EAAE,uBAAuB,IAAI,SAAS,MAAM,EAAE,uBAAuB,IAAI,SAAS,MAAM,EAAE,4BAA4B,IAAI,SAAS,QAAQ,EAAE,wCAAwC,IAAI,SAAS,QAAQ,EAAE,4BAA4B,IAAI,SAAS,QAAQ,EAAE,wCAAwC,IAAI,SAAS,QAAQ,EAAE,2BAA2B,IAAI,QAAQ,EAAE,8BAA8B,IAAI,MAAM;AAAA,QAC1c;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,UAAU;AAAA,QACV,WAAW;AAAA,QACX,MAAM;AAAA,QACN,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAOH;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,QAAQ,UAAU,GAAG,4BAA4B,GAAG,UAAU,GAAG,CAAC,WAAW,IAAI,UAAU,OAAO,GAAG,CAAC,WAAW,IAAI,UAAU,MAAM,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,WAAW,IAAI,UAAU,gBAAgB,GAAG,+BAA+B,GAAG,CAAC,WAAW,IAAI,UAAU,eAAe,GAAG,+BAA+B,CAAC;AAAA,MACze,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,kDAAkD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,kDAAkD,GAAG,GAAG,eAAe,CAAC;AAAA,QACzM;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,wBAA2B,YAAY,CAAC;AAC9C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,oBAAoB,IAAI,cAAc,qBAAqB,EAAE,2BAA8B,gBAAgB,GAAGC,MAAK,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,QACvJ;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAgB;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2DV,MAAM;AAAA,QACJ,+BAA+B;AAAA,QAC/B,+BAA+B;AAAA,QAC/B,+BAA+B;AAAA,QAC/B,oCAAoC;AAAA,QACpC,gDAAgD;AAAA,QAChD,oCAAoC;AAAA,QACpC,gDAAgD;AAAA,QAChD,mCAAmC;AAAA,QACnC,sCAAsC;AAAA,QACtC,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,cAAc,gBAAgB;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,kBAAkB,CAAC;AACxB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,uBAAuB,CAAC;AAAA,EAC/B;AAAA,EACA,iBAAiB,MAAM;AACrB,QAAI,KAAK,aAAa,MAAM;AAC1B,WAAK,eAAe,KAAK,IAAI;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,SAAS,OAAO;AACtB,UAAM,QAAQ,KAAK,MAAM,SAAS,OAAO,OAAO,KAAK,SAAS,CAAC;AAC/D,SAAK,gBAAgB,KAAK,KAAK;AAC/B,WAAO,QAAQ;AAAA,EACjB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,mBAAmB,QAAQ;AACzC,WAAK,uBAAuB,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAG,KAAK,iBAAiB,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,WAAS;AAAA,QAC9F,OAAO;AAAA,QACP,OAAO,GAAG,IAAI,IAAI,KAAK,OAAO,cAAc;AAAA,MAC9C,EAAE;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,GAAG;AAC3D,aAAO,KAAK,KAAK,+BAA8B;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,yBAAyB,EAAE,CAAC;AAAA,MAC/C,WAAW,CAAC,GAAG,wBAAwB;AAAA,MACvC,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,QACV,iBAAiB;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAOC;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,uCAAuC,GAAG,cAAc,UAAU,SAAS,GAAG,CAAC,GAAG,qCAAqC,GAAG,CAAC,GAAG,uCAAuC,GAAG,iBAAiB,cAAc,UAAU,SAAS,GAAG,CAAC,GAAG,WAAW,SAAS,GAAG,CAAC,GAAG,iBAAiB,UAAU,CAAC;AAAA,MACrS,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,aAAa,CAAC,EAAE,GAAG,qDAAqD,GAAG,GAAG,OAAO,CAAC;AAAA,QACpK;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,kBAAkB,IAAI,EAAE;AAChD,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,kBAAkB,IAAI,EAAE;AAAA,QAClD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAqB,mBAAwB,mBAAmB,aAAgB,iBAAoB,OAAO;AAAA,MAC1H,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,gBAAgB,WAAW;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,YAAY,KAAK,UAAU,YAAY,gBAAgB;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,kBAAkB,CAAC,IAAI,IAAI,IAAI,EAAE;AACtC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,SAAS,CAAC,GAAG,CAAC;AACnB,SAAK,iBAAiB,CAAC;AACvB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,aAAS,YAAY,SAAS,WAAW,WAAW,aAAa,GAAG,WAAW,aAAa;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,eAAe;AACpB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,QAAQ,OAAO;AACtB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC5E,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,kBAAkB,KAAK;AAAA,EAC9B;AAAA,EACA,SAAS,MAAM;AACb,SAAK,SAAS,KAAK,YAAY,IAAI;AAAA,EACrC;AAAA,EACA,gBAAgB,GAAG,OAAO;AACxB,WAAO,GAAG,MAAM,IAAI,IAAI,MAAM,KAAK;AAAA,EACrC;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,gBAAgB,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,iBAAiB,MAAM;AACrB,SAAK,eAAe,KAAK,IAAI;AAAA,EAC/B;AAAA,EACA,aAAa,OAAO,UAAU;AAC5B,WAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EACnC;AAAA,EACA,eAAe;AACb,UAAM,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,QAAQ;AAC7D,SAAK,iBAAiB,KAAK,kBAAkB,KAAK,WAAW,SAAS;AAAA,EACxE;AAAA,EACA,kBAAkB,WAAW,WAAW;AAEtC,UAAM,qBAAqB,gBAAc;AACvC,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN,UAAU,cAAc;AAAA,MAC1B;AACA,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN,UAAU,cAAc;AAAA,MAC1B;AACA,aAAO,CAAC,UAAU,GAAG,YAAY,QAAQ;AAAA,IAC3C;AACA,UAAM,eAAe,CAAC,OAAO,QAAQ;AACnC,YAAM,OAAO,CAAC;AACd,eAAS,IAAI,OAAO,KAAK,KAAK,KAAK;AACjC,aAAK,KAAK;AAAA,UACR,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AACA,QAAI,aAAa,GAAG;AAClB,aAAO,mBAAmB,aAAa,GAAG,SAAS,CAAC;AAAA,IACtD,OAAO;AAEL,YAAM,oBAAoB,CAAC,UAAU,SAAS;AAC5C,YAAI,cAAc,CAAC;AACnB,cAAM,eAAe;AAAA,UACnB,MAAM;AAAA,QACR;AACA,cAAM,eAAe;AAAA,UACnB,MAAM;AAAA,QACR;AACA,cAAM,gBAAgB,aAAa,GAAG,CAAC;AACvC,cAAM,eAAe,aAAa,WAAW,SAAS;AACtD,YAAI,WAAW,GAAG;AAEhB,gBAAM,UAAU,aAAa,IAAI,IAAI;AACrC,wBAAc,CAAC,GAAG,aAAa,GAAG,OAAO,GAAG,YAAY;AAAA,QAC1D,WAAW,WAAW,OAAO,GAAG;AAC9B,wBAAc,CAAC,cAAc,GAAG,aAAa,WAAW,GAAG,WAAW,CAAC,GAAG,YAAY;AAAA,QACxF,OAAO;AAEL,gBAAM,WAAW,aAAa,OAAO,IAAI,OAAO,IAAI,OAAO;AAC3D,wBAAc,CAAC,cAAc,GAAG,aAAa,UAAU,OAAO,CAAC,CAAC;AAAA,QAClE;AACA,eAAO,CAAC,GAAG,eAAe,GAAG,aAAa,GAAG,YAAY;AAAA,MAC3D;AACA,aAAO,mBAAmB,kBAAkB,WAAW,SAAS,CAAC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,YAAY,OAAO;AAClC,WAAK,SAAS,EAAE,KAAK,YAAY,KAAK,KAAK,WAAW,GAAG,KAAK,IAAI,KAAK,YAAY,KAAK,UAAU,KAAK,KAAK,CAAC;AAC7G,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,GAAG;AAC3D,aAAO,KAAK,KAAK,+BAAiC,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAuB,gBAAgB,CAAC,CAAC;AAAA,IAClN;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,SAAS,mCAAmC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,QACV,iBAAiB;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,sBAAsB,IAAI,GAAG,UAAU,QAAQ,SAAS,YAAY,cAAc,UAAU,WAAW,GAAG,CAAC,yBAAyB,IAAI,GAAG,SAAS,UAAU,YAAY,UAAU,mBAAmB,mBAAmB,aAAa,YAAY,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,sBAAsB,IAAI,GAAG,aAAa,aAAa,UAAU,QAAQ,SAAS,YAAY,cAAc,UAAU,WAAW,GAAG,CAAC,yBAAyB,IAAI,GAAG,mBAAmB,kBAAkB,SAAS,UAAU,YAAY,UAAU,mBAAmB,mBAAmB,aAAa,YAAY,iBAAiB,CAAC;AAAA,MACvsB,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC/H;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,2BAA2B,4BAA4B;AAAA,MACxF,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8CV,SAAS,CAAC,kBAAkB,2BAA2B,4BAA4B;AAAA,MACnF,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,YAAY,KAAK,UAAU,YAAY,gBAAgB;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,aAAS,YAAY,SAAS,WAAW,WAAW,aAAa,GAAG,WAAW,aAAa;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,eAAe;AACpB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,QAAQ,OAAO;AACtB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC5E,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,oBAAoB;AAAA,IAC/E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,SAAS,OAAO;AACtB,UAAM,QAAQ,SAAS,OAAO,OAAO,KAAK,SAAS;AACnD,SAAK,kBAAkB,KAAK;AAC5B,WAAO,QAAQ,GAAG,KAAK,SAAS;AAAA,EAClC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,KAAK,YAAY,CAAC;AAAA,EAC3C;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,KAAK,YAAY,CAAC;AAAA,EAC3C;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,gBAAgB,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,SAAK,YAAY,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ;AACrD,SAAK,eAAe,KAAK,cAAc;AACvC,SAAK,cAAc,KAAK,cAAc,KAAK;AAAA,EAC7C;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,SAAS,UAAU;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,aAAO,KAAK,KAAK,8BAAgC,kBAAqB,iBAAiB,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAuB,gBAAgB,CAAC,CAAC;AAAA,IACjN;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW,GAAG;AAAA,QACjE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,sBAAsB,IAAI,QAAQ,QAAQ,GAAG,SAAS,UAAU,YAAY,aAAa,YAAY,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,QAAQ,KAAK,GAAG,iBAAiB,YAAY,OAAO,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,sBAAsB,IAAI,QAAQ,QAAQ,GAAG,SAAS,UAAU,YAAY,aAAa,YAAY,CAAC;AAAA,MAC7W,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,oDAAoD,GAAG,IAAI,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC/H;AAAA,MACF;AAAA,MACA,cAAc,CAAC,yBAAyB;AAAA,MACxC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+BV,SAAS,CAAC,yBAAyB;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAC9B,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,kBAAkB,OAAO,WAAW;AAClC,QAAI,QAAQ,WAAW;AACrB,aAAO;AAAA,IACT,WAAW,QAAQ,GAAG;AACpB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,kBAAkB,OAAO;AACvB,UAAM,YAAY,KAAK,aAAa,KAAK,SAAS,KAAK,UAAU;AACjE,UAAM,aAAa,KAAK,kBAAkB,OAAO,SAAS;AAC1D,QAAI,eAAe,KAAK,eAAe,CAAC,KAAK,YAAY;AACvD,WAAK,cAAc;AACnB,WAAK,kBAAkB,KAAK,KAAK,WAAW;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM;AACrB,SAAK,aAAa;AAClB,SAAK,iBAAiB,KAAK,IAAI;AAC/B,UAAM,YAAY,KAAK,aAAa,KAAK,SAAS,KAAK,UAAU;AACjE,QAAI,KAAK,cAAc,WAAW;AAChC,WAAK,kBAAkB,SAAS;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,UAAM,YAAY,KAAK,aAAa,OAAO,KAAK,UAAU;AAC1D,QAAI,KAAK,cAAc,WAAW;AAChC,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,kBAAkB,SAAS;AAChC,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,aAAa,OAAO,UAAU;AAC5B,WAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EACnC;AAAA,EACA,YAAY,MAAM,KAAK,mBAAmB,iBAAiB,gBAAgB;AACzE,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,oBAAoB,CAAC,IAAI,IAAI,IAAI,EAAE;AACxC,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,SAAS,IAAI,cAAc,CAAC;AAAA,EACnC;AAAA,EACA,WAAW;AACT,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,YAAY;AAClD,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC5D,WAAK,cAAc,KAAK;AAAA,IAC1B,CAAC;AACD,SAAK,kBAAkB,UAAU,iBAAiB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,QAAM;AACjG,UAAI,KAAK,cAAc;AACrB,aAAK,OAAO,OAAO,iBAAiB,KAAK,UAAU;AACnD,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,OAAO,KAAK,KAAK,OAAO;AAAA,IAC/B;AACA,QAAI,sBAAsB,WAAW,YAAY;AAC/C,WAAK,iBAAiB,KAAK,sBAAsB,KAAK,UAAU,KAAK,cAAc,KAAK,UAAU,KAAK,CAAC,KAAK;AAAA,IAC/G;AACA,QAAI,QAAQ;AACV,WAAK,OAAO,OAAO;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAuB,aAAa,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,mBAAmB,GAAM,kBAAqB,eAAe,GAAM,kBAAuB,gBAAgB,CAAC,CAAC;AAAA,IACtQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,gBAAgB;AAAA,MAC/B,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,QAAQ,EAAE,2BAA2B,IAAI,UAAU,EAAE,uBAAuB,CAAC,IAAI,YAAY,IAAI,SAAS,OAAO,EAAE,sBAAsB,IAAI,QAAQ,KAAK;AAAA,QACxM;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,mBAAmB,YAAY,cAAc,UAAU,YAAY,SAAS,WAAW,GAAG,CAAC,GAAG,mBAAmB,kBAAkB,UAAU,cAAc,aAAa,YAAY,UAAU,mBAAmB,mBAAmB,SAAS,aAAa,YAAY,iBAAiB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MACnX,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,WAAW,GAAG,8CAA8C,GAAG,CAAC;AACnE,UAAG,eAAe,GAAG,wBAAwB,GAAG,CAAC;AACjD,UAAG,WAAW,mBAAmB,SAAS,+EAA+E,QAAQ;AAC/H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,kBAAkB,MAAM,CAAC;AAAA,UACrD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,yBAAyB,GAAG,CAAC;AAClD,UAAG,WAAW,mBAAmB,SAAS,gFAAgF,QAAQ;AAChI,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,kBAAkB,MAAM,CAAC;AAAA,UACrD,CAAC,EAAE,kBAAkB,SAAS,+EAA+E,QAAQ;AACnH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,UACpD,CAAC;AACD,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,iBAAiB,IAAI,EAAE;AAC/C,UAAG,UAAU;AACb,UAAG,WAAW,YAAY,IAAI,UAAU,EAAE,cAAc,IAAI,YAAY,EAAE,UAAU,IAAI,MAAM,EAAE,YAAY,IAAI,UAAU,EAAE,SAAS,IAAI,OAAO,EAAE,aAAa,IAAI,WAAW;AAC9K,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,UAAU,IAAI,IAAI,EAAE,cAAc,IAAI,YAAY,EAAE,aAAa,IAAI,WAAW,EAAE,YAAY,IAAI,UAAU,EAAE,UAAU,IAAI,MAAM,EAAE,mBAAmB,IAAI,iBAAiB,EAAE,mBAAmB,IAAI,iBAAiB,EAAE,SAAS,IAAI,OAAO,EAAE,aAAa,IAAI,WAAW,EAAE,YAAY,IAAI,UAAU,EAAE,mBAAmB,IAAI,iBAAiB;AAAA,QAChW;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,6BAA6B,4BAA4B;AAAA,MAC1F,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,CAAC,GAAG,sBAAsB,WAAW,UAAU,MAAM;AAC5E,WAAW,CAAC,WAAW,CAAC,GAAG,sBAAsB,WAAW,qBAAqB,MAAM;AACvF,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,sBAAsB,WAAW,qBAAqB,MAAM;AACvG,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,sBAAsB,WAAW,qBAAqB,MAAM;AACvG,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,sBAAsB,WAAW,YAAY,MAAM;AAC9F,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,cAAc,MAAM;AAClF,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,gBAAgB,MAAM;AACpF,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,sBAAsB,MAAM;AAC1F,WAAW,CAAC,YAAY,CAAC,GAAG,sBAAsB,WAAW,WAAW,MAAM;AAC9E,WAAW,CAAC,YAAY,CAAC,GAAG,sBAAsB,WAAW,eAAe,MAAM;AAClF,WAAW,CAAC,YAAY,CAAC,GAAG,sBAAsB,WAAW,cAAc,MAAM;AAAA,CAChF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoCV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,QACnC,+BAA+B;AAAA,QAC/B,8BAA8B;AAAA,MAChC;AAAA,MACA,SAAS,CAAC,kBAAkB,6BAA6B,4BAA4B;AAAA,MACrF,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,uBAAuB,6BAA6B,8BAA8B,2BAA2B,4BAA4B;AAAA,MACnJ,SAAS,CAAC,qBAAqB;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,uBAAuB,6BAA6B,8BAA8B,2BAA2B,4BAA4B;AAAA,IACrJ,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,uBAAuB,6BAA6B,8BAA8B,2BAA2B,4BAA4B;AAAA,MACnJ,SAAS,CAAC,qBAAqB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AClgDH,IAAME,OAAM,CAAC,GAAG;AAChB,SAAS,8CAA8C,IAAI,KAAK;AAAC;AACjE,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,WAAW,iBAAiB,SAAS,6FAA6F;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,IAAI,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,cAAc,EAAE;AAChC,IAAG,WAAW,WAAW,KAAK,OAAO;AAAA,EACvC;AACF;AACA,SAAS,4DAA4D,IAAI,KAAK;AAC5E,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,EAAE;AAChC,IAAG,WAAW,iBAAiB,SAAS,6FAA6F;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,OAAU,cAAc,EAAE;AAChC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,IAAI,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAU,cAAc,EAAE;AAChC,IAAG,WAAW,WAAW,KAAK,OAAO;AAAA,EACvC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,EAAE;AAC7B,IAAG,WAAW,SAAS,SAAS,0EAA0E;AACxG,YAAM,OAAU,cAAc,GAAG,EAAE;AACnC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,MAAM,IAAI,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,WAAW,GAAG,6DAA6D,GAAG,GAAG,SAAS,EAAE,EAAE,GAAG,6DAA6D,GAAG,GAAG,SAAS,EAAE;AAClL,IAAG,eAAe,GAAG,MAAM;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,KAAK,OAAO;AACxC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,cAAc;AAC5C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,KAAK,IAAI;AAAA,EAChC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,qBAAqB,CAAC;AAC3C,IAAG,WAAW,mBAAmB,SAAS,4FAA4F,QAAQ;AAC5I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,oBAAoB,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC;AACzE,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,MAAM,CAAC;AACnF,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,UAAU,EAAE;AAC9C,IAAG,WAAW,SAAS,SAAS,yEAAyE;AACvG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,MAAM,CAAC;AAAA,IACtC,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,WAAW,SAAS,SAAS,0EAA0E;AACxG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,QAAQ,CAAC;AAAA,IACxC,CAAC;AACD,IAAG,OAAO,EAAE;AACZ,IAAG,aAAa,EAAE,EAAE,EAAE;AACtB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,gBAAmB,YAAY,CAAC;AACtC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAS,EAAE,YAAY,OAAO,SAAS,EAAE,kBAAkB,aAAa;AAC1G,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,kBAAkB,EAAE,gBAAgB,OAAO,YAAY;AACvF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,YAAY,CAAC,OAAO,SAAS;AAC3C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,OAAO,aAAa,GAAG;AACzD,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO,aAAa;AAAA,EAClD;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,iBAAiB,SAAS,0EAA0E,QAAQ;AACxH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,yCAAyC,OAAO,gBAAgB;AAC/E,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,cAAc,OAAO,QAAQ,EAAE,mBAAmB,OAAO,aAAa;AAC/G,IAAG,YAAY,cAAc,OAAO,KAAK;AAAA,EAC3C;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,SAAS,SAAS,oEAAoE;AAClG,YAAM,eAAkB,cAAc,GAAG,EAAE;AAC3C,aAAU,YAAY,aAAa,SAAS,CAAC;AAAA,IAC/C,CAAC;AACD,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,aAAa,MAAM,GAAG;AAAA,EACnD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC3C,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,oBAAoB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC;AAC5D,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,MAAM,CAAC;AAC7E,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,mBAAsB,YAAY,CAAC;AACzC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,kBAAkB,gBAAgB;AAChD,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,gBAAgB;AAAA,EAClD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,cAAc,QAAQ;AAAA,EACxD;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,UAAU,OAAO,cAAc,SAAS;AAAA,EACzD;AACF;AACA,IAAMC,OAAM,CAAC,aAAa,EAAE;AAC5B,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,gBAAgB,SAAS,wFAAwF,QAAQ;AACrI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,OAAO,QAAQ,EAAE,aAAa,CAAC,OAAO,YAAY;AAAA,EAC5E;AACF;AACA,SAAS,wEAAwE,IAAI,KAAK;AAAC;AAC3F,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,yEAAyE,GAAG,GAAG,eAAe,CAAC;AAChH,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,YAAY;AAAA,EACvD;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,iBAAiB,CAAC;AAClC,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2DAA2D,GAAG,GAAG,gBAAgB,CAAC;AACzN,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAkB,YAAY,CAAC;AACrC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,cAAc,OAAO,YAAY;AAC/C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,QAAQ,OAAO,YAAY,EAAE,YAAY,YAAY;AAAA,EACrE;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,iBAAiB,SAAS,mEAAmE,QAAQ;AACjH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,gBAAgB,MAAM,CAAC;AAAA,IACtD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,SAAS,EAAE,mBAAmB,OAAO,eAAe;AACrH,IAAG,YAAY,cAAc,OAAO,OAAO;AAAA,EAC7C;AACF;AACA,IAAMC,OAAM,CAAC,eAAe,EAAE;AAC9B,IAAMC,OAAM,CAAC,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC,mBAAmB,CAAC,GAAG,GAAG;AACpE,IAAMC,OAAM,CAAC,iBAAiB,qBAAqB,GAAG;AACtD,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,mBAAmB,CAAC;AACzC,IAAG,WAAW,gBAAgB,SAAS,sFAAsF,QAAQ;AACnI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,oBAAoB,MAAM,CAAC;AAAA,IAC1D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,uBAA0B,YAAY,CAAC;AAC7C,UAAM,mBAAsB,YAAY,CAAC;AACzC,IAAG,WAAW,mBAAmB,oBAAoB,EAAE,iBAAiB,gBAAgB,EAAE,gBAAgB,OAAO,cAAc,EAAE,kBAAkB,OAAO,gBAAgB,EAAE,gBAAgB,OAAO,SAAS;AAAA,EAC9M;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AAAC;AAC3E,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,yDAAyD,GAAG,GAAG,eAAe,CAAC;AAAA,EAClG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,kBAAqB,YAAY,CAAC;AACxC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,oBAAoB,OAAO,aAAa,kBAAkB,kBAAkB;AAAA,EAC5F;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,oBAAoB,CAAC;AAAA,EACvC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,aAAa,OAAO,SAAS,EAAE,kBAAkB,OAAO,cAAc,EAAE,mBAAmB,kBAAkB;AAAA,EAC7H;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,MAAM,CAAC,gBAAgB,EAAE;AAC/B,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,SAAS,uCAAuC,IAAI,KAAK;AACvD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,KAAK;AAAA,EACvB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,YAAY,SAAS,QAAQ,EAAE,aAAa,QAAQ;AAAA,EACzD;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,SAAS,CAAC;AAC/B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAC/F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,aAAa;AAAA,EACxD;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAAC;AAClE,IAAM,MAAM,CAAC,WAAW;AACxB,IAAM,MAAM,CAAC,sBAAsB,EAAE;AACrC,SAAS,sDAAsD,IAAI,KAAK;AAAC;AACzE,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,WAAW,GAAG,uDAAuD,GAAG,GAAG,eAAe,CAAC;AAC9F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,YAAY,SAAY,YAAY,GAAG,GAAG,OAAO,UAAU,GAAG,IAAI;AACrE,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,IAAM,MAAM,CAAC,wBAAwB,EAAE;AACvC,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,MAAM,GAAG,CAAC;AAAA,EAC5B;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,mBAAmB,SAAS,4EAA4E,QAAQ;AAC5H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,wBAAwB,MAAM,CAAC;AAAA,IAC9D,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,yBAA4B,cAAc,EAAE;AAClD,IAAG,WAAW,uBAAuB,sBAAsB;AAAA,EAC7D;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,MAAM,CAAC;AAC7E,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,yBAAyB,IAAI;AACnC,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,iBAAiB,uBAAuB,MAAM;AAAA,EAC7E;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,UAAU,GAAG,kBAAkB,CAAC;AACnC,IAAG,OAAO,GAAG,OAAO;AACpB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,mBAAsB,YAAY,GAAG,GAAG,OAAO,SAAS,CAAC;AAAA,EACzE;AACF;AACA,IAAM,OAAO,CAAC,oBAAoB;AAClC,IAAM,OAAO,CAAC,kBAAkB;AAChC,IAAMC,QAAO,CAAC,IAAI,QAAQ;AAAA,EACxB,WAAW;AAAA,EACX,OAAO;AACT;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,mBAAmB,OAAO,eAAe;AAAA,EAC7H;AACF;AACA,SAAS,+GAA+G,IAAI,KAAK;AAAC;AAClI,SAAS,iGAAiG,IAAI,KAAK;AACjH,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,gHAAgH,GAAG,GAAG,eAAe,EAAE;AACxJ,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,UAAU,IAAI;AACpB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAGA,OAAM,SAAS,IAAI,CAAC;AAAA,EACjI;AACF;AACA,SAAS,kFAAkF,IAAI,KAAK;AAClG,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,+BAA+B,IAAI,CAAC,EAAE,GAAG,SAAS,EAAE,EAAE,GAAG,OAAO;AACrF,IAAG,WAAW,GAAG,kGAAkG,GAAG,GAAG,gBAAgB,EAAE;AAC3I,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,UAAU,OAAO,KAAK,SAAS,OAAO,UAAU,OAAO,mBAAmB;AACzF,IAAG,WAAW,YAAY,OAAO,eAAe,EAAE,eAAe,OAAO,kBAAkB,EAAE,eAAe,OAAO,kBAAkB;AACpI,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc;AAChF,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,mBAAmB,OAAO,IAAI,EAAE,wBAAwB,OAAO,iBAAiB;AAAA,EAChG;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,IAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,mFAAmF,GAAG,GAAG,+BAA+B,CAAC;AACxN,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,cAAc;AAC9C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa;AACvH,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,CAAC,OAAO,eAAe;AAC7C,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,eAAe;AAAA,EAC9C;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,WAAW,OAAO,YAAY;AAC5C,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,OAAO,EAAE,kBAAkB,OAAO,cAAc,EAAE,iBAAiB,OAAO,aAAa,EAAE,mBAAmB,OAAO,eAAe;AAAA,EACpK;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,KAAK;AAAA,EACnC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAChG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,wBAA2B,YAAY,EAAE;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,qBAAqB;AAAA,EACzD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,EAAE;AAAA,EAC7C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,SAAS,OAAO,OAAO;AAAA,EACvC;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,EAAE;AAAA,EAC7C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,sBAAyB,YAAY,CAAC;AAC5C,UAAM,qBAAwB,YAAY,EAAE;AAC5C,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,mBAAmB,kBAAkB,EAAE,kBAAkB,OAAO,kBAAkB,EAAE,iBAAiB,OAAO,aAAa,EAAE,0BAA0B,OAAO,sBAAsB,EAAE,mBAAmB,OAAO,2BAA2B,OAAO,yBAAyB,cAAc,IAAI,EAAE,mBAAmB,OAAO,iBAAiB,EAAE,sBAAsB,OAAO,oBAAoB,EAAE,sBAAsB,OAAO,oBAAoB,EAAE,oBAAoB,mBAAmB,EAAE,qBAAqB,OAAO,mBAAmB;AAAA,EACnmB;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,0BAA0B,EAAE;AAAA,EAC9C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,EAAE;AAC5C,IAAG,WAAW,eAAe,OAAO,aAAa,EAAE,kBAAkB,OAAO,oBAAoB,EAAE,iBAAiB,OAAO,aAAa,EAAE,mBAAmB,kBAAkB;AAAA,EAChL;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,yBAAyB,EAAE;AAAA,EAC7C;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,QAAQ;AAAA,EACzC;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,EAAE;AAChG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,wBAA2B,YAAY,EAAE;AAC/C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,qBAAqB;AAAA,EACzD;AACF;AACA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,iBAAiB,EAAE;AACxC,IAAG,WAAW,oBAAoB,SAAS,mGAAmG,QAAQ;AACpJ,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,qBAAqB,SAAS,oGAAoG,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,UAAU,CAAC,OAAO,cAAc,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,gBAAgB,OAAO,YAAY,EAAE,qBAAqB,OAAO,iBAAiB,EAAE,sBAAsB,OAAO,kBAAkB,EAAE,eAAe,OAAO,WAAW,EAAE,UAAU,OAAO,qBAAqB,UAAU,UAAU,OAAO,WAAW,YAAY,YAAY,OAAO,EAAE,cAAc,OAAO,UAAU,EAAE,WAAW,OAAO,OAAO,EAAE,YAAY,OAAO,QAAQ,EAAE,eAAe,OAAO,WAAW;AAAA,EAC/hB;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,0DAA0D,GAAG,IAAI,iBAAiB,EAAE;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,QAAQ,OAAO,oBAAoB,OAAO,KAAK,MAAM;AAAA,EACrE;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,IAAMC,QAAO,CAAC,iBAAiB;AAC/B,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC;AAC/F,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,kBAAkB;AAAA,EACtD;AACF;AACA,IAAM,0BAA0B;AAChC,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,gBAAgB,SAAS;AACvB,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,iBAAiB,QAAQ,KAAK,UAAU;AAClD,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,kBAAkB,IAAI,aAAa;AAAA,EAC1C;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAClG,cAAM,gBAAgB;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA6B,kBAAqB,eAAe,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAC7M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,MACjC,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,GAAG,UAAU;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AAAA,QACnE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,YAAY;AAAA,MACd;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,mBAAmB;AAAA,MAC5E,oBAAoBN;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,IAAI,aAAa,SAAS,eAAe,eAAe,GAAG,4BAA4B,GAAG,mBAAmB,cAAc,eAAe,kBAAkB,WAAW,CAAC;AAAA,MACjM,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,mBAAmB,SAAS,kEAAkE,QAAQ;AAClH,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AACD,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAU,IAAI,QAAQ,EAAE,yBAAyB,IAAI,SAAS;AAC7E,UAAG,WAAW,cAAc,IAAI,UAAU,EAAE,eAAe,KAAK,EAAE,kBAAkB,IAAI,cAAc,EAAE,aAAa,IAAI,SAAS;AAAA,QACpI;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAqB,mBAAmB;AAAA,MACvD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,yBAAyB,WAAW,cAAc,MAAM;AAAA,CAClG,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBV,WAAW,CAAC,gBAAgB;AAAA,MAC5B,SAAS,CAAC,gBAAgB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,aAAa,GAAG,MAAM;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAMO,SAAQ;AACZ,QAAI,KAAK,gBAAgB;AACvB,WAAK,qBAAqB,KAAK,mBAAmB,IAAI,UAAQ;AAC5D,YAAI,SAASA,SAAQ;AACnB,iBAAO,iCACF,OADE;AAAA,YAEL,SAAS,CAACA,QAAO;AAAA,UACnB;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,MAAAA,QAAO,UAAU,CAACA,QAAO;AAAA,IAC3B,OAAO;AACL,WAAK,qBAAqB,KAAK,mBAAmB,IAAI,UAAS,iCAC1D,OAD0D;AAAA,QAE7D,SAAS,SAASA;AAAA,MACpB,EAAE;AAAA,IACJ;AACA,SAAK,YAAY,KAAK,iBAAiB,KAAK,kBAAkB;AAAA,EAChE;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,SAAK,YAAY;AACjB,SAAK,qBAAqB,KAAK,kBAAkB,KAAK,cAAc,IAAI;AACxE,SAAK,YAAY,KAAK,iBAAiB,KAAK,kBAAkB;AAC9D,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,YAAY;AACjB,QAAI,CAAC,OAAO;AACV,WAAK,eAAe;AAAA,IACtB,OAAO;AACL,WAAK,gBAAgB,KAAK,mBAAmB,OAAO,UAAQ,KAAK,OAAO,EAAE,IAAI,UAAQ,KAAK,KAAK;AAAA,IAClG;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,UAAM,gBAAgB,KAAK,mBAAmB,OAAO,UAAQ,KAAK,OAAO,EAAE,IAAI,UAAQ,KAAK,KAAK;AACjG,QAAI,CAAC,YAAY,KAAK,eAAe,aAAa,GAAG;AACnD,UAAI,KAAK,gBAAgB;AACvB,aAAK,aAAa,KAAK,aAAa;AAAA,MACtC,OAAO;AACL,aAAK,aAAa,KAAK,cAAc,SAAS,IAAI,cAAc,CAAC,IAAI,IAAI;AAAA,MAC3E;AAAA,IACF;AAAA,EACF;AAAA,EACA,kBAAkB,cAAc,OAAO;AACrC,WAAO,aAAa,IAAI,UAAQ;AAC9B,YAAM,UAAU,QAAQ,QAAQ,CAAC,CAAC,KAAK;AACvC,aAAO;AAAA,QACL,MAAM,KAAK;AAAA,QACX,OAAO,KAAK;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,oBAAoB;AACnC,WAAO,mBAAmB,KAAK,UAAQ,KAAK,OAAO;AAAA,EACrD;AAAA,EACA,YAAY,KAAK,MAAM;AACrB,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,eAAe,CAAC;AACrB,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,qBAAqB,CAAC;AAC3B,SAAK,gBAAgB,CAAC;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACpE,WAAK,SAAS,KAAK,KAAK,cAAc,OAAO;AAC7C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,KAAK,gBAAgB,KAAK,aAAa,QAAQ;AACjE,WAAK,qBAAqB,KAAK,kBAAkB,KAAK,YAAY;AAClE,WAAK,YAAY,KAAK,iBAAiB,KAAK,kBAAkB;AAAA,IAChE;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAqB,iBAAiB,GAAM,kBAAuB,aAAa,CAAC;AAAA,IAC/H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,yBAAyB;AAAA,MACxC,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,cAAc,gBAAgB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,mBAAmB,aAAa,YAAY,gBAAgB,GAAG,CAAC,WAAW,IAAI,UAAU,UAAU,WAAW,MAAM,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,GAAG,cAAc,SAAS,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,aAAa,IAAI,UAAU,QAAQ,UAAU,SAAS,GAAG,SAAS,UAAU,GAAG,CAAC,aAAa,IAAI,UAAU,WAAW,UAAU,SAAS,GAAG,OAAO,GAAG,CAAC,gBAAgB,IAAI,GAAG,SAAS,YAAY,GAAG,CAAC,YAAY,IAAI,GAAG,WAAW,iBAAiB,GAAG,MAAM,GAAG,CAAC,eAAe,IAAI,GAAG,WAAW,iBAAiB,GAAG,MAAM,GAAG,CAAC,YAAY,IAAI,GAAG,iBAAiB,SAAS,GAAG,CAAC,eAAe,IAAI,GAAG,iBAAiB,SAAS,CAAC;AAAA,MAC70B,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,eAAe,CAAC;AACtF,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,gDAAgD,IAAI,GAAG,gBAAgB,CAAC;AAAA,QAC3F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,YAAY,EAAE,YAAY,IAAI,aAAa;AAAA,QACxE;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,MAAM,0BAA0B,cAAmB,iBAAiB,kBAAqB,iBAAoB,qBAAwB,yBAAyB,SAAS,kBAAkB,kBAAqB,qBAAqB,aAAgB,iBAAoB,SAAS,gBAAmB,mBAAsB,4BAAgC,eAAe;AAAA,MACzY,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqCV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,kBAAkB,MAAM,0BAA0B,cAAc,kBAAkB,SAAS,kBAAkB,kBAAkB,aAAa,cAAc;AAAA,MACpK,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,eAAe,IAAI,aAAa;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,SAAS,CAAC,KAAK;AACpB,WAAK,aAAa,KAAK,KAAK,MAAM;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,wBAAwB,EAAE,CAAC;AAAA,MAClD,WAAW,CAAC,GAAG,2BAA2B;AAAA,MAC1C,UAAU;AAAA,MACV,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,sDAAsD;AACpF,mBAAO,IAAI,YAAY;AAAA,UACzB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,QAAQ,QAAQ;AAClC,UAAG,YAAY,sCAAsC,CAAC,IAAI,aAAa,IAAI,WAAW,IAAI,EAAE,uCAAuC,CAAC,IAAI,aAAa,IAAI,WAAW,KAAK,EAAE,oCAAoC,IAAI,SAAS;AAAA,QAC9N;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,8CAA8C;AAAA,QAC9C,+CAA+C;AAAA,QAC/C,4CAA4C;AAAA,QAC5C,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAsB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,YAAY,IAAI;AAAA,QACrD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,MACd;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,2BAA2B;AAAA,MAC7B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,mBAAmB,CAAC;AACzB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,gBAAgB,SAAS;AACvB,SAAK,UAAU;AACf,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA2B;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,QAAQ;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,eAAe;AAAA,QACf,OAAO;AAAA,QACP,cAAc;AAAA,QACd,kBAAkB;AAAA,MACpB;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,iBAAiB,gBAAgB,GAAG,CAAC,eAAe,IAAI,GAAG,yCAAyC,WAAW,cAAc,mBAAmB,iBAAiB,GAAG,MAAM,GAAG,CAAC,SAAS,6BAA6B,GAAG,MAAM,GAAG,CAAC,eAAe,IAAI,GAAG,iBAAiB,WAAW,cAAc,iBAAiB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,eAAe,IAAI,eAAe,cAAc,GAAG,4BAA4B,GAAG,gBAAgB,GAAG,CAAC,WAAW,IAAI,UAAU,MAAM,GAAG,CAAC,WAAW,IAAI,GAAG,0BAA0B,GAAG,CAAC,gBAAgB,IAAI,GAAG,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,gBAAgB,IAAI,GAAG,OAAO,CAAC;AAAA,MACnnB,UAAU,SAAS,mCAAmC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC5I;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,YAAY;AACtC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,gBAAgB;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,aAAgB,iBAAoB,SAAS,kBAAqB,qBAAqB,kBAAqB,iBAAoB,qBAAwB,qBAAwB,yBAAyB,cAAmB,iBAAiB,OAAO;AAAA,MACzQ,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,MAAM,aAAa,kBAAkB,kBAAkB,cAAc,OAAO;AAAA,MACtF,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,iBAAiB,CAAC,UAAU,WAAW,IAAI;AAChD,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB;AAClB,WAAK,OAAO,KAAK,eAAe,QAAQ,QAAQ,MAAM;AACtD,WAAK,SAAS,KAAK,eAAe,QAAQ,SAAS,MAAM;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,CAAC,GAAG,0BAA0B;AAAA,MACzC,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,WAAW,IAAI,UAAU,YAAY,SAAS,8BAA8B,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,cAAc,SAAS,gCAAgC,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,WAAW,IAAI,UAAU,YAAY,GAAG,4BAA4B,GAAG,CAAC,WAAW,IAAI,UAAU,cAAc,GAAG,8BAA8B,CAAC;AAAA,MACze,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,eAAe,CAAC;AACvF,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC;AAC5C,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,QAAQ,CAAC;AACtI,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,eAAe;AACrD,UAAG,UAAU;AACb,UAAG,YAAY,gCAAgC,IAAI,UAAU,IAAI,IAAI;AACrE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,IAAI;AAC9B,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,MAAM;AAAA,QAClC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAmB,iBAAiB,kBAAkB,IAAI;AAAA,MACzE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,cAAc,kBAAkB,IAAI;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,iBAAiB,UAAU;AACzB,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,QAAQ,QAAQ;AAAA,EACxE;AAAA,EACA,kBAAkB,WAAW;AAC3B,SAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS,SAAS;AAAA,EAC1E;AAAA,EACA,gBAAgB,cAAc;AAC5B,SAAK,YAAY,cAAc,gCAAgC;AAAA,EACjE;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,YAAY,YAAY,8BAA8B;AAAA,EAC7D;AAAA,EACA,YAAY,MAAM,WAAW;AAE3B,SAAK,SAAS,YAAY,KAAK,WAAW,eAAe,SAAS;AAClE,QAAI,MAAM;AACR,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS;AAAA,IACjE;AAAA,EACF;AAAA,EACA,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,KAAK;AAC1B,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK,WAAW,MAAM,KAAK,WAAW;AACxD,SAAK,cAAc,KAAK,YAAY,MAAM,KAAK,YAAY;AAC3D,SAAK,cAAc,KAAK,WAAW;AACnC,SAAK,eAAe,KAAK,YAAY;AACrC,SAAK,UAAU,KAAK,eAAe,KAAK;AACxC,UAAM,aAAa,WAAS;AAC1B,UAAI,OAAO,UAAU,YAAY,UAAU,IAAI;AAC7C,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,iBAAiB,WAAW,KAAK,MAAM,CAAC;AAC7C,SAAK,kBAAkB,WAAW,KAAK,OAAO,CAAC;AAC/C,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,IAChH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,UAAU,EAAE,GAAG,CAAC,MAAM,UAAU,EAAE,CAAC;AAAA,MACpG,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,YAAY,IAAI,UAAU,WAAW,IAAI;AACxD,UAAG,YAAY,4BAA4B,IAAI,YAAY,EAAE,2BAA2B,IAAI,WAAW;AAAA,QACzG;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,oCAAoC;AAAA,QACpC,mCAAmC;AAAA,QACnC,oBAAoB;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,iBAAiB,UAAU;AACzB,SAAK,eAAe,KAAK,QAAQ;AAAA,EACnC;AAAA,EACA,cAAc,YAAY;AACxB,SAAK,YAAY,KAAK,UAAU;AAAA,EAClC;AAAA,EACA,eAAe,aAAa;AAC1B,SAAK,aAAa,KAAK,WAAW;AAAA,EACpC;AAAA,EACA,oBAAoB,aAAa;AAC/B,SAAK,oBAAoB,KAAK,WAAW;AAAA,EAC3C;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,cAAc;AAClB,aAAS,QAAQ,QAAM;AACrB,qBAAe,GAAG,WAAW,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,WAAW;AAAA,IAC3E,CAAC;AACD,UAAM,aAAa,SAAS,IAAI,UAAQ,KAAK,OAAO;AACpD,SAAK,aAAa,KAAK,WAAW;AAClC,SAAK,uBAAuB,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,CAAC;AACpB,aAAS,QAAQ,QAAM;AACrB,YAAM,SAAS,GAAG,WAAW,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,WAAW;AACzE,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,mBAAW,KAAK,eAAe,CAAC,EAAE;AAAA,MACpC;AAAA,IACF,CAAC;AACD,SAAK,qBAAqB,KAAK,UAAU;AAAA,EAC3C;AAAA,EACA,mBAAmB,iBAAiB;AAClC,SAAK,mBAAmB,KAAK,gBAAgB,IAAI,WAAS,GAAG,KAAK,IAAI,CAAC;AAAA,EACzE;AAAA,EACA,aAAa,WAAW;AACtB,SAAK,WAAW,KAAK,SAAS;AAAA,EAChC;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,UAAU,SAAS,SAAS;AAC1B,UAAM,oBAAoB,CAAC,EAAE,WAAW;AACxC,QAAI,CAAC,mBAAmB;AACtB,WAAK,mBAAmB,CAAC,CAAC;AAAA,IAC5B;AACA,SAAK,mBAAmB,KAAK,iBAAiB;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,IAAI,cAAc,CAAC;AACzC,SAAK,cAAc,IAAI,cAAc,CAAC;AACtC,SAAK,eAAe,IAAI,cAAc,CAAC;AACvC,SAAK,aAAa,IAAI,cAAc,CAAC;AACrC,SAAK,eAAe,IAAI,cAAc,CAAC;AACvC,SAAK,aAAa,IAAI,cAAc,CAAC;AACrC,SAAK,YAAY,IAAI,cAAc,CAAC;AACpC,SAAK,yBAAyB,IAAI,gBAAgB,CAAC,CAAC;AACpD,SAAK,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AACjD,SAAK,uBAAuB,cAAc,CAAC,KAAK,qBAAqB,KAAK,sBAAsB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,aAAa,WAAW,MAAM,YAAY,SAAS,cAAc,WAAW,CAAC;AAC3L,SAAK,qBAAqB,IAAI,cAAc,CAAC;AAC7C,SAAK,yBAAyB;AAAA;AAAA,MAC9B,KAAK;AAAA,MAAsB,cAAc,CAAC,KAAK,oBAAoB,KAAK,oBAAoB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,WAAW,WAAW,MAAM;AAEpI,YAAI,UAAU,WAAW,YAAY,QAAQ;AAC3C,iBAAO,UAAU,IAAI,CAAC,OAAO,UAAU;AACrC,gBAAI,UAAU,OAAO;AACnB,qBAAO,YAAY,KAAK,KAAK;AAAA,YAC/B,OAAO;AACL,qBAAO,YAAY,KAAK,KAAK;AAAA,YAC/B;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC;AAAA,IAAC;AACH,SAAK,uBAAuB,IAAI,cAAc,CAAC;AAC/C,SAAK,uBAAuB,KAAK,mBAAmB,KAAK,IAAI,UAAQ,KAAK,IAAI,WAAS,SAAS,OAAO,EAAE,CAAC,CAAC,CAAC;AAC5G,SAAK,qBAAqB,IAAI,cAAc,CAAC;AAAA,EAC/C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,qBAAqB;AAC/B,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAkB,qBAAqB,CAAC,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,GAAG,iBAAiB,GAAG,YAAY,EAAE,GAAG,CAAC,MAAM,GAAG,iBAAiB,GAAG,YAAY,EAAE,CAAC;AAAA,MACxG,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,IAAI,aAAa;AAAA,QACpD;AAAA,MACF;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,0BAA0B;AAAA,MAC5B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,eAAe,MAAM;AACnB,SAAK,UAAU,KAAK,IAAI;AAAA,EAC1B;AAAA,EACA,sBAAsB,YAAY;AAChC,SAAK,iBAAiB,KAAK,UAAU;AAAA,EACvC;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,WAAW,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,iBAAiB,MAAM;AACrB,SAAK,YAAY,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,yBAAyB,MAAM;AAC7B,SAAK,oBAAoB,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,aAAa,IAAI,gBAAgB,CAAC;AACvC,SAAK,mBAAmB,IAAI,gBAAgB,IAAI;AAChD,SAAK,YAAY,IAAI,gBAAgB,EAAE;AACvC,SAAK,cAAc,IAAI,gBAAgB,CAAC,CAAC;AACzC,SAAK,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AACjD,SAAK,qBAAqB,KAAK,WAAW,KAAK,qBAAqB,CAAC;AACrE,SAAK,oBAAoB,KAAK,UAAU,KAAK,qBAAqB,CAAC;AACnE,SAAK,sBAAsB,IAAI,gBAAgB,CAAC,CAAC;AACjD,SAAK,eAAe,cAAc,CAAC,KAAK,oBAAoB,KAAK,mBAAmB,KAAK,mBAAmB,CAAC,EAAE,KAAK,aAAa,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,UAAU,UAAU,OAAO;AAAA,MACxL;AAAA,MACA;AAAA,MACA,MAAM,WAAW,OAAO,UAAQ,KAAK,MAAM,EAAE,IAAI,WAAS;AAAA,QACxD,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,MACd,EAAE;AAAA,MACF,QAAQ,WAAW,OAAO,UAAQ,KAAK,QAAQ,EAAE,IAAI,WAAS;AAAA,QAC5D,KAAK,KAAK;AAAA,QACV,OAAO,KAAK;AAAA,MACd,EAAE;AAAA,IACJ,EAAE,CAAC;AACH,SAAK,uBAAuB,cAAc,CAAC,KAAK,aAAa,KAAK,mBAAmB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,YAAY,kBAAkB,MAAM;AACrI,UAAI,sBAAsB,CAAC,GAAG,UAAU;AACxC,YAAM,uBAAuB,mBAAmB,OAAO,UAAQ;AAC7D,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,UAAU,gBAAgB,QAAQ,gBAAgB,UAAa,MAAM,QAAQ,WAAW,KAAK,YAAY,WAAW;AAC1H,eAAO,CAAC,WAAW,OAAO,aAAa;AAAA,MACzC,CAAC;AACD,iBAAW,QAAQ,sBAAsB;AACvC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,8BAAsB,oBAAoB,OAAO,UAAQ,SAAS,aAAa,IAAI,CAAC;AAAA,MACtF;AACA,YAAM,qBAAqB,mBAAmB,OAAO,UAAQ,KAAK,cAAc,QAAQ,OAAO,KAAK,WAAW,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,EAAE,eAAe,CAAC,EAAE,YAAY;AAC3K,UAAI,mBAAmB,QAAQ;AAC7B,4BAAoB,KAAK,CAAC,SAAS,YAAY;AAC7C,qBAAW,QAAQ,oBAAoB;AACrC,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AACJ,gBAAI,UAAU,WAAW;AACvB,oBAAM,gBAAgB,OAAO,SAAS,SAAS,SAAS;AACxD,kBAAI,kBAAkB,GAAG;AACvB,uBAAO,cAAc,WAAW,gBAAgB,CAAC;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AACF,SAAK,iCAAiC,cAAc,CAAC,KAAK,oBAAoB,KAAK,mBAAmB,KAAK,oBAAoB,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,WAAS;AAC/K,YAAM,CAAC,WAAW,UAAU,UAAU,IAAI;AAC1C,YAAM,eAAe,KAAK,KAAK,WAAW,SAAS,QAAQ,KAAK;AAChE,aAAO,aAAa;AAAA,IACtB,CAAC,GAAG,IAAI,CAAC,CAAC,WAAW,UAAU,UAAU,MAAM,WAAW,OAAO,YAAY,KAAK,UAAU,YAAY,QAAQ,CAAC,CAAC;AAClH,SAAK,yBAAyB,KAAK,iBAAiB,KAAK,UAAU,gBAAc,aAAa,KAAK,iCAAiC,KAAK,oBAAoB,CAAC;AAC9J,SAAK,SAAS,KAAK,iBAAiB,KAAK,UAAU,gBAAc,aAAa,KAAK,uBAAuB,KAAK,WAAW,GAAG,IAAI,UAAQ,KAAK,MAAM,GAAG,qBAAqB,CAAC;AAAA,EAC/K;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,IAAI,UAAU,oBAAoB;AAC5C,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB,oBAAoB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3F,UAAI,KAAK,QAAQ;AACf,aAAK,QAAQ,CAAC,GAAG,MAAM;AACrB,cAAI,EAAE,UAAU,KAAK,eAAe;AAClC,gBAAI,CAAC,EAAE,SAAS;AACd,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,WAAW,MAAM;AAAA,YACjE,OAAO;AACL,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,WAAW,OAAO;AAAA,YAClE;AACA,iBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,SAAS,CAAC;AACxD,gBAAI,CAAC,GAAG,UAAU;AAChB,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,QAAQ,OAAO,EAAE,KAAK,IAAI;AAAA,YAC1E,OAAO;AACL,mBAAK,SAAS,SAAS,KAAK,GAAG,eAAe,QAAQ,OAAO,EAAE,KAAK,IAAI;AAAA,YAC1E;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,kBAAkB,CAAC;AAAA,IAC7J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,iBAAiB,EAAE,GAAG,CAAC,MAAM,iBAAiB,EAAE,CAAC;AAAA,MACpE,QAAQ;AAAA,QACN,eAAe;AAAA,MACjB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,iBAAiB,IAAI,aAAa;AACvC,SAAK,wBAAwB;AAC7B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,gBAAgB,SAAS;AACvB,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,eAAe,QAAQ;AACrB,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK,MAAM;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,gBAAgB,WAAS,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACpF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,gBAAgB;AAClB,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,cAAc,QAAQ,KAAK,CAAC,KAAK,uBAAuB;AAC1D,WAAK,eAAe;AAAA,IACtB;AACA,QAAI,cAAc,SAAS,KAAK,CAAC,KAAK,yBAAyB;AAC7D,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,MAAM,cAAc,EAAE,GAAG,CAAC,MAAM,mBAAmB,EAAE,GAAG,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,MAC1M,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,8BAA8B,IAAI,gBAAgB,IAAI,eAAe,CAAC,EAAE,8BAA8B,IAAI,cAAc;AAAA,QACzI;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAON;AAAA,MACP,oBAAoBD;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,eAAe,IAAI,GAAG,cAAc,WAAW,mBAAmB,iBAAiB,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,QAAQ,UAAU,GAAG,CAAC,wBAAwB,IAAI,GAAG,gBAAgB,UAAU,WAAW,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,eAAe,IAAI,GAAG,iBAAiB,cAAc,WAAW,iBAAiB,CAAC;AAAA,MACzW,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qCAAqC,GAAG,GAAG,SAAS,CAAC;AAC9I,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,gBAAgB,IAAI,eAAe,CAAC;AAC9D,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,cAAc;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,sBAAsB,4BAA4B,MAAM,kBAAkB,kBAAqB,qBAAqB,aAAgB,iBAAoB,OAAO;AAAA,MAC9K,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,gBAAgB,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,kBAAkB,MAAM;AACnF,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,YAAY,MAAM;AAAA,CAC5E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BV,MAAM;AAAA,QACJ,sCAAsC;AAAA,QACtC,sCAAsC;AAAA,MACxC;AAAA,MACA,SAAS,CAAC,sBAAsB,4BAA4B,MAAM,kBAAkB,kBAAkB,WAAW;AAAA,MACjH,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,qBAAqB,gBAAgB,SAAS;AAC5C,UAAM,QAAQ,eAAe,QAAQ,OAAO;AAC5C,QAAI,UAAU,eAAe,SAAS,GAAG;AACvC,aAAO,eAAe,CAAC;AAAA,IACzB,OAAO;AACL,aAAO,eAAe,QAAQ,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,SAAK,iBAAiB,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,cAAc,MAAM;AAC3B,WAAK,aAAa,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,SAAK,eAAe,KAAK,KAAK;AAC9B,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,qBAAqB;AACnB,SAAK,oBAAoB,KAAK;AAAA,EAChC;AAAA,EACA,YAAY,MAAM,KAAK,QAAQ,UAAU;AACvC,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,oBAAoB,IAAI,QAAQ;AACrC,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,gBAAgB;AACrB,SAAK,YAAY;AACjB,SAAK,iBAAiB,CAAC,UAAU,WAAW,IAAI;AAChD,SAAK,mBAAmB,IAAI,QAAQ;AACpC,SAAK,sBAAsB;AAC3B,SAAK,wBAAwB;AAC7B,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,CAAC,UAAU,WAAW,IAAI;AAClD,SAAK,YAAY,CAAC;AAClB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,iBAAiB,IAAI,aAAa;AAAA,EACzC;AAAA,EACA,WAAW;AACT,SAAK,OAAO,kBAAkB,MAAM,UAAU,KAAK,KAAK,eAAe,OAAO,EAAE,KAAK,OAAO,MAAM,KAAK,UAAU,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC5J,YAAM,YAAY,KAAK,qBAAqB,KAAK,gBAAgB,KAAK,SAAS;AAC/E,WAAK,OAAO,IAAI,MAAM;AACpB,aAAK,aAAa,SAAS;AAC3B,aAAK,kBAAkB,KAAK,IAAI;AAAA,MAClC,CAAC;AAAA,IACH,CAAC,CAAC;AACF,SAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACtE,UAAI,KAAK,cAAc,OAAO;AAC5B,aAAK,YAAY;AACjB,aAAK,kBAAkB,KAAK,KAAK;AAAA,MACnC;AACA,WAAK,mBAAmB;AACxB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,kBAAkB;AACpB,UAAI,KAAK,oBAAoB,KAAK,iBAAiB,QAAQ;AACzD,aAAK,iBAAiB,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,aAAa;AACf,WAAK,YAAY,KAAK;AACtB,WAAK,aAAa,KAAK,WAAW;AAAA,IACpC;AACA,QAAI,YAAY;AACd,WAAK,sBAAsB;AAAA,IAC7B;AACA,QAAI,cAAc;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AACA,UAAM,gBAAgB,WAAS,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACpF,SAAK,cAAc,WAAW,KAAK,cAAc,QAAQ,MAAM,CAAC,KAAK,qBAAqB;AACxF,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,cAAc,SAAS,KAAK,CAAC,KAAK,uBAAuB;AAC3D,WAAK,eAAe;AAAA,IACtB;AACA,SAAK,aAAa,qBAAqB,KAAK,cAAc;AACxD,YAAM,cAAc,KAAK,UAAU,OAAO,UAAQ,KAAK,SAAS,EAAE,IAAI,UAAQ,KAAK,KAAK;AACxF,WAAK,gBAAgB,KAAK,mBAAmB,cAAc,YAAY,CAAC,KAAK;AAAA,IAC/E;AACA,QAAI,YAAY,cAAc,kBAAkB,WAAW;AACzD,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IAClM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,MAAM,cAAc,EAAE,GAAG,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,kBAAkB,EAAE,CAAC;AAAA,MACrM,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gCAAgC,IAAI,UAAU,EAAE,yBAAyB,IAAI,cAAc,aAAa,IAAI,cAAc,QAAQ;AAAA,QACnJ;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACrG,OAAOE;AAAA,MACP,oBAAoBE;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,mBAAmB,iBAAiB,gBAAgB,kBAAkB,gBAAgB,gBAAgB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,gBAAgB,mBAAmB,iBAAiB,gBAAgB,kBAAkB,cAAc,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,aAAa,kBAAkB,iBAAiB,CAAC;AAAA,MACra,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgBD,IAAG;AACtB,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,mBAAmB,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACxf;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,uBAA0B,YAAY,CAAC;AAC7C,UAAG,WAAW,QAAQ,IAAI,gBAAgB,IAAI,cAAc,EAAE,YAAY,oBAAoB;AAAA,QAChG;AAAA,MACF;AAAA,MACA,cAAc,CAAC,wBAAwB,MAAM,kBAAkB,uBAAuB;AAAA,MACtF,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,cAAc,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,gBAAgB,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,kBAAkB,MAAM;AAAA,CAClF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4BV,MAAM;AAAA,QACJ,wCAAwC;AAAA,QACxC,iCAAiC;AAAA,MACnC;AAAA,MACA,WAAW,CAAC,gBAAgB;AAAA,MAC5B,SAAS,CAAC,wBAAwB,MAAM,kBAAkB,uBAAuB;AAAA,MACjF,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,UAAU,YAAY;AAChC,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,SAAS;AACtB,YAAM,MAAM,KAAK,WAAW,KAAK;AACjC,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,WAAW,GAAG,GAAG,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,SAAS;AAAA,MACxE;AAAA,IACF;AACA,QAAI,WAAW,SAAS;AACtB,YAAM,MAAM,KAAK,WAAW,KAAK;AACjC,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,aAAK,SAAS,aAAa,KAAK,WAAW,eAAe,WAAW,GAAG,GAAG,EAAE;AAAA,MAC/E,OAAO;AACL,aAAK,SAAS,gBAAgB,KAAK,WAAW,eAAe,SAAS;AAAA,MACxE;AAAA,IACF;AACA,QAAI,WAAW,SAAS;AACtB,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,IAChH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,CAAC;AAAA,MAClB,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,eAAe,CAAC;AACrB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,wBAAwB;AAC7B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,gBAAgB,SAAS;AACvB,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,gBAAgB,WAAS,SAAS,MAAM,eAAe,MAAM,iBAAiB;AACpF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,cAAc;AAChB,WAAK,wBAAwB;AAAA,IAC/B;AACA,QAAI,gBAAgB;AAClB,WAAK,0BAA0B;AAAA,IACjC;AACA,QAAI,cAAc,YAAY,KAAK,CAAC,KAAK,uBAAuB;AAC9D,WAAK,qBAAqB;AAAA,IAC5B;AACA,QAAI,cAAc,SAAS,KAAK,CAAC,KAAK,yBAAyB;AAC7D,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,gBAAgB,EAAE,GAAG,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,MAAM,kBAAkB,EAAE,GAAG,CAAC,MAAM,sBAAsB,EAAE,CAAC;AAAA,MAC/H,WAAW,CAAC,GAAG,4BAA4B;AAAA,MAC3C,QAAQ;AAAA,QACN,cAAc;AAAA,QACd,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,MACtB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,oBAAoBH;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,iBAAiB,WAAW,YAAY,iBAAiB,SAAS,oBAAoB,gBAAgB,kBAAkB,CAAC;AAAA,MACtI,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,sBAAsB,CAAC;AAC5C,UAAG,WAAW,iBAAiB,SAAS,4EAA4E,QAAQ;AAC1H,mBAAO,IAAI,gBAAgB,MAAM;AAAA,UACnC,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,SAAS,EAAE,YAAY,IAAI,UAAU,EAAE,iBAAiB,IAAI,eAAe,EAAE,SAAS,IAAI,OAAO,EAAE,oBAAoB,IAAI,YAAY,EAAE,gBAAgB,IAAI,cAAc,EAAE,oBAAoB,IAAI,kBAAkB;AAAA,QACtP;AAAA,MACF;AAAA,MACA,cAAc,CAAC,yBAAyB;AAAA,MACxC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,kBAAkB,MAAM;AACvF,WAAW,CAAC,aAAa,CAAC,GAAG,uBAAuB,WAAW,sBAAsB,MAAM;AAAA,CAC1F,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,yBAAyB;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAsB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,MAAM,WAAW,EAAE,CAAC;AAAA,MACxD,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,cAAc,IAAI,OAAO;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,sBAAsB;AAAA,MACxB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,cAAc,EAAE,GAAG,CAAC,MAAM,cAAc,EAAE,CAAC;AAAA,MAC9D,UAAU;AAAA,MACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,2BAA2B,IAAI,UAAU;AAAA,QAC1D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,MACd;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,wBAAwB,WAAW,cAAc,MAAM;AAAA,CACnF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,mCAAmC;AAAA,MACrC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA0B;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,MAAM,eAAe,EAAE,CAAC;AAAA,MAChE,UAAU;AAAA,MACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,cAAc,IAAI,cAAc,cAAc,EAAE;AAAA,QACjE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,yBAAyB,WAAW,eAAe,MAAM;AAAA,CACrF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,sBAAsB;AAAA,MACxB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB,CAAC;AACvB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,oBAAoB,EAAE,CAAC;AAAA,MAC7C,UAAU;AAAA,MACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,WAAW,EAAE,SAAS,IAAI,OAAO,EAAE,aAAa,IAAI,UAAU,SAAS,IAAI;AAC9G,UAAG,YAAY,mBAAmB,IAAI,OAAO;AAAA,QAC/C;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,oBAAoBA;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,SAAS,YAAY,GAAG,SAAS,SAAS,GAAG,CAAC,SAAS,mBAAmB,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,iBAAiB,CAAC;AAAA,MAClJ,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,wCAAwC,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,0CAA0C,GAAG,GAAG,SAAS,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,eAAe,CAAC;AACjN,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,cAAc;AAC3C,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,aAAa;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,eAAe;AAAA,QACvD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,kBAAkB,MAAM,OAAO;AAAA,MAC9C,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,MAAM;AAAA,QACJ,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,CAAC,kBAAkB,MAAM,OAAO;AAAA,MACzC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,qBAAqB,UAAU;AACzC,SAAK,sBAAsB;AAC3B,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,gBAAgB,IAAI;AAC1C,SAAK,qBAAqB,IAAI,gBAAgB,KAAK;AACnD,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,QAAI,KAAK,qBAAqB;AAC5B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,yBAAmB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,kBAAkB;AACnF,iBAAW,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,UAAU;AAAA,IACrE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,oBAAoB,aAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AACtF,WAAK,SAAS,aAAa,KAAK,UAAU,eAAe,WAAW,GAAG,KAAK,EAAE;AAAA,IAChF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA6B,kBAAkB,mBAAmB,GAAM,kBAAqB,SAAS,CAAC;AAAA,IAC1H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,sBAAsB,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC;AAAA,MACpE,WAAW,SAAS,+BAA+B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,QAClE;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,oBAAoBA;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,iBAAiB,gBAAgB,GAAG,CAAC,SAAS,gCAAgC,SAAS,kDAAkD,GAAG,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,gCAAgC,GAAG,YAAY,UAAU,QAAQ,OAAO,YAAY,QAAQ,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChW,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,MAAM,GAAG,CAAC;AAC/B,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,OAAO,CAAC;AACxE,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,iDAAiD,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC3H;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,kBAAkB,CAAC,EAAE,YAAY,kBAAkB;AAAA,QACpG;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,WAAW,gBAAgB;AAAA,MAChD,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAeV,SAAS,CAAC,MAAM,WAAW,gBAAgB;AAAA,MAC3C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,iBAAiB,CAAC;AACvB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,GAAG;AAC3D,aAAO,KAAK,KAAK,+BAA8B;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,wBAAwB,CAAC;AAAA,MACtC,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mBAAmB,GAAG,CAAC,oBAAoB,IAAI,GAAG,mBAAmB,eAAe,kBAAkB,eAAe,CAAC;AAAA,MACnI,UAAU,SAAS,sCAAsC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,SAAS,CAAC;AAC1B,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,mBAAmB,IAAI,eAAe,EAAE,eAAe,IAAI,WAAW,EAAE,kBAAkB,IAAI,cAAc,EAAE,iBAAiB,IAAI,aAAa;AAAA,QAChK;AAAA,MACF;AAAA,MACA,cAAc,CAAC,uBAAuB;AAAA,MACtC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,uBAAuB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,kBAAkB,QAAQ;AACpC,SAAK,mBAAmB;AACxB,SAAK,SAAS;AACd,SAAK,sBAAsB,CAAC;AAC5B,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,YAAY,GAAG,KAAK;AAClB,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,eAAe,CAAC,EAAE,KAAK,UAAU,UAAQ,cAAc,KAAK,QAAQ,EAAE,IAAI,UAAQ,KAAK,iBAAiB,QAAQ,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM;AAC3L,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,OAAO,sBAAsB;AACvC,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAOpE,UAAI,KAAK,kBAAkB,UAAU,OAAO,gBAAgB,GAAG;AAC7D,aAAK,gBAAgB,KAAK,IAAI;AAAA,MAChC,OAAO;AACL,aAAK,OAAO,IAAI,MAAM,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,MACvD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAuB,gBAAgB,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACrH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,wBAAwB,EAAE,CAAC;AAAA,MAC9C,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB;AAAA,QACrE;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,uBAAuB;AAAA,MACtC,QAAQ;AAAA,QACN,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,iBAAiB,SAAS,2CAA2C,GAAG,SAAS,WAAW,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,WAAW,OAAO,UAAU,OAAO,UAAU,KAAK,CAAC;AAAA,MAC9N,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,MAAM,CAAC;AAAA,QACpE;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,IAAI,mBAAmB,EAAE,gBAAgB,IAAI,WAAW;AAAA,QACnF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,OAAO;AAAA,MACtB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,OAAO;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,qBAAqB;AAC/B,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB;AACrB,SAAK,aAAa,IAAI,gBAAgB,KAAK;AAC3C,SAAK,YAAY,IAAI,gBAAgB,MAAS;AAC9C,SAAK,uBAAuB,IAAI,gBAAgB,CAAC,CAAC;AAClD,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB,CAAC,CAAC,KAAK;AAC5B,QAAI,KAAK,qBAAqB;AAC5B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,gBAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,SAAS;AACjE,2BAAqB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,oBAAoB;AACvF,iBAAW,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,UAAU;AAAA,IACrE;AAAA,EACF;AAAA,EACA,wBAAwB,iBAAiB;AACvC,SAAK,oBAAoB,mBAAmB,eAAe;AAAA,EAC7D;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAkB,qBAAqB,CAAC,CAAC;AAAA,IACjF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,OAAO,CAAC;AAAA,MACrB,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,mBAAmB,IAAI,aAAa;AAAA,QACrD;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoBA;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,yBAAyB,sBAAsB,IAAI,GAAG,MAAM,GAAG,CAAC,wBAAwB,IAAI,GAAG,uBAAuB,mBAAmB,GAAG,MAAM,GAAG,CAAC,wBAAwB,IAAI,GAAG,mBAAmB,qBAAqB,GAAG,CAAC,sBAAsB,IAAI,GAAG,uBAAuB,GAAG,CAAC,mBAAmB,SAAS,GAAG,iBAAiB,CAAC;AAAA,MAC1W,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAClF,UAAG,OAAO,GAAG,OAAO;AACpB,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,MAAM,CAAC;AAC9D,UAAG,OAAO,GAAG,OAAO;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,oBAAoB,CAAC;AACpE,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAW,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC;AAAA,QAC5D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,WAAW,sBAAsB,0BAA0B,eAAoB,qBAAqB;AAAA,MACzH,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,MAAM;AAAA,QACJ,2BAA2B;AAAA,MAC7B;AAAA,MACA,SAAS,CAAC,MAAM,WAAW,sBAAsB,0BAA0B,aAAa;AAAA,MACxF,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,2BAA2B,QAAQ,OAAO;AACxC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK,iBAAiB;AAC1B,UAAM,gBAAgB;AACtB,UAAM,iBAAiB;AACvB,QAAI,gBAAgB,eAAe,gBAAgB,KAAK,OAAO;AAC7D,WAAK,SAAS,YAAY,KAAK,kBAAkB,aAAa;AAC9D,WAAK,SAAS,YAAY,KAAK,kBAAkB,cAAc;AAAA,IACjE,WAAW,eAAe,GAAG;AAC3B,WAAK,SAAS,YAAY,KAAK,kBAAkB,aAAa;AAC9D,WAAK,SAAS,SAAS,KAAK,kBAAkB,cAAc;AAAA,IAC9D,WAAW,gBAAgB,aAAa,aAAa;AACnD,WAAK,SAAS,YAAY,KAAK,kBAAkB,cAAc;AAC/D,WAAK,SAAS,SAAS,KAAK,kBAAkB,aAAa;AAAA,IAC7D,OAAO;AACL,WAAK,SAAS,SAAS,KAAK,kBAAkB,aAAa;AAC3D,WAAK,SAAS,SAAS,KAAK,kBAAkB,cAAc;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,YAAY,UAAU,QAAQ,UAAU,eAAe;AACrD,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,OAAO,CAAC;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,cAAc,CAAC;AACpB,SAAK,iBAAiB,CAAC;AACvB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB,WAAS;AAClC,SAAK,iBAAiB,CAAC;AACvB,SAAK,eAAe,CAAC;AACrB,SAAK,yBAAyB;AAC9B,SAAK,sBAAsB;AAC3B,SAAK,QAAQ,IAAI,QAAQ;AACzB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,SAAS;AACtB,YAAM,uBAAuB,KAAK,2BAA2B;AAC7D,WAAK,iBAAiB;AAAA,QACpB,WAAW;AAAA,QACX,WAAW,KAAK,WAAW,uBAAuB,WAAW;AAAA,MAC/D;AACA,WAAK,eAAe;AAAA,QAClB,WAAW,KAAK,UAAU,WAAW;AAAA,QACrC,WAAW,KAAK,UAAU,SAAS;AAAA,QACnC,WAAW,KAAK;AAAA,MAClB;AAGA,WAAK,OAAO,kBAAkB,MAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,IACzD;AACA,QAAI,MAAM;AAER,WAAK,OAAO,kBAAkB,MAAM,KAAK,MAAM,KAAK,CAAC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,OAAO,kBAAkB,MAAM;AAClC,cAAM,eAAe,KAAK,QAAQ,KAAK,UAAU,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,MAAM,UAAU,KAAK,iBAAiB,eAAe,QAAQ,EAAE,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC3L,cAAM,UAAU,KAAK,cAAc,UAAU,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC;AAC5E,cAAM,QAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC;AACtD,cAAM,gBAAgB,MAAM,cAAc,SAAS,OAAO,KAAK,OAAO,EAAE,KAAK,UAAU,IAAI,GAAG,MAAM,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAChI,sBAAc,UAAU,MAAM,KAAK,2BAA2B,CAAC;AAC/D,qBAAa,KAAK,OAAO,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,cAAc,aAAa,KAAK,iBAAiB,cAAc,UAAU;AAAA,MACnK,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,2BAA2B,IAAI;AACpC,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,aAAO,KAAK,KAAK,8BAAgC,kBAAqB,SAAS,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,QAAQ,GAAM,kBAAqB,eAAe,CAAC;AAAA,IAClM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,SAAS,kCAAkC,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,GAAG,UAAU;AAClC,UAAG,YAAY,MAAM,GAAG,UAAU;AAClC,UAAG,YAAY,0BAA0B,GAAG,wBAAwB;AAAA,QACtE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AACzE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,QACjF;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,qBAAqB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,2BAA2B,GAAG,SAAS,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,eAAe,GAAG,CAAC,SAAS,kBAAkB,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,eAAe,eAAe,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,iBAAiB,GAAG,CAAC,GAAG,YAAY,eAAe,aAAa,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,gBAAgB,GAAG,CAAC,GAAG,iBAAiB,mBAAmB,sBAAsB,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,qBAAqB,GAAG,SAAS,GAAG,CAAC,oBAAoB,IAAI,eAAe,SAAS,GAAG,WAAW,kBAAkB,iBAAiB,iBAAiB,CAAC;AAAA,MACh8B,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,4CAA4C,GAAG,GAAG,OAAO,CAAC;AAAA,QAC9J;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,QAAQ,IAAI,OAAO;AACjC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,OAAO;AAAA,QACpC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,yBAAyB,MAAM,SAAS,iBAAsB,2BAAgC,iBAAsB,0BAA0B,kBAAkB,gBAAgB;AAAA,MAC/L,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmDV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,yBAAyB,MAAM,SAAS,iBAAiB,kBAAkB,gBAAgB;AAAA,MACrG,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,QAC3B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,QAC/B,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO,uBAAuB,MAAM,MAAM;AACxC,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,GAAG;AAC5D,aAAO,KAAK,KAAK,gCAAkC,kBAAqB,WAAW,CAAC;AAAA,IACtF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,cAAc;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,aAAO,KAAK,KAAK,8BAA6B;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,yCAAyC,IAAI,KAAK;AACvE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,mBAAmB,IAAI,UAAU,IAAI,EAAE,oBAAoB,IAAI,WAAW,IAAI;AAAA,QAC/F;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACtC,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAAA,QAChL;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,0BAA0B,IAAI,KAAK;AACjD,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAqB,+BAA+B;AAAA,MACnE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,MAAM;AAAA,QACJ,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,MAC9B;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAMQ,yBAAwB;AAC9B,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,iBAAiB,MAAM;AACrB,SAAK,mBAAmB,eAAe,IAAI;AAAA,EAC7C;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,mBAAmB,gBAAgB,KAAK;AAAA,EAC/C;AAAA,EACA,YAAY,YAAY,kBAAkB,iBAAiB,KAAK,qBAAqB,oBAAoB,gBAAgB;AACvH,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,MAAM;AACX,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,gBAAgBA;AACrB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,oBAAoB,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;AAC5C,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,uBAAuB;AAC5B,SAAK,sBAAsB,WAAS;AACpC,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,gBAAgB,CAAC;AACtB,SAAK,SAAS,CAAC;AACf,SAAK,iBAAiB,CAAC;AACvB,SAAK,uBAAuB;AAC5B,SAAK,WAAW;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAC1B,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,WAAW;AAChB,SAAK,mBAAmB,IAAI,aAAa;AACzC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,0BAA0B,IAAI,aAAa;AAChD,SAAK,uBAAuB,IAAI,aAAa;AAE7C,SAAK,OAAO,CAAC;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,qBAAqB,CAAC;AAC3B,SAAK,uBAAuB,CAAC;AAC7B,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB,IAAI,gBAAgB,KAAK;AAC9C,SAAK,MAAM;AACX,SAAK,yBAAyB;AAC9B,SAAK,gBAAgB,iCAAiCA,sBAAqB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1H,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,iBAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,aAAa;AACxE,uBAAmB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AACvE,UAAI,cAAc,KAAK,aAAa;AAClC,aAAK,cAAc;AACnB,aAAK,kBAAkB,KAAK,SAAS;AAAA,MACvC;AAAA,IACF,CAAC;AACD,sBAAkB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACrE,UAAI,aAAa,KAAK,YAAY;AAChC,aAAK,aAAa;AAClB,aAAK,iBAAiB,KAAK,QAAQ;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,MAAM,KAAK,iBAAiB,CAAC,EAAE,UAAU,WAAS;AAC7F,UAAI,UAAU,KAAK,SAAS;AAC1B,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AACD,2BAAuB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACtE,WAAK,OAAO;AACZ,WAAK,wBAAwB,KAAK,IAAI;AACtC,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,wBAAoB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACnE,WAAK,iBAAiB;AACtB,WAAK,qBAAqB,KAAK,IAAI;AACnC,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,mBAAe,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,mBAAiB;AACvE,WAAK,gBAAgB;AACrB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,gBAAY,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,gBAAc;AACjE,WAAK,aAAa;AAClB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,iBAAa,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACnE,WAAK,cAAc;AACnB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,kBAAc,CAAC,QAAQ,KAAK,aAAa,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,OAAO,YAAY,MAAM,UAAU,KAAK,CAAC,YAAY,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1J,WAAK,oBAAoB,aAAa,KAAK;AAAA,IAC7C,CAAC;AACD,SAAK,yBAAyB,iBAAiB,UAAU;AACzD,SAAK,oBAAoB,uBAAuB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACtG,WAAK,qBAAqB;AAC1B,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,oBAAoB,qBAAqB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACpG,WAAK,uBAAuB;AAC5B,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,aAAa;AACf,WAAK,mBAAmB,gBAAgB,KAAK,WAAW;AAAA,IAC1D;AACA,QAAI,YAAY;AACd,WAAK,mBAAmB,eAAe,KAAK,UAAU;AAAA,IACxD;AACA,QAAI,QAAQ;AACV,WAAK,SAAS,KAAK,UAAU,CAAC;AAC9B,WAAK,mBAAmB,iBAAiB,KAAK,MAAM;AAAA,IACtD;AACA,QAAI,gBAAgB;AAClB,WAAK,iBAAiB,KAAK,kBAAkB,CAAC;AAC9C,WAAK,mBAAmB,yBAAyB,KAAK,cAAc;AAAA,IACtE;AACA,QAAI,mBAAmB;AACrB,WAAK,mBAAmB,sBAAsB,KAAK,iBAAiB;AAAA,IACtE;AACA,QAAI,UAAU;AACZ,WAAK,mBAAmB;AAAA,IAC1B;AACA,QAAI,eAAe;AACjB,WAAK,oBAAoB,oBAAoB,KAAK,aAAa;AAAA,IACjE;AACA,QAAI,gBAAgB;AAClB,WAAK,cAAc,KAAK,KAAK,cAAc;AAAA,IAC7C;AACA,QAAI,YAAY;AACd,WAAK,oBAAoB,YAAY,KAAK,UAAU;AAAA,IACtD;AACA,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAChB,SAAK,iBAAiB,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM;AACnE,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,OAAO,sBAAsB;AACvC,YAAM,iBAAiB,KAAK,UAAU,KAAK,yBAAyB;AACpE,aAAO,KAAK,MAAM,QAAQ,cAAc;AAAA,IAC1C,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,oBAAoB,UAAU;AAC3E,QAAI,KAAK,+BAA+B,KAAK,4BAA4B,0BAA0B;AACjG,WAAK,2BAA2B,KAAK,4BAA4B;AAAA,IACnE;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,KAAK,YAAY,KAAK,SAAS,KAAK;AACnD,SAAK,UAAU,KAAK,YAAY,KAAK,SAAS,KAAK;AACnD,SAAK,oBAAoB,UAAU,KAAK,SAAS,KAAK,OAAO;AAAA,EAC/D;AAAA,EACA,uBAAuB;AACrB,SAAK,iBAAiB,KAAK,sBAAsB,KAAK,OAAO,SAAS,KAAK,cAAc,KAAK,OAAO,SAAS,KAAK,CAAC,KAAK,sBAAsB,CAAC,KAAK,qBAAqB,KAAK,UAAU,KAAK;AAAA,EAChM;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,UAAU,GAAM,kBAAuB,gBAAgB,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,mBAAmB,GAAM,kBAAkB,kBAAkB,GAAM,kBAAuB,gBAAgB,CAAC,CAAC;AAAA,IAC9U;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,+BAA+B,CAAC;AAAA,QAC9D;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B,GAAG;AAAA,QACjF;AAAA,MACF;AAAA,MACA,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,CAAC;AAAA,QAC/C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,8BAA8B,GAAG;AAAA,QACpF;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,QAAQ,KAAK,EAAE,2BAA2B,IAAI,eAAe,MAAM;AAAA,QACjH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,aAAa;AAAA,QACb,cAAc;AAAA,QACd,SAAS;AAAA,QACT,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,kBAAkB;AAAA,QAClB,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,yBAAyB;AAAA,QACzB,sBAAsB;AAAA,MACxB;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,kBAAkB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MAC5H,oBAAoBR;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,WAAW,cAAc,aAAa,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,WAAW,WAAW,mBAAmB,kBAAkB,iBAAiB,0BAA0B,mBAAmB,mBAAmB,sBAAsB,sBAAsB,oBAAoB,qBAAqB,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,QAAQ,WAAW,WAAW,mBAAmB,kBAAkB,iBAAiB,0BAA0B,mBAAmB,mBAAmB,sBAAsB,sBAAsB,oBAAoB,mBAAmB,GAAG,CAAC,GAAG,eAAe,kBAAkB,iBAAiB,iBAAiB,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,SAAS,mDAAmD,GAAG,UAAU,qBAAqB,qBAAqB,gBAAgB,qBAAqB,sBAAsB,eAAe,UAAU,cAAc,WAAW,YAAY,eAAe,oBAAoB,qBAAqB,GAAG,MAAM,GAAG,CAAC,GAAG,wBAAwB,8BAA8B,GAAG,oBAAoB,qBAAqB,UAAU,qBAAqB,qBAAqB,gBAAgB,qBAAqB,sBAAsB,eAAe,UAAU,cAAc,WAAW,YAAY,aAAa,CAAC;AAAA,MACr7C,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,WAAW,CAAC;AACjC,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAClF,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,yBAAyB,CAAC,EAAE,GAAG,mDAAmD,GAAG,IAAI,yBAAyB,CAAC,EAAE,GAAG,yCAAyC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,mDAAmD,GAAG,GAAG,yBAAyB,CAAC;AAC1X,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAClF,UAAG,aAAa;AAChB,UAAG,WAAW,IAAI,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,IAAI,0CAA0C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QAC5N;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,qBAAwB,YAAY,CAAC;AAC3C,UAAG,WAAW,WAAW,IAAI,cAAc,EAAE,cAAc,IAAI,SAAS,EAAE,eAAe,IAAI,kBAAkB;AAC/G,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,yBAAyB,UAAU,IAAI,yBAAyB,KAAK;AAC/F,UAAG,UAAU;AACb,UAAG,YAAY,iBAAiB,IAAI,QAAQ,KAAK,EAAE,0BAA0B,IAAI,OAAO,UAAU,IAAI,OAAO,EAAE,0BAA0B,IAAI,OAAO,EAAE,0BAA0B,IAAI,UAAU,EAAE,2BAA2B,IAAI,WAAW,EAAE,sBAAsB,IAAI,UAAU,EAAE,yBAAyB,IAAI,mBAAmB,CAAC,IAAI,UAAU,EAAE,oBAAoB,IAAI,WAAW,QAAQ,EAAE,mBAAmB,IAAI,WAAW,OAAO;AACza,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,OAAO;AACjC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,OAAO,EAAE,YAAY,kBAAkB;AAChF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,QAAQ;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,yBAAyB,UAAU,IAAI,yBAAyB,QAAQ;AAAA,QACpG;AAAA,MACF;AAAA,MACA,cAAc,CAAC,iBAAiB,MAAM,kBAAkB,6BAA6B,6BAA6B,8BAA8B,oBAAyB,qBAAqB;AAAA,MAC9L,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,qBAAqB,MAAM;AACpF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,kBAAkB,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,oBAAoB,MAAM;AACnF,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,aAAa,MAAM;AAC5E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,mBAAmB,MAAM;AAClF,WAAW,CAAC,WAAW,CAAC,GAAG,iBAAiB,WAAW,sBAAsB,MAAM;AACnF,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAC3F,WAAW,CAAC,WAAW,CAAC,GAAG,iBAAiB,WAAW,UAAU,MAAM;AACvE,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,iBAAiB,WAAW,qBAAqB,MAAM;AAClG,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,iBAAiB,WAAW,sBAAsB,MAAM;AACnG,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,iBAAiB,WAAW,qBAAqB,MAAM;AAClG,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,iBAAiB,WAAW,YAAY,MAAM;AAAA,CACxF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,kBAAkB;AAAA,MACnD,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAyEV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,QACjC,mCAAmC;AAAA,MACrC;AAAA,MACA,SAAS,CAAC,iBAAiB,MAAM,kBAAkB,6BAA6B,6BAA6B,8BAA8B,kBAAkB;AAAA,MAC7J,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,+BAA+B;AAAA,QACpC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,6BAA6B,CAAC;AAAA,MAC5B,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,qBAAqB;AAC/B,SAAK,sBAAsB;AAC3B,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,sBAAsB,IAAI,cAAc,CAAC;AAC9C,SAAK,iBAAiB,IAAI,cAAc,CAAC;AACzC,SAAK,6BAA6B,KAAK,oBAAoB,KAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,KAAK,qBAAqB,GAAG,KAAK,IAAI,OAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,KAAK,mBAAmB,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC9N,SAAK,gCAAgC,KAAK,2BAA2B,KAAK,IAAI,UAAQ,KAAK,OAAO,UAAQ,KAAK,WAAW,KAAK,CAAC,CAAC;AACjI,SAAK,iCAAiC,KAAK,2BAA2B,KAAK,IAAI,UAAQ,KAAK,OAAO,UAAQ,KAAK,YAAY,KAAK,CAAC,CAAC;AACnI,SAAK,wBAAwB,KAAK,eAAe,KAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,KAAK,gBAAgB,GAAG,KAAK,IAAI,OAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,KAAK,cAAc,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC1M,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,yBAAyB,QAAQ,KAAK,UAAU,KAAK,wBAAwB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,mBAAmB;AACjJ,WAAK,oBAAoB,QAAQ,KAAK,UAAU,KAAK,mBAAmB,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,KAAK,cAAc;AAElI,WAAK,8BAA8B,UAAU,qBAAmB;AAC9D,wBAAgB,QAAQ,UAAQ,KAAK,cAAc,SAAS,gBAAgB,gBAAgB,SAAS,CAAC,CAAC,CAAC;AAAA,MAC1G,CAAC;AACD,WAAK,+BAA+B,UAAU,sBAAoB;AAChE,yBAAiB,QAAQ,UAAQ,KAAK,gBAAgB,SAAS,iBAAiB,CAAC,CAAC,CAAC;AAAA,MACrF,CAAC;AAED,oBAAc,CAAC,KAAK,oBAAoB,sBAAsB,KAAK,6BAA6B,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,cAAc,MAAM;AACjL,uBAAe,QAAQ,CAAC,MAAM,UAAU;AACtC,cAAI,KAAK,YAAY;AACnB,kBAAM,eAAe,eAAe,MAAM,GAAG,KAAK;AAClD,kBAAM,QAAQ,aAAa,OAAO,CAAC,KAAK,QAAQ,OAAO,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC;AAC1F,kBAAM,QAAQ,gBAAgB,MAAM,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC;AAC/E,iBAAK,iBAAiB,GAAG,KAAK,IAAI;AAAA,UACpC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,oBAAc,CAAC,KAAK,oBAAoB,sBAAsB,KAAK,8BAA8B,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,eAAe,MAAM;AACnL,wBAAgB,QAAQ,CAAC,GAAG,UAAU;AACpC,gBAAM,OAAO,gBAAgB,gBAAgB,SAAS,QAAQ,CAAC;AAC/D,cAAI,KAAK,aAAa;AACpB,kBAAM,eAAe,gBAAgB,MAAM,gBAAgB,SAAS,OAAO,gBAAgB,MAAM;AACjG,kBAAM,QAAQ,aAAa,OAAO,CAAC,KAAK,QAAQ,OAAO,IAAI,WAAW,IAAI,WAAW,IAAI,CAAC;AAC1F,kBAAM,QAAQ,gBAAgB,MAAM,gBAAgB,SAAS,OAAO,gBAAgB,MAAM,EAAE,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC;AAC7H,iBAAK,kBAAkB,GAAG,KAAK,IAAI;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,kBAAkB,qBAAqB,CAAC,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,GAAG,WAAW,IAAI,GAAG,kBAAkB,IAAI,GAAG,wBAAwB,IAAI,GAAG,YAAY,IAAI,GAAG,sBAAsB,EAAE,CAAC;AAAA,MAC5I,gBAAgB,SAAS,6BAA6B,IAAI,KAAK,UAAU;AACvE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,sBAAsB,CAAC;AACnD,UAAG,eAAe,UAAU,sBAAsB,CAAC;AAAA,QACrD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,aAAa;AAAA,QACnD;AAAA,MACF;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,yBAAyB;AAAA,MAC3B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,YAAY,UAAU,qBAAqB,oBAAoB;AACzE,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB;AACrB,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,gBAAgB,CAAC,CAAC,KAAK;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,iBAAiB,KAAK,WAAW;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,qBAAqB;AAC5B,YAAM,iBAAiB,KAAK,oBAAoB,QAAQ,KAAK,UAAU,KAAK,mBAAmB,GAAG,IAAI,UAAQ,QAAQ,KAAK,KAAK,CAAC;AACjI,YAAM,wBAAwB,eAAe,KAAK,UAAU,mBAAiB,gBAAgB,cAAc,wBAAwB,KAAK,GAAG,UAAU,KAAK,QAAQ,CAAC;AACnK,4BAAsB,UAAU,UAAQ,KAAK,oBAAoB,YAAY,IAAI,CAAC;AAElF,WAAK,oBAAoB,mBAAmB,KAAK,UAAU,YAAU,SAAS,wBAAwB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ,KAAK,oBAAoB,uBAAuB,IAAI,CAAC;AACrN,YAAM,gCAAgC,eAAe,KAAK,UAAU,aAAW,UAAU,QAAQ,gCAAgC,KAAK,GAAG,UAAU,KAAK,QAAQ,CAAC;AACjK,YAAM,iCAAiC,eAAe,KAAK,UAAU,aAAW,UAAU,QAAQ,iCAAiC,KAAK,GAAG,UAAU,KAAK,QAAQ,CAAC;AACnK,oCAA8B,UAAU,2BAAyB;AAC/D,aAAK,oBAAoB,cAAc,sBAAsB,WAAW,CAAC;AAAA,MAC3E,CAAC;AACD,qCAA+B,UAAU,4BAA0B;AACjE,aAAK,oBAAoB,eAAe,uBAAuB,WAAW,CAAC;AAAA,MAC7E,CAAC;AAAA,IACH;AACA,QAAI,KAAK,oBAAoB;AAC3B,YAAM,gBAAgB,KAAK,yBAAyB,QAAQ,KAAK,UAAU,KAAK,wBAAwB,CAAC;AACzG,YAAM,cAAc,cAAc,KAAK,UAAU,MAAM,MAAM,GAAG,KAAK,yBAAyB,IAAI,QAAM,GAAG,iBAAiB,CAAC,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC;AACzJ,kBAAY,UAAU,UAAQ;AAC5B,cAAM,YAAY;AAAA,UAChB,KAAK,KAAK;AAAA,UACV,OAAO,KAAK;AAAA,QACd;AACA,aAAK,kBAAkB,KAAK,SAAS;AACrC,YAAI,KAAK,YAAY,KAAK,mBAAmB,OAAO;AAClD,eAAK,yBAAyB,OAAO,QAAM,OAAO,IAAI,EAAE,QAAQ,QAAM,GAAG,eAAe,CAAC;AAAA,QAC3F;AAAA,MACF,CAAC;AACD,YAAM,sBAAsB,cAAc;AAAA,QAAK,UAAU,UAAQ,MAAM,GAAG,CAAC,eAAe,GAAG,KAAK,IAAI,OAAK,EAAE,mBAAmB,CAAC,CAAC,EAAE,KAAK,SAAS,MAAM,aAAa,CAAC,CAAC;AAAA,QAAG,IAAI,UAAQ,KAAK,OAAO,UAAQ,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,KAAK,UAAU,EAAE,IAAI,UAAQ;AAC1P,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,iBAAO;AAAA,YACL,KAAK;AAAA,YACL,QAAQ;AAAA,YACR,cAAc;AAAA,YACd;AAAA,YACA,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF,CAAC,CAAC;AAAA;AAAA,QAEF,MAAM,CAAC;AAAA,QAAG,UAAU,KAAK,QAAQ;AAAA,MAAC;AAClC,0BAAoB,UAAU,UAAQ;AACpC,aAAK,mBAAmB,oBAAoB,KAAK,IAAI;AAAA,MACvD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,qBAAqB;AAC5B,WAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,IAClH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,qBAAqB,CAAC,GAAM,kBAAkB,oBAAoB,CAAC,CAAC;AAAA,IACvM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,GAAG,iBAAiB,CAAC;AAAA,MAC3C,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,eAAe,CAAC;AAC5C,UAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,sBAAsB;AACvE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,YAAYM,OAAM,CAAC;AAAA,QACxB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoBN;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MACtE,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,yCAAyC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACzL;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,CAAC,IAAI,aAAa;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,gBAAgB;AAAA,MACrC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQV,SAAS,CAAC,MAAM,gBAAgB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,YAAY,EAAE,CAAC;AAAA,MAClC,WAAW,CAAC,GAAG,wBAAwB;AAAA,MACvC,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,CAAC,IAAI,QAAQ;AAAA,QAC3C;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,qBAAqB,+BAA+B,sBAAsB,yBAAyB,yBAAyB,6BAA6B,8BAA8B,6BAA6B,sBAAsB,sBAAsB,4BAA4B,0BAA0B,sBAAsB,yBAAyB,wBAAwB,2BAA2B,yBAAyB,0BAA0B,0BAA0B,sBAAsB;AAAA,MAC9pB,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,+BAA+B,sBAAsB,yBAAyB,0BAA0B,qBAAqB,0BAA0B,sBAAsB,yBAAyB,0BAA0B,sBAAsB;AAAA,IAC3Z,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,oBAAoB,oBAAoB,kBAAkB,6BAA6B,6BAA6B,yBAAyB,wBAAwB,2BAA2B,0BAA0B,sBAAsB;AAAA,IAC9Q,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,qBAAqB,+BAA+B,sBAAsB,yBAAyB,yBAAyB,6BAA6B,8BAA8B,6BAA6B,sBAAsB,sBAAsB,4BAA4B,0BAA0B,sBAAsB,yBAAyB,wBAAwB,2BAA2B,yBAAyB,0BAA0B,0BAA0B,sBAAsB;AAAA,MAC9pB,SAAS,CAAC,kBAAkB,oBAAoB,sBAAsB,sBAAsB,oBAAoB,kBAAkB,kBAAkB,eAAe,+BAA+B,sBAAsB,yBAAyB,0BAA0B,qBAAqB,0BAA0B,sBAAsB,yBAAyB,0BAA0B,sBAAsB;AAAA,IAC3Z,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["_c0", "_c1", "_c2", "_forTrack0", "_c0", "_c1", "_c2", "_c3", "_c4", "_c12", "_c13", "filter", "NZ_CONFIG_MODULE_NAME"]}
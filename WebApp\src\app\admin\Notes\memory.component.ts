import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Memory, MemoryServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { MarkdownModule } from 'ngx-markdown';

@Component({
  selector: 'app-memory',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule,RouterLink,MarkdownModule],
  templateUrl: './memory.component.html',
  styleUrl: './memory.component.css',
})
export class MemoryComponent implements OnInit {
  memory: Memory[] = [];
  filteredMemory: Memory[] = [];
  searchTerm: string = '';
  loading: boolean = true;
  confirmDeleteId: string | null = null;
  selectedMemory: Memory | null = null;

  constructor(private _memoryService: MemoryServiceProxy, private router: Router) { }

  ngOnInit() {
    this.loadMemory();
  }

  loadMemory() {
    this.loading = true;
    this._memoryService.getAll().subscribe((res: any) => {
      this.memory = res;
      this.filteredMemory = [...this.memory];
      this.loading = false;
      console.log(res);
    });
  }

  deleteMemory(id: string) {
    this._memoryService.delete(id).subscribe({
      next: () => {
        this.memory = this.memory.filter(x => x.id !== id);
        this.filteredMemory = this.filteredMemory.filter(x => x.id !== id);
        this.confirmDeleteId = null;
      },
      error: (err) => {
        console.error('Error deleting Memory:', err);
        this.confirmDeleteId = null;
      }
    });
  }

  confirmDelete(id: string) {
    this.confirmDeleteId = id;
  }

  cancelDelete() {
    this.confirmDeleteId = null;
  }

  searchMemory() {
    if (!this.searchTerm.trim()) {
      this.filteredMemory = [...this.memory];
      return;
    }

    const term = this.searchTerm.toLowerCase().trim();
    this.filteredMemory = this.memory.filter(memory => {
      // Search in content
      const content = this.getDisplayContent(memory);
      // Search in email
      const email = memory.email?.toLowerCase() || '';

      return content.toLowerCase().includes(term) || email.includes(term);
    });
  }

  addMemory() {
    this.router.navigate(['/settings/memory/add']);
  }


  getDisplayContent(memory: Memory): string {
    if (!memory.content) return '';

    try {
      // Check if content is in EditorJS format
      if (memory.content.startsWith('{')) {
        const parsed = JSON.parse(memory.content);
        if (parsed.blocks && Array.isArray(parsed.blocks)) {
          // For card display, extract text from blocks
          return parsed.blocks.map((block: any) => {
            // Handle different block types
            switch (block.type) {
              case 'paragraph':
              case 'header':
                return block.data?.text || '';

              case 'list':
                if (block.data?.items && Array.isArray(block.data.items)) {
                  // Extract text from list items
                  return block.data.items.map((item: any) => this.getItemText(item)).join(', ');
                }
                return '';

              case 'checklist':
                if (block.data?.items && Array.isArray(block.data.items)) {
                  // Extract text from checklist items
                  return block.data.items.map((item: any) => this.getItemText(item)).join(', ');
                }
                return '';

              case 'quote':
                return block.data?.text || '';

              case 'code':
                return '[Code block]';

              case 'table':
                return '[Table]';

              default:
                return block.data?.text || '';
            }
          }).filter((text: string) => text.trim() !== '').join(' ');
        }
      }
      // Return as is if not in EditorJS format
      return memory.content;
    } catch (e) {
      // If parsing fails, return content as is
      return memory.content;
    }
  }

  // Format EditorJS content to HTML for detailed display
  formatEditorContent(content: string): string {
    if (!content) return '';

    try {
      const parsedContent = JSON.parse(content);

      if (!parsedContent.blocks || !Array.isArray(parsedContent.blocks)) {
        return content; // Return original content if not in expected format
      }

      let html = '';

      parsedContent.blocks.forEach((block: any) => {
        switch (block.type) {
          case 'header':
            const level = block.data.level || 2;
            html += `<h${level} class="text-[var(--text-dark)] font-semibold mb-3 mt-4">${block.data.text}</h${level}>`;
            break;

          case 'paragraph':
            html += `<p class="text-[var(--text-dark)] mb-3">${block.data.text}</p>`;
            break;

          case 'list':
            const listTag = block.data.style === 'ordered' ? 'ol' : 'ul';
            html += `<${listTag} class="list-${block.data.style === 'ordered' ? 'decimal' : 'disc'} pl-5 mb-3 text-[var(--text-dark)]">`;
            if (block.data.items && Array.isArray(block.data.items)) {
              block.data.items.forEach((item: any) => {
                // Handle different item formats (string, object, etc.)
                const itemText = this.getItemText(item);
                html += `<li class="mb-1">${itemText}</li>`;
              });
            }
            html += `</${listTag}>`;
            break;

          case 'checklist':
            html += `<div class="mb-3">`;
            if (block.data.items && Array.isArray(block.data.items)) {
              block.data.items.forEach((item: any) => {
                // Get the checked state (default to false if not present)
                const isChecked = item.checked === true;
                // Get the text content using our helper method
                const itemText = this.getItemText(item);

                html += `
                  <div class="checklist-item ${isChecked ? 'checked' : ''}">
                    <div class="checklist-item-checkbox ${isChecked ? 'checked' : ''}">
                      ${isChecked ? '<i class="ri-check-line text-white text-xs"></i>' : ''}
                    </div>
                    <span class="checklist-item-text">${itemText}</span>
                  </div>
                `;
              });
            }
            html += `</div>`;
            break;

          case 'quote':
            html += `
              <blockquote class="border-l-4 border-[var(--primary-purple)] pl-4 py-1 mb-3 italic text-[var(--text-dark)]">
                <p>${block.data.text}</p>
                ${block.data.caption ? `<cite class="text-sm text-[var(--text-medium-gray)] mt-1 block">— ${block.data.caption}</cite>` : ''}
              </blockquote>
            `;
            break;

          case 'delimiter':
            html += `<hr class="my-4 border-t border-[var(--hover-blue-gray)]">`;
            break;

          case 'code':
            html += `
              <pre class="bg-[var(--background-light-gray)] p-3 rounded-lg mb-3 overflow-x-auto">
                <code class="text-[var(--text-dark)] text-sm font-mono">${block.data.code}</code>
              </pre>
            `;
            break;

          case 'table':
            html += `<div class="mb-4 overflow-x-auto">
              <table class="min-w-full border-collapse border border-[var(--hover-blue-gray)]">
                <tbody>`;

            block.data.content.forEach((row: string[]) => {
              html += `<tr>`;
              row.forEach((cell: string) => {
                html += `<td class="border border-[var(--hover-blue-gray)] p-2 text-[var(--text-dark)]">${cell}</td>`;
              });
              html += `</tr>`;
            });

            html += `</tbody></table></div>`;
            break;

          default:
            if (block.data && block.data.text) {
              html += `<p class="text-[var(--text-dark)] mb-3">${block.data.text}</p>`;
            }
        }
      });

      return html;
    } catch (e) {
      console.error('Error formatting EditorJS content:', e);
      return content; // Return original content if parsing fails
    }
  }

  // Select a memory to view details
  selectMemory(memory: Memory) {
    this.selectedMemory = memory;

    // Add any additional logic needed when selecting a memory
    console.log('Selected memory:', memory);
  }

  // Close the memory detail view
  closeMemoryDetail() {
    this.selectedMemory = null;
  }

  // Helper method to safely extract text from various data types
  getItemText(item: any): string {
    if (item === null || item === undefined) {
      return '';
    }

    // If item is a string, return it directly
    if (typeof item === 'string') {
      return item;
    }

    // If item is an object with a 'content' property (common in EditorJS)
    if (typeof item === 'object') {
      // Check for common properties that might contain text
      if (item.content) {
        return item.content;
      }

      if (item.text) {
        return item.text;
      }

      if (item.value) {
        return item.value;
      }

      // If it's an object but doesn't have expected properties, try to get a meaningful string
      try {
        // Try to convert to JSON string if it's a complex object
        const jsonStr = JSON.stringify(item);
        // If it looks like a complex object, return a simplified version
        if (jsonStr.startsWith('{') && jsonStr.length > 2) {
          return item.toString ? item.toString() : 'List item';
        }
        return jsonStr;
      } catch (e) {
        return 'List item';
      }
    }

    // For any other type, convert to string
    return String(item);
  }

  formatDate(dateString: any): string {
    if (!dateString) return '';

    // Handle different date formats
    let date: Date;
    if (typeof dateString === 'string') {
      date = new Date(dateString);
    } else if (dateString && typeof dateString === 'object' && dateString.toString) {
      date = new Date(dateString.toString());
    } else {
      return 'Unknown date';
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    // Check if date is today
    if (date.toDateString() === now.toDateString()) {
      return 'Today, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // Check if date is yesterday
    if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // Otherwise return formatted date
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
}

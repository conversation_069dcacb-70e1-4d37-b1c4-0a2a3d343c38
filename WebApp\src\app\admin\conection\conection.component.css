/* Hover effects for cards */
.group {
  transition: all 0.3s ease;
  border: 1px solid var(--hover-blue-gray);
}

.group:hover {
  transform: translateX(4px);
  border-color: var(--primary-purple);
}

/* Animation for fade in effect */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Card actions visibility */
.card-actions {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.api-card:hover .card-actions {
  opacity: 1;
}
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import EditorJS, { ToolConstructable } from '@editorjs/editorjs';

import Header from '@editorjs/header';
import Table from '@editorjs/table';
import { MarkdownModule } from 'ngx-markdown';
import List from '@editorjs/list';
import SimpleImage from '@editorjs/simple-image';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Warning from '@editorjs/warning';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import LinkTool from '@editorjs/link';
import Embed from '@editorjs/embed';


@Component({
  selector: 'app-new-note-book',
  imports: [CommonModule, MarkdownModule, FormsModule, ReactiveFormsModule],
  standalone: true,
  templateUrl: './new-note-book.component.html',
  styleUrl: './new-note-book.component.css'
})
export class NewNoteBookComponent implements OnInit, AfterViewInit {
  @ViewChild('editor', { static: true })
  editorElement!: ElementRef;

  private editor!: EditorJS;
  hasContent = false;

  constructor() { }

  ngOnInit(): void {
    // Remove initialization from here
  }

  ngAfterViewInit(): void {
    this.initializeEditor();
  }

  private initializeEditor() {
    this.editor = new EditorJS({
      holder: 'editor',
      minHeight: 200,
      placeholder: 'Start writing or paste your content here...',
      onChange: () => {
        this.editor.save().then(data => {
          this.hasContent = data.blocks.length > 0;
        });
      },
      tools: {
        header: {
          class: Header as unknown as ToolConstructable,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4],
            defaultLevel: 1,
            placeholder: 'Enter a heading'
          }
        },
        image: {
          class: SimpleImage as unknown as ToolConstructable,
          config: {
            placeholder: 'Paste image URL or choose file',
            buttonContent: 'Choose an image',
            uploader: {
              uploadByFile(file: File): Promise<{ success: number; file: { url: string } }> {
                // Create FormData
                const formData = new FormData();
                formData.append('image', file);

                // Upload to your API endpoint
                return fetch('https://localhost:44350/api/Upload/image', {
                  method: 'POST',
                  body: formData
                })
                .then(response => response.json())
                .then(data => {
                  return {
                    success: 1,
                    file: {
                      url: data.url
                    }
                  };
                });
              },
              uploadByUrl(url: string) {
                return fetch('https://localhost:44350/api/Upload/url', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({ url })
                })
                .then(response => response.json())
                .then(data => {
                  return {
                    success: 1,
                    file: {
                      url: data.url
                    }
                  };
                });
              }
            }
          }
        },
        list: List as unknown as ToolConstructable,
        checklist: Checklist as unknown as ToolConstructable,
        quote: Quote as unknown as ToolConstructable,
        warning: Warning as unknown as ToolConstructable,
        marker: Marker as unknown as ToolConstructable,
        code: CodeTool as unknown as ToolConstructable,
        delimiter: Delimiter as unknown as ToolConstructable,
        inlineCode: InlineCode as unknown as ToolConstructable,
        linkTool: LinkTool as unknown as ToolConstructable,
        embed: Embed as unknown as ToolConstructable,
        table: Table as unknown as ToolConstructable
      },
      defaultBlock: 'paragraph'
    });
  }

  showEditorData() {
    this.editor.save().then(data => {
      console.log(JSON.stringify(data, null, 2));
    });
  }
}

<div class="min-h-screen bg-[#171717] text-white">
  <div class="  border-r border-[#2A2A2A] flex gap-2">
    <div
      class="w-9 h-9 rounded-xl hover:bg-gray-800 transition-all flex justify-center items-center cursor-pointer ml-1  mt-1"
      (click)="togglingservice.toggleNavbar()" *ngIf="!togglingservice.isNavbarOpen">
      <i class="ri-menu-2-fill cursor-pointer text-[#8C8C8C]"></i>
    </div>
    <div class="flex gap-1 p-1 *:cursor-pointer *:transition-all">
      <!-- <div class="flex items-center gap-2 mb-4">
        <i class="ri-user-3-line text-xl"></i>
        <span class="text-lg">Overview</span>
      </div> -->
      <div class="flex items-center gap-1 px-3 py-2 hover:text-white transition-all cursor-pointer rounded-md"
        [ngClass]="{
          'text-white ': adminTab === 'aiAgent',
          'underline ': adminTab === 'aiAgent',
          'text-gray-500': adminTab !== 'aiAgent'
        }" (click)="adminTab = 'aiAgent'" routerLink="../aiAgent">
        <span class="text-sm">Ai Agents</span>
      </div>
      <div class="flex items-center gap-1 px-3 py-2 hover:text-white transition-all rounded-md" [ngClass]="{
          'text-white': adminTab === 'chatModel',
          'underline': adminTab === 'chatModel',
          'text-gray-500': adminTab !== 'chatModel'
        }" (click)="adminTab = 'chatModel'" routerLink="../chatModel">
        <span class="text-sm">Chat Model</span>
      </div>

    </div>
  </div>
  <div *ngIf="adminTab === 'aiAgent'">
    <app-ai-agent></app-ai-agent>
  </div>
  <div *ngIf="adminTab === 'chatModel'">
    <app-chat-model></app-chat-model>
  </div>

</div>
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { AiServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { Router } from '@angular/router';

@Component({
  selector: 'app-workspace-chat',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './workspace-chat.component.html',
  styleUrl: './workspace-chat.component.css',
})
export class WorkspaceChatComponent {
  messages: any[] = [];
  newMessage: string = '';
  isLoading: boolean = false;
  workspaceName: string = '';
  constructor(private aiService: AiServiceProxy, private router: Router) {}
  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    let router = this.router.url.split('/');
    this.workspaceName = router[2];
  }

  sendMessage() {
    if (!this.newMessage.trim()) return;
    let msgToSend = `WorkspaceName: ${this.workspaceName}, question: ${this.newMessage}`;

    this.messages.push({
      sender: 'user',
      content: this.newMessage,
      timestamp: new Date(),
    });

    this.newMessage = '';
    this.isLoading = true;
    this.aiService
      .callAgent('TaskGeneratorAgent', msgToSend)
      .subscribe((result) => {
        console.log(result);
        this.messages.push({
          sender: 'bot',
          content: result.message,
          timestamp: new Date(),
        });
        this.scrollToBottom();
        this.isLoading = false;
      });
  }
  scrollToBottom() {
    const chatContainer = document.querySelector('.chat-container');
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // Prevent new line in textarea
      this.sendMessage();
    }
  }
}

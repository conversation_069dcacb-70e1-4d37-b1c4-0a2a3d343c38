<div
  class="flex flex-col bg-[var(--background-light-gray)]"
  style="height: calc(100vh - 65px)"
>
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50"
    >
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-folder-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">Files</h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]"
        >
          {{ files.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div
            class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none"
          >
            <i
              class="ri-search-line text-[var(--text-medium-gray)] text-sm"
            ></i>
          </div>
          <input
            type="text"
            placeholder="Search files..."
            [(ngModel)]="searchTerm"
            (input)="searchFiles()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200"
          />
          <div
            class="absolute inset-y-0 right-0 flex items-center justify-center pr-2"
            *ngIf="searchTerm"
          >
            <button
              (click)="searchTerm = ''; searchFiles()"
              class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] transition-colors focus:outline-none"
            >
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Upload File Button - Teams-style -->
        <button
          routerLink="/settings/files/0"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
        >
          <i class="ri-upload-line"></i>
          <span>Upload File</span>
        </button>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--primary-purple)]"
        ></div>
      </div>

      <!-- Empty State -->
      <div
        *ngIf="!loading && files.length === 0"
        class="bg-[var(--background-white)] rounded-lg shadow-sm p-8 text-center"
      >
        <div class="flex flex-col items-center justify-center gap-4">
          <i
            class="ri-file-list-3-line text-5xl text-[var(--text-medium-gray)]"
          ></i>
          <h3 class="text-xl font-medium text-[var(--text-dark)]">
            No files found
          </h3>
          <p class="text-[var(--text-medium-gray)] max-w-md mx-auto">
            {{
              searchTerm
                ? "No files match your search criteria. Try a different search term."
                : 'You haven\'t uploaded any files yet. Click the "Upload File" button to upload your first file.'
            }}
          </p>
          <button
            *ngIf="searchTerm"
            (click)="searchTerm = ''; searchFiles()"
            class="mt-2 px-4 py-2 text-sm bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-all duration-200"
          >
            Clear Search
          </button>
        </div>
      </div>

      <!-- Files Grid -->
      <div
        *ngIf="!loading && files.length > 0"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        <!-- File Card -->
        <div
          *ngFor="let file of files; let i = index"
          class="bg-[var(--background-white)] rounded-md overflow-hidden group relative h-full flex flex-col cursor-pointer border border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:-translate-y-1 transition-all duration-300 dark:hover:shadow-[0_4px_12px_rgba(16,163,127,0.3)] hover:shadow-[0_4px_12px_rgba(107,70,193,0.3)]"
          [ngStyle]="{ 'animation-delay': i * 0.05 + 's' }"
          (click)="selectFile(file)"
        >
          <!-- Action Buttons (Top Right) -->
          <div
            class="absolute top-3 right-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
          >
            <button
              (click)="downloadFile(file); $event.stopPropagation()"
              class="w-8 h-8 rounded-md bg-[#E6F7FF] hover:bg-[#BAE7FF] hover:-translate-y-1 transition-all duration-200 flex items-center justify-center border-none shadow-sm opacity-90 hover:opacity-100 dark:hover:shadow-[0_2px_4px_rgba(16,163,127,0.4)] hover:shadow-[0_2px_4px_rgba(107,70,193,0.4)]"
              title="Download File"
            >
              <i class="ri-download-line text-blue-500 text-base"></i>
            </button>
            <button
              (click)="confirmDelete(file); $event.stopPropagation()"
              class="w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] hover:-translate-y-1 transition-all duration-200 flex items-center justify-center border-none shadow-sm opacity-90 hover:opacity-100 dark:hover:shadow-[0_2px_4px_rgba(16,163,127,0.4)] hover:shadow-[0_2px_4px_rgba(107,70,193,0.4)]"
              title="Delete File"
            >
              <i class="ri-delete-bin-6-line text-red-500 text-base"></i>
            </button>
          </div>

          <!-- Card Content -->
          <div class="p-4 flex-1">
            <!-- File Icon -->
            <div class="flex items-center justify-center mb-3">
              <i
                class="ri-file-text-line text-4xl text-[var(--primary-purple)]"
              ></i>
            </div>

            <!-- File Name -->
            <h3
              class="text-base font-medium text-[var(--text-dark)] text-center mb-2 line-clamp-1"
            >
              {{ file.fileName }}
            </h3>

            <!-- File Description -->
            <p
              class="text-sm text-[var(--text-medium-gray)] line-clamp-3 break-words"
            >
              {{ getShortDescription(file.description || "") }}
            </p>

            <!-- AI Analysis Status -->
            <div class="mt-3 flex items-center justify-center">
              <span
                class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium text-white shadow-sm"
                [ngClass]="file.aiAnalysis ? 'bg-green-500 border border-green-600' : 'bg-yellow-500 border border-yellow-600'"
              >
                <i
                  [class]="
                    file.aiAnalysis ? 'ri-check-line mr-1.5' : 'ri-time-line mr-1.5'
                  "
                ></i>
                <span>{{ file.aiAnalysis ? "Generated" : "Pending" }}</span>
              </span>
            </div>
          </div>

          <!-- Card Footer -->
          <div
            class="px-4 py-3 border-t border-[var(--hover-blue-gray)] border-opacity-30 flex justify-between items-center"
          >
            <div
              class="text-xs text-[var(--text-medium-gray)] flex items-center gap-1"
            >
              <i class="ri-file-list-line"></i>
              <span>{{ file.fileType || "Document" }}</span>
            </div>
          </div>

          <!-- Delete Confirmation Overlay -->
          <div
            *ngIf="fileToDelete === file"
            class="absolute inset-0 bg-[var(--background-white)] bg-opacity-95 flex flex-col items-center justify-center p-4 z-20 animate-fadeIn"
            (click)="$event.stopPropagation()"
          >
            <p class="text-center text-[var(--text-dark)] mb-4">
              Are you sure you want to delete this file?
            </p>
            <div class="flex gap-3">
              <button
                (click)="deleteFile(file)"
                class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-all duration-200 hover:-translate-y-1 dark:hover:shadow-[0_4px_8px_rgba(16,163,127,0.4)] hover:shadow-[0_4px_8px_rgba(107,70,193,0.4)]"
              >
                Delete
              </button>
              <button
                (click)="cancelDelete()"
                class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-all duration-200 hover:-translate-y-1 dark:hover:shadow-[0_4px_8px_rgba(16,163,127,0.2)] hover:shadow-[0_4px_8px_rgba(107,70,193,0.2)]"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- File Detail Modal -->
  <div
    *ngIf="selectedFile"
    class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-[fadeIn_0.3s_ease-out]"
    (click)="closeFileDetail()"
  >
    <!-- Modal Content -->
    <div
      class="bg-[var(--background-white)] rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden animate-[scaleIn_0.3s_ease-out]"
      (click)="$event.stopPropagation()"
    >
      <!-- Modal Header -->
      <div
        class="flex items-center justify-between p-4 border-b border-[var(--hover-blue-gray)]"
      >
        <div class="flex items-center gap-3">
          <div
            class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center text-white"
          >
            <i class="ri-file-text-line text-lg"></i>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-[var(--text-dark)]">
              {{ selectedFile.fileName }}
            </h3>
            <p class="text-sm text-[var(--text-medium-gray)]">
              {{ selectedFile.fileType || "Document" }}
            </p>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <button
            (click)="downloadFile(selectedFile); $event.stopPropagation()"
            class="w-9 h-9 flex items-center justify-center rounded-md bg-[#E6F7FF] hover:bg-[#BAE7FF] hover:-translate-y-1 transition-all duration-200 border-none shadow-sm opacity-90 hover:opacity-100 dark:hover:shadow-[0_2px_4px_rgba(16,163,127,0.4)] hover:shadow-[0_2px_4px_rgba(107,70,193,0.4)]"
            title="Download File"
          >
            <i class="ri-download-line text-blue-500 text-base"></i>
          </button>
          <button
            (click)="closeFileDetail()"
            class="w-9 h-9 flex items-center justify-center rounded-md bg-[var(--hover-blue-gray)] hover:bg-opacity-80 hover:-translate-y-1 transition-all duration-200 border-none shadow-sm opacity-90 hover:opacity-100 dark:hover:shadow-[0_2px_4px_rgba(16,163,127,0.4)] hover:shadow-[0_2px_4px_rgba(107,70,193,0.4)]"
            title="Close"
          >
            <i class="ri-close-line text-[var(--text-dark)] text-base"></i>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="flex-1 overflow-y-auto p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Left Column: File Info -->
          <div>
            <h4 class="text-base font-medium text-[var(--text-dark)] mb-3">
              File Information
            </h4>
            <div class="bg-[var(--background-light-gray)] p-4 rounded-md">
              <div class="mb-4">
                <p class="text-sm text-[var(--text-medium-gray)] mb-1">
                  File Name
                </p>
                <p class="text-sm text-[var(--text-dark)]">
                  {{ selectedFile.fileName }}
                </p>
              </div>
              <div class="mb-4">
                <p class="text-sm text-[var(--text-medium-gray)] mb-1">
                  File Type
                </p>
                <p class="text-sm text-[var(--text-dark)]">
                  {{ selectedFile.fileType || "Document" }}
                </p>
              </div>
              <div>
                <p class="text-sm text-[var(--text-medium-gray)] mb-1">
                  Description
                </p>
                <p class="text-sm text-[var(--text-dark)] whitespace-pre-line">
                  {{ selectedFile.description }}
                </p>
              </div>
            </div>
          </div>

          <!-- Right Column: AI Analysis -->
          <div>
            <div class="flex items-center gap-2 mb-3">
              <h4 class="text-base font-medium text-[var(--text-dark)]">
                AI Analysis
              </h4>
              <span
                class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium text-white shadow-sm"
                [ngClass]="selectedFile.aiAnalysis ? 'bg-green-500 border border-green-600' : 'bg-yellow-500 border border-yellow-600'"
              >
                <i [class]="selectedFile.aiAnalysis ? 'ri-check-line mr-1.5' : 'ri-time-line mr-1.5'"></i>
                <span>{{ selectedFile.aiAnalysis ? "Generated" : "Pending" }}</span>
              </span>
            </div>
            <div class="bg-[var(--background-light-gray)] p-4 rounded-md">
              <div
                *ngIf="selectedFile.aiAnalysis"
                class="prose prose-sm max-w-none"
              >
                <!-- AI Analysis Content -->
                <div class="mb-3">
                  <p class="text-sm text-[var(--text-dark)] whitespace-pre-line">
                    {{ selectedFile.aiAnalysis }}
                  </p>
                </div>

                <!-- Regenerate Button -->
                <div class="flex justify-end mt-4 border-t border-[var(--hover-blue-gray)] border-opacity-30 pt-3">
                  <button
                    *ngIf="!showRegenerateForm"
                    (click)="toggleRegenerateForm()"
                    class="px-3 py-1.5 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-all duration-200 flex items-center justify-center gap-1 text-sm hover:shadow-sm"
                  >
                    <i class="ri-refresh-line"></i>
                    <span>Regenerate Analysis</span>
                  </button>
                </div>

                <!-- Regenerate Form -->
                <div *ngIf="showRegenerateForm" class="mt-4 border-t border-[var(--hover-blue-gray)] border-opacity-30 pt-3">
                  <div class="mb-3">
                    <p class="text-sm text-[var(--text-medium-gray)] mb-2">
                      Enter a new prompt to regenerate the AI analysis:
                    </p>
                    <textarea
                      [(ngModel)]="aiPrompt"
                      placeholder="Enter a prompt for AI analysis (e.g., 'Analyze this file and provide key insights')"
                      class="w-full p-3 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200 min-h-[100px]"
                    ></textarea>
                  </div>

                  <div class="flex justify-end gap-2">
                    <button
                      (click)="toggleRegenerateForm()"
                      class="px-3 py-1.5 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-all duration-200 flex items-center justify-center gap-1 text-sm"
                    >
                      <span>Cancel</span>
                    </button>
                    <button
                      (click)="syncWithAI(selectedFile)"
                      [disabled]="!aiPrompt.trim() || syncingAI"
                      class="px-3 py-1.5 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <i class="ri-refresh-line" [class.animate-spin]="syncingAI"></i>
                      <span>{{ syncingAI ? 'Processing...' : 'Regenerate' }}</span>
                    </button>
                  </div>
                </div>
              </div>
              <div
                *ngIf="!selectedFile.aiAnalysis"
                class="flex flex-col py-4"
              >
                <div class="flex items-center justify-center mb-4">
                  <i class="ri-time-line text-2xl text-[var(--text-medium-gray)] mr-2"></i>
                  <p class="text-sm text-[var(--text-medium-gray)]">
                    AI analysis is pending. Generate it now with a custom prompt:
                  </p>
                </div>

                <!-- Prompt textarea -->
                <div class="mb-4">
                  <textarea
                    [(ngModel)]="aiPrompt"
                    placeholder="Enter a prompt for AI analysis (e.g., 'Analyze this file and provide key insights')"
                    class="w-full p-3 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200 min-h-[100px]"
                  ></textarea>
                </div>

                <!-- Sync button -->
                <div class="flex justify-end">
                  <button
                    (click)="syncWithAI(selectedFile)"
                    [disabled]="!aiPrompt.trim() || syncingAI"
                    class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <i class="ri-refresh-line" [class.animate-spin]="syncingAI"></i>
                    <span>{{ syncingAI ? 'Processing...' : 'Generate AI Analysis' }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

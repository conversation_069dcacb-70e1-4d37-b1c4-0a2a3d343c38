import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../../../shared/services/auth.service';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-settingsidebarcomponent',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink
  ],
  templateUrl: './settingsidebarcomponent.component.html',
  styleUrls: ['./settingsidebarcomponent.component.css']
})
export class SettingsidebarcomponentComponent {
  // Inject services
  router = inject(Router);
  themeService = inject(ThemeService);

  // Input properties
  @Input() activeAdminTab: string = 'prompt-library';

  // Inject AuthService
  constructor(public authService: AuthService) {}

  /**
   * Navigate to a specific settings section
   * @param event The click event
   * @param section The settings section to navigate to
   */
  navigateToSettings(event: Event, section: string) {
    event.stopPropagation();

    // Update the active tab
    this.activeAdminTab = section;

    // Navigate to the settings section
    this.router.navigate(['/settings', section]);
  }

  /**
   * Check if a settings tab is active
   * @param tabName The tab name to check
   * @returns True if the tab is active, false otherwise
   */
  isSettingsTabActive(tabName: string): boolean {
    // Get the current route segments
    const urlSegments = this.router.url.split('/');
    // Get the last segment of the URL (the active tab)
    const currentTab = urlSegments[urlSegments.length - 1];

    // Return true only if the current tab matches the given tabName
    return currentTab === tabName;
  }
}

/* Right Sidebar Splitter Styles */
:host {
  display: flex;
  height: calc(100vh - 74px);
  width: 100%;
}

/* Styles for the split layout */
as-split {
  --gutter-color: transparent;
  --gutter-hover-color: var(--primary-purple);
}

/* Custom styling for the gutter - completely invisible by default */
.as-split-gutter {
  background-color: transparent !important;
  transition: all 0.2s ease;
  position: relative;
  z-index: 200; /* Higher z-index to ensure it's above other content */
  width: 2px !important; /* Make the gutter slightly thicker for better usability */
  opacity: 0.5;
}

/* Show a more visible gutter on hover */
.as-split-gutter:hover {
  opacity: 1;
  width: 4px !important;
}

/* Add a visual indicator for the gutter */
.as-split-gutter::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background-color: var(--hover-blue-gray);
  opacity: 0.5;
  transition: all 0.2s ease;
}

/* Enhance the gutter indicator on hover */
.as-split-gutter:hover::after {
  width: 3px;
  background-color: var(--primary-purple);
  opacity: 1;
}

/* Add resize cursor */
.as-split-gutter {
  cursor: col-resize !important;
}

/* Create a larger hover area for the gutter */
.as-split-gutter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -10px;
  width: 24px; /* Wider hover area */
  height: 100%;
  background-color: transparent;
  z-index: 199; /* Just below the gutter z-index */
}

/* Ensure the split areas take full height */
as-split-area {
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Right sidebar area */
.right-sidebar-area {
  border-left: 2px solid var(--hover-blue-gray);
  position: relative;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  background-color: #2b2b33;
  overflow: hidden; /* Prevent content from spilling out */
  width: 100%; /* Ensure it takes full width */
  display: flex; /* Use flexbox for better layout control */
}

/* Sidebar container to properly constrain width */
.sidebar-container {
  height: 100%;
  width: 100%; /* Take full width of parent */
  position: relative;
  overflow: hidden;
  display: flex; /* Use flexbox for better layout control */
}

/* Main content area */
.main-content-area {
  overflow: hidden;
}

/* Width indicator during drag */
.width-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: bold;
  z-index: 300; /* Higher z-index to ensure visibility */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.2s ease;
  opacity: 0.9;
  transition: opacity 0.2s ease;
  pointer-events: none; /* Prevent it from interfering with drag operations */
}

/* Animation for width indicator */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 0.9; transform: translateY(0); }
}

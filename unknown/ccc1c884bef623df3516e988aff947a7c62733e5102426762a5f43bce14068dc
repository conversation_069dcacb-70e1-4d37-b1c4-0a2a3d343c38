<!-- src/app/user-dialog/user-dialog.component.html -->
<div class="p-0 mx-auto w-auto">
  <div class="overflow-x-auto w-auto max-h-[300px]">
    <table class="w-full text-[var(--text-dark)]">
      <thead>
        <tr class="bg-[var(--background-light-gray)]">
          <th class="p-[var(--padding-small)] text-left">Name</th>
          <th class="p-[var(--padding-small)] text-left">Email</th>
          <th class="p-[var(--padding-small)] text-left">Roles</th>
          <th class="p-[var(--padding-small)] text-left">Skills</th>
          <th class="p-[var(--padding-small)] text-left">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let user of users; let i = index"
          class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)]">
          <td class="p-[var(--padding-small)]">{{ user.name }}</td>
          <td class="p-[var(--padding-small)]">{{ user.email }}</td>
          <td class="p-[var(--padding-small)]">{{ user.roles.join(', ') }}</td>
          <td class="p-[var(--padding-small)]">{{ user.skills }}</td>
          <td class="p-[var(--padding-small)]">
            <button (click)="addUser(user)"
              class="bg-[var(--primary-purple)] text-[var(--background-white)] p-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] outline-none border-none cursor-pointer hover:text-black">
              <i class="ri-user-add-line text-lg"></i>
            </button>
          </td>
        </tr>
        <tr *ngIf="users.length === 0">
          <td colspan="5" class="p-[var(--padding-small)] text-center text-[var(--text-medium-gray)]">
            No users available.
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="mt-4 flex justify-between items-center px-4 pb-4">
    <button (click)="closeDialog()"
      class="p-2 px-4 bg-gray-500 text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-gray-600 transition-[var(--transition-default)] outline-none border-none cursor-pointer hover:text-white">
      Close
    </button>
  </div>
</div>
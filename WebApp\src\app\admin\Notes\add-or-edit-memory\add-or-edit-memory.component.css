/* Editor container styles with theme support */
#editor {
  min-height: 500px;
  font-family: var(--font-family);
  background-color: var(--background-white);
  color: var(--text-dark);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

/* Ensure editor content is properly themed */
#editor .codex-editor {
  background-color: var(--background-white);
  color: var(--text-dark);
}

/* EditorJS custom styles with ::ng-deep for proper encapsulation */
::ng-deep .ce-block__content {
  max-width: 900px;
  margin: 0 auto;
  color: var(--text-dark);
}

::ng-deep .ce-header {
  font-weight: 600;
  color: var(--text-dark) !important;
  font-family: var(--font-family);
}

::ng-deep .ce-toolbar__content {
  max-width: 900px;
  background-color: var(--background-white);
}

/* Ensure editor background matches theme */
::ng-deep .codex-editor {
  background-color: var(--background-white) !important;
}

::ng-deep .codex-editor__redactor {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
}

/* Improve the appearance of the built-in toolbar */
::ng-deep .ce-toolbar {
  background-color: var(--background-white) !important;
  border-bottom: 1px solid var(--hover-blue-gray);
}

::ng-deep .ce-toolbar__plus {
  color: var(--primary-purple) !important;
  background-color: transparent !important;
}

::ng-deep .ce-toolbar__plus:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

::ng-deep .ce-toolbar__settings-btn {
  color: var(--primary-purple) !important;
  background-color: transparent !important;
}

::ng-deep .ce-toolbar__settings-btn:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

/* Remove the dash near the plus icon */
::ng-deep .ce-toolbar__actions {
  border-right: none !important;
}

::ng-deep .ce-toolbar__plus::after,
::ng-deep .ce-toolbar__plus::before {
  display: none !important;
}

::ng-deep .ce-toolbar__separator {
  display: none !important;
}

::ng-deep .ce-toolbar__content {
  border-right: none !important;
  background-color: var(--background-white) !important;
}

/* Header styles */
::ng-deep .ce-header {
  font-weight: 600;
  color: var(--text-dark);
}

::ng-deep .ce-header h1 {
  font-size: 1.75rem;
  margin-bottom: 0.75rem;
}

::ng-deep .ce-header h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

::ng-deep .ce-header h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

::ng-deep .ce-header h4 {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

/* Paragraph styles */
::ng-deep .ce-paragraph {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-dark);
}

/* Checklist styles */
::ng-deep .cdx-checklist__item {
  padding: 0.5rem 0;
}

::ng-deep .cdx-checklist__item-checkbox {
  border-color: var(--primary-purple);
}

::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-checkbox {
  background: var(--primary-purple);
}

::ng-deep .cdx-checklist__item-text {
  color: var(--text-dark);
}

::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-text {
  text-decoration: line-through;
  color: var(--text-medium-gray);
}

/* Quote styles */
::ng-deep .cdx-quote {
  border-left: 3px solid var(--primary-purple);
  padding-left: 1rem;
  font-style: italic;
  color: var(--text-dark);
}

/* Warning block styles */
::ng-deep .cdx-warning {
  background-color: rgba(var(--primary-purple-rgb), 0.1);
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
}

::ng-deep .cdx-warning__title {
  color: var(--primary-purple);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

::ng-deep .cdx-warning__message {
  color: var(--text-dark);
}

/* Code block styles */
::ng-deep .ce-code {
  background-color: var(--background-light-gray);
  border-radius: 0.375rem;
  font-family: monospace;
  padding: 1rem;
  margin: 1rem 0;
  color: var(--text-dark);
}

::ng-deep .ce-popover-item {
    color: black !important;
}
::ng-deep .ce-popover-item__icon {
    color: black !important;
}

::ng-deep .ce-popover--inline .ce-popover__container i{
  color: black !important;


}

::ng-deep .ce-inline-tool svg{
  color: black !important;
}

/* Delimiter styles */
::ng-deep .ce-delimiter {
  color: var(--text-medium-gray);
  margin: 1rem 0;
}

/* General block styles */
::ng-deep .cdx-block {
  padding: 0.5rem 0;
  color: var(--text-dark);
}

/* Table styles */
::ng-deep .tc-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

::ng-deep .tc-table td,
::ng-deep .tc-table th {
  border: 1px solid var(--hover-blue-gray);
  padding: 0.5rem;
  color: var(--text-dark);
}

/* List styles */
::ng-deep .cdx-list {
  margin: 1rem 0;
  padding-left: 1.5rem;
  color: var(--text-dark);
}

::ng-deep .cdx-list__item {
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

/* Image styles */
::ng-deep .image-tool {
  margin: 1rem 0;
}

::ng-deep .image-tool__image {
  max-width: 100%;
  border-radius: 0.375rem;
}

::ng-deep .image-tool__caption {
  color: var(--text-medium-gray);
  font-size: 0.875rem;
  text-align: center;
  margin-top: 0.5rem;
}

/* Link styles */
::ng-deep .link-tool__content {
  border: 1px solid var(--hover-blue-gray);
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
}

::ng-deep .link-tool__title {
  color: var(--text-dark);
  font-weight: 600;
}

::ng-deep .link-tool__description {
  color: var(--text-medium-gray);
}

/* Dark theme support for EditorJS - Using .dark class selector */
:host-context(.dark) ::ng-deep #editor,
:host-context(.dark) ::ng-deep .codex-editor,
:host-context(.dark) ::ng-deep .codex-editor__redactor {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar {
  background-color: var(--background-white) !important;
  border-bottom-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar__plus,
:host-context(.dark) ::ng-deep .ce-toolbar__settings-btn {
  color: var(--primary-purple) !important;
  background-color: transparent !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar__plus:hover,
:host-context(.dark) ::ng-deep .ce-toolbar__settings-btn:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-toolbar__content {
  background-color: var(--background-white) !important;
}

:host-context(.dark) ::ng-deep .ce-popover {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__item {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__item:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__item-icon {
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__search {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-popover__search::placeholder {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-toolbar {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-toolbar__buttons {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-tool {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-tool:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-inline-tool--active {
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-toolbar {
  background-color: var(--background-white) !important;
  border-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-tool {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-tool:hover {
  background-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .ce-conversion-tool__icon {
  color: var(--primary-purple) !important;
}

/* Dark theme content block styling */
:host-context(.dark) ::ng-deep .ce-header,
:host-context(.dark) ::ng-deep .ce-paragraph,
:host-context(.dark) ::ng-deep .cdx-checklist__item-text,
:host-context(.dark) ::ng-deep .cdx-warning__message,
:host-context(.dark) ::ng-deep .ce-code,
:host-context(.dark) ::ng-deep .cdx-block,
:host-context(.dark) ::ng-deep .tc-table td,
:host-context(.dark) ::ng-deep .tc-table th,
:host-context(.dark) ::ng-deep .cdx-list__item,
:host-context(.dark) ::ng-deep .link-tool__title,
:host-context(.dark) ::ng-deep .cdx-quote {
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .cdx-checklist__item-checkbox {
  border-color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-checkbox {
  background: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .cdx-checklist__item--checked .cdx-checklist__item-text {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) ::ng-deep .cdx-warning {
  background-color: rgba(var(--primary-purple-rgb), 0.1) !important;
}

:host-context(.dark) ::ng-deep .cdx-warning__title {
  color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .cdx-quote {
  border-left-color: var(--primary-purple) !important;
}

:host-context(.dark) ::ng-deep .ce-code {
  background-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) ::ng-deep .ce-delimiter {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) ::ng-deep .tc-table td,
:host-context(.dark) ::ng-deep .tc-table th {
  border-color: var(--hover-blue-gray) !important;
}

:host-context(.dark) ::ng-deep .link-tool__content {
  border-color: var(--hover-blue-gray) !important;
  background-color: var(--background-white) !important;
}

:host-context(.dark) ::ng-deep .link-tool__description {
  color: var(--text-medium-gray) !important;
}

/* Placeholder text styling */
::ng-deep .ce-paragraph[data-placeholder]::before {
  color: var(--text-medium-gray) !important;
  opacity: 0.7;
  font-style: italic;
}

:host-context(.dark) ::ng-deep .ce-paragraph[data-placeholder]::before {
  color: var(--text-medium-gray) !important;
  opacity: 0.8;
}

/* Focus states for better accessibility */
::ng-deep .ce-block--focused {
  background-color: rgba(var(--primary-purple-rgb), 0.05) !important;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

:host-context(.dark) ::ng-deep .ce-block--focused {
  background-color: rgba(var(--primary-purple-rgb), 0.1) !important;
}

/* Improve selection styling */
::ng-deep .ce-block--selected {
  background-color: rgba(var(--primary-purple-rgb), 0.1) !important;
  border-radius: 0.25rem;
}

:host-context(.dark) ::ng-deep .ce-block--selected {
  background-color: rgba(var(--primary-purple-rgb), 0.15) !important;
}

/* Sticky header styling */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  ::ng-deep .ce-block__content,
  ::ng-deep .ce-toolbar__content {
    max-width: 100%;
    padding: 0 1rem;
  }

  ::ng-deep .ce-header h1 {
    font-size: 1.5rem;
  }

  ::ng-deep .ce-header h2 {
    font-size: 1.25rem;
  }

  ::ng-deep .ce-header h3 {
    font-size: 1.125rem;
  }

  ::ng-deep .ce-header h4 {
    font-size: 1rem;
  }
}

/* Text selection styling for better visibility */
::ng-deep .ce-paragraph::selection,
::ng-deep .ce-header::selection,
::ng-deep .cdx-list__item::selection,
::ng-deep .cdx-checklist__item-text::selection,
::ng-deep .cdx-quote::selection,
::ng-deep .ce-code::selection,
::ng-deep .cdx-block::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Dark theme text selection */
:host-context(.dark) ::ng-deep .ce-paragraph::selection,
:host-context(.dark) ::ng-deep .ce-header::selection,
:host-context(.dark) ::ng-deep .cdx-list__item::selection,
:host-context(.dark) ::ng-deep .cdx-checklist__item-text::selection,
:host-context(.dark) ::ng-deep .cdx-quote::selection,
:host-context(.dark) ::ng-deep .ce-code::selection,
:host-context(.dark) ::ng-deep .cdx-block::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Global text selection for the editor container */
#editor::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

#editor *::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Ensure selection works in dark theme */
:host-context(.dark) #editor::selection,
:host-context(.dark) #editor *::selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

/* Mozilla Firefox selection styling */
::ng-deep .ce-paragraph::-moz-selection,
::ng-deep .ce-header::-moz-selection,
::ng-deep .cdx-list__item::-moz-selection,
::ng-deep .cdx-checklist__item-text::-moz-selection,
::ng-deep .cdx-quote::-moz-selection,
::ng-deep .ce-code::-moz-selection,
::ng-deep .cdx-block::-moz-selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

#editor::-moz-selection,
#editor *::-moz-selection {
  background-color: var(--primary-purple) !important;
  color: white !important;
}

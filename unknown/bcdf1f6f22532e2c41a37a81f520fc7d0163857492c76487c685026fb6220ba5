{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-badge.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgStyle } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Optional, Host, NgModule } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { zoomBadgeMotion } from 'ng-zorro-antd/core/animation';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i3 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r1 = ctx.$implicit;\n    const i_r2 = i0.ɵɵnextContext(2).$index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current\", p_r1 === ctx_r2.countArray[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", p_r1, \" \");\n  }\n}\nfunction NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_For_1_Template, 2, 3, \"p\", 2, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵrepeater(ctx_r2.countSingleArray);\n  }\n}\nfunction NzBadgeSupComponent_Conditional_0_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵtemplate(1, NzBadgeSupComponent_Conditional_0_For_1_Conditional_1_Template, 2, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r2 = ctx.$index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"transform\", \"translateY(\" + -ctx_r2.countArray[i_r2] * 100 + \"%)\");\n    i0.ɵɵproperty(\"nzNoAnimation\", ctx_r2.noAnimation);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, !ctx_r2.nzDot && ctx_r2.countArray[i_r2] !== undefined ? 1 : -1);\n  }\n}\nfunction NzBadgeSupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzBadgeSupComponent_Conditional_0_For_1_Template, 2, 4, \"span\", 0, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.maxNumberArray);\n  }\n}\nfunction NzBadgeSupComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.nzOverflowCount, \"+ \");\n  }\n}\nconst _c0 = [\"*\"];\nfunction NzBadgeComponent_Conditional_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzText);\n  }\n}\nfunction NzBadgeComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 1);\n    i0.ɵɵelementStart(1, \"span\", 2);\n    i0.ɵɵtemplate(2, NzBadgeComponent_Conditional_0_ng_container_2_Template, 2, 1, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-badge-status-dot ant-badge-status-\", ctx_r0.nzStatus || ctx_r0.presetColor, \"\");\n    i0.ɵɵstyleProp(\"background\", !ctx_r0.presetColor && ctx_r0.nzColor);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.nzStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzText);\n  }\n}\nfunction NzBadgeComponent_ng_container_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-badge-sup\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzOffset\", ctx_r0.nzOffset)(\"nzSize\", ctx_r0.nzSize)(\"nzTitle\", ctx_r0.nzTitle)(\"nzStyle\", ctx_r0.nzStyle)(\"nzDot\", ctx_r0.nzDot)(\"nzOverflowCount\", ctx_r0.nzOverflowCount)(\"disableAnimation\", !!(ctx_r0.nzStandalone || ctx_r0.nzStatus || ctx_r0.nzColor || (ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.nzNoAnimation)))(\"nzCount\", ctx_r0.nzCount)(\"noAnimation\", !!(ctx_r0.noAnimation == null ? null : ctx_r0.noAnimation.nzNoAnimation));\n  }\n}\nfunction NzBadgeComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzBadgeComponent_ng_container_2_Conditional_1_Template, 1, 9, \"nz-badge-sup\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.showSup ? 1 : -1);\n  }\n}\nfunction NzRibbonComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.nzText);\n  }\n}\nclass NzBadgeSupComponent {\n  constructor() {\n    this.nzStyle = null;\n    this.nzDot = false;\n    this.nzOverflowCount = 99;\n    this.disableAnimation = false;\n    this.noAnimation = false;\n    this.nzSize = 'default';\n    this.maxNumberArray = [];\n    this.countArray = [];\n    this.count = 0;\n    this.countSingleArray = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];\n  }\n  generateMaxNumberArray() {\n    this.maxNumberArray = this.nzOverflowCount.toString().split('');\n  }\n  ngOnInit() {\n    this.generateMaxNumberArray();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzOverflowCount,\n      nzCount\n    } = changes;\n    if (nzCount && typeof nzCount.currentValue === 'number') {\n      this.count = Math.max(0, nzCount.currentValue);\n      this.countArray = this.count.toString().split('').map(item => +item);\n    }\n    if (nzOverflowCount) {\n      this.generateMaxNumberArray();\n    }\n  }\n  static {\n    this.ɵfac = function NzBadgeSupComponent_Factory(t) {\n      return new (t || NzBadgeSupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBadgeSupComponent,\n      selectors: [[\"nz-badge-sup\"]],\n      hostAttrs: [1, \"ant-scroll-number\"],\n      hostVars: 17,\n      hostBindings: function NzBadgeSupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@.disabled\", ctx.disableAnimation)(\"@zoomBadgeMotion\", undefined);\n          i0.ɵɵattribute(\"title\", ctx.nzTitle === null ? \"\" : ctx.nzTitle || ctx.nzCount);\n          i0.ɵɵstyleMap(ctx.nzStyle);\n          i0.ɵɵstyleProp(\"right\", ctx.nzOffset && ctx.nzOffset[0] ? -ctx.nzOffset[0] : null, \"px\")(\"margin-top\", ctx.nzOffset && ctx.nzOffset[1] ? ctx.nzOffset[1] : null, \"px\");\n          i0.ɵɵclassProp(\"ant-badge-count\", !ctx.nzDot)(\"ant-badge-count-sm\", ctx.nzSize === \"small\")(\"ant-badge-dot\", ctx.nzDot)(\"ant-badge-multiple-words\", ctx.countArray.length >= 2);\n        }\n      },\n      inputs: {\n        nzOffset: \"nzOffset\",\n        nzTitle: \"nzTitle\",\n        nzStyle: \"nzStyle\",\n        nzDot: \"nzDot\",\n        nzOverflowCount: \"nzOverflowCount\",\n        disableAnimation: \"disableAnimation\",\n        nzCount: \"nzCount\",\n        noAnimation: \"noAnimation\",\n        nzSize: \"nzSize\"\n      },\n      exportAs: [\"nzBadgeSup\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"ant-scroll-number-only\", 3, \"nzNoAnimation\", \"transform\"], [1, \"ant-scroll-number-only\", 3, \"nzNoAnimation\"], [1, \"ant-scroll-number-only-unit\", 3, \"current\"], [1, \"ant-scroll-number-only-unit\"]],\n      template: function NzBadgeSupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzBadgeSupComponent_Conditional_0_Template, 2, 0)(1, NzBadgeSupComponent_Conditional_1_Template, 1, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.count <= ctx.nzOverflowCount ? 0 : 1);\n        }\n      },\n      dependencies: [NzNoAnimationDirective],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBadgeMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBadgeSupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-badge-sup',\n      exportAs: 'nzBadgeSup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [zoomBadgeMotion],\n      standalone: true,\n      imports: [NzNoAnimationDirective],\n      template: `\n    @if (count <= nzOverflowCount) {\n      @for (n of maxNumberArray; track n; let i = $index) {\n        <span\n          [nzNoAnimation]=\"noAnimation\"\n          class=\"ant-scroll-number-only\"\n          [style.transform]=\"'translateY(' + -countArray[i] * 100 + '%)'\"\n        >\n          @if (!nzDot && countArray[i] !== undefined) {\n            @for (p of countSingleArray; track p) {\n              <p class=\"ant-scroll-number-only-unit\" [class.current]=\"p === countArray[i]\">\n                {{ p }}\n              </p>\n            }\n          }\n        </span>\n      }\n    } @else {\n      {{ nzOverflowCount }}+\n    }\n  `,\n      host: {\n        class: 'ant-scroll-number',\n        '[@.disabled]': `disableAnimation`,\n        '[@zoomBadgeMotion]': '',\n        '[attr.title]': `nzTitle === null ? '' : nzTitle || nzCount`,\n        '[style]': `nzStyle`,\n        '[style.right.px]': `nzOffset && nzOffset[0] ? -nzOffset[0] : null`,\n        '[style.margin-top.px]': `nzOffset && nzOffset[1] ? nzOffset[1] : null`,\n        '[class.ant-badge-count]': `!nzDot`,\n        '[class.ant-badge-count-sm]': `nzSize === 'small'`,\n        '[class.ant-badge-dot]': `nzDot`,\n        '[class.ant-badge-multiple-words]': `countArray.length >= 2`\n      }\n    }]\n  }], () => [], {\n    nzOffset: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzStyle: [{\n      type: Input\n    }],\n    nzDot: [{\n      type: Input\n    }],\n    nzOverflowCount: [{\n      type: Input\n    }],\n    disableAnimation: [{\n      type: Input\n    }],\n    nzCount: [{\n      type: Input\n    }],\n    noAnimation: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst badgePresetColors = ['pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime'];\nconst NZ_CONFIG_MODULE_NAME = 'badge';\nclass NzBadgeComponent {\n  constructor(nzConfigService, renderer, cdr, elementRef, directionality, noAnimation) {\n    this.nzConfigService = nzConfigService;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.showSup = false;\n    this.presetColor = null;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.nzShowZero = false;\n    this.nzShowDot = true;\n    this.nzStandalone = false;\n    this.nzDot = false;\n    this.nzOverflowCount = 99;\n    this.nzColor = undefined;\n    this.nzStyle = null;\n    this.nzText = null;\n    this.nzSize = 'default';\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.prepareBadgeForRtl();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.prepareBadgeForRtl();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzColor,\n      nzShowDot,\n      nzDot,\n      nzCount,\n      nzShowZero\n    } = changes;\n    if (nzColor) {\n      this.presetColor = this.nzColor && badgePresetColors.indexOf(this.nzColor) !== -1 ? this.nzColor : null;\n    }\n    if (nzShowDot || nzDot || nzCount || nzShowZero) {\n      this.showSup = this.nzShowDot && this.nzDot || typeof this.nzCount === 'number' && this.nzCount > 0 || typeof this.nzCount === 'number' && this.nzCount <= 0 && this.nzShowZero;\n    }\n  }\n  prepareBadgeForRtl() {\n    if (this.isRtlLayout) {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-badge-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-badge-rtl');\n    }\n  }\n  get isRtlLayout() {\n    return this.dir === 'rtl';\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzBadgeComponent_Factory(t) {\n      return new (t || NzBadgeComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(i3.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBadgeComponent,\n      selectors: [[\"nz-badge\"]],\n      hostAttrs: [1, \"ant-badge\"],\n      hostVars: 4,\n      hostBindings: function NzBadgeComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-badge-status\", ctx.nzStatus)(\"ant-badge-not-a-wrapper\", !!(ctx.nzStandalone || ctx.nzStatus || ctx.nzColor));\n        }\n      },\n      inputs: {\n        nzShowZero: \"nzShowZero\",\n        nzShowDot: \"nzShowDot\",\n        nzStandalone: \"nzStandalone\",\n        nzDot: \"nzDot\",\n        nzOverflowCount: \"nzOverflowCount\",\n        nzColor: \"nzColor\",\n        nzStyle: \"nzStyle\",\n        nzText: \"nzText\",\n        nzTitle: \"nzTitle\",\n        nzStatus: \"nzStatus\",\n        nzCount: \"nzCount\",\n        nzOffset: \"nzOffset\",\n        nzSize: \"nzSize\"\n      },\n      exportAs: [\"nzBadge\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[4, \"nzStringTemplateOutlet\"], [3, \"ngStyle\"], [1, \"ant-badge-status-text\"], [3, \"nzOffset\", \"nzSize\", \"nzTitle\", \"nzStyle\", \"nzDot\", \"nzOverflowCount\", \"disableAnimation\", \"nzCount\", \"noAnimation\"]],\n      template: function NzBadgeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzBadgeComponent_Conditional_0_Template, 3, 7);\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, NzBadgeComponent_ng_container_2_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.nzStatus || ctx.nzColor ? 0 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzCount);\n        }\n      },\n      dependencies: [NgStyle, NzBadgeSupComponent, NzOutletModule, i4.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBadgeMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzBadgeComponent.prototype, \"nzShowZero\", void 0);\n__decorate([InputBoolean()], NzBadgeComponent.prototype, \"nzShowDot\", void 0);\n__decorate([InputBoolean()], NzBadgeComponent.prototype, \"nzStandalone\", void 0);\n__decorate([InputBoolean()], NzBadgeComponent.prototype, \"nzDot\", void 0);\n__decorate([WithConfig()], NzBadgeComponent.prototype, \"nzOverflowCount\", void 0);\n__decorate([WithConfig()], NzBadgeComponent.prototype, \"nzColor\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBadgeComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-badge',\n      exportAs: 'nzBadge',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [zoomBadgeMotion],\n      standalone: true,\n      imports: [NgStyle, NzBadgeSupComponent, NzOutletModule],\n      template: `\n    @if (nzStatus || nzColor) {\n      <span\n        class=\"ant-badge-status-dot ant-badge-status-{{ nzStatus || presetColor }}\"\n        [style.background]=\"!presetColor && nzColor\"\n        [ngStyle]=\"nzStyle\"\n      ></span>\n      <span class=\"ant-badge-status-text\">\n        <ng-container *nzStringTemplateOutlet=\"nzText\">{{ nzText }}</ng-container>\n      </span>\n    }\n    <ng-content />\n    <ng-container *nzStringTemplateOutlet=\"nzCount\">\n      @if (showSup) {\n        <nz-badge-sup\n          [nzOffset]=\"nzOffset\"\n          [nzSize]=\"nzSize\"\n          [nzTitle]=\"nzTitle\"\n          [nzStyle]=\"nzStyle\"\n          [nzDot]=\"nzDot\"\n          [nzOverflowCount]=\"nzOverflowCount\"\n          [disableAnimation]=\"!!(nzStandalone || nzStatus || nzColor || noAnimation?.nzNoAnimation)\"\n          [nzCount]=\"nzCount\"\n          [noAnimation]=\"!!noAnimation?.nzNoAnimation\"\n        />\n      }\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-badge',\n        '[class.ant-badge-status]': 'nzStatus',\n        '[class.ant-badge-not-a-wrapper]': '!!(nzStandalone || nzStatus || nzColor)'\n      }\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    nzShowZero: [{\n      type: Input\n    }],\n    nzShowDot: [{\n      type: Input\n    }],\n    nzStandalone: [{\n      type: Input\n    }],\n    nzDot: [{\n      type: Input\n    }],\n    nzOverflowCount: [{\n      type: Input\n    }],\n    nzColor: [{\n      type: Input\n    }],\n    nzStyle: [{\n      type: Input\n    }],\n    nzText: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzCount: [{\n      type: Input\n    }],\n    nzOffset: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRibbonComponent {\n  constructor() {\n    this.nzPlacement = 'end';\n    this.nzText = null;\n    this.presetColor = null;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzColor\n    } = changes;\n    if (nzColor) {\n      this.presetColor = this.nzColor && badgePresetColors.indexOf(this.nzColor) !== -1 ? this.nzColor : null;\n    }\n  }\n  static {\n    this.ɵfac = function NzRibbonComponent_Factory(t) {\n      return new (t || NzRibbonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzRibbonComponent,\n      selectors: [[\"nz-ribbon\"]],\n      hostAttrs: [1, \"ant-ribbon-wrapper\"],\n      inputs: {\n        nzColor: \"nzColor\",\n        nzPlacement: \"nzPlacement\",\n        nzText: \"nzText\"\n      },\n      exportAs: [\"nzRibbon\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 11,\n      consts: [[1, \"ant-ribbon\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-ribbon-corner\"], [1, \"ant-ribbon-text\"]],\n      template: function NzRibbonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵtemplate(2, NzRibbonComponent_ng_container_2_Template, 3, 1, \"ng-container\", 1);\n          i0.ɵɵelement(3, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.presetColor && \"ant-ribbon-color-\" + ctx.presetColor);\n          i0.ɵɵstyleProp(\"background-color\", !ctx.presetColor && ctx.nzColor);\n          i0.ɵɵclassProp(\"ant-ribbon-placement-end\", ctx.nzPlacement === \"end\")(\"ant-ribbon-placement-start\", ctx.nzPlacement === \"start\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzText);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"color\", !ctx.presetColor && ctx.nzColor);\n        }\n      },\n      dependencies: [NzOutletModule, i4.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRibbonComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-ribbon',\n      exportAs: 'nzRibbon',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [NzOutletModule],\n      template: `\n    <ng-content></ng-content>\n    <div\n      class=\"ant-ribbon\"\n      [class]=\"presetColor && 'ant-ribbon-color-' + presetColor\"\n      [class.ant-ribbon-placement-end]=\"nzPlacement === 'end'\"\n      [class.ant-ribbon-placement-start]=\"nzPlacement === 'start'\"\n      [style.background-color]=\"!presetColor && nzColor\"\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzText\">\n        <span class=\"ant-ribbon-text\">{{ nzText }}</span>\n      </ng-container>\n      <div class=\"ant-ribbon-corner\" [style.color]=\"!presetColor && nzColor\"></div>\n    </div>\n  `,\n      host: {\n        class: 'ant-ribbon-wrapper'\n      }\n    }]\n  }], () => [], {\n    nzColor: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzText: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBadgeModule {\n  static {\n    this.ɵfac = function NzBadgeModule_Factory(t) {\n      return new (t || NzBadgeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzBadgeModule,\n      imports: [NzBadgeComponent, NzRibbonComponent],\n      exports: [NzBadgeComponent, NzRibbonComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzBadgeComponent, NzRibbonComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBadgeModule, [{\n    type: NgModule,\n    args: [{\n      exports: [NzBadgeComponent, NzRibbonComponent],\n      imports: [NzBadgeComponent, NzRibbonComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzBadgeComponent, NzBadgeModule, NzRibbonComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,KAAK,CAAC;AAC3B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,OAAU,cAAc,CAAC,EAAE;AACjC,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,WAAW,SAAS,OAAO,WAAW,IAAI,CAAC;AAC1D,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,MAAM,GAAG;AAAA,EACtC;AACF;AACA,SAAS,+DAA+D,IAAI,KAAK;AAC/E,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,sEAAsE,GAAG,GAAG,KAAK,GAAM,yBAAyB;AAAA,EACzI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,OAAO,gBAAgB;AAAA,EACvC;AACF;AACA,SAAS,iDAAiD,IAAI,KAAK;AACjE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,gEAAgE,GAAG,CAAC;AACrF,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,aAAa,gBAAgB,CAAC,OAAO,WAAW,IAAI,IAAI,MAAM,IAAI;AACjF,IAAG,WAAW,iBAAiB,OAAO,WAAW;AACjD,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,CAAC,OAAO,SAAS,OAAO,WAAW,IAAI,MAAM,SAAY,IAAI,EAAE;AAAA,EACrF;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,iBAAiB,GAAG,kDAAkD,GAAG,GAAG,QAAQ,GAAM,yBAAyB;AAAA,EACxH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,cAAc;AAAA,EACrC;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AAAA,EACb;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,mBAAmB,KAAK,OAAO,iBAAiB,IAAI;AAAA,EACzD;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,wCAAwC,IAAI,KAAK;AACxD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAChG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,uBAAuB,0CAA0C,OAAO,YAAY,OAAO,aAAa,EAAE;AAC7G,IAAG,YAAY,cAAc,CAAC,OAAO,eAAe,OAAO,OAAO;AAClE,IAAG,WAAW,WAAW,OAAO,OAAO;AACvC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,0BAA0B,OAAO,MAAM;AAAA,EACvD;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,gBAAgB,CAAC;AAAA,EACnC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,YAAY,OAAO,QAAQ,EAAE,UAAU,OAAO,MAAM,EAAE,WAAW,OAAO,OAAO,EAAE,WAAW,OAAO,OAAO,EAAE,SAAS,OAAO,KAAK,EAAE,mBAAmB,OAAO,eAAe,EAAE,oBAAoB,CAAC,EAAE,OAAO,gBAAgB,OAAO,YAAY,OAAO,YAAY,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,eAAe,EAAE,WAAW,OAAO,OAAO,EAAE,eAAe,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc;AAAA,EAC5c;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,gBAAgB,CAAC;AAChG,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,EAAE;AAAA,EAC7C;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,iBAAiB,CAAC;AACvB,SAAK,aAAa,CAAC;AACnB,SAAK,QAAQ;AACb,SAAK,mBAAmB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACvD;AAAA,EACA,yBAAyB;AACvB,SAAK,iBAAiB,KAAK,gBAAgB,SAAS,EAAE,MAAM,EAAE;AAAA,EAChE;AAAA,EACA,WAAW;AACT,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,OAAO,QAAQ,iBAAiB,UAAU;AACvD,WAAK,QAAQ,KAAK,IAAI,GAAG,QAAQ,YAAY;AAC7C,WAAK,aAAa,KAAK,MAAM,SAAS,EAAE,MAAM,EAAE,EAAE,IAAI,UAAQ,CAAC,IAAI;AAAA,IACrE;AACA,QAAI,iBAAiB;AACnB,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,cAAc,IAAI,gBAAgB,EAAE,oBAAoB,MAAS;AAC5F,UAAG,YAAY,SAAS,IAAI,YAAY,OAAO,KAAK,IAAI,WAAW,IAAI,OAAO;AAC9E,UAAG,WAAW,IAAI,OAAO;AACzB,UAAG,YAAY,SAAS,IAAI,YAAY,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI,EAAE,cAAc,IAAI,YAAY,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI;AACrK,UAAG,YAAY,mBAAmB,CAAC,IAAI,KAAK,EAAE,sBAAsB,IAAI,WAAW,OAAO,EAAE,iBAAiB,IAAI,KAAK,EAAE,4BAA4B,IAAI,WAAW,UAAU,CAAC;AAAA,QAChL;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,0BAA0B,GAAG,iBAAiB,WAAW,GAAG,CAAC,GAAG,0BAA0B,GAAG,eAAe,GAAG,CAAC,GAAG,+BAA+B,GAAG,SAAS,GAAG,CAAC,GAAG,6BAA6B,CAAC;AAAA,MAChN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,4CAA4C,GAAG,CAAC,EAAE,GAAG,4CAA4C,GAAG,CAAC;AAAA,QACxH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,SAAS,IAAI,kBAAkB,IAAI,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,sBAAsB;AAAA,MACrC,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe;AAAA,MAC7B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,MAC5B,YAAY;AAAA,MACZ,SAAS,CAAC,sBAAsB;AAAA,MAChC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,2BAA2B;AAAA,QAC3B,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,oCAAoC;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAoB,CAAC,QAAQ,OAAO,UAAU,UAAU,QAAQ,SAAS,QAAQ,UAAU,YAAY,WAAW,WAAW,QAAQ,MAAM;AACjJ,IAAM,wBAAwB;AAC9B,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,iBAAiB,UAAU,KAAK,YAAY,gBAAgB,aAAa;AACnF,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,mBAAmB;AACxB,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,cAAc,KAAK,WAAW,kBAAkB,QAAQ,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU;AAAA,IACrG;AACA,QAAI,aAAa,SAAS,WAAW,YAAY;AAC/C,WAAK,UAAU,KAAK,aAAa,KAAK,SAAS,OAAO,KAAK,YAAY,YAAY,KAAK,UAAU,KAAK,OAAO,KAAK,YAAY,YAAY,KAAK,WAAW,KAAK,KAAK;AAAA,IACvK;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,aAAa;AACpB,WAAK,SAAS,SAAS,KAAK,WAAW,eAAe,eAAe;AAAA,IACvE,OAAO;AACL,WAAK,SAAS,YAAY,KAAK,WAAW,eAAe,eAAe;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,eAAe,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,wBAAwB,CAAC,CAAC;AAAA,IAClS;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,WAAW,CAAC,GAAG,WAAW;AAAA,MAC1B,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,oBAAoB,IAAI,QAAQ,EAAE,2BAA2B,CAAC,EAAE,IAAI,gBAAgB,IAAI,YAAY,IAAI,QAAQ;AAAA,QACjI;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,YAAY,UAAU,WAAW,WAAW,SAAS,mBAAmB,oBAAoB,WAAW,aAAa,CAAC;AAAA,MAC/M,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,yCAAyC,GAAG,CAAC;AAC9D,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,gBAAgB,CAAC;AAAA,QACpF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,YAAY,IAAI,UAAU,IAAI,EAAE;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA0B,IAAI,OAAO;AAAA,QACrD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,qBAAqB,gBAAmB,+BAA+B;AAAA,MAC/F,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe;AAAA,MAC7B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,aAAa,MAAM;AAC5E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,gBAAgB,MAAM;AAC/E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,SAAS,MAAM;AACxE,WAAW,CAAC,WAAW,CAAC,GAAG,iBAAiB,WAAW,mBAAmB,MAAM;AAChF,WAAW,CAAC,WAAW,CAAC,GAAG,iBAAiB,WAAW,WAAW,MAAM;AAAA,CACvE,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,MAC5B,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS,qBAAqB,cAAc;AAAA,MACtD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA4BV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,4BAA4B;AAAA,QAC5B,mCAAmC;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,SAAS;AACX,WAAK,cAAc,KAAK,WAAW,kBAAkB,QAAQ,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU;AAAA,IACrG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,WAAW,CAAC,GAAG,oBAAoB;AAAA,MACnC,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,aAAa;AAAA,QACb,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,iBAAiB,CAAC;AAAA,MAC3G,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AACjB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC;AACnF,UAAG,UAAU,GAAG,OAAO,CAAC;AACxB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,IAAI,eAAe,sBAAsB,IAAI,WAAW;AACtE,UAAG,YAAY,oBAAoB,CAAC,IAAI,eAAe,IAAI,OAAO;AAClE,UAAG,YAAY,4BAA4B,IAAI,gBAAgB,KAAK,EAAE,8BAA8B,IAAI,gBAAgB,OAAO;AAC/H,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,MAAM;AAClD,UAAG,UAAU;AACb,UAAG,YAAY,SAAS,CAAC,IAAI,eAAe,IAAI,OAAO;AAAA,QACzD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc;AAAA,MACxB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAeV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,IAC/C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,IAC/C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}
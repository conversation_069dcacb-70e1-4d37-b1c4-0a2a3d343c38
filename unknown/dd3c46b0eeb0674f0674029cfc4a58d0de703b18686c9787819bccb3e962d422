/* Link block styles */
:host ::ng-deep .content-body a {
  color: inherit;
  text-decoration: none;
}

:host ::ng-deep .content-body .line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dark mode hover state */
.dark :host ::ng-deep .content-body a:hover {
  background-color: rgba(55, 65, 81, 0.5);
}

/* Smooth transitions */
:host ::ng-deep .content-body a {
  transition: all 0.2s ease-in-out;
}

<div class="p-6 bg-[var(--background-white)] text-[var(--text-dark)]">
  <h2 class="text-xl font-semibold mb-6">Add OpenAPI Plugin</h2>

  <form nz-form #pluginForm="ngForm" (ngSubmit)="submitForm(pluginForm)" class="space-y-6">
    <!-- Plugin Name -->
    <nz-form-item>
      <nz-form-label [nzSpan]="24" nzRequired class="!text-[var(--text-dark)]">
        Plugin Name
      </nz-form-label>
      <nz-form-control [nzSpan]="24"
                      nzHasFeedback
                      [nzErrorTip]="pluginNameError">
        <input nz-input
               name="pluginName"
               [(ngModel)]="plugin.pluginName"
               required
               class="bg-[var(--background-white)] text-[var(--text-dark)] border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)]"
               placeholder="Enter a unique name for this plugin" />
        <ng-template #pluginNameError let-control>
          <ng-container *ngIf="control.hasError('required')">
            Please input a plugin name!
          </ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <!-- OpenAPI URL -->
    <nz-form-item>
      <nz-form-label [nzSpan]="24" nzRequired class="!text-[var(--text-dark)]">
        OpenAPI URL
      </nz-form-label>
      <nz-form-control [nzSpan]="24"
                      nzHasFeedback
                      [nzErrorTip]="urlError"
                      nzExtra="The URL must point to a valid OpenAPI/Swagger specification">
        <input nz-input
               name="openApiUrl"
               [(ngModel)]="plugin.openApiUrl"
               required
               class="bg-[var(--background-white)] text-[var(--text-dark)] border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)]"
               placeholder="https://api.example.com/swagger/v1/swagger.json" />
        <ng-template #urlError let-control>
          <ng-container *ngIf="control.hasError('required')">
            Please input the OpenAPI URL!
          </ng-container>
        </ng-template>
      </nz-form-control>
    </nz-form-item>

    <!-- Action Buttons -->
    <div class="flex justify-end gap-3">
      <button nz-button
              (click)="handleCancel()"
              class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] hover:bg-[var(--secondary-purple)] transition-all duration-300">
        Cancel
      </button>
      <button nz-button
              nzType="primary"
              type="submit"
              [disabled]="!pluginForm.form.valid"
              class="bg-[var(--primary-purple)] text-white hover:bg-[var(--secondary-purple)] transition-all duration-300 disabled:opacity-50">
        Create Plugin
      </button>
    </div>
  </form>
</div>

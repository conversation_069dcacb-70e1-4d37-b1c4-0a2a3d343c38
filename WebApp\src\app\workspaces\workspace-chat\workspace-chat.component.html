<div>
  <!-- Chat Messages -->
  <div class="h-[75vh] flex flex-col justify-between overflow-y-auto relative ">
    <div class="flex flex-col gap-2 overflow-y-auto h-[80vh] chat-container">
      <!-- Messages -->
      <div *ngIf="messages.length === 0" class="flex flex-col items-center justify-center h-full text-center">
        <div
          class="flex items-center justify-center min-w-12 h-12 sm:min-w-14 sm:h-14 bg-[var(--primary-purple)] rounded-[var(--border-radius-large)] mb-4">
          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-2xl sm:text-3xl"></i>
        </div>
        <h3 class=" font-[var(--font-weight-bold)] text-[var(--text-dark)] mb-2">
          Start the Conversation!
        </h3>
        <p class="text-[var(--text-medium-gray)] text-sm sm:text-base">
          Ask <PERSON> anything to get started. Type your message below and press Enter or click Send.
        </p>
      </div>
      <div *ngFor="let message of messages" class="my-2 ">
        <!-- User Message -->
        <div *ngIf="message.sender === 'user'"
          class="flex group gap-2 sm:gap-3 items-start justify-start bg-[var(--background-white)] px-2 py-2 sm:px-4 sm:py-3">
          <div
            class="h-8 w-8 sm:h-10 sm:w-10 bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] flex items-center justify-center">
            <i class="ri-user-2-fill text-lg sm:text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="flex flex-col w-full">
            <span class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1">You • {{ message.timestamp |
              date:'shortTime' }}</span>
            <div class="flex gap-1 justify-between">
              <div
                class="text-sm sm:text-[15px] md:text-[16px]  p-2 sm:p-3 rounded-[8px] shadow-[var(--box-shadow)] text-[var(--text-dark)] break-words">
                <span>{{ message.content }}</span>
              </div>
              <button
                class="p-2 outline-none border-none h-8 w-8 sm:h-10 sm:w-10 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)]  mt-2 flex items-center justify-center cursor-pointer text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)] opacity-0 group-hover:opacity-100 transition-all duration-300">
                <i class="ri-pencil-line text-lg sm:text-xl"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Bot Message -->
        <div *ngIf="message.sender === 'bot'"
          class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3 group">
          <div class="flex gap-2">
            <div
              class="flex items-center justify-center min-w-8 h-8 sm:min-w-10 sm:h-10 bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
              <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
            </div>
            <div class="flex justify-between mb-1 w-full">
              <div class="flex justify-center px-2 gap-2 flex-col">
                <span class="text-[var(--text-medium-gray)] text-xs sm:text-sm">ChatAI • {{ message.timestamp |
                  date:'shortTime' }}</span>
                <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                  {{ message.content }}
                </div>
              </div>
              <!-- Thumbs Up/Down Buttons -->
              <div class="flex gap-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                <button
                  class="p-2 outline-none border-none h-8 w-8 sm:h-10 sm:w-10 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] mt-2 flex items-center justify-center cursor-pointer text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]">
                  <i class="ri-thumb-up-line text-sm sm:text-base"></i>
                </button>
                <button
                  class="p-2 outline-none border-none h-8 w-8 sm:h-10 sm:w-10 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] mt-2 flex items-center justify-center cursor-pointer text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]">
                  <i class="ri-thumb-down-line text-sm sm:text-base"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading Indicator (Bouncing Dots) -->
      <div *ngIf="isLoading" class="flex justify-start my-2">
        <div
          class="flex gap-2 items-center px-4 py-2 bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)]">
          <div
            class="flex items-center justify-center min-w-8 h-8 sm:min-w-10 sm:h-10 bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
            <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
          </div>
          <div class="flex gap-1">
            <span class="dot dot1">.</span>
            <span class="dot dot2">.</span>
            <span class="dot dot3">.</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="mt-[var(--margin-small)] flex items-center">
      <input type="text" [(ngModel)]="newMessage" (keypress)="onKeyPress($event)" placeholder="Type your message..."
      autofocus
        class="flex-1 outline-none bg-[#D8DCE6] border-none rounded-[6px] text-sm sm:text-[16px] md:text-[17px] p-4 resize-none max-h-[150px] placeholder-[var(--text-medium-gray)] line-height-[var(--line-height)]" />
      <button (click)="sendMessage()"
        class="ml-[var(--margin-small)] bg-[var(--primary-purple)] text-[var(--background-white)] p-[var(--padding-small)] rounded-[var(--border-radius-small)] outline-none border-none hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] hover:text-black px-4 cursor-pointer">
        Send
      </button>
    </div>
  </div>
</div>

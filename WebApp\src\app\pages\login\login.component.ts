import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import {
  AuthServiceProxy,
  LoginDto,
  UserAccountServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { AuthService } from '../../../shared/services/auth.service';

import { NzMessageService } from 'ng-zorro-antd/message';
interface User {
  email: string;
  password: string;
}
@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterLink, ServiceProxyModule],
  templateUrl: './login.component.html',
  styles: [
    `
      :host {
        display: block;
      }
    `,
  ],
})
export class LoginComponent {
  user: LoginDto = new LoginDto();
  constructor(
    private router: Router,
    private _userAccountService: UserAccountServiceProxy,
    private auth: AuthService,
    private message: NzMessageService,
    private _authService: AuthServiceProxy
  ) { }

  login() {
    // Add your login logic here
    this._authService.login(this.user).subscribe(
      (res) => {
        console.log('Login response:', res);
        if (res === null) {
          // Handle null response (login failed)
          this.message.error('Login failed! Please check your credentials.');
          return;
        }

        // Save the user information using auth service
        this.auth.setToken(res.token || '', res.expiration); // Save the token and expiration date
        this.message.success('Login successful!');
        this.router.navigate(['/']); // Redirect to the home page or dashboard after successful login

      },
      (error) => {
        console.error('Login error:', error);
        this.message.error('Login failed! An error occurred.');
      }
    );
  }
}

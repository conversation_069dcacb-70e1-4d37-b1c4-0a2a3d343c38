import { Component, inject } from '@angular/core';
import { KeyValue } from '@angular/common';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ChatServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { AuthService } from '../../../shared/services/auth.service';
import { ChatListService } from '../../services/chat-list.service';
import { TogglingService } from '../../toggling.service';

// Define the Chat interface
interface Chat {
  id: number;
  title: string;
  lastMessage?: string;
  isPinned: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  isToggled?: boolean;
  isActive?: boolean;
}

// Define a union type for chats (can be either grouped or non-grouped)
type Chats = { [key: string]: Chat[] } | Chat[];

@Component({
  selector: 'app-workspace-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterLink,
    RouterLinkActive,
    NzPopoverModule,
    NzButtonModule,
    ServiceProxyModule
  ],
  templateUrl: './workspace-sidebar.component.html',
  styleUrls: ['./workspace-sidebar.component.css'],
})
export class WorkspaceSidebarComponent {
  togglingService = inject(TogglingService);
  chatListService = inject(ChatListService);
  router = inject(Router);

  groupedChats: { [key: string]: Chat[] } = { History: [] };
  chatId: any;
  filteredGroupedChats: { [key: string]: Chat[] } = { ...this.groupedChats };
  isAllChatsOpen = true;
  user: any;
  activeTab: 'all' | 'pinned-history' | 'favorite' | 'archive' | 'history' = 'all';
  hasMoreMessages = true;
  originalChatList: Chat[] = [];
  pinnedChats: Chat[] = [];
  favoriteChats: Chat[] = [];
  archivedChats: Chat[] = [];
  counter = 1;

  originalOrder = (a: KeyValue<string, Chat[]>, b: KeyValue<string, Chat[]>): number => {
    const order = ['History'];
    return order.indexOf(a.key) - order.indexOf(b.key);
  };

  tabConfig: { [key: string]: { title: string; chats: () => Chat[] | { [key: string]: Chat[] }; isGrouped: boolean; hasMore?: boolean } } = {
    'all': { title: 'All Chats', chats: () => this.groupedChats, isGrouped: true, hasMore: true },
    'pinned-history': { title: 'Pinned Chats', chats: () => this.pinnedChats, isGrouped: false },
    'favorite': { title: 'Favorite Chats', chats: () => this.favoriteChats, isGrouped: false },
    'archive': { title: 'Archive Chats', chats: () => this.archivedChats, isGrouped: false },
    'history': { title: 'Chat History', chats: () => [], isGrouped: false },
  };

  constructor(
    private _chatService: ChatServiceProxy,
    public authService: AuthService,
    private nzMessageService: NzMessageService
  ) { }
  workspaceName: string = '';
  isLoading: boolean = false;
  isLoadingMore: boolean = false;
  ngOnInit(): void {
    this.user = this.authService.getUser();
    let router = this.router.url.split('/');
    if (router[router.length - 1] === 'chat') {
      this.workspaceName = router[router.length - 2];
    } else if (router[router.length - 2] === 'chat') {
      this.workspaceName = router[router.length - 3];
    }

    // Check if we're on the daily insight page (root path)
    const isDailyInsightMode = this.router.url === '/' || this.router.url === '';

    // Only load chat lists if we're not in daily insight mode
    if (!isDailyInsightMode) {
      console.log('Not in daily insight mode, loading chat lists');

      // Only load the primary chat list initially
      this.loadChatList();

      // Only load data for the active tab
      if (this.activeTab === 'pinned-history') {
        this.loadPinnedChats();
      } else if (this.activeTab === 'favorite') {
        this.loadFavoriteChats();
      } else if (this.activeTab === 'archive') {
        this.loadArchivedChats();
      }
    } else {
      console.log('In daily insight mode, skipping chat list loading');
    }
  }

  async loadChatList() {
    // Show loading indicator if we're on the all chats tab
    if (this.activeTab === 'all') {
      this.isLoading = true;
    }

    try {
      console.log('Loading chat list for workspace:', this.workspaceName);

      let res: any = await this._chatService
        .list(this.workspaceName, this.counter, 15)
        .toPromise();

      this.chatListService.chatList = res.messages;
      this.originalChatList = res.messages;
      this.hasMoreMessages = res.hasMoreMessages;
      this.chatListService.groupChatsByDate();
      this.groupedChats = this.chatListService.groupedChats;

      // Only update filteredGroupedChats if we're on the 'all' tab
      if (this.activeTab === 'all') {
        this.filteredGroupedChats = { ...this.groupedChats };
        this.isLoading = false;
      } else {
        // For other tabs, maintain the existing filtered list by refreshing it
        switch (this.activeTab) {
          case 'pinned-history':
            this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);
            break;
          case 'favorite':
            this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);
            break;
          case 'archive':
            this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);
            break;
        }
      }

      this.counter++;
    } catch (err) {
      console.error('Error loading chat list:', err);
      this.chatListService.chatList = [];
      this.originalChatList = [];
      this.hasMoreMessages = false;
      this.chatListService.groupedChats = {};
      this.groupedChats = this.chatListService.groupedChats;

      if (this.activeTab === 'all') {
        this.filteredGroupedChats = { ...this.groupedChats };
        this.isLoading = false;
      }
    }
  }

  loadMoreChatList() {
    // Show loading indicator for "load more"
    this.isLoadingMore = true;

    console.log('Loading more chats for workspace:', this.workspaceName);

    this._chatService
      .list(this.workspaceName, this.counter, 15)
      .subscribe({
        next: (res: any) => {
          this.counter++;
          if (res) {
            this.chatListService.chatList.push(...res.messages);
            this.originalChatList.push(...res.messages);
            this.hasMoreMessages = res.hasMoreMessages;
            this.chatListService.groupChatsByDate();
            this.groupedChats = this.chatListService.groupedChats;

            // Only update filteredGroupedChats if we're on the 'all' tab
            if (this.activeTab === 'all') {
              this.filteredGroupedChats = { ...this.groupedChats };
            }
          }
          this.isLoadingMore = false;
        },
        error: (err) => {
          console.error('Error loading more chats:', err);
          this.isLoadingMore = false;
        }
      });
  }

  loadPinnedChats() {
    // Show loading indicator if we're on the pinned tab
    if (this.activeTab === 'pinned-history') {
      this.isLoading = true;
    }

    this._chatService.pinned(this.workspaceName).subscribe({
      next: (res: any) => {
        this.pinnedChats = res || [];
        if (this.activeTab === 'pinned-history') {
          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);
          this.isLoading = false;
        }
      },
      error: (err) => {
        console.error('Error loading pinned chats:', err);
        this.pinnedChats = [];
        if (this.activeTab === 'pinned-history') {
          this.filteredGroupedChats = {};
          this.isLoading = false;
        }
      }
    });
  }

  loadFavoriteChats() {
    // Show loading indicator if we're on the favorites tab
    if (this.activeTab === 'favorite') {
      this.isLoading = true;
    }

    this._chatService.favorites(this.workspaceName).subscribe({
      next: (res: any) => {
        this.favoriteChats = res || [];
        if (this.activeTab === 'favorite') {
          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);
          this.isLoading = false;
        }
      },
      error: (err) => {
        console.error('Error loading favorite chats:', err);
        this.favoriteChats = [];
        if (this.activeTab === 'favorite') {
          this.filteredGroupedChats = {};
          this.isLoading = false;
        }
      }
    });
  }

  loadArchivedChats() {
    // Show loading indicator if we're on the archive tab
    if (this.activeTab === 'archive') {
      this.isLoading = true;
    }

    this._chatService.archived(this.workspaceName).subscribe({
      next: (res: any) => {
        this.archivedChats = res || [];
        if (this.activeTab === 'archive') {
          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);
          this.isLoading = false;
        }
      },
      error: (err) => {
        console.error('Error loading archived chats:', err);
        this.archivedChats = [];
        if (this.activeTab === 'archive') {
          this.filteredGroupedChats = {};
          this.isLoading = false;
        }
      }
    });
  }

  toggleTab(tab: any) {
    // If we're already on this tab, don't do anything
    if (this.activeTab === tab) {
      return;
    }

    // Update the activeTab
    this.activeTab = tab;
    this.isAllChatsOpen = true;

    // Apply changes based on the selected tab
    switch (tab) {
      case 'pinned-history':
        // Check if we already have pinned chats data
        if (!this.pinnedChats || this.pinnedChats.length === 0) {
          // Only load if we don't have data
          this.loadPinnedChats();
        } else {
          // Use existing data
          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);
        }
        break;
      case 'favorite':
        // Check if we already have favorite chats data
        if (!this.favoriteChats || this.favoriteChats.length === 0) {
          // Only load if we don't have data
          this.loadFavoriteChats();
        } else {
          // Use existing data
          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);
        }
        break;
      case 'archive':
        // Check if we already have archived chats data
        if (!this.archivedChats || this.archivedChats.length === 0) {
          // Only load if we don't have data
          this.loadArchivedChats();
        } else {
          // Use existing data
          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);
        }
        break;
      case 'all':
      default:
        this.filteredGroupedChats = this.groupedChats;
        break;
    }
  }

  ngDoCheck(): void {
    if (this.chatListService.groupedChats !== this.groupedChats) {
      this.groupedChats = this.chatListService.groupedChats;
      this.filteredGroupedChats = { ...this.groupedChats };
    }
  }

  trackByChatId(index: number, chat: Chat): string {
    return chat.id.toString();
  }

  filterChats(event: Event) {
    event.stopPropagation();
    const searchTerm = (event.target as HTMLInputElement).value.toLowerCase();

    if (!searchTerm) {
      this.filteredGroupedChats = { ...this.groupedChats };
      return;
    }

    this.filteredGroupedChats = {};
    Object.keys(this.groupedChats).forEach((group) => {
      this.filteredGroupedChats[group] = this.groupedChats[group].filter(
        (chat: Chat) => chat.title.toLowerCase().includes(searchTerm)
      );
    });
  }

  toggleAllChats(event: Event) {
    event.stopPropagation();
    this.isAllChatsOpen = !this.isAllChatsOpen;
  }

  addNewChats(event: Event) {
    event.stopPropagation();
    this.isAllChatsOpen = true;
    this.router.navigate(['workspaces', this.workspaceName, 'chat']);
  }

  toggleChat(event: Event, chat: Chat) {
    this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);
    event.stopPropagation();
    chat.isToggled = !chat.isToggled;
  }

  addToPinnedChat(chat: Chat) {
    this.chatId = chat.id;
    this._chatService.pin(this.chatId, !chat.isPinned).subscribe((res: any) => {
      if (res.isPinned) {
        this.nzMessageService.success('Chat pinned successfully!');
        this.pinnedChats = [...this.pinnedChats, res];
      } else {
        this.nzMessageService.success('Chat unpinned successfully!');
        this.pinnedChats = this.pinnedChats.filter((c) => c.id !== res.id);
      }
      const index = this.chatListService.chatList.findIndex((c: any) => c.id === res.id);
      if (index !== -1) {
        this.chatListService.chatList[index] = res;
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;
        this.filteredGroupedChats = { ...this.groupedChats };
      }
    });
  }

  addToFavChat(chat: Chat) {
    this.chatId = chat.id;
    this._chatService.favorite(this.chatId, !chat.isFavorite).subscribe((res: any) => {
      if (res.isFavorite) {
        this.nzMessageService.success('Chat favorited successfully!');
        this.favoriteChats = [...this.favoriteChats, res];
      } else {
        this.nzMessageService.success('Chat unfavorited successfully!');
        this.favoriteChats = this.favoriteChats.filter((c) => c.id !== res.id);
      }
      const index = this.chatListService.chatList.findIndex((c: any) => c.id === res.id);
      if (index !== -1) {
        this.chatListService.chatList[index] = res;
        this.chatListService.groupChatsByDate();
        this.groupedChats = this.chatListService.groupedChats;
        this.filteredGroupedChats = { ...this.groupedChats };
      }
    });
  }

  addToArchiveChat(chat: Chat) {
    this.chatId = chat.id;
    this._chatService.archive(this.chatId, !chat.isArchived).subscribe((res: any) => {
      if (res.isArchived) {
        this.nzMessageService.success('Chat archived successfully!');
        this.archivedChats = [...this.archivedChats, res];
        this.chatListService.chatList = this.chatListService.chatList.filter((c: any) => c.id !== res.id);
      } else {
        this.nzMessageService.success('Chat unarchived successfully!');
        this.archivedChats = this.archivedChats.filter((c) => c.id !== res.id);
        this.chatListService.chatList.push(res);
      }
      this.chatListService.groupChatsByDate();
      this.groupedChats = this.chatListService.groupedChats;
      this.filteredGroupedChats = { ...this.groupedChats };
    });
  }

  getCurrentTabChats() {
    return this.tabConfig[this.activeTab].chats();
  }

  getCurrentTabTitle() {
    return this.tabConfig[this.activeTab].title;
  }

  isCurrentTabGrouped() {
    return this.tabConfig[this.activeTab].isGrouped;
  }

  hasMoreForCurrentTab() {
    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;
  }

  getChatsForTab(): Chat[] | { [key: string]: Chat[] } {
    const chats = this.tabConfig[this.activeTab].chats();
    if (Array.isArray(chats)) {
      return chats;
    }
    return chats || {};
  }

  isGroupedForTab(): boolean {
    return this.tabConfig[this.activeTab].isGrouped;
  }

  /**
   * Groups chats by date for display in the sidebar
   * @param chats The list of chats to group
   * @returns An object with date groups as keys and arrays of chats as values
   */
  groupChatsByDate(chats: any[]): { [key: string]: any[] } {
    if (!chats || chats.length === 0) {
      return {};
    }

    const groupedChats: { [key: string]: any[] } = {
      'Today': [],
      'Yesterday': [],
      'Last 7 Days': [],
      'Last 30 Days': [],
      'Older': [],
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const last7Days = new Date(today);
    last7Days.setDate(last7Days.getDate() - 7);

    const last30Days = new Date(today);
    last30Days.setDate(last30Days.getDate() - 30);

    chats.forEach((chat) => {
      const chatDate = new Date(chat.createdAt);
      chatDate.setHours(0, 0, 0, 0);

      if (chatDate.getTime() === today.getTime()) {
        groupedChats['Today'].push(chat);
      } else if (chatDate.getTime() === yesterday.getTime()) {
        groupedChats['Yesterday'].push(chat);
      } else if (chatDate >= last7Days) {
        groupedChats['Last 7 Days'].push(chat);
      } else if (chatDate >= last30Days) {
        groupedChats['Last 30 Days'].push(chat);
      } else {
        groupedChats['Older'].push(chat);
      }
    });

    // Remove empty groups
    Object.keys(groupedChats).forEach((key) => {
      if (groupedChats[key].length === 0) {
        delete groupedChats[key];
      }
    });

    return groupedChats;
  }
}

<div class="p-4">
  <div class="mb-4">
    <label for="modelId" class="text-body text-text-dark block mb-2">Model ID</label>
    <input id="modelId" name="modelId" [(ngModel)]="apiConfig.modelId"
      class="w-full p-small rounded-small border border-hover-blue-gray text-body text-text-dark focus:outline-none focus:ring-2 focus:ring-primary-purple"
      required />
  </div>
  <div class="mb-4">
    <label for="provider" class="text-body text-text-dark block mb-2">Provider</label>
    <input id="provider" name="provider" [(ngModel)]="apiConfig.provider"
      class="w-full p-small rounded-small border border-hover-blue-gray text-body text-text-dark focus:outline-none focus:ring-2 focus:ring-primary-purple"
      required />
  </div>
  <div class="mb-4">
    <label for="apiKey" class="text-body text-text-dark block mb-2">API Key</label>
    <input id="apiKey" name="apiKey" [(ngModel)]="apiConfig.apiKey"
      class="w-full p-small rounded-small border border-hover-blue-gray text-body text-text-dark focus:outline-none focus:ring-2 focus:ring-primary-purple"
      required />
  </div>

  <div class="flex justify-end space-x-4">
    <button type="button" (click)="onCancel()"
      class="bg-hover-blue-gray text-text-dark py-2 px-4 rounded-small cursor-pointer hover:bg-gray-300 transition-default">
      Cancel
    </button>
    <button type="submit" (click)="onSubmit()"
      class="bg-primary-purple hover:text-black cursor-pointer text-background-white py-2 px-4 rounded-small hover:bg-secondary-purple transition-default">
      {{ isUpdating ? 'Update' : 'Add' }}
    </button>
  </div>
</div>
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';
import { EmbeddingConfigServiceProxy, EmbeddingConfiguration } from '../../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';

@Component({
  selector: 'app-add-or-edit-embedding',
  standalone: true,
  imports: [CommonModule, FormsModule, NzModalModule],
  templateUrl: './add-or-edit-embedding.component.html',
  styleUrl: './add-or-edit-embedding.component.css'
})
export class AddOrEditEmbeddingComponent implements OnInit {
  isUpdating = false;
  apiConfig: EmbeddingConfiguration = new EmbeddingConfiguration();
  @Output() save = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<void>();

  constructor(
    private _embeddingService: EmbeddingConfigServiceProxy,
    private modal: NzModalRef
  ) { }

  ngOnInit() {
    // Get the data passed from the modal
    const modalData = this.modal.getConfig().nzData;
    if (modalData) {
      this.isUpdating = modalData.isUpdating;
      this.apiConfig = modalData.apiConfig;
      // Set isActive to false for new configurations
      if (!this.isUpdating) {
        this.apiConfig.isActive = false;
      }
      console.log('Modal Data:', modalData);
    }
  }

  onSubmit() {
    const config = new EmbeddingConfiguration({
      ...this.apiConfig,
      isActive: Boolean(this.apiConfig.isActive)
    });

    if (this.isUpdating) {
      this._embeddingService.createEmbeddingConfig(config).subscribe((res: any) => {
        console.log(res);
        this.save.emit(res);
      }, (error: any) => {
        console.error('Failed to update embedding configuration:', error);
      });
    } else {
      this._embeddingService.createEmbeddingConfig(config).subscribe((res: any) => {
        console.log(res);
        this.save.emit(res);
      }, (error: any) => {
        console.error('Failed to create embedding configuration:', error);
      });
    }
  }

  onCancel() {
    this.cancel.emit();
  }
}

<div [ngClass]="{'spinner-overlay': overlay}" class="flex items-center justify-center">
  <div class="flex flex-col items-center">
    <!-- Clean, Simple Spinner with dynamic size based on input -->
    <div class="spinner-container" [ngClass]="{
      'small': size === 'small',
      'medium': size === 'medium',
      'large': size === 'large'
    }">
      <!-- Main spinner circle -->
      <svg class="spinner-svg" viewBox="0 0 50 50">
        <!-- Background track -->
        <circle class="spinner-track" cx="25" cy="25" r="20" fill="none" stroke-width="2" />

        <!-- Animated progress arc -->
        <circle class="spinner-progress" cx="25" cy="25" r="20" fill="none" stroke-width="3" stroke-linecap="round" />
      </svg>

      <!-- Pulsing dot in center -->
      <div class="spinner-dot"></div>
    </div>

    <!-- Loading message with conditional display -->
    <div *ngIf="showMessage" class="loading-text-container fade-in-animation" role="status">
      <div class="loading-text-wrapper">
        <span class="loading-text-content">{{ message }}</span>
        <span class="loading-dots"></span>
      </div>
      <div class="loading-text-bar"></div>
      <span class="sr-only">Please wait while content is loading</span>
    </div>
  </div>
</div>

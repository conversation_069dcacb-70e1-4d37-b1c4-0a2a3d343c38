import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class TogglingService {

  isNavbarOpen = true;

  constructor() { }

  ngOnInit() {
    const storedValue = localStorage.getItem('isNavbarOpen');
    if (storedValue === 'true') {
      this.isNavbarOpen = true;
    } else if (storedValue === 'false') {
      this.isNavbarOpen = false;
    } else {
      this.isNavbarOpen = true; // Default value if not set in localStorage
    }
    console.log('isNavbarOpen', this.isNavbarOpen);
  }

  /**
   * Toggles the sidebar between states
   * @param action Optional action to specify the toggle behavior:
   *   - 'toggle': Cycle through states (collapsed -> narrow -> expanded -> collapsed)
   *   - 'collapse': Collapse the sidebar
   *   - 'expand': Expand the sidebar to full width
   *   - 'narrow': Set the sidebar to narrow width
   */
  toggleNavbar(action?: 'toggle' | 'collapse' | 'expand' | 'narrow') {
    // If no action is specified, use the default toggle behavior
    if (!action) {
      action = this.isNavbarOpen ? 'collapse' : 'narrow';
    }

    // Update the isNavbarOpen state based on the action
    if (action === 'collapse') {
      this.isNavbarOpen = false;
    } else {
      this.isNavbarOpen = true;
    }

    localStorage.setItem('isNavbarOpen', this.isNavbarOpen.toString());

    // Emit an event that the app component can listen to
    const toggleEvent = new CustomEvent('sidebar-toggle', {
      detail: {
        action: action,
        isOpen: this.isNavbarOpen
      }
    });
    window.dispatchEvent(toggleEvent);

    // Trigger a resize event to ensure the split layout updates
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 10);
  }

  /**
   * Collapses the sidebar to its minimum width
   */
  collapseSidebar() {
    this.toggleNavbar('collapse');
  }

  /**
   * Expands the sidebar to its maximum width
   */
  expandSidebar() {
    this.toggleNavbar('expand');
  }

  /**
   * Sets the sidebar to its narrow width
   */
  narrowSidebar() {
    this.toggleNavbar('narrow');
  }

  /**
   * Cycles through the sidebar states
   */
  cycleSidebarState() {
    this.toggleNavbar('toggle');
  }
}

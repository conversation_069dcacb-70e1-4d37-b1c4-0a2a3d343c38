/* Previous CSS variables (unchanged) */
:root {
  --primary-purple: #6B46C1;
  --primary-purple-rgb: 107, 70, 193; /* RGB values for primary-purple */
  --secondary-purple: #D6BCFA;
  --background-light-gray: #F7FAFC;
  --background-white: #FFFFFF;
  --text-dark: #333333;
  --text-medium-gray: #555555;
  --hover-blue-gray: #D8DCE6;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --font-family: 'Roboto', sans-serif;
  --font-size-header: 24px;
  --font-size-body: 14px;
  --font-weight-bold: 700;
  --font-weight-regular: 400;
  --line-height: 1.6;
  --border-radius-small: 5px;
  --border-radius-large: 10px;
  --padding-small: 10px;
  --padding-medium: 15px;
  --padding-large: 20px;
  --margin-small: 10px;
  --box-shadow: 0 2px 5px var(--shadow-color);
  --transition-default: all 0.3s ease;
  --header-bg: #EDEDED;
  --header-text: var(--text-dark);
  --dialog-bg: #EDF2F7;
  --active-tab-bg: #BEE3F8;
  --active-chat-bg: #C6F6D5;
}

.dark {
  --primary-purple: #10A37F;
  --primary-purple-rgb: 16, 163, 127; /* RGB values for primary-purple in dark mode */
  --secondary-purple: #19C59A;
  --background-light-gray: #202123;
  --background-white: #343541;
  --text-dark: #ECECF1;
  --text-medium-gray: #ACACBE;
  --hover-blue-gray: #2B2C2F;
  --shadow-color: rgba(0, 0, 0, 0.6);
  --header-bg: #2B2C2F;
  --header-text: #ECECF1;
  --dialog-bg: #444654;
  --active-tab-bg: #10A37F;
  --active-chat-bg: #343541;
}

/* You can add global styles to this file, and also import other style files */
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;

}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 9999px;
  /* rounded-full */
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

:hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
}

body {
  font-family: Archivo, sans-serif;
  background-color: #171717;
}

.ant-modal-header {
  display: none;
}

.ant-breadcrumb-separator {
  margin: 0 4px !important;
}



.ant-modal-close {

  display: none !important;
}

.ant-input-group-addon:last-child {
  display: none !important;

}

.ant-breadcrumb a {
  color: var(--text-dark) !important;
  font-weight: var(--font-weight-regular) !important;
  font-size: 16px !important;
}

.ant-breadcrumb-separator {
  color: var(--text-dark) !important;
}

.ant-modal-body {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
}

.ant-popover-title {
  display: none !important;
}

.ant-popover-inner {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
  border-radius: var(--border-radius-small) !important;
  box-shadow: var(--box-shadow) !important;
}

.ant-drawer-body {
  padding: 0 !important;
  padding-left: 5px !important;
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
}



/* Only keep EditorJS specific styles that can't be handled by Tailwind */
.codex-editor__redactor {
  padding-bottom: 200px !important;
  color: var(--text-dark);
}

/* Add any other editor-specific styles that can't be handled by Tailwind */

/* Theme-aware EditorJS elements */
.editor-container {
  color: var(--text-dark);
  background-color: var(--background-white);
}

/* Header styles */
.ce-header {
  color: var(--text-dark);
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  margin-bottom: 0.75rem;
}

/* Dark theme support for headers */
@media (prefers-color-scheme: dark) {
  .ce-header {
    color: white !important;
  }
}

/* For theme toggling via class */
.dark-theme .ce-header,
[data-theme="dark"] .ce-header {
  color: white !important;
}

/* Apply to header blocks in EditorJS */
#editor.dark-mode .ce-header,
.dark-mode .codex-editor .ce-header {
  color: white !important;
}

/* Target header directly when parent has dark theme */
:host-context(.dark-theme) .ce-header,
:host-context([data-theme="dark"]) .ce-header {
  color: white !important;
}

/* Paragraph styles */
.ce-paragraph {
  color: var(--text-dark);
  font-family: var(--font-family);
  line-height: 1.6;
}

/* Code blocks and inline code */
.cdx-block code {
  background-color: var(--hover-blue-gray);
  border-radius: var(--border-radius-small);
  padding: 0.2em 0.4em;
  font-family: monospace;
  color: var(--text-dark);
}

/* List items */
.cdx-list {
  color: var(--text-dark);
  padding-left: 1rem;
}

.cdx-list__item {
  margin-bottom: 0.5rem;
}

/* Delimiter styling */
.ce-delimiter {
  color: var(--text-medium-gray);
  margin: 1rem 0;
}

/* Toolbar styling */
.ce-toolbar__plus,
.ce-toolbar__settings-btn {
  color: var(--text-medium-gray);
  background-color: var(--background-light-gray);
  border-radius: 50%;
  transition: var(--transition-default);
}

.ce-toolbar__plus:hover,
.ce-toolbar__settings-btn:hover {
  background-color: var(--hover-blue-gray);
  color: var(--primary-purple);
}

/* Popover styling */
.ce-popover {
  background-color: var(--background-white);
  border-radius: var(--border-radius-small);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--hover-blue-gray);
}

.ce-popover-item {
  color: var(--text-dark);
  transition: var(--transition-default);
}

.ce-popover-item:hover {
  background-color: var(--hover-blue-gray);
}

.ce-popover-item__icon {
  color: var(--text-medium-gray);
}

.ce-popover-item:hover .ce-popover-item__icon {
  color: var(--primary-purple);
}

/* Search field */
.cdx-search-field {
  background-color: var(--background-light-gray);
  border-radius: var(--border-radius-small);
}

.cdx-search-field__input {
  color: var(--text-dark);
  background-color: transparent;
}

/* Link Tool Styles */
.link-tool__content {
  background-color: var(--background-white);
  border-color: var(--hover-blue-gray);
  border-radius: var(--border-radius-small);
}

.link-tool__title {
  color: var(--text-dark);
  font-weight: var(--font-weight-medium);
}

.link-tool__description {
  color: var(--text-medium-gray);
}

.link-tool__anchor {
  color: var(--primary-purple);
}

/* Dark Mode Support */
:host-context([data-theme="dark"]) .codex-editor__redactor,
:host-context([data-theme="dark"]) .ce-header,
:host-context([data-theme="dark"]) .ce-paragraph,
:host-context([data-theme="dark"]) .cdx-list,
:host-context([data-theme="dark"]) .cdx-block code {
  color: white;
}

:host-context([data-theme="dark"]) .ce-toolbar__plus,
:host-context([data-theme="dark"]) .ce-toolbar__settings-btn {
  background-color: rgba(255, 255, 255, 0.1);
}

:host-context([data-theme="dark"]) .ce-popover {
  background-color: var(--background-dark);
  border-color: rgba(255, 255, 255, 0.1);
}

:host-context([data-theme="dark"]) .ce-popover-item {
  color: white;
}

:host-context([data-theme="dark"]) .ce-popover-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

:host-context([data-theme="dark"]) .cdx-block code {
  background-color: rgba(0, 0, 0, 0.3);
}

/* For browsers that don't support :host-context */
.dark-theme .codex-editor__redactor,
.dark-theme .ce-header,
.dark-theme .ce-paragraph,
.dark-theme .cdx-list,
.dark-theme .cdx-block code {
  color: white;
}

/* Editor placeholder text */
.ce-paragraph[data-placeholder]::before {
  color: var(--text-medium-gray);
  opacity: 0.7;
}

:host-context([data-theme="dark"]) .ce-paragraph[data-placeholder]::before {
  color: rgba(255, 255, 255, 0.5);
}
/* .cdk-overlay-pane{
  width: 200px !important;
}

.cdk-overlay-pane{
  width: 200px !important;
} */
.ant-select-dropdown{
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
  margin: 0 !important;
}
.ant-select-item-option-content {
  color: var(--text-dark) !important;
  font-size: 14px !important;
  font-weight: var(--font-weight-regular) !important;
  padding: 0 !important;
  margin: 0 !important;
}
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {

  background-color: var(--active-tab-bg) !important;
}
.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: var(--hover-blue-gray) !important;
}
.ant-select-item-group {
  color: var(--text-dark) !important;
}
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: var(--background-white) !important;
  color: var(--text-dark) !important;
  border-radius: var(--border-radius-small) !important;
  border: 1px solid var(--hover-blue-gray) !important;
}
.ant-select-single.ant-select-show-arrow .ant-select-selection-item, .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
  color: var(--text-dark) !important;
}

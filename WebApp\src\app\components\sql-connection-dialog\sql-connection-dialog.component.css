.sql-connection-dialog {
    font-family: var(--font-family);
}

.dialog-header {
    margin-bottom: 1rem;
}

.dialog-body {
    margin-bottom: 1.5rem;
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* Override for select dropdown */
select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.25em;
    padding-right: 2rem;
}
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { WorkspaceServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-add-or-edit-prompt-library',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSwitchModule,
    NzButtonModule,
    NzSelectModule
  ],
  templateUrl: './add-or-edit-prompt-library.component.html',
  styleUrls: ['./add-or-edit-prompt-library.component.css']
})
export class AddOrEditPromptLibraryComponent implements OnInit {
  promptData: any = {
    id: 0,
    prompt: '',
    shortMessage: '',
    workspaceName: '',
  };
  workspaces: any[] = [];
  workspaceName: string | undefined = '';
  constructor(
    private modalRef: NzModalRef,
    private workspaceService: WorkspaceServiceProxy,
    public authService: AuthService,
    private router: Router
  ) { }

  ngOnInit() {
    let router = this.router.url.split('/');
    if (router[1] === 'workspaces') {
      this.workspaceName = router[2];
      this.workspaceName = decodeURIComponent(this.workspaceName);
    } else {
      this.workspaceName = undefined;
    }

    const data = this.modalRef.getConfig().nzData;
    if (data && data.promptData) {
      this.promptData = { ...data.promptData };
    }

    // If we're in a workspace context, set the workspace name for the prompt
    if (this.workspaceName) {
      this.promptData.workspaceName = this.workspaceName;
    }

    this.loadWorkspaces();
  }

  loadWorkspaces() {
    if (this.authService.isAdmin()) {
      this.workspaceService.getAll().subscribe((res: any) => {
        this.workspaces = res;
      });
    } else {
      this.workspaceService.getWorkspacesByUserEmail().subscribe((res: any) => {
        this.workspaces = res;
      });
    }
  }

  onSubmit() {
    if (this.promptData.prompt) {
      // Ensure workspace name is set if we're in a workspace context
      if (this.workspaceName && !this.promptData.workspaceName) {
        this.promptData.workspaceName = this.workspaceName;
      }
      this.modalRef.close(this.promptData);
    }
  }

  onCancel() {
    this.modalRef.close();
  }
}

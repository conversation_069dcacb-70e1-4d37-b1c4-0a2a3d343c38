<div class="p-0 mx-auto w-auto">
  <div class="overflow-x-auto w-auto">
    <table class="w-full  max-h-[50vh] overflow-y-scroll h-[50vh] text-[var(--text-dark)]">
      <thead>
        <tr class="bg-[var(--background-light-gray)]">
          <th class="p-2 text-left">Name</th>
          <th class="p-2 text-left">Email</th>
          <th class="p-2 text-left">Roles</th>
          <th class="p-2 text-left">Skills</th>
          <th class="p-2 text-left" *ngIf="isAdmin">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let user of users; let i = index"
          class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] transition-[var(--transition-default)]">
          <td class="p-2">{{ user.name }}</td>
          <td class="p-2">{{ user.email }}</td>
          <td class="p-2">{{ user.roles.join(', ') }}</td>
          <td class="p-2">{{ user.skills }}</td>
          <td class="p-2"  *ngIf="isAdmin">
            <button (click)="removeUser(user)"
              class="bg-red-500 text-[var(--background-white)] p-2 px-3 rounded-[var(--border-radius-small)] hover:bg-red-600 transition-[var(--transition-default)] outline-none border-none cursor-pointer">
              <i class="ri-delete-bin-line text-lg"></i>
            </button>
          </td>
        </tr>
        <tr *ngIf="users.length === 0">
          <td colspan="5" class="p-2 text-center text-[var(--text-medium-gray)]">
            No users available.
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="mt-4 flex justify-between items-center px-4 pb-4">
    <button (click)="closeDialog()"
      class="p-2 px-4 bg-gray-500 text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-gray-600 transition-[var(--transition-default)] outline-none border-none cursor-pointer">
      Close
    </button>
    <button (click)="addUser()"  *ngIf="isAdmin"
      class="cursor-pointer bottom-60 right-[50%] p-2 px-3 bg-[var(--primary-purple)] rounded-full text-[var(--background-white)] shadow-md hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] outline-none border-none">
      <i class="ri-user-add-line mx-2"></i> Add More User
    </button>
  </div>
</div>

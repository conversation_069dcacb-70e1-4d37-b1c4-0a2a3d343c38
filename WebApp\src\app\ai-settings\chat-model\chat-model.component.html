<div class="flex flex-col p-6 min-h-screen text-white">
  <!-- Header -->
  <div class="flex justify-between items-center mb-8">
    <h1 class="text-2xl font-bold text-white">Chat Models</h1>
    <button (click)="addNewModel()"
      class="px-4 py-1 bg-emerald-600 hover:bg-emerald-500 rounded-md flex items-center gap-2 text-lg border-none outline-none cursor-pointer transition-all duration-300">
      Add Model
    </button>
  </div>

  <!-- Model List -->
  <div class="grid gap-2 mb-8" *ngIf="models.length > 0">
    <div *ngFor="let model of models"
      class="bg-[rgb(13,13,13)] rounded-sm p-4 border border-zinc-800 hover:border-emerald-600 transition-all hover:shadow-lg cursor-pointer">
      <div class="flex justify-between items-center gap-1">
        <div class="flex flex-col gap-1">
          <h3 class="text-xl font-medium text-white">{{ model.modelId }}</h3>
          <p class="text-zinc-400">Provider: {{ model.provider }}</p>
          <p class="text-zinc-400">API Key: {{ hideApiKey(model.apIkey) }}</p>
          <p *ngIf="model.endpoint" class="text-zinc-400">Endpoint: {{ model.endpoint }}</p>
        </div>
        <div class="flex gap-2">
          <button (click)="editModel(model)"
            class="p-2 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors cursor-pointer outline-none border-none">
            <i class="ri-edit-2-line text-emerald-400 hover:text-emerald-300 transition-colors text-lg"></i>
          </button>

          <button (click)="deleteModel(model.modelId)"
            class="p-2 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors cursor-pointer outline-none border-none">
            <i class="ri-delete-bin-6-line text-red-500 hover:text-red-400 transition-colors text-lg"></i>
          </button>

        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="models.length === 0 && !showForm"
    class="flex flex-col items-center justify-center p-10 bg-zinc-900 rounded-lg border border-dashed border-zinc-700">
    <span class="material-icons text-6xl text-zinc-600 mb-4">psychology</span>
    <p class="text-zinc-400 text-center mb-4">No chat models have been added yet.</p>
    <button (click)="addNewModel()"
      class="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 rounded-md flex items-center gap-2 transition-colors outline-none border-none cursor-pointer"> 
      Add Your First Model
    </button>
  </div>

  <!-- Form Overlay -->
  <div *ngIf="showForm" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
    <div class="bg-zinc-900 rounded-lg p-6 w-full max-w-md border border-zinc-800 shadow-lg">
      <h2 class="text-xl font-bold text-emerald-400 mb-6">{{ isEditing ? 'Edit' : 'Add' }} Chat Model</h2>

      <div class="space-y-4">
        <div>
          <label for="modelId" class="block text-sm font-medium text-zinc-400 mb-1">Model ID</label>
          <input type="text" id="modelId" [(ngModel)]="currentModel.modelId"
            class="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 text-white"
            placeholder="e.g. gpt-4o-mini">
        </div>

        <div>
          <label for="provider" class="block text-sm font-medium text-zinc-400 mb-1">Provider</label>
          <input type="text" id="provider" [(ngModel)]="currentModel.provider"
            class="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 text-white"
            placeholder="e.g. OpenAI">
        </div>

        <div>
          <label for="apiKey" class="block text-sm font-medium text-zinc-400 mb-1">API Key</label>
          <input type="password" id="apiKey" [(ngModel)]="currentModel.apIkey"
            class="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 text-white"
            placeholder="sk-...">
        </div>

        <div>
          <label for="endpoint" class="block text-sm font-medium text-zinc-400 mb-1">Endpoint URL (optional)</label>
          <input type="text" id="endpoint" [(ngModel)]="currentModel.endpoint"
            class="w-full p-2 bg-zinc-800 border border-zinc-700 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 text-white"
            placeholder="https://...">
        </div>
      </div>

      <div class="flex justify-end gap-3 mt-6">
        <button (click)="resetForm()"
          class="px-4 py-2 bg-zinc-700 hover:bg-zinc-700 rounded-md transition-colors cursor-pointer outline-none border-none">
          Cancel
        </button>
        <button (click)="saveModel()"
          class="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors cursor-pointer outline-none border-none">
          Save
        </button>
      </div>
    </div>
  </div>
</div>

<div class="docs-layout" [class.toggled]="isToogled">
  <!-- Sidebar/Documents list -->
  <div class="docs-list">
    <!-- ... existing sidebar content ... -->
  </div>

  <!-- Editor Container -->
  <div class="editor-wrapper" *ngIf="!isToogled">
    <div #editor id="editor" class="editor-container">
      <div class="editor-placeholder" *ngIf="!hasContent">
      </div>
    </div>
  </div>

  <!-- Chat Container -->
  <div class="chat-container" [class.expanded]="isToogled">
    <!-- ... existing chat content ... -->
  </div>
</div>

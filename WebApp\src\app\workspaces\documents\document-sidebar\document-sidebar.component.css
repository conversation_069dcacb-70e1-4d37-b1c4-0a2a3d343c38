/* Width indicator during drag */
.width-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Splitter hover area */
.splitter-hover-area {
  position: absolute;
  top: 0;
  right: -10px;
  width: 20px;
  height: 100%;
  background-color: transparent;
  z-index: 39;
  cursor: col-resize;
}

/* Custom scrollbar for better visibility in dark mode */
:host-context(.dark-theme) ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:host-context(.dark-theme) ::-webkit-scrollbar-track {
  background: #1f2937;
}

:host-context(.dark-theme) ::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

:host-context(.dark-theme) ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Custom scrollbar for light theme */
:host-context(:not(.dark-theme)) ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Ensure proper height and overflow handling */
:host {
  display: block;
  height: 100%;
  overflow: hidden;
}

/* Smooth transitions for theme changes */
.transition-all {
  transition: all 0.2s ease;
}

/* Button hover effects */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Document item hover effects */
.cursor-pointer:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Theme-specific enhancements */
:host-context(.dark-theme) .cursor-pointer:hover {
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  .text-sm {
    font-size: 0.75rem;
  }
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

.cursor-pointer:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Empty state styling */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-medium-gray);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

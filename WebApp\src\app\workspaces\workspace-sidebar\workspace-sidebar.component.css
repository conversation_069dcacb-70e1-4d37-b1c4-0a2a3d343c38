.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-right: 1px solid #e8e8e8;
}

.tab-navigation {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.tab {
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 8px;
}

.tab.active {
  background-color: #1890ff;
  color: white;
}

.actions {
  padding: 10px;
  display: flex;
  gap: 10px;
}

.search-box {
  flex: 1;
}

.search-box input {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.chat-group {
  margin-bottom: 16px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: #f5f5f5;
  cursor: pointer;
}

.chat-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.chat-item:hover {
  background-color: #f5f5f5;
}

.chat-item.active {
  background-color: #e6f7ff;
}

.chat-info {
  flex: 1;
}

.chat-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.chat-message {
  color: #666;
  font-size: 0.9em;
}

.chat-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.load-more {
  text-align: center;
  padding: 16px;
}

import {
  BrowserDomAdapter,
  BrowserGetTestability,
  BrowserModule,
  By,
  DomEventsPlugin,
  DomRendererFactory2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mp<PERSON>,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  INTERNAL_BROWSER_PLATFORM_PROVIDERS,
  KeyEventsPlugin,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  Title,
  TransferState,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  initDomAdapter,
  makeStateKey,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withHttpTransferCacheOptions,
  withNoHttpTransferCache
} from "./chunk-V5C6TIEW.js";
import "./chunk-F5QSOM2P.js";
import {
  getDOM
} from "./chunk-M644BQ5H.js";
import "./chunk-QI6NZCQM.js";
import "./chunk-FBTKCNEF.js";
import "./chunk-CRSXJIOC.js";
import "./chunk-AQGUTHVG.js";
import "./chunk-EIB7IA3J.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  TransferState,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  makeStateKey,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withHttpTransferCacheOptions,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM,
  initDomAdapter as ɵinitDomAdapter
};
//# sourceMappingURL=@angular_platform-browser.js.map

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmbeddingConfigurationProvider, EmbeddingModelDto, ModelDetailsServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ChangeActiveModelComponent } from '../embedding/change-active-model/change-active-model.component';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { RemoveProviderPrefixPipe } from "../../../shared/pipes/remove-provider-prefix.pipe";
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

@Component({
  selector: 'app-models',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule, NzModalModule, RemoveProviderPrefixPipe, SpinnerComponent],
  templateUrl: './models.component.html',
  // Assuming you'll create a separate HTML file for models
})
export class ModelsComponent {
  models: any = [];
  filterdModels: any = [];
  searchModelsQuery = '';
  selectedProvider = '';
  isLoading = false; // Loading state for spinner
  uniqueProviders: any[] = [];
  embeddingModels: EmbeddingModelDto[] = [];
  activeEmbeddingModel: EmbeddingConfigurationProvider = new EmbeddingConfigurationProvider();


  constructor(
    private modelDetails: ModelDetailsServiceProxy,
    private nzMessageService: NzMessageService,
    public nzModalService: NzModalService,
  ) { }

  ngOnInit() {
    this.loadModels();
  }

  loadModels() {
    this.isLoading = true; // Show spinner
    this.modelDetails.getAll().subscribe({
      next: (res: any) => {
        if (res) {
          this.models = res;
          this.filterdModels = res;
          this.uniqueProviders = [...new Set(res.map((model: any) => model.modelProvider))];
        }
        this.modelDetails.current().subscribe({
          next: (res: any) => {
            if (res) {
              this.activeEmbeddingModel = res;
            }
            this.isLoading = false; // Hide spinner after all data is loaded
          },
          error: (error: any) => {
            console.error('Error loading current embedding model:', error);
            this.isLoading = false; // Hide spinner on error
          }
        });
      },
      error: (error: any) => {
        console.error('Error loading models:', error);
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  updateModelIsActive(model: any) {
    this.modelDetails
      .updateIsActive(model.modelName, !model.isActive)
      .subscribe((res: any) => {
        if (!res.isError) {
          this.nzMessageService.success(res.message);
        }
      });
  }

  filterModels() {
    this.filterdModels = this.models.filter((model: any) =>
      model.modelName.toLowerCase().includes(this.searchModelsQuery) &&
      (this.selectedProvider ? model.modelProvider === this.selectedProvider : true)
    );
  }
  openChangeModelDialog() {
    const modalRef = this.nzModalService.create({
      nzTitle: '',
      nzContent: ChangeActiveModelComponent,
      nzFooter: null,
      nzWidth: '400px',
      nzClassName: 'custom-modal-style'
    });

    const instance = modalRef.componentInstance;
    if (instance) {
      instance.modelChanged.subscribe((result: any) => {
        // Handle the model change event
        this.nzMessageService.success('Embedding model changed successfully');
        this.nzMessageService.info('Please Restart the Application to see the changes');
        setInterval(() => {
          this.nzMessageService.info('Please Restart the Application to see the changes of embedding');
        }, 5000);
      });
    }
  }
}

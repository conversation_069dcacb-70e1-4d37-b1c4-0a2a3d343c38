import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TogglingService } from '../../../toggling.service';
import { ChatListService } from '../../../services/chat-list.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-daily-insights-sidebar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './daily-insights-sidebar.component.html',
  styleUrls: ['./daily-insights-sidebar.component.css']
})
export class DailyInsightsSidebarComponent {
  // Inject services
  router = inject(Router);
  togglingService = inject(TogglingService);
  chatListService = inject(ChatListService);
  nzMessageService = inject(NzMessageService);

  // Input properties
  @Input() workspaceName: string = '';

  /**
   * Navigate to chat page
   */
  goToChat() {
    // Reset chatId to 0 for new chat
    this.chatListService.chatId = 0;

    // Navigate based on workspace context
    if (this.workspaceName) {
      this.router.navigate(['workspaces', this.workspaceName, 'chat']);
    } else {
      this.router.navigate(['/chat']);
    }

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Navigate to workspaces page
   */
  goToWorkspace() {
    // Reset chatId
    this.chatListService.chatId = 0;

    // Navigate to workspaces
    this.router.navigate(['/workspaces']);

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Navigate to prompt library page
   */
  goToPromptLibrary() {
    // Navigate to prompt library
    this.router.navigate(['/admin', 'prompt-library']);

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Refresh daily insights data
   */
  // refreshDailyInsights() {
  //   // Show loading indicator
  //   this.nzMessageService.loading('Refreshing daily insights...');

  //   // Show success message after a short delay (simulating refresh)
  //   // setTimeout(() => {
  //   //   this.nzMessageService.success('Daily insights refreshed');
  //   // }, 1000);
  // }
}

<div class="sql-connection-dialog">
  <div class="dialog-header">
    <div class="flex items-center gap-3">
      <div
        class="w-10 h-10 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
        <i class="ri-database-2-line text-[var(--primary-purple)] text-xl"></i>
      </div>
      <h3 class="text-lg font-medium text-[var(--text-dark)]">SQL Connection</h3>
    </div>
  </div>

  <div class="dialog-body mt-4">
    <div class="mb-4">
      <label class="font-medium text-[var(--text-dark)] block mb-2">Connection Type</label>
      <nz-radio-group [(ngModel)]="connectionType" (ngModelChange)="onConnectionTypeChange()">
        <label nz-radio nzValue="existing">Use Existing Connection</label>
        <label nz-radio nzValue="custom">Custom Connection</label>
      </nz-radio-group>
    </div>
    <div *ngIf="connectionType === 'existing'" class="mb-4">
      <label class="font-medium text-[var(--text-dark)] block mb-2">Select Connection</label>
      <div class="flex flex-col gap-2">
        <select [(ngModel)]="selectedConnection" (change)="onConnectionSelect()"
          class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md"
          [disabled]="isLoading">
          <option *ngIf="isLoading" value="">Loading connections...</option>
          <option *ngIf="!isLoading && savedConnections.length === 0" value="">No saved connections found</option>
          <option *ngFor="let conn of savedConnections" [value]="conn">{{ conn }}</option>
        </select>

        <button (click)="testConnection()"
          class="self-end px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all"
          [disabled]="isTesting || !connectionString">
          <i *ngIf="isTesting" class="ri-loader-4-line animate-spin mr-1"></i>
          Test Connection
        </button>
      </div>
    </div>
    <div *ngIf="connectionType === 'custom'" class="mb-4">
      <label for="connectionString" class="font-medium text-[var(--text-dark)] block mb-2">Connection String</label>
      <div class="flex flex-col gap-2">
        <input type="text" id="connectionString" [(ngModel)]="connectionString"
          placeholder="Enter your connection string"
          class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md">
        <p class="text-xs text-[var(--text-medium-gray)] mt-1">Format:
          Server=myServerAddress;Database=myDataBase;User Id=myUsername;Password=myPassword;</p>

        <button (click)="testConnection()"
          class="self-end px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all"
          [disabled]="isTesting || !connectionString">
          <i *ngIf="isTesting" class="ri-loader-4-line animate-spin mr-1"></i>
          Test Connection
        </button>
      </div>
    </div>

    <div class="mb-4">
      <label for="sqlQuery" class="font-medium text-[var(--text-dark)] block mb-2">SQL Query</label>
      <textarea id="sqlQuery" [(ngModel)]="sql" rows="5"
        class="w-full p-2 bg-[var(--background-white)] text-[var(--text-dark)] border border-[var(--hover-blue-gray)] rounded-md font-mono"
        placeholder="Your SQL query will appear here"></textarea>
    </div>
  </div>
  <div class="dialog-footer mt-6 pt-4 border-t border-[var(--hover-blue-gray)] flex justify-end gap-3">
    <button (click)="onCancel()"
      class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-opacity-80 transition-all"
      [disabled]="isTesting">
      Cancel
    </button>
    <button (click)="onSubmit()"
      class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-opacity-90 transition-all"
      [disabled]="isTesting || !connectionString || !sql">
      <i *ngIf="isTesting" class="ri-loader-4-line animate-spin mr-1"></i>
      <i *ngIf="!isTesting" class="ri-send-plane-line mr-1"></i>
      Execute
    </button>
  </div>
</div>
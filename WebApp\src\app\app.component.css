/* Route transition animations */
.route-container {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.route-transition {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    opacity: 1;
    transform: translateY(0);
    will-change: opacity, transform;
    width: 100%;
}

.route-transition.fade-out {
    opacity: 0;
    transform: translateY(10px);
}

/* Special handling for chat routes */
.route-container[class*="chat"] .route-transition {
    transition-duration: 0.2s;
}

/* Optimize performance */
.route-container,
.route-transition {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

/* Full width layout */
.full-width-container {
    width: 100%;
    max-width: 100%;
}

/* Adjust content area to account for header and sidebar */
.content-area {
    padding-top: 52px;
    min-height: calc(100vh - 52px);
}

/* Styles for the split layout */
as-split {
  --gutter-color: transparent;
  --gutter-hover-color: var(--primary-purple);
}

/* Custom styling for the gutter - single clean line */
.as-split-gutter {
  background-color: transparent !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 40;
  width: 1px !important; /* Single thin line by default */
  opacity: 0.7;
  cursor: pointer !important;

  /* Single clean line with the green color from the image */
  background: rgba(0, 195, 154, 0.6) !important;

  /* Subtle glow effect */
  box-shadow: 0 0 6px rgba(0, 195, 154, 0.15);
}

/* Change cursor on mousedown to indicate resize mode */
.as-split-gutter:active {
  cursor: e-resize !important;
  background: rgba(0, 195, 154, 0.9) !important; /* Brighter green when active */
  box-shadow: 0 0 10px rgba(0, 195, 154, 0.3);
  width: 2px !important; /* Slightly wider when active */
}

/* Show a more visible and attractive gutter on hover */
.as-split-gutter:hover {
  opacity: 1;
  width: 2px !important; /* Slightly wider but still clean */
  cursor: pointer !important;
  background: rgba(0, 195, 154, 0.8) !important; /* Brighter green on hover */
  box-shadow: 0 0 8px rgba(0, 195, 154, 0.25);
}

/* Add interactive handle indicator - only visible on hover */
.as-split-gutter::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 30px;
  background-color: rgba(0, 195, 154, 0.2); /* Subtle green handle */
  border-radius: 2px;
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 41;
  pointer-events: none; /* Prevent the handle from interfering with clicks */
}

/* Show handle on hover */
.as-split-gutter:hover::before {
  opacity: 1;
  background-color: rgba(0, 195, 154, 0.7); /* More visible green handle on hover */
  box-shadow: 0 0 6px rgba(0, 195, 154, 0.3);
}

/* Enhanced handle during active dragging */
.as-split-gutter:active::before {
  opacity: 1;
  height: 40px;
  background-color: rgba(0, 195, 154, 0.9);
  box-shadow: 0 0 8px rgba(0, 195, 154, 0.4);
}

/* Add tooltip to indicate click functionality */
.as-split-gutter .tooltip {
  visibility: hidden;
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background-color: rgba(20, 20, 25, 0.85);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
  opacity: 0;
  z-index: 42;
  pointer-events: none; /* Prevent the tooltip from interfering with clicks */
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-left: 2px solid rgba(0, 195, 154, 0.7);
}

/* Show tooltip on hover */
.as-split-gutter:hover .tooltip {
  visibility: visible;
  opacity: 1;
  right: 15px;
  animation: fadeIn 0.3s ease;
}

/* Create a larger hover/click area for the gutter */
.as-split-gutter-hitarea {
  position: absolute;
  top: 0;
  left: -10px;
  width: 20px;
  height: 100%;
  background-color: transparent;
  z-index: 39;
  cursor: pointer;
}

/* Ensure the split areas take full height */
as-split-area {
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Sidebar state indicators */
.sidebar-collapsed-area {
  border-right: 2px solid transparent;
}

.sidebar-narrow-area {
  border-right: 2px solid var(--hover-blue-gray);
}

.sidebar-expanded-area {
  border-right: 2px solid var(--primary-purple);
}

/* Width indicator during drag */
.width-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(20, 20, 25, 0.85);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  z-index: 100;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
  animation: fadeIn 0.3s ease;
  opacity: 0.9;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 2px solid rgba(0, 195, 154, 0.7);

  /* Add a subtle glow effect */
  text-shadow: 0 0 5px rgba(0, 195, 154, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Splitter hover area for mouse interaction */
.splitter-hover-area {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 100%;
  z-index: 45;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  touch-action: none;
}

/* Add a single line indicator */
.splitter-hover-area::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-color: rgba(0, 195, 154, 0.6);
  opacity: 0.7;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Show a subtle indicator on hover */
.splitter-hover-area:hover::after {
  width: 2px;
  background-color: rgba(0, 195, 154, 0.8);
  opacity: 1;
  box-shadow: 0 0 6px rgba(0, 195, 154, 0.2);
}

/* Change cursor on mousedown to indicate resize mode */
.splitter-hover-area:active::after,
.splitter-dragging .splitter-hover-area::after {
  width: 2px;
  background-color: rgba(0, 195, 154, 0.9);
  opacity: 1;
  box-shadow: 0 0 8px rgba(0, 195, 154, 0.3);
}

/* Change cursor on mousedown to indicate resize mode */
.splitter-hover-area:active,
.splitter-dragging .splitter-hover-area {
  cursor: e-resize !important; /* Show resize cursor when actively dragging */
}

/* Global dragging state */
body.splitter-dragging {
  cursor: e-resize !important;
  user-select: none;
}

/* Add handle indicator for the splitter hover area */
.splitter-hover-area::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 2px;
  transform: translateY(-50%);
  width: 4px;
  height: 30px;
  background-color: rgba(0, 195, 154, 0.2);
  border-radius: 2px;
  opacity: 0;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

/* Show handle on hover */
.splitter-hover-area:hover::before {
  opacity: 1;
  background-color: rgba(0, 195, 154, 0.7);
  box-shadow: 0 0 6px rgba(0, 195, 154, 0.3);
}

/* Enhanced handle during active dragging */
.splitter-hover-area:active::before,
.splitter-dragging .splitter-hover-area::before {
  opacity: 1;
  height: 40px;
  background-color: rgba(0, 195, 154, 0.9);
  box-shadow: 0 0 8px rgba(0, 195, 154, 0.4);
}

/* Add a tooltip-like hint on hover */
.splitter-hover-area .tooltip {
  visibility: hidden;
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background-color: rgba(20, 20, 25, 0.85);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
  opacity: 0;
  z-index: 46;
  pointer-events: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border-left: 2px solid rgba(0, 195, 154, 0.7);
}

/* Show tooltip on hover */
.splitter-hover-area:hover .tooltip {
  visibility: visible;
  opacity: 1;
  right: 15px;
  animation: fadeIn 0.3s ease;
}

/* Double-click behavior for the splitter */
.as-split-gutter {
  transition: all 0.3s ease;
}

/* State indicators for the sidebar */
.sidebar-collapsed-area::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 3px;
  height: 30px;
  background-color: var(--hover-blue-gray);
  opacity: 0;
  border-radius: 2px;
  transform: translateY(-50%);
  transition: opacity 0.3s ease;
}

.sidebar-narrow-area::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 3px;
  height: 30px;
  background-color: var(--hover-blue-gray);
  opacity: 0;
  border-radius: 2px;
  transform: translateY(-50%);
  transition: opacity 0.3s ease;
}

.sidebar-expanded-area::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 3px;
  height: 30px;
  background-color: var(--hover-blue-gray);
  opacity: 0;
  border-radius: 2px;
  transform: translateY(-50%);
  transition: opacity 0.3s ease;
}

/* Show state indicators on hover */
.sidebar-collapsed-area:hover::after,
.sidebar-narrow-area:hover::after,
.sidebar-expanded-area:hover::after {
  opacity: 0.5;
}

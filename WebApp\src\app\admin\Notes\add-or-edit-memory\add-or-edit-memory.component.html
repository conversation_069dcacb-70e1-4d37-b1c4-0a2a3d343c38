<!-- Main Container with theme-aware background -->
<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Header Section - Teams-style with consistent spacing -->
  <div class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
    <!-- Left side with title and mode indicator -->
    <div class="flex items-center gap-2">
      <i class="ri-brain-line text-[var(--primary-purple)] text-xl"></i>
      <h1 class="text-lg font-medium text-[var(--text-dark)]">
        {{ isEditMode ? 'Edit Memory' : 'Create New Memory' }}
      </h1>
      <div class="inline-flex items-center justify-center px-2 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
        {{ isEditMode ? 'Edit Mode' : 'Create Mode' }}
      </div>
    </div>

    <!-- Right side with action buttons -->
    <div class="flex items-center gap-2">
      <button
        (click)="router.navigate(['/settings/memory'])"
        class="h-8 px-3 py-1 border border-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] text-sm rounded-md bg-[var(--hover-blue-gray)] hover:opacity-65 focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50 transition-all duration-200 flex items-center gap-1"
      >
        <i class="ri-arrow-left-line text-sm"></i>
        <span>Back</span>
      </button>
      <button
        (click)="saveContent()"
        [disabled]="isSaving || !hasContent"
        class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
      >
        <i [class]="isSaving ? 'ri-loader-4-line animate-spin text-sm' : 'ri-save-line text-sm'"></i>
        <span>{{ isSaving ? 'Saving...' : 'Save' }}</span>
      </button>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="flex-1 overflow-hidden flex flex-col p-4">
    <!-- Editor Container with proper theme styling -->
    <div class="flex flex-col h-full bg-[var(--background-white)] rounded-lg shadow-sm border border-[var(--hover-blue-gray)] border-opacity-30 overflow-hidden">
      <!-- Editor Content Area -->
      <div
        id="editor"
        class="flex-grow overflow-auto p-6 text-[var(--text-dark)] bg-[var(--background-white)] scrollbar-thin scrollbar-thumb-[var(--hover-blue-gray)] scrollbar-track-transparent"
        style="min-height: 500px;"
      ></div>

      <!-- Footer with secondary actions -->
      <div class="flex justify-between items-center p-4 border-t border-[var(--hover-blue-gray)] border-opacity-30 bg-[var(--background-white)]">
        <div class="flex items-center gap-2">
          <button
            (click)="clearContent()"
            class="flex items-center gap-2 px-3 py-1.5 border border-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] text-sm rounded-md bg-[var(--hover-blue-gray)] hover:opacity-60 focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:ring-opacity-50 transition-all duration-200"
          >
            <i class="ri-eraser-line text-sm"></i>
            <span>Clear</span>
          </button>
        </div>

        <!-- Content status indicator -->
        <div class="flex items-center gap-2 text-xs text-[var(--text-medium-gray)]">
          <div class="flex items-center gap-1">
            <div class="w-2 h-2 rounded-full" [class]="hasContent ? 'bg-green-500' : 'bg-[var(--text-medium-gray)]'"></div>
            <span>{{ hasContent ? 'Content ready' : 'No content' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

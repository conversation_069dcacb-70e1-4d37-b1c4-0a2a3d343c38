<div class="min-h-[calc(100vh-74px)] bg-[var(--background-light-gray)] flex items-center justify-center p-4">
  <div class="w-full max-w-lg text-center space-y-6">
    <!-- 404 Text -->
    <div class="relative">
      <h1 class="text-9xl font-bold text-[var(--primary-purple)] opacity-10">404</h1>
      <div class="absolute inset-0 flex items-center justify-center">
        <span class="text-5xl font-[var(--font-weight-bold)] text-[var(--primary-purple)]">Oops!</span>
      </div>
    </div>

    <!-- Error Illustration -->
    <div class="flex justify-center">
      <div
        class="w-48 h-48 rounded-full bg-[var(--background-white)] shadow-[var(--box-shadow)] flex items-center justify-center">
        <i class="ri-error-warning-line text-8xl text-[var(--primary-purple)]"></i>
      </div>
    </div>

    <!-- Error Message -->
    <div class="space-y-4">
      <h2 class="text-2xl font-[var(--font-weight-bold)] text-[var(--text-dark)]">Page Not Found</h2>
      <p class="text-[var(--text-medium-gray)] text-lg font-[var(--font-weight-regular)] leading-relaxed">
        The page you're looking for doesn't exist or you don't have permission to access it.
      </p>
    </div>

    <!-- Action Button -->
    <div class="mt-8">
      <a routerLink="/"
        class="px-6 py-3 bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] font-[var(--font-family)] hover:text-black inline-block">
        Back to Home
      </a>
    </div>
  </div>
</div>

<div class="bg-background-white p-medium rounded-large shadow-default w-full">
  <h2 class="text-header font-bold text-text-dark mb-medium">

    {{data.isUpdating ? 'Update' : 'Add'}}

    Project Memory</h2>
  <div>
    <div class="mb-medium">
      <label for="projectCategory" class="text-body text-text-dark block mb-2">Category</label>
      <input id="projectCategory" name="projectCategory" [(ngModel)]="projectMemory.projectCategory"
        class="w-full p-small rounded-small border  !text-black border-hover-blue-gray text-body text-text-dark focus:outline-none focus:ring-2 focus:ring-primary-purple"
        required />
    </div>
    <div class="mb-medium">
      <label for="description" class="text-body text-text-dark block mb-2">Description</label>
      <textarea id="description" name="description" [(ngModel)]="projectMemory.projectDescription"
        class="w-full p-small rounded-small border border-hover-blue-gray text-body text-black focus:outline-none focus:ring-2 focus:ring-primary-purple h-24"
        required></textarea>
    </div>

    <div class="flex justify-end space-x-4
            *:outline-none *:border-none my-2">
      <button type="button" (click)="closeDialog()"
        class="bg-hover-blue-gray text-text-dark py-2 px-4 rounded-small cursor-pointer hover:bg-gray-300
        hover:text-black transition-default">
        Cancel
      </button>
      @if (!data.isUpdating) {

      <button type="submit" (click)="addProjectMemory()"
        class="bg-primary-purple text-black hover:text-black cursor-pointer text-background-white py-2 px-4 rounded-small hover:bg-secondary-purple transition-default">
        Add
      </button>
      }@else {
      <button type="submit" (click)="addProjectMemory()"
        class="bg-primary-purple hover:text-black cursor-pointer text-background-white py-2 px-4 rounded-small hover:bg-secondary-purple transition-default">
        Update
      </button>
      }
    </div>
  </div>
</div>

import { Component, model } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ApiCredentialsServiceProxy,
  ModelDetailsServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { FormsModule } from '@angular/forms';
import { AiAgentComponent } from '../../ai-settings/ai-agent/ai-agent.component';
import { NzMessageService } from 'ng-zorro-antd/message';
import { concatMap } from 'rxjs/operators';
import { UsersManagementComponent } from '../user-management/user-management.component';
import { AgentsComponent } from '../../workspaces/agents/agents.component';
import { PromptsLibraryComponent } from '../prompts-library/prompts-library.component';
import { ProjectMemoryComponent } from "../../workspaces/project-memory/project-memory.component";


@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule, ServiceProxyModule, FormsModule, AgentsComponent, UsersManagementComponent, PromptsLibraryComponent, ProjectMemoryComponent],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.css',

})
export class SettingsComponent {
  // Add any necessary component logic here
  activeTab: 'user-management' | 'connections' | 'models' | 'agents' | 'embedding' | 'prompts-library' | 'files' = 'user-management';
  models: any = [];
  apiLists: any = [];
  apiData: any = {
    tokenUrl: '',
    apiKey: '',
  };
  showForm = false;
  isCredentialsValid = false;
  filterdModels: any = [];
  searchModelsQuery = '';
  selectedProvider = '';
  uniqueProviders: any[] = [];

  constructor(
    private modelDetails: ModelDetailsServiceProxy,
    private apiCredentials: ApiCredentialsServiceProxy,
    private nzMessageService: NzMessageService
  ) { }

  ngOnInit() {
    this.loadModels();
    this.loadApiCredentials();
  }
  ///////////////Models Detais code//////////////////////
  loadModels() {
    this.modelDetails.getAll().subscribe((res: any) => {
      if (res) {
        this.models = res;
        this.filterdModels = res;
        this.uniqueProviders = [...new Set(res.map((model: any) => model.modelProvider))];
        // console.log(this.models);
      }
    });
  }
  updateModelIsActive(model: any) {
    this.modelDetails
      .updateIsActive(model.modelName, !model.isActive)
      .subscribe((res: any) => {
        // console.log(res);
        if (!res.isError) {
          this.nzMessageService.success(res.message);
        }
      });
  }
  filterModels() {
    this.filterdModels = this.models.filter((model: any) =>
      model.modelName.toLowerCase().includes(this.searchModelsQuery) &&
      (this.selectedProvider ? model.modelProvider === this.selectedProvider : true)
    );
  }

  // ***********************************************************************************************

  ////////////////Api Credentials code//////////////////////
  loadApiCredentials() {
    this.apiCredentials.getAll().subscribe((res: any) => {
      if (res) {
        this.apiLists = res;
      }
    });
  }
  onAddApi() {
    this.showForm = true;
  }

  async validateCredentials() {
    if (this.apiData.tokenUrl && this.apiData.apiKey) {
      this.showValidationMessage();
      try {
        let res: any = await this.apiCredentials
          .validate(this.apiData)
          .toPromise();
        if (!res.isError) {
          this.isCredentialsValid = true;
        }
      } catch (error) {
        this.isCredentialsValid = false;
      }
    }
  }
  showValidationMessage(): void {
    const loadingMessage = this.nzMessageService.loading(
      'Checking the credentials'
    );

    loadingMessage
      .onClose!.pipe(
        concatMap(() =>
          this.isCredentialsValid
            ? this.nzMessageService.success('Given are credentials ').onClose!
            : this.nzMessageService.error('Given credentials are invalid')
              .onClose!
        )
      )
      .subscribe(() => { });
  }

  saveApi() {
    this.apiCredentials.create(this.apiData).subscribe((res: any) => {
      if (res) {
        this.apiLists.push(this.apiData);
        this.nzMessageService.success(res.message);
        // console.log(res);
        // console.log(this.apiLists);

        this.resetForm();
      }
    });
  }
  editApi(api: any) {
    this.apiData = api;
    this.showForm = true;
  }
  onEditApi() {
    this.apiCredentials.getById(this.apiData).subscribe((res: any) => {
      if (res) {
        this.resetForm();
      }
    });
  }
  async deleteApi(api: any) {
    try {
      let res: any = await this.apiCredentials.delete(api.id).toPromise();

      if (!res.isError) {
        this.apiLists = this.apiLists.filter((item: any) => item.id != api.id);
        this.nzMessageService.success(res.message);
      }
    } catch (error: any) {
      this.nzMessageService.error(
        'Cannot delete credentials; remove linked models in Agent first'
      );
    }
  }
  /////////////General methods//////////////////////

  resetForm() {
    this.apiData = {
      tokenUrl: '',
      apiKey: '',
    };
    this.isCredentialsValid = false;
    this.showForm = false;
  }

  setActiveTab(tab: any) {
    this.activeTab = tab;
  }
}

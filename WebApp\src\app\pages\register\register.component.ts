import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { RegisterDto, UserAccountServiceProxy } from '../../../shared/service-proxies/service-proxies';
import { NzMessageService } from 'ng-zorro-antd/message';
interface User {
  email: string;
  password: string;
  name: string;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [FormsModule, RouterLink],
  templateUrl: './register.component.html',
})
export class RegisterComponent {
  user: RegisterDto = new RegisterDto();
  constructor(private router: Router, private _userAccount: UserAccountServiceProxy,
    private message: NzMessageService
  ) { }

  register() {
    this._userAccount.register(this.user).subscribe((res) => {
      this.message.success('Registration successful!');
      this.router.navigate(['/login']);
    })

  }
}

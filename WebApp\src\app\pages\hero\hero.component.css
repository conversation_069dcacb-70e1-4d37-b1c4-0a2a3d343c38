/* Add any custom styles here */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Add to existing styles */
:host ::ng-deep {
  .prose {
    color: #fff;
  }

  .prose h3 {
    color: #fff;
    font-size: 1.1rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .prose ul {
    list-style-type: none;
    padding-left: 1rem;
  }

  .prose li {
    position: relative;
    padding-left: 1rem;
  }

  .prose li::before {
    content: "•";
    position: absolute;
    left: -0.5rem;
    color: #6B7280;
  }

  .prose strong {
    color: #E5E7EB;
    font-weight: 600;
  }

  .prose p {
    margin-bottom: 0.75rem;
  }

  .prose pre {
    background-color: #1F1F1F !important;
    border: 1px solid #2A2A2A;
  }

  .prose code {
    background-color: #2A2A2A;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 0.875em;
  }

  .prose pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    color: inherit;
  }

  .prose a {
    color: #60A5FA;
    text-decoration: none;
  }

  .prose a:hover {
    text-decoration: underline;
  }

  .prose blockquote {
    border-left-color: #2A2A2A;
    color: #9CA3AF;
  }

  .prose table {
    width: 100%;
    border-collapse: collapse;
  }

  .prose th,
  .prose td {
    border-color: #2A2A2A;
    padding: 0.5rem;
  }

  .prose th {
    background-color: #1F1F1F;
  }
}

.ant-drawer-body {
  padding: 0;
  padding-left: 5px !important;
}

::ng-deep code.language-json {
  text-wrap: auto;
}


/* SQL View Styles */
.sql-view-container {
  width: 100%;
}

.sql-code-container {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.sql-code-container pre {
  margin: 0;
  padding: 0;
}

.sql-code-container code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

::ng-deep .language-sql {
  text-wrap: auto;
  line-height: 1.5;
  white-space: pre;
  overflow-x: auto;
  max-width: 100%;
}

/* Syntax highlighting for SQL */
::ng-deep code.language-sql .keyword {
  color: #569CD6;
}

::ng-deep code.language-sql .function {
  color: #DCDCAA;
}

::ng-deep code.language-sql .string {
  color: #CE9178;
}

::ng-deep code.language-sql .number {
  color: #B5CEA8;
}

::ng-deep code.language-sql .operator {
  color: #D4D4D4;
}

::ng-deep code.language-sql .comment {
  color: #6A9955;
}

/* Enhanced Blog View Styles */
.blog-view-container {
  margin-top: 1.5rem;
  margin-bottom: 2rem;
}


/* Blog Header Enhancements */
.blog-header {
  position: relative;
  background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-purple) 100%);
}

.blog-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

/* Blog Body Enhancements */
.blog-body {
  background: linear-gradient(180deg, var(--background-white) 0%, rgba(var(--primary-purple-rgb), 0.02) 100%);
}

/* Blog Footer Enhancements */
.blog-footer {
  background: linear-gradient(90deg, var(--hover-blue-gray) 0%, var(--background-light-gray) 50%, var(--hover-blue-gray) 100%);
  backdrop-filter: blur(10px);
}

/* Enhanced Button Styles */
.blog-content button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.blog-content button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.blog-content button:hover::before {
  left: 100%;
}

/* Word Count Animation */
@keyframes countUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.word-count {
  animation: countUp 0.3s ease-out;
}

/* Enhance Markdown for Blog View */
:host ::ng-deep .markdown-blog h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-purple);
}

:host ::ng-deep .markdown-blog h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--secondary-purple);
  border-bottom: 1px solid var(--hover-blue-gray);
  padding-bottom: 0.25rem;
}

:host ::ng-deep .markdown-blog h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: var(--primary-purple);
}

:host ::ng-deep .markdown-blog p {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: var(--text-dark);
}

:host ::ng-deep .markdown-blog ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

:host ::ng-deep .markdown-blog ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
}

:host ::ng-deep .markdown-blog li {
  margin-bottom: 0.25rem;
  color: var(--text-dark);
}

:host ::ng-deep .markdown-blog a {
  color: var(--primary-purple);
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

:host ::ng-deep .markdown-blog a:hover {
  color: var(--secondary-purple);
  text-decoration-thickness: 2px;
}

:host ::ng-deep .markdown-blog blockquote {
  border-left: 4px solid var(--hover-blue-gray);
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--text-medium-gray);
}

:host ::ng-deep .markdown-blog img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-small);
  margin: 1rem 0;
}

:host ::ng-deep .markdown-blog code {
  background-color: var(--hover-blue-gray);
  padding: 0.125rem 0.25rem;
  border-radius: var(--border-radius-small);
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--primary-purple);
}

:host ::ng-deep .markdown-blog pre {
  background-color: var(--hover-blue-gray);
  padding: 1rem;
  border-radius: var(--border-radius-small);
  overflow-x: auto;
  margin: 1rem 0;
}

:host ::ng-deep .markdown-blog pre code {
  background-color: transparent;
  padding: 0;
  color: var(--text-dark);
}

:host ::ng-deep .markdown-blog table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

:host ::ng-deep .markdown-blog th,
:host ::ng-deep .markdown-blog td {
  border: 1px solid var(--hover-blue-gray);
  padding: 0.5rem;
}

:host ::ng-deep .markdown-blog th {
  background-color: var(--hover-blue-gray);
  font-weight: 600;
}

/* Email View Styles */
.email-view-container {
  margin: 16px 0;
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.email-content {
  background-color: var(--background-white);
  color: var(--text-dark);
}

.email-content .form-header {
  background-color: var(--primary-purple);
  color: var(--background-white);
}

.email-content .form-footer {
  background-color: var(--hover-blue-gray);
  border-top: 1px solid var(--hover-blue-gray);
}

.email-content input,
.email-content textarea {
  background-color: var(--background-white);
  color: var(--text-dark);
  border: 1px solid var(--hover-blue-gray);
}

.email-content input::placeholder,
.email-content textarea::placeholder {
  color: var(--text-medium-gray);
  opacity: 0.7;
}

.email-content input:focus,
.email-content textarea:focus {
  outline: none;
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 1px var(--primary-purple);
}

.email-content textarea {
  resize: vertical;
}

/* Dark mode specific styles */
:host-context(.dark) .email-content {
  background-color: #2A2A2A;
  border-color: #333333;
}

:host-context(.dark) .email-content input,
:host-context(.dark) .email-content textarea {
  background-color: #1E1E1E;
  border-color: #333333;
  color: var(--text-dark);
}

:host-context(.dark) .email-content .form-footer {
  background-color: #1E1E1E;
  border-color: #333333;
}

:host-context(.dark) .email-content .form-header {
  background-color: rgb(16, 163, 127);
  /* Dark mode primary color */
}

:host-context(.dark) .email-content .copy-button {
  background-color: #333333;
  color: #E0E0E0;
}

:host-context(.dark) .email-content .copy-button:hover {
  background-color: #444444;
}

:host-context(.dark) .email-content input:focus,
:host-context(.dark) .email-content textarea:focus {
  box-shadow: 0 0 0 1px rgb(16, 163, 127);
  border-color: rgb(16, 163, 127);
}

/* Left accent for input fields in dark mode */
:host-context(.dark) .email-content .input-accent {
  border-left: 3px solid rgb(16, 163, 127);
}

/* SQL Results Modal Styles */
:host ::ng-deep .sql-results-modal {
  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .sql-results-container {
    width: 100%;
    overflow-x: auto;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
    font-size: 14px;
  }

  th {
    background-color: #f5f5f5;
    padding: 8px 12px;
    text-align: left;
    font-weight: 600;
    border: 1px solid #e8e8e8;
  }

  td {
    padding: 8px 12px;
    border: 1px solid #e8e8e8;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  tbody tr:nth-child(odd) {
    background-color: #fafafa;
  }

  tbody tr:hover {
    background-color: #f0f0f0;
  }
}
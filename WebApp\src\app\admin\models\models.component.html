<!-- Model Settings -->
<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-ai-generate text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">AI Models</h1>
        <div class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filterdModels.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input
            type="text"
            placeholder="Search models..."
            [(ngModel)]="searchModelsQuery"
            (input)="filterModels()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200"
          />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchModelsQuery">
            <button
              (click)="searchModelsQuery = ''; filterModels()"
              class="text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] transition-colors focus:outline-none"
            >
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Provider Filter - Teams-style -->
        <div class="relative w-auto flex items-center">
          <select
            [(ngModel)]="selectedProvider"
            (change)="filterModels()"
            class="appearance-none w-auto h-8 px-3 py-1 pr-8 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200 cursor-pointer"
          >
            <option value="">All Providers</option>
            <option *ngFor="let provider of uniqueProviders" [value]="provider">{{ provider }}</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2 pointer-events-none">
            <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
        </div>

        <!-- Change Model Button - Teams-style -->
        <button
          (click)="openChangeModelDialog()"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1"
        >
          <i class="ri-settings-line"></i>
          <span>Change Model</span>
        </button>
      </div>
    </div>


    <!-- Content Area with Active Model and Models List -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="relative min-h-[300px]">
        <app-spinner message="Loading models..." [overlay]="false"></app-spinner>
      </div>

      <!-- Active Model Card -->
      <div *ngIf="!isLoading" class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 mb-4">
        <div class="p-3 border-b border-[var(--hover-blue-gray)]">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div
                class="w-8 h-8 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
                <i class="ri-ai-generate text-[var(--primary-purple)] text-lg"></i>
              </div>
              <div>
                <h3 class="text-sm font-medium text-[var(--text-dark)]">Active Embedding Model</h3>
                <p class="text-xs text-[var(--text-medium-gray)] leading-tight">Currently used for all embedding
                  operations</p>
              </div>
            </div>
            <span
              class="inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-600">
              {{ activeEmbeddingModel.embeddingModelId ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>

        <div class="p-3">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-0.5">Model ID</label>
              <div class="bg-[var(--hover-blue-gray)] bg-opacity-50 rounded-md px-2 py-1.5 flex-1">
                <p class="text-sm text-[var(--text-dark)] font-medium break-all m-0">
                  {{ activeEmbeddingModel.embeddingModelId}}
                </p>
              </div>
            </div>

            <div>
              <label class="text-xs font-medium text-[var(--text-medium-gray)] block mb-0.5">API Key</label>
              <div class="bg-[var(--hover-blue-gray)] bg-opacity-50 rounded-md px-2 py-1.5 flex-1">
                <p class="text-sm text-[var(--text-dark)] font-medium font-mono m-0">
                  {{ activeEmbeddingModel.apiKey ? (activeEmbeddingModel.apiKey | slice:0:4) + '****' : 'No API key' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Models Card View -->
      <div *ngIf="!isLoading" class="bg-[var(--background-light-gray)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30 p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Model Card -->
        <div *ngFor="let model of filterdModels; let i = index"
          class="bg-[var(--background-white)] rounded-md overflow-hidden group animate-fadeIn relative"
          [ngStyle]="{'animation-delay': (i * 0.05) + 's'}">
          <!-- Action Buttons (Top Right) -->
          <div class="absolute top-3 right-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              (click)="openChangeModelDialog()"
              class="action-button w-8 h-8 rounded-md bg-[#E6F7FF] hover:bg-[#BAE7FF] transition-all duration-200 flex items-center justify-center border-none shadow-sm"
              title="Change Active Model"
            >
              <i class="ri-settings-line text-blue-500 text-base"></i>
            </button>
          </div>

          <div class="p-3">
            <div class="flex items-start gap-3">
              <!-- Model Icon -->
              <div
                class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center flex-shrink-0">
                <i class="ri-file-text-line text-white"></i>
              </div>

              <!-- Model Info -->
              <div class="flex-1 min-w-0">
                <!-- Model Name and ID -->
                <div class="flex flex-col">
                  <h3 class="text-base font-medium text-[var(--text-dark)] truncate max-w-[180px]"
                    title="{{ model.modelName | removeProviderPrefix  }}">
                    {{ model.modelName | removeProviderPrefix }}
                  </h3>
                </div>

                <!-- Provider Badge -->
                <div class="mt-1">
                  <span
                    class="inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
                    {{ model.modelProvider }}
                  </span>
                </div>

                <!-- AI Agents -->
                <div class="mt-3">
                  <p class="text-xs text-[var(--text-medium-gray)] mb-1">AI Agents</p>
                  <div class="flex flex-wrap gap-1">
                    <span *ngFor="let agent of model.agentNames"
                      class="inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)] max-w-[120px] overflow-hidden"
                      title="{{ agent }}">
                      <span class="truncate">{{ agent }}</span>
                    </span>
                    <span *ngIf="model.agentNames.length === 0" class="text-xs text-[var(--text-medium-gray)]">
                      No agents associated
                    </span>
                  </div>
                </div>

                <!-- Status Indicator -->
                <div class="flex items-center gap-2 mt-2">
                  <div class="flex items-center justify-center">
                    <span class="w-2 h-2 rounded-full"
                      [ngClass]="model.isActive ? 'bg-green-500' : 'bg-gray-400'"></span>
                  </div>
                  <span class="text-xs text-[var(--text-medium-gray)] leading-none">{{ model.isActive ? 'Active' :
                    'Inactive' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isLoading && filterdModels.length === 0" class="flex flex-col items-center justify-center py-16 px-4">
          <div
            class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
            <i class="ri-ai-generate text-3xl text-[var(--text-medium-gray)]"></i>
          </div>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No models found</h3>
          <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-6">
            No models match your current search criteria. Try adjusting your search or filter settings.
          </p>
          <button (click)="searchModelsQuery = ''; selectedProvider = ''; filterModels()"
            class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md hover:bg-[var(--secondary-purple)] transition-all duration-300 flex items-center gap-2">
            <i class="ri-refresh-line"></i>
            <span>Reset Filters</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

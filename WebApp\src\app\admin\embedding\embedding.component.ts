// import { CommonModule } from '@angular/common';
// import { Component } from '@angular/core';
// import { FormsModule } from '@angular/forms';
// import { EmbeddingConfigServiceProxy } from '../../../shared/service-proxies/service-proxies';
// import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
// import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
// import { AddOrEditEmbeddingComponent } from './add-or-edit-embedding/add-or-edit-embedding.component';
// import { NzMessageService } from 'ng-zorro-antd/message';
// import { ChangeActiveModelComponent } from './change-active-model/change-active-model.component';

// @Component({
//   selector: 'app-embedding',
//   standalone: true,
//   imports: [CommonModule, FormsModule, ServiceProxyModule, NzModalModule, AddOrEditEmbeddingComponent],
//   templateUrl: './embedding.component.html',
//   styleUrl: './embedding.component.css',
// })
// export class EmbeddingComponent {
//   apiConfigs: any = [];
//   isUpdating = false;
//   apiConfig: any = { id: 0, modelId: '', provider: '', apiKey: '', isActive: true };

//   currentActiveModelData: any;
//   isActiveModel = false;



//   constructor(
//     private _embeddingService: EmbeddingConfigServiceProxy,
//     public modal: NzModalService,
//     private message: NzMessageService
//   ) { }

//   ngOnInit() {
//     this.loadEmbedding();
//     this.currentActiveModel();
//   }

//   currentActiveModel() {
//     this._embeddingService.current().subscribe((res: any) => {
//       if (res) {
//         this.currentActiveModelData = res;
//         this.isActiveModel = true;
//       }

//       console.log(res);
//     });
//   }

//   changeActiveModel() {
//     const modalRef = this.modal.create({
//       nzTitle: '',
//       nzContent: ChangeActiveModelComponent,
//       nzFooter: null,
//       nzWidth: 500,
//       nzClassName: 'change-active-model-modal',
//     });

//     const instance = modalRef.componentInstance;
//     if (instance) {
//       instance.modelChanged.subscribe((result: any) => {
//         this.currentActiveModel();
//         this.loadEmbedding();
//         this.message.success('Active model changed successfully');
//       });
//     }
//   }


//   resetForm() {
//     this.apiConfig = {
//       id: 0,
//       modelId: '',
//       provider: '',
//       apiKey: '',
//       isActive: false,
//     };
//   }

//   loadEmbedding() {
//     this._embeddingService.getAllConfigurations().subscribe((res: any) => {
//       if (res) {
//         console.log(res);
//         this.apiConfigs = res;
//       }
//     });

//   }

//   openAddEditModal(config?: any) {
//     this.isUpdating = !!config;
//     if (config) {
//       this.apiConfig = { ...config };
//     } else {
//       this.resetForm();
//     }

//     const modalRef = this.modal.create({
//       nzTitle: this.isUpdating ? 'Update API Configuration' : 'Add API Configuration',
//       nzContent: AddOrEditEmbeddingComponent,
//       nzData: {
//         isUpdating: this.isUpdating,
//         apiConfig: this.apiConfig,
//         id: this.apiConfig.id
//       },
//       nzFooter: null,
//       nzWidth: 400,
//       nzClassName: 'embedding-modal',
//     });

//     const instance = modalRef.componentInstance;
//     if (instance) {
//       instance.save.subscribe((result: any) => {
//         if (this.isUpdating) {
//           this.editConfig(result);
//         } else {
//           this.addApiConfig(result);
//         }
//       });

//       instance.cancel.subscribe(() => {
//         this.modal.closeAll();
//       });
//     }
//   }



//   addApiConfig(config: any) {
//     this._embeddingService.createEmbeddingConfig(config).subscribe(
//       (res: any) => {
//         console.log(res);
//         this.loadEmbedding();
//         this.currentActiveModel();
//         this.message.success('Configuration created successfully');
//         this.modal.closeAll();
//       },
//       (error: any) => {
//         this.message.error('Failed to create embedding configuration');
//         console.error('Failed to create embedding configuration:', error);
//       }
//     );
//   }


//   editConfig(config: any) {
//     const index = this.apiConfigs.findIndex(
//       (c: any) => c.id === config.id
//     );
//     if (index !== -1) {
//       this.apiConfigs[index] = { ...config };
//       this.message.success('Configuration updated successfully');
//     }
//     this.modal.closeAll();
//     this.loadEmbedding();
//     this.currentActiveModel();
//   }

//   deleteConfig(config: any) {
//     this.modal.confirm({
//       nzTitle: 'Are you sure you want to delete this configuration?',
//       nzContent: 'This action cannot be undone.',
//       nzOkText: 'Yes',
//       nzOkType: 'primary',
//       nzOkDanger: true,
//       nzOnOk: () => {
//         console.log(`Deleting configuration with id: ${config.id}`);
//         this._embeddingService.deleteEmbeddingConfig(config.id).subscribe((res: any) => {
//           this.message.success('Configuration deleted successfully');
//           this.loadEmbedding();
//         }, (error: any) => {
//           if (error.isError && error.message) {
//             this.message.error(error.message);
//           } else {
//             this.message.error('Failed to delete embedding configuration');
//             console.error('Failed to delete embedding configuration:', error);
//           }
//         });
//       },
//       nzCancelText: 'No',
//     });
//   }
// }

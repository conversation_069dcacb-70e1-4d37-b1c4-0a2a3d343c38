.spinner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

:host-context(.dark) .spinner-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Spinner container with size variations */
.spinner-container {
  position: relative;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-container.small {
  width: 40px;
  height: 40px;
}

.spinner-container.medium {
  width: 60px;
  height: 60px;
}

.spinner-container.large {
  width: 80px;
  height: 80px;
}

/* SVG spinner styles */
.spinner-svg {
  width: 100%;
  height: 100%;
  animation: rotate 2s linear infinite;
  transform-origin: center;
}

/* Track circle (background) */
.spinner-track {
  stroke: rgba(107, 70, 193, 0.1);
  stroke-width: 2;
}

/* Progress circle (animated) */
.spinner-progress {
  stroke: var(--primary-purple, #6B46C1);
  stroke-dasharray: 126; /* Circumference of circle with r=20 */
  stroke-dashoffset: 126; /* Start with full offset (invisible) */
  animation: progress 1.5s ease-in-out infinite;
  transform-origin: center;
  filter: drop-shadow(0 0 2px rgba(107, 70, 193, 0.7));
  stroke-linecap: round;
}

/* Center dot */
.spinner-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20%;
  height: 20%;
  background-color: #FF5722;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
  box-shadow: 0 0 8px rgba(255, 87, 34, 0.7);
}

/* Add a subtle glow effect to the dot */
.spinner-dot::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 87, 34, 0.3) 0%, rgba(255, 87, 34, 0) 70%);
  border-radius: 50%;
  z-index: -1;
}

/* Loading text container styling */
.loading-text-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
}

/* Loading text wrapper */
.loading-text-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0.5rem;
}

/* Loading text content */
.loading-text-content {
  font-weight: 600;
  letter-spacing: 0.5px;
  font-size: 0.95rem;
  color: var(--primary-purple, #6B46C1);
  text-shadow: 0 0 10px rgba(107, 70, 193, 0.2);
  background: linear-gradient(90deg, var(--primary-purple, #6B46C1), #FF5722, var(--primary-purple, #6B46C1));
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  animation: text-shimmer 2s infinite;
}

/* Text shimmer effect */
@keyframes text-shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Loading text progress bar */
.loading-text-bar {
  height: 2px;
  width: 100%;
  background: linear-gradient(90deg,
    rgba(107, 70, 193, 0.2),
    rgba(255, 87, 34, 0.2),
    rgba(107, 70, 193, 0.2)
  );
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

/* Loading text progress bar animation */
.loading-text-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 30%;
  background: linear-gradient(90deg,
    rgba(107, 70, 193, 0.1),
    rgba(255, 87, 34, 0.7),
    rgba(107, 70, 193, 0.1)
  );
  border-radius: 2px;
  animation: loading-bar 1.5s ease-in-out infinite;
}

/* Fade in animation for text */
.fade-in-animation {
  animation: fadeIn 0.5s ease-in-out;
}

/* Dark mode specific styles */
:host-context(.dark) .spinner-track {
  stroke: rgba(255, 255, 255, 0.15);
}

:host-context(.dark) .spinner-progress {
  stroke: var(--primary-purple, #6B46C1);
}

:host-context(.dark) .spinner-dot {
  background-color: #FF5722;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.6);
}

:host-context(.dark) .loading-text-content {
  color: var(--primary-purple, #6B46C1);
  text-shadow: 0 0 10px rgba(107, 70, 193, 0.4);
}

:host-context(.dark) .loading-text-bar {
  background: linear-gradient(90deg,
    rgba(107, 70, 193, 0.3),
    rgba(255, 87, 34, 0.3),
    rgba(107, 70, 193, 0.3)
  );
}

/* White theme specific styles */
:host-context(:not(.dark)) .spinner-track {
  stroke: rgba(107, 70, 193, 0.1);
}

:host-context(:not(.dark)) .spinner-progress {
  stroke: var(--primary-purple, #6B46C1);
}

:host-context(:not(.dark)) .spinner-dot {
  background-color: #FF5722;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.5);
}

/* Animation keyframes */
@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progress {
  0% {
    stroke-dashoffset: 126;
    stroke: var(--primary-purple, #6B46C1);
  }
  50% {
    stroke-dashoffset: 0;
    stroke: #FF5722;
  }
  100% {
    stroke-dashoffset: 126;
    stroke: var(--primary-purple, #6B46C1);
  }
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.7; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.7; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Loading dots animation */
.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
  margin-left: 2px;
  font-weight: bold;
  color: #FF5722;
  background: linear-gradient(90deg, var(--primary-purple, #6B46C1), #FF5722);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes loading-dots {
  0% { content: ''; }
  25% { content: '.'; }
  50% { content: '..'; }
  75% { content: '...'; }
  100% { content: ''; }
}

/* Loading bar animation */
@keyframes loading-bar {
  0% { left: -30%; }
  100% { left: 100%; }
}

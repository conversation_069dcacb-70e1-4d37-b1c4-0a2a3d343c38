<div class="flex flex-col min-h-screen bg-[var(--background-light-gray)]">
  <!-- Header - Now visible on all pages including daily insights -->
  <div *ngIf="authService.isUserLoggedIn" class="w-full fixed top-0 left-0 z-50 h-[65px]">
    <app-header></app-header>
  </div>

  <!-- Main Content Area with Split Layout -->
  <div class="flex-grow" *ngIf="authService.isUserLoggedIn">
    <as-split direction="horizontal" [gutterSize]="2" [useTransition]="true"
      (dragEnd)="onSplitDragEnd($event)"
      (dragProgress)="onSplitDragProgress($event)"
      (gutterDblClick)="onGutterDoubleClick($event)"
      (gutterClick)="onGutterClick($event)"
      [gutterClickDeltaPx]="5"
      class=" mt-[65px]" style="max-height: calc(100vh - 65px)!important; height: calc(100vh - 65px)!important;">

      <!-- Custom gutter template -->
      <ng-template asSplitGutter>
        <!-- <div class="tooltip">Click to toggle | Drag to resize</div> -->
      </ng-template>
      <!-- Sidebar Split Area -->
      <as-split-area [size]="splitSizes.sidebarArea" [minSize]="minSizes.sidebarArea" class="overflow-hidden"
        [ngClass]="{'sidebar-collapsed-area': sidebarState === 'collapsed', 'sidebar-narrow-area': sidebarState === 'narrow', 'sidebar-expanded-area': sidebarState === 'expanded'}">
        <div class="h-full relative">
          <!-- Width indicator (visible during drag) -->
          <div class="width-indicator" *ngIf="isDragging">{{ Math.round(splitSizes.sidebarArea) }}%</div>

          <!-- Hidden splitter controls - only shown on hover -->
          <div class="splitter-hover-area"
               (click)="onGutterClick($event)"
               (dblclick)="onGutterDoubleClick($event)"
               (mousedown)="onSplitterMouseDown($event)"
               (mousemove)="onSplitterMouseMove($event)"
               (mouseup)="onSplitterMouseUp($event)">
            <!-- Tooltip for user guidance -->
            <div class="tooltip">Click to toggle | Drag to resize</div>
          </div>

          <app-sidebar />
        </div>
      </as-split-area>

      <!-- Main Content Split Area -->
      <as-split-area [size]="splitSizes.contentArea" [minSize]="minSizes.contentArea">
        <div class="h-full overflow-auto ">
          <div class="route-transition w-full h-full" [class.fade-out]="isNavigating">
            <router-outlet />
          </div>
        </div>
      </as-split-area>
    </as-split>
  </div>

  <!-- Login/Register Pages (No Sidebar) -->
  <div *ngIf="!authService.isUserLoggedIn" class="w-full min-h-screen">
    <router-outlet />
  </div>
</div>

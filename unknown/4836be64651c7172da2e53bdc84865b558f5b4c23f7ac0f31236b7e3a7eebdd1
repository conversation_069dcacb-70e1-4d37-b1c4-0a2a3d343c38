<div class="bg-background-light-gray h-full p-2 " style="height: calc(100vh - 74px); overflow-y: auto;">
  <!-- Header -->
  <!-- <div class="flex items-center justify-between mb-4">
    <h1 class="text-primary-purple text-xl sm:text-2xl md:text-3xl font-bold mb-2">Workspaces</h1>
  </div>

  <div class="flex items-center justify-between mb-4">
    <nz-breadcrumb class="m-0 text-[var(--text-dark)] text-sm sm:text-base">
      <nz-breadcrumb-item>
        <a [routerLink]="['/']" class="!text-[var(--text-dark)]">AI Hub</a>
      </nz-breadcrumb-item>
      <nz-breadcrumb-item>
        <a [routerLink]="['/workspaces']" class="!text-[var(--text-dark)]">Workspaces</a>
      </nz-breadcrumb-item>
    </nz-breadcrumb>
  </div> -->

  <!-- Main Content -->
  <main class="flex-1 rounded-lg p-4 sm:p-6 shadow-md overflow-auto">
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
      <!-- Add Workspace Card - Only visible to admin users -->
      <div *ngIf="isAdmin"
        class="border-2 border-dashed border-gray-300 bg-gray-50 rounded-lg  h-64 flex items-center flex-col justify-center cursor-pointer transition-all duration-300 hover:border-indigo-500 hover:bg-indigo-50 group"
        (click)="addWorkspace()" style="margin: auto 0;">
        <i
          class="ri-add-line text-3xl sm:text-4xl text-gray-600 group-hover:text-indigo-500 transition-colors duration-300"></i>
        <span
          class="text-gray-600 text-base sm:text-lg font-semibold mt-2 group-hover:text-indigo-500 transition-colors duration-300">
          Add Workspace
        </span>
      </div>

      <!-- Workspace Cards -->
      @for (workspace of workspaceList; track $index) {
      <div
        class="relative border-2 border-gray-300 bg-[var(--secondary-purple)] rounded-xl h-auto flex flex-col cursor-pointer px-4 py-3 transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl hover:border-indigo-400 group">

        <!-- Action Buttons -->
        <div *ngIf="isAdmin"
          class="p-2 flex justify-end w-auto absolute z-50 right-3 top-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button
            class="text-teal-600 outline-none border-none p-1.5 px-2.5 text-lg hover:text-indigo-600 hover:bg-indigo-100/80 rounded-full transition-all duration-200 backdrop-blur-sm shadow-sm"
            (click)="editWorkspace(workspace, $event)" aria-label="Edit Workspace">
            <i class="ri-edit-line"></i>
          </button>
          <button
            class="text-teal-600 outline-none border-none p-1.5 px-2.5 text-lg hover:text-red-500 hover:bg-red-100/80 cursor-pointer z-50 rounded-full transition-all duration-200 ml-2 backdrop-blur-sm shadow-sm"
            (click)="deleteWorkspace(workspace, $event)" aria-label="Delete Workspace">
            <i class="ri-delete-bin-line"></i>
          </button>
        </div>

        <!-- Workspace Content -->
        <div class="flex flex-col h-full justify-between" (click)="viewWorkspace(workspace.title, $event)">
          <div class="space-y-2">
            <h2
              class="text-teal-800 text-xl sm:text-2xl font-semibold line-clamp-1 drop-shadow-sm group-hover:text-indigo-800 transition-colors duration-300">
              {{ workspace.title }}
            </h2>

            <!-- Description -->
            <div class="bg-white/50 p-2 rounded-md">
              <p class="text-gray-800 text-sm font-medium">
                {{ workspace.description }}
              </p>
            </div>

            <!-- System Information -->
            <div class="bg-white/50 p-2 rounded-md">
              <h3 class="text-sm font-semibold text-gray-700">System Information:</h3>
              <p class="text-gray-800 text-sm">
                {{ workspace.systemInformation }}
              </p>
            </div>

            <!-- Model Information -->
            <div class="bg-white/50 p-2 rounded-md">
              <h3 class="text-sm font-semibold text-gray-700">Model:</h3>
              <p class="text-gray-800 text-sm">
                {{ workspace.modelName }}
              </p>
            </div>
          </div>

          <!-- Workspace Properties -->
          <div class="flex flex-wrap gap-2 mt-3">
            <div class="flex items-center px-2 py-1 rounded-full text-xs font-medium"
              [ngClass]="workspace.isDefault ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
              <i class="ri-checkbox-circle-fill mr-1" *ngIf="workspace.isDefault"></i>
              <i class="ri-close-circle-fill mr-1" *ngIf="!workspace.isDefault"></i>
              <span>Default Workspace</span>
            </div>

            <div class="flex items-center px-2 py-1 rounded-full text-xs font-medium"
              [ngClass]="workspace.isProjectManagement ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'">
              <i class="ri-checkbox-circle-fill mr-1" *ngIf="workspace.isProjectManagement"></i>
              <i class="ri-close-circle-fill mr-1" *ngIf="!workspace.isProjectManagement"></i>
              <span>Project Management</span>
            </div>
          </div>
        </div>

        <!-- Members Section -->
        <div class="mt-3 flex items-center border-t border-gray-200/50 pt-2">
          <div class="relative flex items-center gap-2">
            @if (workspace.members?.length) {
            <div *ngFor="let member of workspace.members?.slice(0, 4); let i = index" (click)="openDialog(workspace.id)"
              class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 text-gray-800 text-xs font-medium border-2 border-white shadow-sm hover:scale-110 transition-transform duration-200"
              [ngStyle]="{ 'z-index': workspace.members.length + i, 'margin-left': i > 0 ? '-0.75rem' : '0' }">
              {{ member.name.slice(0, 2) | uppercase }}
            </div>
            <div *ngIf="workspace.members.length > 4"
              class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-500 text-white text-xs font-medium border-2 border-white shadow-sm"
              [ngStyle]="{ 'z-index': workspace.members.length + 3, 'margin-left': '-0.75rem' }">
              +{{ workspace.members.length - 4 }}
            </div>
            } @else {
            <div
              class="flex items-center justify-center h-8 text-sm text-gray-600 hover:text-indigo-600 transition-colors duration-200"
              *ngIf="isAdmin" (click)="openDialog(workspace.id)">
              <i class="ri-user-add-line"></i>
              <span class="ml-1.5 font-medium">Add members</span>
            </div>

            <div *ngIf="!isAdmin" class="flex items-center justify-center h-8 text-sm text-gray-600">
              <span>No members</span>
            </div>
            }
          </div>
        </div>
      </div>
      }
    </div>
  </main>
</div>
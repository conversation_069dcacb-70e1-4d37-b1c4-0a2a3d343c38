import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AgentDefinitionServiceProxy,
  ModelDetailsServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';

import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-ai-agent',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ServiceProxyModule,
    NzInputModule,
    NzIconModule,
    NzAutocompleteModule,
  ],
  templateUrl: './ai-agent.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  styles: [
    `
      .search-item {
        display: flex;
      }

      .search-item-desc {
        flex: auto;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .search-item-count {
        flex: none;
      }
      .ant-select-dropdown{
        background:black !important;
      }
    `,
  ],
})
export class AiAgentComponent implements OnInit {
  agents: any[] = [];
  currentAgent: any = {
    id: '',
    agentName: '',
    instructions: '',
  };
  isEditing = false;
  showForm = false;
  activeModels: any = [];
  modelSearchQuery = '';
  selectedModel = '';
  filteredModels: any[] = [];
  constructor(
    private agentDefinitionService: AgentDefinitionServiceProxy,
    private modelDetailsService: ModelDetailsServiceProxy
  ) { }

  ngOnInit(): void {
    // Initialize agents if needed
    this.loadAiAgents();
    this.loadActiveModels();
  }
  loadAiAgents() {
    this.agentDefinitionService.getAll().subscribe((result) => {
      this.agents = result;
      // console.log(this.agents);
    });
  }
  loadActiveModels() {
    this.modelDetailsService.getAllActiveModel().subscribe((result: any) => {
      this.activeModels = result;
      this.filteredModels = result;
    });
  }

  addNewAgent(): void {
    this.isEditing = false;
    this.currentAgent = {
      agentName: '',
      instructions: '',
    };
    this.showForm = true;
  }

  editAgent(agent: any): void {
    this.showForm = true;
    this.isEditing = true;
    this.currentAgent = { ...agent };
    this.selectedModel = agent.modelName;
    this.modelSearchQuery = agent.modelName;
  }
  saveEditedAgent(): void {
    this.currentAgent.modelName = this.selectedModel;

    // Ensure we maintain the workspace when editing
    // If no workspace is set (from global settings), use "Default"
    if (!this.currentAgent.workspace) {
      this.currentAgent.workspace = "Default";
    }

    this.agentDefinitionService
      .createOrUpdate(this.currentAgent)
      .subscribe((res: any) => {
        console.log(res);
        if (res) {
          // let updatedModel = this.agents.filter(
          //   (agent) => agent.id == this.currentAgent.id,
          // );
          // console.log(this.agents);
          this.loadAiAgents();
          this.showForm = false;
        }
      });
    this.resetForm();
  }
  updateModel(model: string) {

    this.selectedModel = model;
  }
  saveAgent(): void {
    this.currentAgent.modelName = this.selectedModel;
    // Set workspace to Default when adding from global settings
    this.currentAgent.workspace = "Default";

    console.log(this.currentAgent);
    this.agentDefinitionService
      .createOrUpdate(this.currentAgent)
      .subscribe((res: any) => {
        console.log(res);
        if (res) {
          this.agents.push(res);
          this.showForm = false;
        }
      });
    this.resetForm();
    this.selectedModel = '';
    this.modelSearchQuery = '';
  }

  deleteAgent(name: string): void {
    this.agentDefinitionService.delete(name).subscribe((res: any) => {
      if (res) {
        this.agents = this.agents.filter((agent) => agent.agentName !== name);
      }
    });
  }

  resetForm(): void {
    this.currentAgent = {
      id: '',
      agentName: '',
      instructions: '',
    };
    this.selectedModel = '';
    this.modelSearchQuery = '';
    this.showForm = false;

  }

  onChange(e: Event): void {
    const value = (e.target as HTMLInputElement).value;
    this.filteredModels = this.activeModels.filter((model: any) =>
      model.modelName.toLowerCase().includes(value.toLowerCase())
    );
  }
}

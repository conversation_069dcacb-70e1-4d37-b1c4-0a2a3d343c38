/* Table-specific styling */
.memory-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.memory-table thead th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--hover-blue-gray);
  font-weight: 500;
}

.memory-table tbody tr:last-child {
  border-bottom: none;
}

.memory-table tbody tr:hover {
  background-color: var(--background-light-gray);
}

/* Description truncation */
.description-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

/* Action button styling */
.action-button {
  opacity: 0.9;
  transition: all 0.2s ease;
}

.action-button:hover {
  opacity: 1;
  transform: translateY(-1px);
}

/* Animation */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dark Theme Styling */
:host-context(.dark-theme) input,
:host-context(.dark-theme) textarea,
:host-context(.dark-theme) select {
  background-color: #3F3F5A !important;
  color: white !important;
  border-color: #4D4D6A !important;
}

:host-context(.dark-theme) .dialog-container {
  background-color: #2D2D3A !important;
}

:host-context(.dark-theme) label {
  color: white !important;
}

:host-context(.dark-theme) .cancel-button {
  background-color: #444455 !important;
  color: white !important;
}

:host-context(.dark-theme) .primary-button {
  background-color: #00C39A !important;
  color: white !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .description-truncate {
    max-width: 200px;
  }
}

@media (max-width: 640px) {
  .memory-table {
    min-width: 100%;
  }

  .memory-table th,
  .memory-table td {
    padding: 0.5rem;
    font-size: 12px;
  }

  .description-truncate {
    max-width: 150px;
    -webkit-line-clamp: 1;
  }
}

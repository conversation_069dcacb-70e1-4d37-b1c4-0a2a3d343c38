import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Model {
  name: string;
}

@Component({
  selector: 'app-evaluations',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './evaluations.component.html',
  styleUrl: './evaluations.component.css'
})
export class EvaluationsComponent {
  tabs: 'leaderboard' | 'feedbacks' = 'leaderboard';
  models: Model[] = [
    { name: 'deepseek-rt:1.5b' },
    { name: 'deepseek-rt:14b' },
    { name: 'mistral-nemo:latest' }
  ];

  setTab(tab: 'leaderboard' | 'feedbacks') {
    this.tabs = tab;
    console.log('Tab changed to:', tab);

  }
}

<!-- Main Container -->
<div class=" flex flex-col  relative bg-[var(--background-light-gray)] ">
  <!-- Header -->
  <header class="mb-6">
    <div class="flex justify-end items-center">

      <div class="flex justify-start  ga-2">

        <button *ngIf=" documentId"
          class="mx-4 px-4 hover:text-black cursor-pointer py-2 bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] border-none outline-none "
          (click)="saveDocument()">
          {{ documentId!=0 ? 'Update' : 'Save' }}
        </button>
      </div>
    </div>
  </header>

  <!-- Document Form -->
  <main
    class="flex-1 rounded-[var(--border-radius-large)] p-4 sm:p-6 shadow-[var(--box-shadow)] bg-[var(--background-white)]">
    <div class="flex flex-col md:flex-row gap-6">
      <!-- Left Section (Title and Uploads) -->
      <div class="flex-1 space-y-6">
        <!-- Title -->
        <div>
          <label class=" text-[var(--text-dark)] block mb-2">Title</label>
          <input type="text" [(ngModel)]="documentData.title"
            class="w-full p-2 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] bg-[var(--background-light-gray)] focus:ring-2 focus:ring-[var(--primary-purple)] transition-[var(--transition-default)]"
            placeholder="Enter document title..." />
        </div>

        <!-- Upload Section -->
        <div>
          <label class=" text-[var(--text-dark)] block mb-2">Upload Your Docs</label>
          <div class="grid grid-cols-2 gap-4 overflow-y-auto  h-[45vh]">
            <!-- Add Document Placeholder -->


            <!-- Uploaded Files -->
            <div *ngFor="let file of documentData.files; let i = index"
              class="relative border-2 border-[var(--hover-blue-gray)] bg-[var(--background-light-gray)] rounded-[var(--border-radius-large)] h-32 flex items-center justify-center cursor-pointer transition-[var(--transition-default)] group ">
              <img [src]="file.filePath" class="w-full h-full object-cover rounded-[var(--border-radius-large)]"
                alt="Uploaded image" />

              <button
                class="absolute top-2 right-2 p-1 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] text-[var(--text-dark)] hover:bg-[var(--primary-purple)] hover:text-[var(--background-white)] transition-[var(--transition-default)] outline-none border-none text-red-700 opacity-0 group-hover:opacity-100 cursor-pointer"
                (click)="removeFile(i)" aria-label="Remove file">
                <i class="ri-delete-bin-line text-lg"></i>
              </button>
            </div>
            <label
              class="border-2 border-dashed border-[var(--hover-blue-gray)] bg-[var(--background-light-gray)] rounded-[var(--border-radius-large)] h-32 flex flex-col items-center justify-center cursor-pointer hover:border-[var(--primary-purple)] hover:bg-[var(--secondary-purple)]/20 transition-[var(--transition-default)] group">
              <input type="file" (change)="onFileSelect($event)" accept="image/*,.pdf" class="hidden" multiple />
              <i
                class="ri-add-line text-3xl text-[var(--text-dark)] group-hover:text-[var(--primary-purple)] transition-[var(--transition-default)]"></i>
              <span
                class=" text-[var(--text-dark)] group-hover:text-[var(--primary-purple)] transition-[var(--transition-default)]">
                Click to Add
              </span>
            </label>
          </div>
        </div>

        <!-- Save/Update Button (Bottom Left) -->

      </div>

      <!-- Right Section (Description) -->
      <div class="flex-1">
        <label class=" text-[var(--text-dark)] block mb-2">Description</label>
        <div id="editor"
          class="w-full p-2 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] h-[95%] bg-[var(--background-light-gray)]">
        </div>
      </div>
    </div>
  </main>
</div>

<div class="flex-1 flex flex-col overflow-hidden bg-gray-800 rounded-lg m-3">
  <div class="flex items-center justify-between p-4 border-b border-gray-700">
    <div class="flex items-center">
      <button (click)="goBack()" class="mr-4 text-gray-400 hover:text-white">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
      <h2 class="text-xl font-semibold">{{ isNew ? 'Create New Document' : 'Edit Document' }}</h2>
    </div>
  </div>

  <div class="p-4 flex-1 overflow-y-auto">
    <!-- Your existing form elements, styled with Tailwind -->
    <form>
      <div class="mb-4">
        <label class="block text-gray-300 mb-2">Title</label>
        <input type="text" [(ngModel)]="docToEdit.title" name="title"
               class="w-full bg-gray-700 text-white border border-gray-600 rounded p-2 focus:outline-none focus:border-blue-500">
      </div>

      <div class="mb-4">
        <label class="block text-gray-300 mb-2">Content</label>
        <!-- Your existing editor component -->
      </div>

      <!-- Keep your existing file upload component -->
      <div class="mb-4">
        <label class="block text-gray-300 mb-2">Attachments</label>
        <!-- Your existing file upload HTML -->
      </div>

      <div class="flex justify-end space-x-3 mt-6">
        <button type="button" (click)="goBack()" class="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600">
          Cancel
        </button>
        <button type="button" (click)="saveDocument()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-500">
          Save
        </button>
      </div>
    </form>
  </div>
</div>

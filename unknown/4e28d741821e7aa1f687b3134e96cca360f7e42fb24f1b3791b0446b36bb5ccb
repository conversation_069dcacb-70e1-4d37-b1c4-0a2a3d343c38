import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface User {
  role: string;
  name: string;
  email: string;
  lastActive: string;
  createdAt: string;
  oauthId?: string;
}

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './users.component.html',
  styleUrl: './users.component.css'
})
export class UsersComponent {
  tabs:'overview' | 'groups' = 'overview';

  users = [
    {
      role: 'ADMIN',
      name: 'Test',
      email: '<EMAIL>',
      lastActive: 'a few seconds ago',
      createdAt: 'December 20, 2024'
    }
  ];

  changeUserRole(user: any) {
    // Implement role change logic here
    console.log('Changing role for user:', user);
  }
}
